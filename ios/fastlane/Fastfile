# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD = "giir-tcaj-zazm-zwjk"

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  
  desc "Push a new beta build to TestFlight"
  lane :stage do

    # get_certificates           # invokes cert
    # get_provisioning_profile   # invokes sigh

    changelog = changelog_from_git_commits(
      commits_count: 20,
      pretty: "- %s",
      date_format: "short",
      match_lightweight_tag: true,
      merge_commit_filtering: "exclude_merges"
    )
    
    # remove the lines that are not needed
    filtered_changelog = changelog.split("\n").reject { |line| line.include?("- update version")|| line.include?("missing commit") || line.include?("for testing") }.join("\n")
    # UI.message("Changelog content:")
    # UI.message(filtered_changelog)
    increment_build_number(xcodeproj: "TaylorMade.xcodeproj")

    build_app(
      workspace: "TaylorMade.xcworkspace", 
      scheme: "Staging",
      clean: true,
      silent: true,
      output_directory: "./build", # Destination directory. Defaults to current directory.
      output_name: "TaylorMade-TestFlight.ipa",
      xcargs: "-allowProvisioningUpdates"
    )
    
    upload_to_testflight(
      ipa: './build/TaylorMade-TestFlight.ipa',
      changelog: filtered_changelog,
      groups: ["H&L"],
      distribute_external: true,
      notify_external_testers: true,
    )
  end

  desc "Push a new beta build to TestFairy"
  lane :qa do

    sync_code_signing(force_for_new_devices: true, type: "adhoc")

    #get_certificates           # invokes cert
    #get_provisioning_profile   # invokes sigh

    increment_build_number(xcodeproj: "TaylorMade.xcodeproj")

    build_app(
      workspace: "TaylorMade.xcworkspace", 
      scheme: "Staging",
      clean: true,
      silent: true,
      output_directory: "./build", # Destination directory. Defaults to current directory.
      output_name: "TaylorMade-AdHoc.ipa",
      export_options: {
        method: "ad-hoc",
        provisioningProfiles: { 
          "com.taylormadegolf.mytaylormadeplus.ios" => "match AdHoc com.taylormadegolf.mytaylormadeplus.ios"
        }
      }
    )
    
    testfairy(
      api_key: "1edcf0a35febed6a34671acf154e12f5fd3b77a9",
      ipa: "./build/TaylorMade-AdHoc.ipa",
      comment: "Build #{lane_context[SharedValues::BUILD_NUMBER]}",
    )
  end

  desc "Update the Apple device list and certs. (Requires specific Apple user permissions)"
  lane :updatedevicelist do
    # Before calling match, we make sure all our devices are registered on the Apple Developer Portal
    # note: This will not work with currently configured ADP user since it does not have the correct permissions
    register_devices(devices_file: "./fastlane/devices.txt")

    sync_code_signing(force_for_new_devices: true, type: "adhoc")
  end


  desc "Create Sandbox Account"
  lane :create_tester do
    require 'pp'
    require 'spaceship'

    Spaceship::Tunes.login("<EMAIL>", "Hnl@2021")
    # desc "Select Team ID if you have multiple teams in your account"
    # desc "If you don't know your team ID Spaceship::Tunes.select_If you write only the team, you can select from the candidates later"
    # Spaceship::Tunes.select_team(team_id: "Team ID")

    tester = Spaceship::ConnectAPI::SandboxTester.create(
      first_name: "HnL",
      last_name: "QA",
      email: "<EMAIL>",
      password: "Bonjour1@qA",
      confirm_password: "Bonjour1@qA",
      secret_question: "Question",
      secret_answer: "Answer",
      birth_date: "1993-03-01",
      app_store_territory: "USA"
    )
    pp tester
  end


end
