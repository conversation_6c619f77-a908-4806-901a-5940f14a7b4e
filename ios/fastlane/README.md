fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## iOS

### ios stage

```sh
[bundle exec] fastlane ios stage
```

Push a new beta build to TestFlight

### ios qa

```sh
[bundle exec] fastlane ios qa
```

Push a new beta build to TestFairy

### ios updatedevicelist

```sh
[bundle exec] fastlane ios updatedevicelist
```

Update the Apple device list and certs. (Requires specific Apple user permissions)

### ios create_tester

```sh
[bundle exec] fastlane ios create_tester
```

Create Sandbox Account

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
