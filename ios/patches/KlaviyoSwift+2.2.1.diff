diff --git a/cocoapods-patch-20231012-45716-ew5qqj/KlaviyoSwift/Sources/KlaviyoSwift/Klaviyo.swift b/Pods/KlaviyoSwift/Sources/KlaviyoSwift/Klaviyo.swift
index c29a7d06..330cba75 100644
--- a/cocoapods-patch-20231012-45716-ew5qqj/KlaviyoSwift/Sources/KlaviyoSwift/Klaviyo.swift
+++ b/Pods/KlaviyoSwift/Sources/KlaviyoSwift/Klaviyo.swift
@@ -30,6 +30,7 @@ public class Klaviyo: NSObject {
     // Create the singleton instance
     @available(
         iOS, deprecated: 9999, message: "Deprecated as of version 2.0.0. See KlaviyoSDK to set profile properties.")
+    @objc
     public static let sharedInstance = Klaviyo()
 
     private static let sdkInstance = KlaviyoSDK()
@@ -204,6 +205,19 @@ public class Klaviyo: NSObject {
         }
     }
 
+    @available(
+        iOS, deprecated: 9999, message: "Deprecated as of version 2.0.0. Use `KlaviyoSDK().handle(notificationResponse:withCompletionHandler:) instead.")
+    @objc
+    public func handlePushSkipDeepLink(userInfo: NSDictionary) {
+        if let properties = userInfo as? [String: Any],
+           let body = properties["body"] as? [String: Any], let _ = body["_k"] {
+            Self.sdkInstance
+                .create(event: Event(name: .OpenedPush,
+                                     properties: properties))
+            
+        }
+    }
+    
     /**
      trackEvent: KL Event tracking for event name only
 
@@ -304,6 +318,17 @@ public class Klaviyo: NSObject {
     public func addPushDeviceToken(deviceToken: Data) {
         Self.sdkInstance.set(pushToken: deviceToken)
     }
+    
+    @available(
+        iOS, deprecated: 9999, message: "Deprecated as of version 2.0.0. Use `KlaviyoSDK().set(pushToken:) instead.")
+    @objc
+    public func addPushDeviceTokenWithString(deviceToken: String) {
+        environment.getNotificationSettings { enablement in
+            dispatchOnMainThread(action: .setPushToken(
+                deviceToken,
+                enablement))
+        }
+    }
 
     /**
      updatePropertiesDictionary: Internal function that configures the properties dictionary so that it holds the minimum info needed to track events and users
