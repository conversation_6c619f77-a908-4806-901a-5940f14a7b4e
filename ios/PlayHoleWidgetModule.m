//
//  PlayHoleWidgetModule.m
//  TaylorMade
//
//  Created by <PERSON> on 6/11/24.
//


#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(PlayHoleWidgetModule, NSObject)

+ (bool)requiresMainQueueSetup {
  return NO;
}

RCT_EXTERN_METHOD(startLiveActivity:(NSInteger)holeNumber distanceToPin:(NSInteger)distanceToPin  distanceToBack:(NSInteger)distanceToBack distanceToFront:(NSInteger)distanceToFront  totalDistance:(NSInteger)totalDistance par:(NSInteger)par score:(NSString)score distanceUnit:(NSString)score)
RCT_EXTERN_METHOD(updateLiveActivity:(NSInteger)holeNumber distanceToPin:(NSInteger)distanceToPin distanceToBack:(NSInteger)distanceToBack distanceToFront:(NSInteger)distanceToFront totalDistance:(NSInteger)totalDistance par:(NSInteger)par score:(NSString)score distanceUnit:(NSString)score)
RCT_EXTERN_METHOD(stopLiveActivity)

@end
