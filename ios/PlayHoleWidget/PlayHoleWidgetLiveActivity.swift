//
//  PlayHoleWidgetLiveActivity.swift
//  PlayHoleWidget
//
//  Created by <PERSON> on 5/11/24.
//

import ActivityKit
import WidgetKit
import SwiftUI

@available(iOS 16.0, *)
let lightGrayColor = Color(red: 179/255, green: 179/255, blue: 179/255, opacity: 1)
@available(iOS 16.0, *)
let greenColor = Color(red: 3/255, green: 168/255, blue: 0, opacity: 1)

struct PlayHoleWidgetAttributes: ActivityAttributes {
  public struct ContentState: Codable, Hashable {
    // Dynamic stateful properties about your activity go here!
    var holeNumber: Int
    var par: Int
    var currentScore: String
    var distanceToPin: Int
    var totalDistance: Int
    var distanceToFront: Int
    var distanceToBack: Int
    var distanceUnit: String
  }
}

@available(iOS 16.0, *)
struct HoleInfoHStack: View {
  var titleText: String
  var valueText: String
  
  var body: some View {
    HStack(spacing: 0) {
      Text(titleText).font(.custom("SF Pro", size: 16))
        .foregroundStyle(lightGrayColor).padding(.trailing, 7)
      Text(valueText).font(.custom("SF Mono", size: 16).bold())
        .foregroundStyle(.white)
    }.padding(.bottom, 1.5)
  }
}

@available(iOS 16.0, *)
struct DistanceText: View {
  var text: String
  
  var body: some View {
    Text(text).font(.custom("DIN Next 79", size: 22).bold())
      .foregroundStyle(lightGrayColor).tracking(1.1)
  }
}

@available(iOS 16.0, *)
struct DistanceTextSFPro: View {
  var text: String
  
  var body: some View {
    Text(text).font(.custom("SF Pro", size: 16).weight(.semibold))
      .foregroundStyle(lightGrayColor).tracking(-0.32)
  }
}

@available(iOS 16.0, *)
struct DynamicIslandDistanceInfoVStack: View {
  var distanceText: String
  var distanceColor: Color
  var distanceUnit: String
  
  var body: some View {
    VStack() {
      Text(distanceText).font(.custom("SF Mono", size: 16).weight(.semibold)).tracking(-0.32)
        .foregroundStyle(distanceColor)
      Text(distanceUnit).font(.custom("SF Pro", size: 8).weight(.semibold))
        .foregroundStyle(.white).tracking(-0.32).offset(y: -4)
    }
    .safeAreaInset(edge: .top) {}
  }
}

@available(iOS 16.1, *)
struct LockScreenView: View {
  var context: ActivityViewContext<PlayHoleWidgetAttributes>
  var body: some View {
    ZStack {
      Color.black
        .ignoresSafeArea()
      VStack(spacing: 0) {
        // White line at the top to represent the distance from tee to pin
        GeometryReader { geometry in
          let position = min(max(0, CGFloat((context.state.totalDistance - context.state.distanceToPin)) / CGFloat(context.state.totalDistance)), 1.0)
          ZStack(alignment: .leading) {
            @State var width: CGFloat = geometry.size.width
            Rectangle()
              .frame(height: 4)
              .foregroundColor(Color(red: 1, green: 1, blue: 1, opacity: 0.5))
              .clipShape(RoundedRectangle(cornerRadius: 2))
            Rectangle()
              .frame(width: position * width, height: 4)
              .foregroundColor(.white)
              .clipShape(RoundedRectangle(cornerRadius: 2))
            //.animation(.linear(duration: 1))
            Image("icon_tee")
              .resizable()
              .frame(width: 13, height: 14.54).offset(x: -20)
            // Golf ball icon placed along the line based on distance percentage
            Image("icon_tm_ball") // System icon to represent the golf ball
              .resizable()
              .frame(width: 22, height: 22)
              .offset(x: (geometry.size.width - 20) * position - 2)
            //.animation(.linear(duration: 1))
            Image("icon_pin")
              .resizable()
              .frame(width: 24, height: 24)
              .offset(x: geometry.size.width - 5)
          }
        }.padding(.leading, 15).padding(.trailing, 20)
        HStack(spacing: 0) {
          // Left-side information display
          VStack(alignment: .leading, spacing: 0) {
            HoleInfoHStack(titleText: "Hole", valueText: "\(context.state.holeNumber)")
            HoleInfoHStack(titleText: "Par", valueText: "\(context.state.par)")
            HoleInfoHStack(titleText: "Total", valueText: "\(context.state.currentScore)")
          }
          
          Spacer() // Space between left and right sections
          
          HStack{
            // Right-side display of the distance to the pin
            Text("\(context.state.distanceToPin)")
              .font(.custom("DIN Next 79", size: 76).bold())
              .foregroundColor(greenColor).offset(y: -5).tracking(0.76)
            VStack(alignment: .leading, spacing: 5) {
              HStack(spacing: 0){
                Image("icon_down").rotationEffect(.degrees(180)).padding(.trailing, 4).offset(y: 2)
                DistanceText(text: "\(context.state.distanceToBack)")
                  .foregroundStyle(lightGrayColor)
              }.offset(y: -3)
              HStack(spacing: 0){
                Image("icon_down").padding(.trailing, 4).offset(y: 2)
                DistanceText(text: "\(context.state.distanceToFront)")
              }
            }
          }
        }
      }
      .padding(.top, 15) // Top padding
      .padding(.horizontal, 20) // Horizontal padding
    }.activityBackgroundTint(Color.black)
  }
}

@available(iOS 16.1, *)
struct ExpandedLeadingView: View {
  var context: ActivityViewContext<PlayHoleWidgetAttributes>
  var body: some View {
    ZStack() {
      HStack {
        Image("icon_tm_big")
          .resizable()
          .frame(width: 32, height: 32).padding(.trailing, 4).offset(y: 10)
        
        VStack(alignment: .leading, spacing: 0) {
          HoleInfoHStack(titleText: "Hole", valueText: "\(context.state.holeNumber)")
          HoleInfoHStack(titleText: "Par", valueText: "\(context.state.par)")
        }
      }
    }
    VStack(alignment: .leading) {
      HoleInfoHStack(titleText: "Rd. Score", valueText: "\(context.state.currentScore)").padding(.leading, 44).offset(y: -6)
    }
  }
}

@available(iOS 16.1, *)
struct ExpandedTrailingView: View {
  var context: ActivityViewContext<PlayHoleWidgetAttributes>
  var body: some View {
    VStack() {
      Text("\(context.state.distanceToPin)")
        .font(.custom("DIN Next 79", size: 54).bold())
        .foregroundColor(greenColor).tracking(1.89).frame(height: 34)
      HStack(spacing: 0){
        HStack(alignment: .bottom, spacing: 0){
          Image("icon_down").padding(.trailing, 1).offset(y: -4)
          DistanceTextSFPro(text: "\(context.state.distanceToFront)")
        }.padding(.trailing, 8)
        HStack(alignment: .bottom, spacing: 0){
          Image("icon_down").rotationEffect(.degrees(180)).padding(.trailing, 1).offset(y: -3)
          DistanceTextSFPro(text: "\(context.state.distanceToBack)")
            .foregroundStyle(lightGrayColor)
        }
      }.frame(minWidth: 95)
    }.offset(y: 3)
  }
}

@available(iOS 16.1, *)
struct CompactLeadingView: View {
  var context: ActivityViewContext<PlayHoleWidgetAttributes>
  var body: some View {
    Image("icon_pin_transparent")
      .resizable()
      .frame(width: 24, height: 24)
  }
}

@available(iOS 16.1, *)
struct CompactTrailingView: View {
  var context: ActivityViewContext<PlayHoleWidgetAttributes>
  var body: some View {
    DynamicIslandDistanceInfoVStack(distanceText: "\(context.state.distanceToPin)", distanceColor: greenColor, distanceUnit: context.state.distanceUnit)
  }
}

@available(iOS 16.1, *)
struct MinimalView: View {
  var context: ActivityViewContext<PlayHoleWidgetAttributes>
  var body: some View {
    DynamicIslandDistanceInfoVStack(distanceText: "\(context.state.distanceToPin)", distanceColor: .white, distanceUnit: context.state.distanceUnit)
  }
}

@available(iOS 16.1, *)
extension ActivityConfiguration {
    func addSupplementalActivityFamilies() -> some WidgetConfiguration {
        if #available(iOS 18.0, *) {
            //return self.supplementalActivityFamilies([.small, .medium])
            return self
        } else {
            return self
        }
    }
}

@available(iOS 16.1, *)
struct PlayHoleWidgetLiveActivity: Widget {
  var body: some WidgetConfiguration {
    ActivityConfiguration(for: PlayHoleWidgetAttributes.self) { context in
      LockScreenView(context: context)
//      if #available(iOS 18.0, *) {
//        PlayHoleActivityContent(context: context)
//      } else {
//        LockScreenView(context: context)
//      }
    } dynamicIsland: { context in
      DynamicIsland {
        // Expanded UI goes here.  Compose the expanded UI through
        // various regions, like leading/trailing/center/bottom
        //EXPANDED LEADING
        DynamicIslandExpandedRegion(.leading) {
          ExpandedLeadingView(context: context)
        }
        //EXPANDED TRAILING
        DynamicIslandExpandedRegion(.trailing) {
          ExpandedTrailingView(context: context)
        }
      } compactLeading: {
        CompactLeadingView(context: context)
      } compactTrailing: {
        CompactTrailingView(context: context)
      } minimal: {
        MinimalView(context: context)
      }
      .contentMargins(.leading, 15, for: .expanded)
      .contentMargins(.trailing, 18, for: .expanded)
      .contentMargins(.top, 14, for: .expanded)
      .contentMargins(.bottom, 10, for: .expanded)
      .widgetURL(URL(string: "http://www.apple.com"))
      .keylineTint(Color.black)
    }.addSupplementalActivityFamilies()
  }
}

@available(iOS 18.0, *)
struct PlayHoleActivityContent: View {
    @Environment(\.activityFamily) var activityFamily
    var context: ActivityViewContext<PlayHoleWidgetAttributes>

    var body: some View {
        switch activityFamily {
        case .small:
          LockScreenView(context: context)
        case .medium:
          LockScreenView(context: context)
        @unknown default:
          LockScreenView(context: context)
        }
    }
}

extension PlayHoleWidgetAttributes {
  fileprivate static var preview: PlayHoleWidgetAttributes {
    PlayHoleWidgetAttributes()
  }
}

@available(iOS 17.0, *)
extension PlayHoleWidgetAttributes.ContentState {
  fileprivate static var closeToTee: PlayHoleWidgetAttributes.ContentState {
    PlayHoleWidgetAttributes.ContentState( holeNumber: 18, par: 3, currentScore: "-2", distanceToPin: 470, totalDistance: 470, distanceToFront: 450, distanceToBack: 490, distanceUnit: "yds")
  }
  
  fileprivate static var closeToTee2: PlayHoleWidgetAttributes.ContentState {
    PlayHoleWidgetAttributes.ContentState( holeNumber: 18, par: 3, currentScore: "-2", distanceToPin: 460, totalDistance: 470, distanceToFront: 440, distanceToBack: 480, distanceUnit: "yds")
  }
  
  fileprivate static var closeToTee3: PlayHoleWidgetAttributes.ContentState {
    PlayHoleWidgetAttributes.ContentState( holeNumber: 18, par: 3, currentScore: "-2", distanceToPin: 450, totalDistance: 470, distanceToFront: 430, distanceToBack: 470, distanceUnit: "yds")
  }
  
  fileprivate static var closeToTee4: PlayHoleWidgetAttributes.ContentState {
    PlayHoleWidgetAttributes.ContentState( holeNumber: 18, par: 3, currentScore: "-2", distanceToPin: 440, totalDistance: 470, distanceToFront: 420, distanceToBack: 460, distanceUnit: "yds")
  }
  
  fileprivate static var closeToTee5: PlayHoleWidgetAttributes.ContentState {
    PlayHoleWidgetAttributes.ContentState( holeNumber: 18, par: 3, currentScore: "-2", distanceToPin: 430, totalDistance: 470, distanceToFront: 410, distanceToBack: 450, distanceUnit: "yds")
  }
  
  fileprivate static var closeToTee6: PlayHoleWidgetAttributes.ContentState {
    PlayHoleWidgetAttributes.ContentState( holeNumber: 18, par: 3, currentScore: "-2", distanceToPin: 420, totalDistance: 470, distanceToFront: 400, distanceToBack: 440, distanceUnit: "yds")
  }
  
  fileprivate static var closeToTee7: PlayHoleWidgetAttributes.ContentState {
    PlayHoleWidgetAttributes.ContentState( holeNumber: 18, par: 3, currentScore: "-2", distanceToPin: 410, totalDistance: 470, distanceToFront: 390, distanceToBack: 430, distanceUnit: "yds")
  }
  
  fileprivate static var closeToPin: PlayHoleWidgetAttributes.ContentState {
    PlayHoleWidgetAttributes.ContentState( holeNumber: 18, par: 3, currentScore: "-2", distanceToPin: 0, totalDistance: 470, distanceToFront: 20, distanceToBack: 21, distanceUnit: "yds")
  }
  
  fileprivate static var inTheMiddle: PlayHoleWidgetAttributes.ContentState {
    PlayHoleWidgetAttributes.ContentState( holeNumber: 18, par: 3, currentScore: "-2", distanceToPin: 250, totalDistance: 470, distanceToFront: 225, distanceToBack: 275, distanceUnit: "yds")
  }
}
@available(iOS 17, *)
#Preview("Notification", as: .content, using: PlayHoleWidgetAttributes.preview) {
  PlayHoleWidgetLiveActivity()
} contentStates: {
  PlayHoleWidgetAttributes.ContentState.closeToTee
  PlayHoleWidgetAttributes.ContentState.closeToTee2
  PlayHoleWidgetAttributes.ContentState.closeToTee3
  PlayHoleWidgetAttributes.ContentState.closeToTee4
  PlayHoleWidgetAttributes.ContentState.closeToTee5
  PlayHoleWidgetAttributes.ContentState.closeToTee6
  PlayHoleWidgetAttributes.ContentState.closeToTee7
  PlayHoleWidgetAttributes.ContentState.inTheMiddle
  PlayHoleWidgetAttributes.ContentState.closeToPin
}
