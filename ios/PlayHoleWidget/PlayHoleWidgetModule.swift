//
//  PlayHoleWidgetModule.swift
//  TaylorMade
//
//  Created by <PERSON> on 6/11/24.
//

import Foundation
import ActivityKit

@available(iOS 16.2, *)
@objc(PlayHoleWidgetModule)
class PlayHoleWidgetModule: NSObject {
  @objc static func sharedInstance() -> PlayHoleWidgetModule {
    return PlayHoleWidgetModuleInstance
  }
  private static let PlayHoleWidgetModuleInstance = PlayHoleWidgetModule()
  var golfActivity: Activity<PlayHoleWidgetAttributes>?
  private func areActivitiesEnabled() -> Bool {
    if #available(iOS 16.1, *) {
      return ActivityAuthorizationInfo().areActivitiesEnabled
    } else {
      return false;
    }
  }
  
  @objc func startLiveActivity(_ holeNumber: Int, distanceToPin: Int, distanceToBack: Int, distanceToFront: Int, totalDistance: Int, par: Int, score: String, distanceUnit: String) {
    if (!areActivitiesEnabled()) {
      // User disabled Live Activities for the app, nothing to do
      return
    }
    let attributes = PlayHoleWidgetAttributes()
    let contentState = PlayHoleWidgetAttributes.ContentState( holeNumber: holeNumber, par: par, currentScore: score, distanceToPin: distanceToPin, totalDistance: totalDistance, distanceToFront: distanceToFront, distanceToBack: distanceToBack, distanceUnit: distanceUnit)
    
    Task {
      for activity in Activity<PlayHoleWidgetAttributes>.activities {
        await activity.end(nil, dismissalPolicy: .immediate)
      }
      do {
        golfActivity = try Activity<PlayHoleWidgetAttributes>.request(
          attributes: attributes,
          contentState: contentState,
          pushType: nil
        )
      } catch {
        print("Error starting golf activity: \(error)")
      }
    }
  }
  
  
  @objc func updateLiveActivity(_ holeNumber: Int, distanceToPin: Int, distanceToBack: Int, distanceToFront: Int, totalDistance: Int, par: Int, score: String, distanceUnit: String) {
    if (!areActivitiesEnabled()) {
      // User disabled Live Activities for the app, nothing to do
      return
    }
    let updatedContentState = PlayHoleWidgetAttributes.ContentState(holeNumber: holeNumber, par: par, currentScore: score, distanceToPin: distanceToPin, totalDistance: totalDistance, distanceToFront: distanceToFront, distanceToBack: distanceToBack, distanceUnit: distanceUnit)
    
    Task {
      await golfActivity?.update(using: updatedContentState)
    }
  }
  
  @objc func stopLiveActivity() {
    if (!areActivitiesEnabled()) {
      // User disabled Live Activities for the app, nothing to do
      return
    }
    // A task is a unit of work that can run concurrently in a lightweight thread, managed by the Swift runtime
    // It helps to avoid blocking the main thread
    print("Attempting to end Live Activity...")
    Task
    {
      for activity in Activity<PlayHoleWidgetAttributes>.activities
      {
        print("Ending Live Activity: \(activity.id)")
        await activity.end(nil, dismissalPolicy: .immediate)
      }
    }
    
    print("end Live Activity completed")
  }
}

