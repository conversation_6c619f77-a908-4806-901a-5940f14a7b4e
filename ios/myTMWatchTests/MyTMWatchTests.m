//
//  MyTMWatchTests.m
//  MyTMWatchTests
//
//  Created by <PERSON><PERSON> on 04/08/2022.
//

#import <XCTest/XCTest.h>

@interface MyTMWatchTests : XCTestCase

@end

@implementation MyTMWatchTests

- (void)setUp {
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

@end
