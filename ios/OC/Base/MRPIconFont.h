//
//  MRPIconFont.h
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 9/6/16.
//  Copyright © 2016 myroundpro. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface MRPIconFont : NSObject

+(MRPIconFont*)sharedInstance;

#pragma mark - icomoon

-(NSString*)getBackIcon;

-(NSString*)getCheckmarkActiveIcon;

-(NSString*)getDownArrow;

-(NSString*)getDownArrowCircle;

-(NSString*)getDownArrowNoCircle;

-(NSString*)getDriverIcon;

-(NSString*)getMailIcon;

-(NSString*)getConfirmPasswordIcon;

-(NSString*)getFairwayIcon;

-(NSString*)getNameIcon;

-(NSString*)getHybridIcon;

-(NSString*)getIronIcon;

-(NSString*)getLeftHandIcon;

-(NSString*)getPasswordIcon;

-(NSString*)getPutterIcon;

-(NSString*)getBallIcon;

-(NSString*)getQuestmarkCircleIcon;

-(NSString*)getRightArrowIcon;

-(NSString*)getRightHandIcon;

-(NSString*)getCountryIcon;

-(NSString*)getWedgeIcon;

-(NSString*)getStatsIcon;

-(NSString*)getTrackRoundsIcon;

-(NSString*)getCloseIcon;

-(NSString*)getSearchIcon;

-(NSString*)getPhoneIcon;

-(NSString*)getQuestionmarkNoCircleIcon;

-(NSString*)getRightNoCircleArrowIcon;

-(NSString*)getDeleteIcon;

#pragma mark - icomoon 2

-(NSString*)getHomeCourseIcon;

-(NSString*)getPostalCodeIcon;

#pragma mark - TMMRPSprint2

-(NSString*)getMenuScorecard;

-(NSString*)getMenuPause;

-(NSString*)getMenuEndRound;

-(NSString*)getMenuStrokeCheckmark;

-(NSString*)getMenuIcon;

-(NSString*)getPenaltyExclamationMark;

-(NSString*)getBirthdateIcon;

#pragma mark - TMMRPSprint2

-(NSString*)getClearFieldIcon;

-(NSString*)getSubjectIcon;

-(NSString*)getBirthdayIcon2;

-(NSString*)getDrivingLeftArrowIcon;

-(NSString*)getDrivingRightArrowIcon;

-(NSString*)getDrivingUpArrowIcon;

#pragma mark - kFontNotches

-(NSString*)getNotchUp;

-(NSString*)getNotchDown;

@end
