//
//  MRPIconFont.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 9/6/16.
//  Copyright © 2016 myroundpro. All rights reserved.
//

#import "MRPIconFont.h"

@implementation MRPIconFont

+(MRPIconFont*)sharedInstance
{
    static MRPIconFont *sharedApiHandler;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedApiHandler = [[self alloc] init];
    });
    return sharedApiHandler;
}

#pragma mark - icomoon

-(NSString*)getBackIcon
{
    return @"\ue919";
}

-(NSString*)getCheckmarkActiveIcon
{
    return @"\ue902";
}

-(NSString*)getDownArrow
{
    return @"\ue91c";
}

-(NSString*)getDownArrowCircle
{
    return @"\ue91e";
}

-(NSString*)getDownArrowNoCircle
{
    return @"\ue91b";
}

-(NSString*)getDriverIcon
{
    return @"\ue915";
}

-(NSString*)getMailIcon
{
    return @"\ue904";
}

-(NSString*)getConfirmPasswordIcon
{
    return @"\ue91d";
}

-(NSString*)getFairwayIcon
{
    return @"\ue914";
}

-(NSString*)getNameIcon
{
    return @"\ue906";
}

-(NSString*)getHybridIcon
{
    return @"\ue912";
}

-(NSString*)getIronIcon
{
    return @"\ue913";
}

-(NSString*)getLeftHandIcon
{
    return @"\ue903";
}

-(NSString*)getPasswordIcon
{
    return @"\ue905";
}

-(NSString*)getPutterIcon
{
    return @"\ue910";
}

-(NSString*)getBallIcon
{
    return @"\ue910";
}

-(NSString*)getQuestmarkCircleIcon
{
    return @"\ue924";
}

-(NSString*)getRightArrowIcon
{
    return @"\ue923";
}

-(NSString*)getRightHandIcon
{
    return @"\ue902";
}

-(NSString*)getCountryIcon
{
    return @"\ue907";
}

-(NSString*)getStatsIcon
{
    return @"\ue920";
}

-(NSString*)getTrackRoundsIcon
{
    return @"\ue921";
}

-(NSString*)getWedgeIcon
{
    return @"\ue911";
}

-(NSString*)getCloseIcon
{
    return @"\ue922";
}

-(NSString*)getSearchIcon
{
    return @"\ue917";
}

-(NSString*)getPhoneIcon
{
    return @"\ue918";
}

-(NSString*)getQuestionmarkNoCircleIcon
{
    return @"\ue925";
}

-(NSString*)getRightNoCircleArrowIcon {
    return @"\ue91f";
}

-(NSString*)getDeleteIcon
{
    return @"\ue91a";
}

#pragma mark - icomoon 2
-(NSString*)getHomeCourseIcon
{
//    return @"\ue900";
    return @"\ue902";
}

-(NSString*)getPostalCodeIcon
{
    return @"\ue901";
}
#pragma mark - TMMRPSprint2

-(NSString*)getMenuScorecard
{
    return @"\ue900";
}

-(NSString*)getMenuPause
{
    return @"\ue901";
}

-(NSString*)getMenuEndRound
{
    return @"\ue902";
}

-(NSString*)getMenuStrokeCheckmark
{
    return @"\ue903";
}

-(NSString*)getMenuIcon
{
    return @"\ue904";
}

-(NSString*)getPenaltyExclamationMark
{
    return @"\ue905";
}

-(NSString*)getBirthdateIcon
{
    return @"\ue907";
}

#pragma mark - TMMRPSprint5

-(NSString*)getClearFieldIcon
{
    return @"\ue900";
}

-(NSString*)getSubjectIcon
{
    return @"\ue901";
}

-(NSString*)getBirthdayIcon2
{
    return @"\ue902";
}

-(NSString*)getDrivingLeftArrowIcon
{
    return @"\ue903";
}

-(NSString*)getDrivingRightArrowIcon
{
    return @"\ue904";
}

-(NSString*)getDrivingUpArrowIcon
{
    return @"\ue905";
}

#pragma mark - kFontNotches

-(NSString*)getNotchUp
{
    return @"\ue901";
}

-(NSString*)getNotchDown
{
    return @"\ue900";
}

@end
