//
//  KlaviyoEventEmitter.m
//  MyTaylorMade
//
//  Created by <PERSON><PERSON> on 26/06/2023.
//

#import "KlaviyoEventEmitter.h"

@implementation KlaviyoEventEmitter {
  bool hasListeners;
}

// To export a module named KlaviyoEventEmitter
RCT_EXPORT_MODULE(KlaviyoEventEmitter);

+ (id)allocWithZone:(NSZone *)zone {
  static KlaviyoEventEmitter *sharedInstance = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    sharedInstance = [super allocWithZone:zone];
  });
  return sharedInstance;
}

- (NSArray<NSString *> *)supportedEvents {
    return @[@"ReceiptedPush"];
}

// Will be called when this module's first listener is added.
-(void)startObserving {
  hasListeners = YES;
    // Set up any upstream listeners or background tasks as necessary
}

// Will be called when this module's last listener is removed, or on dealloc.
-(void)stopObserving {
  hasListeners = NO;
    // Remove upstream listeners, stop unnecessary background tasks
}

- (void)sendPushNotificationWithData:(NSDictionary *)notification
{
  if (hasListeners) {// Only send events if anyone is listening
    [self sendEventWithName:@"ReceiptedPush" body:notification];
  }
}


@end
