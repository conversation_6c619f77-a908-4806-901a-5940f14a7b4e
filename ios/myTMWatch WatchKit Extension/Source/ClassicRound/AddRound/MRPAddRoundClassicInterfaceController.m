//
//  MRPAddRoundClassicInterfaceController.m
//  MyTaylorMadeOnCourse
//
//  Created by HienNN on 09/05/2022.
//  Copyright © 2022 TaylorMade Golf. All rights reserved.
//

#import "MRPAddRoundClassicInterfaceController.h"

@interface MRPAddRoundClassicInterfaceController ()

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup          *mainGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel          *numberHoleLabel;

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup          *holeScoreGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *minusScoreBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *plusScoreBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel          *holeScoreLabel;

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup          *puttsGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *minusPuttsBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *plusPuttsBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel          *puttsScoreLabel;

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup          *faiwayGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *fwHitBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *fwLeftBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *fwRightBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel          *fwTitleLabel;

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup          *greenInGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *grHitBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *grLongBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *grShotBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *grLeftBtn;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton         *grRightBtn;

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup          *bunkerGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup          *bunkerYesGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup          *bunkerNoGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel          *bunkerYesLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel          *bunkerNoLabel;

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup          *trackGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel          *trackLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel          *completeLabel;

@property (assign, nonatomic) MRPHoleModel                                  *selectedHole;
@property (weak, nonatomic) id <MRPAddRoundClassicDelegate>                  delegate;

@property (assign, nonatomic) NSInteger                                      holeScore;
@property (assign, nonatomic) NSInteger                                      puttsScore;
@property (assign, nonatomic) NSString                                      *fw_stats;
@property (assign, nonatomic) NSString                                      *gr_stats;
@property (assign, nonatomic) BOOL                                           bunker_hit;
@property (assign, nonatomic) NSNumber                                      *classicModeScoresOnly;
@property (assign, nonatomic) NSNumber                                      *isFirst;

@end

@implementation MRPAddRoundClassicInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    self.delegate = context[@"context"];
    self.selectedHole = context[@"selectedHole"];
    self.classicModeScoresOnly = context[@"classicModeScoresOnly"];
    self.isFirst = context[@"isFirst"];
    [self initElement];
}

- (void)willActivate {
    [super willActivate];
    
}

- (void)didDeactivate {
    [super didDeactivate];
}

- (void)initElement {
    self.numberHoleLabel.text = [NSString stringWithFormat:@"Hole %d - Par %d", (int)self.selectedHole.holeNumber, (int)self.selectedHole.par];
    
    [self setHoleScore];
    
    [self setPuttsScore];
    
    if (self.selectedHole.par == 3) {
        self.fwTitleLabel.text = Classic_Fairway_Par3;
        [self.fwHitBtn setEnabled:NO];
        [self.fwLeftBtn setEnabled:NO];
        [self.fwRightBtn setEnabled:NO];
        [self setFaiwayStats: nil];
    } else {
        self.fwTitleLabel.text = Classic_Fairway;
        [self.fwHitBtn setEnabled:YES];
        [self.fwLeftBtn setEnabled:YES];
        [self.fwRightBtn setEnabled:YES];
        if (self.selectedHole.fwStats == nil) {
            [self setFaiwayStats: FW_HIT];
        } else {
            [self setFaiwayStats: self.selectedHole.fwStats];
        }
    }
    
    if (self.selectedHole.grStats == nil) {
        [self setUIGreensInRegulation: GR_HIT];
    } else {
        [self setUIGreensInRegulation: self.selectedHole.grStats];
    }
    
    [self setBunkerButton:self.selectedHole.bunkerHit];
    
    [self setUITrackGroup];
    
    [self.trackGroup setHidden:![self.isFirst boolValue]];
    [self.trackGroup setHeight: [self.isFirst boolValue] ? 26 : 0];
}

- (void)setHoleScore {
    if (self.selectedHole.score > 0) {
        self.holeScore = self.selectedHole.score;
        self.holeScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.selectedHole.score];
        self.completeLabel.text = @"SAVE SCORE";
    } else {
        self.holeScore = self.selectedHole.par;
        self.holeScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.selectedHole.par];
        self.completeLabel.text = @"COMPLETE HOLE";
    }
}

- (void)setPuttsScore {
    if (self.selectedHole.puttsNumber > 0) {
        self.puttsScore = self.selectedHole.puttsNumber;
        self.puttsScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.selectedHole.puttsNumber];
    } else {
        self.puttsScore = 2;
        self.puttsScoreLabel.text = [NSString stringWithFormat:@"%d", 2];
    }
}

- (void)setFaiwayStats:(NSString *)fw {
    self.fw_stats = fw;
    UIImage *imageFwLeft = [UIImage imageNamed:@"ic_classic_miss_left"];
    UIImage *imageFwLeftSelected = [UIImage imageNamed:@"ic_classic_miss_left_selected"];
    UIImage *imageFwRight = [UIImage imageNamed:@"ic_classic_miss_right"];
    UIImage *imageFwRightSelected = [UIImage imageNamed:@"ic_classic_miss_right_selected"];
    UIImage *imageFwHit = [UIImage imageNamed:@"ic_classic_hit"];
    UIImage *imageFwHitSelected = [UIImage imageNamed:@"ic_classic_hit_selected"];
    
    if([fw isEqualToString: FW_LEFT]) {
        [self.fwHitBtn setBackgroundImage:imageFwHit];
        [self.fwLeftBtn setBackgroundImage:imageFwLeftSelected];
        [self.fwRightBtn setBackgroundImage:imageFwRight];
    } else if([fw isEqualToString: FW_RIGHT]) {
        [self.fwHitBtn setBackgroundImage:imageFwHit];
        [self.fwLeftBtn setBackgroundImage:imageFwLeft];
        [self.fwRightBtn setBackgroundImage:imageFwRightSelected];
    } else if([fw isEqualToString: FW_HIT]) {
        [self.fwHitBtn setBackgroundImage:imageFwHitSelected];
        [self.fwLeftBtn setBackgroundImage:imageFwLeft];
        [self.fwRightBtn setBackgroundImage:imageFwRight];
    } else {
        [self.fwHitBtn setBackgroundImage:imageFwHit];
        [self.fwLeftBtn setBackgroundImage:imageFwLeft];
        [self.fwRightBtn setBackgroundImage:imageFwRight];
    }
}

- (void)setUIGreensInRegulation:(NSString *)gr {
    self.gr_stats = gr;
    UIImage *imageGrLeft = [UIImage imageNamed:@"ic_classic_arrow_left"];
    UIImage *imageGrLeftSelected = [UIImage imageNamed:@"ic_classic_arrow_left_selected"];
    UIImage *imageGrRight = [UIImage imageNamed:@"ic_classic_arrow_right"];
    UIImage *imageGrRightSelected = [UIImage imageNamed:@"ic_classic_arrow_right_selected"];
    UIImage *imageGrLong = [UIImage imageNamed:@"ic_classic_arrow_up"];
    UIImage *imageGrLongSelected = [UIImage imageNamed:@"ic_classic_arrow_up_selected"];
    UIImage *imageGrShort = [UIImage imageNamed:@"ic_classic_arrow_down"];
    UIImage *imageGrShortSelected = [UIImage imageNamed:@"ic_classic_arrow_down_selected"];
    UIImage *imageGrHit = [UIImage imageNamed:@"ic_classic_hit"];
    UIImage *imageGrHitSelected = [UIImage imageNamed:@"ic_classic_hit_selected"];
    
    if ([gr isEqualToString:GR_LEFT]) {
        [self.grLeftBtn setBackgroundImage:imageGrLeftSelected];
        [self.grRightBtn setBackgroundImage:imageGrRight];
        [self.grLongBtn setBackgroundImage:imageGrLong];
        [self.grShotBtn setBackgroundImage:imageGrShort];
        [self.grHitBtn setBackgroundImage:imageGrHit];
    } else if ([gr isEqualToString:GR_RIGHT]) {
        [self.grLeftBtn setBackgroundImage:imageGrLeft];
        [self.grRightBtn setBackgroundImage:imageGrRightSelected];
        [self.grLongBtn setBackgroundImage:imageGrLong];
        [self.grShotBtn setBackgroundImage:imageGrShort];
        [self.grHitBtn setBackgroundImage:imageGrHit];
    } else if ([gr isEqualToString:GR_LONG]) {
        [self.grLeftBtn setBackgroundImage:imageGrLeft];
        [self.grRightBtn setBackgroundImage:imageGrRight];
        [self.grLongBtn setBackgroundImage:imageGrLongSelected];
        [self.grShotBtn setBackgroundImage:imageGrShort];
        [self.grHitBtn setBackgroundImage:imageGrHit];
    } else if ([gr isEqualToString:GR_SHORT]) {
        [self.grLeftBtn setBackgroundImage:imageGrLeft];
        [self.grRightBtn setBackgroundImage:imageGrRight];
        [self.grLongBtn setBackgroundImage:imageGrLong];
        [self.grShotBtn setBackgroundImage:imageGrShortSelected];
        [self.grHitBtn setBackgroundImage:imageGrHit];
    } else {
        self.gr_stats = GR_HIT;
        [self.grLeftBtn setBackgroundImage:imageGrLeft];
        [self.grRightBtn setBackgroundImage:imageGrRight];
        [self.grLongBtn setBackgroundImage:imageGrLong];
        [self.grShotBtn setBackgroundImage:imageGrShort];
        [self.grHitBtn setBackgroundImage:imageGrHitSelected];
    }
}

- (void)setBunkerButton:(BOOL)isBunker {
    self.bunker_hit = isBunker;
    if (isBunker) {
        [self setUIBunkerYes];
    } else {
        [self setUIBunkerNo];
    }
}

- (void)setUIBunkerYes {
    [self.bunkerYesGroup setBackgroundColor: [UIColor colorWithRed:56./255. green:186./255. blue:86./255. alpha:1]];
    [self.bunkerNoGroup setBackgroundColor: UIColor.clearColor];
    [self.bunkerYesGroup setBackgroundImage:nil];
    UIImage *image = [UIImage imageNamed:@"ic_bunker"];
    [self.bunkerNoGroup setBackgroundImage:image];
    [self.bunkerYesLabel setTextColor:UIColor.whiteColor];
    [self.bunkerNoLabel setTextColor:UIColor.blackColor];
}

- (void)setUIBunkerNo {
    [self.bunkerYesGroup setBackgroundColor: UIColor.clearColor];
    [self.bunkerNoGroup setBackgroundColor:[UIColor colorWithRed:56./255. green:186./255. blue:86./255. alpha:1]];
    [self.bunkerNoGroup setBackgroundImage:nil];
    UIImage *image = [UIImage imageNamed:@"ic_bunker"];
    [self.bunkerYesGroup setBackgroundImage:image];
    [self.bunkerYesLabel setTextColor:UIColor.blackColor];
    [self.bunkerNoLabel setTextColor:UIColor.whiteColor];
}

- (void)setUITrackGroup {
    if ([self.classicModeScoresOnly boolValue]) {
        [self.holeScoreGroup setBackgroundColor:[UIColor colorWithRed:45./255. green:156./255. blue:219./255. alpha:1]];
        [self.trackGroup setBackgroundImage:[UIImage imageNamed:@"classic_track_down"]];
        [self.trackLabel setText:@"Classic Stats"];
        [self.puttsGroup setHidden:YES];
        [self.faiwayGroup setHidden:YES];
        [self.greenInGroup setHidden:YES];
        [self.bunkerGroup setHidden:YES];
        [self scrollToObject:self.mainGroup atScrollPosition:0 animated:true];
        [self setHoleScore];
    } else {
        [self.holeScoreGroup setBackgroundColor:[UIColor colorWithRed:189./255. green:189./255. blue:189./255. alpha:1]];
        [self.trackGroup setBackgroundImage:[UIImage imageNamed:@"classic_track_up"]];
        [self.trackLabel setText:@"Track Scores Only"];
        [self.puttsGroup setHidden:NO];
        [self.faiwayGroup setHidden:NO];
        [self.greenInGroup setHidden:NO];
        [self.bunkerGroup setHidden:NO];
        [self setHoleScore];
        [self setPuttsScore];
    }
    if (![self.isFirst boolValue]) {
        [self.holeScoreGroup setBackgroundColor:[UIColor colorWithRed:189./255. green:189./255. blue:189./255. alpha:1]];
    }
}

#pragma mark - Action

- (IBAction)actionMinusHoleScore:(id)sender {
    if ([self.classicModeScoresOnly boolValue]) {
        if (self.holeScore > 1) {
            self.holeScore -= 1;
            self.holeScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.holeScore];
        }
    } else {
        if (self.holeScore - 1 > self.puttsScore) {
            self.holeScore -= 1;
            self.holeScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.holeScore];
        } else if (self.puttsScore > 0) {
            self.holeScore -= 1;
            self.holeScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.holeScore];
            self.puttsScore -= 1;
            self.puttsScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.puttsScore];
        }
    }
}

- (IBAction)actionPlusHoleScore:(id)sender {
    if ([self.classicModeScoresOnly boolValue]) {
        if (self.holeScore < 12) {
            self.holeScore += 1;
            self.holeScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.holeScore];
        }
    } else {
        if (self.holeScore < 12) {
            self.holeScore += 1;
            self.holeScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.holeScore];
        }
    }
}


- (IBAction)actionMinusPutts:(id)sender {
    if (self.puttsScore > 0) {
        self.puttsScore -= 1;
        self.puttsScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.puttsScore];
    }
}

- (IBAction)actionPlusPutts:(id)sender {
  if (self.puttsScore < 11) {
      self.puttsScore += 1;
      self.puttsScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.puttsScore];
      if (self.holeScore < 12 && self.puttsScore == self.holeScore) {
          self.holeScore += 1;
          self.holeScoreLabel.text = [NSString stringWithFormat:@"%d", (int)self.holeScore];
      }
  }
}

- (IBAction)actionFwLeft:(id)sender {
    if ([self.fw_stats isEqualToString:FW_LEFT]) {
        [self setFaiwayStats: FW_HIT];
    } else {
        [self setFaiwayStats: FW_LEFT];
    }
}

- (IBAction)actionFwRight:(id)sender {
    if ([self.fw_stats isEqualToString:FW_RIGHT]) {
        [self setFaiwayStats: FW_HIT];
    } else {
        [self setFaiwayStats: FW_RIGHT];
    }
}

- (IBAction)actionFwHit:(id)sender {
    [self setFaiwayStats: FW_HIT];
}

- (IBAction)actionGrLeft:(id)sender {
    if ([self.gr_stats isEqualToString:GR_LEFT]) {
        [self setUIGreensInRegulation:GR_HIT];
    } else {
        [self setUIGreensInRegulation:GR_LEFT];
    }
}

- (IBAction)actionGrRight:(id)sender {
    if ([self.gr_stats isEqualToString:GR_RIGHT]) {
        [self setUIGreensInRegulation:GR_HIT];
    } else {
        [self setUIGreensInRegulation:GR_RIGHT];
    }
}

- (IBAction)actionGrLong:(id)sender {
    if ([self.gr_stats isEqualToString:GR_LONG]) {
        [self setUIGreensInRegulation:GR_HIT];
    } else {
        [self setUIGreensInRegulation:GR_LONG];
    }
}

- (IBAction)actionGrShort:(id)sender {
    if ([self.gr_stats isEqualToString:GR_SHORT]) {
        [self setUIGreensInRegulation:GR_HIT];
    } else {
        [self setUIGreensInRegulation:GR_SHORT];
    }
}

- (IBAction)actionGrHit:(id)sender {
    [self setUIGreensInRegulation:GR_HIT];
}

- (IBAction)actionBunkerYes:(id)sender {
    [self setBunkerButton:YES];
}

- (IBAction)actionBunkerNo:(id)sender {
    [self setBunkerButton:NO];
}

- (IBAction)actionTrack:(id)sender {
    self.classicModeScoresOnly = self.classicModeScoresOnly == [NSNumber numberWithInt: 1] ? [NSNumber numberWithInt: 0] : [NSNumber numberWithInt: 1];
    [self setUITrackGroup];
}


- (IBAction)actionComplete:(id)sender {
    if ([self.classicModeScoresOnly boolValue]) {
        [self.delegate classicAddScoreOnly:self.holeScore];
    } else {
        [self.delegate classicAddFirAndGir:self.holeScore
                                  andPutts:self.puttsScore
                                     andFw:self.fw_stats
                                     andGr:self.gr_stats
                                 andBunker:self.bunker_hit];
    }
    [self dismissController];
}

@end
