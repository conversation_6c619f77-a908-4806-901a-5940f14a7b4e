//
//  MRPAddRoundClassicInterfaceController.h
//  MyTaylorMadeOnCourse
//
//  Created by HienNN on 09/05/2022.
//  Copyright © 2022 TaylorMade Golf. All rights reserved.
//

#import <WatchKit/WatchKit.h>
#import "MRPBaseInterfaceController.h"

@class MRPAddRoundClassicInterfaceController;

@protocol MRPAddRoundClassicDelegate
@optional
- (void)classicAddScoreOnly:(NSInteger)score;
- (void)classicAddFirAndGir:(NSInteger)score andPutts:(NSInteger)putts andFw:(NSString *)fw_stats andGr:(NSString *)gr_stats andBunker:(BOOL)isBunker;
@end

@interface MRPAddRoundClassicInterfaceController : MRPBaseInterfaceController

@end

