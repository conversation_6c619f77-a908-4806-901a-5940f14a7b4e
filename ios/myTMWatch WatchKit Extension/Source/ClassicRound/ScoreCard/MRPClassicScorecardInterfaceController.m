//
//  MRPClassicScorecardInterfaceController.m
//  MyTaylorMadeOnCourse
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 11/05/2022.
//  Copyright © 2022 TaylorMade Golf. All rights reserved.
//

#import "MRPClassicScorecardInterfaceController.h"
#import "MRPScorecardInfo.h"
#import "MRPClassicScorecardCell.h"
#import "MRPWatchRoundManager.h"
#import "MRPClassicFooterScoreCardCell.h"

@interface MRPClassicScorecardInterfaceController () <MRPClassicFooterScoreCardCellDelegate>

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceTable  *scorecardTableView;
@property (strong, nonatomic) NSMutableArray                        *scorecardInfoArray;
@property (strong, nonatomic) NSMutableArray                        *rowTypeArray;

@end

@implementation MRPClassicScorecardInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    // Configure interface objects here.
}

- (void)willActivate {
    [super willActivate];
    
    [self initScorecardInformations];
    
    [self configTableScorecard];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSInteger offet = 0;
        if ([MRPRoundData sharedInstance].round.pausedHoleNumber >= 9) {
            offet = 2;
        }
        [self.scorecardTableView scrollToRowAtIndex:[MRPRoundData sharedInstance].round.pausedHoleNumber+offet];
    });
}

- (void)didDeactivate {
    [super didDeactivate];
}

-(void)initScorecardInformations
{
    self.scorecardInfoArray = [NSMutableArray array];
    
    if([MRPRoundData sharedInstance].round.selectedTee.holesArray.count > 0) {
        [self.scorecardInfoArray addObject:[self createScorecardHolesFromIndex:0]];
        [self.scorecardInfoArray addObject:[[MRPScorecardInfo alloc] init]];
    }
    if([MRPRoundData sharedInstance].round.selectedTee.holesArray.count > 9) {
        [self.scorecardInfoArray addObject:[self createScorecardHolesFromIndex:9]];
        [self.scorecardInfoArray addObject:[[MRPScorecardInfo alloc] init]];
    }
    MRPScorecardInfo *footerScorecard = [[MRPScorecardInfo alloc] init];
    footerScorecard.isFooterActive = YES;
    [self.scorecardInfoArray addObject:footerScorecard];
}

-(MRPScorecardInfo*)createScorecardHolesFromIndex:(NSInteger)index
{
    MRPScorecardInfo *holesScorecardInfo = [[MRPScorecardInfo alloc] init];
    NSMutableArray *array = [NSMutableArray array];
    NSMutableArray *first9HolesArray = [NSMutableArray array];
    NSMutableArray *allHolesArray = [NSMutableArray array];
    NSMutableArray *holes = [[NSMutableArray alloc] init];
    
    if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeBasic]) {
        for (int j = 0; j < [MRPRoundData sharedInstance].round.holesArray.count; j++) {
            [holes addObject:[MRPRoundData sharedInstance].round.holesArray[j]];
        }
        if (holes.count == 0) {
            for (int j = 0; j < [MRPRoundData sharedInstance].round.selectedTee.holesArray.count; j++) {
                [holes addObject:[MRPRoundData sharedInstance].round.selectedTee.holesArray[j]];
            }
        }
    } else {
        for (int j = 0; j < [MRPRoundData sharedInstance].round.selectedTee.holesArray.count; j++) {
            [holes addObject:[MRPRoundData sharedInstance].round.selectedTee.holesArray[j]];
        }
    }
    for(NSInteger i = 0; i < holes.count; i++) {
        if(holes.count > i && i >= index && i<index+9) {
            [array addObject:[holes objectAtIndex:i]];
        }
        if(i < 9) {
            [first9HolesArray addObject:[holes objectAtIndex:i]];
        }
        [allHolesArray addObject:[holes objectAtIndex:i]];
    }
    holesScorecardInfo.holesArray = array;
    holesScorecardInfo.allHolesArray = allHolesArray;
    holesScorecardInfo.first9HolesArray = first9HolesArray;
    return holesScorecardInfo;
}

- (void)configRowTypes {
    self.rowTypeArray = [[NSMutableArray alloc] init];
    
    [self.rowTypeArray addObject:@"scorecardClassicHeaderTableRow"];
    
    for (int i = 0; i < [MRPRoundData sharedInstance].round.selectedTee.holesArray.count; i++) {
        [self.rowTypeArray addObject:@"scorecardClassicTableRow"];
    }
    
    if ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count > 9) {
        // Add row for IN, OUT, TOTAL
        [self.rowTypeArray addObject:@"scorecardClassicTableRow"];
        [self.rowTypeArray addObject:@"scorecardClassicTableRow"];
        [self.rowTypeArray addObject:@"scorecardClassicTableRow"];
    } else {
        //add row for TOTAL
        [self.rowTypeArray addObject:@"scorecardClassicTableRow"];
    }
    
    [self.rowTypeArray addObject:@"scorecardClassicFooterTableRow"];
}

- (void)configTableScorecard {
    
    [self configRowTypes];
    
    NSInteger row = 0;
    if ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count > 9) {
        row = 9*2+4+1; // 3 is include Header, IN, OUT, TOTAL, Putts
        
        [self.scorecardTableView setRowTypes:self.rowTypeArray];
        
        
        for (int i = 0; i < row; i++) {
            if (i == 0) {

            } else if (i < 10) {
                MRPScorecardInfo *scorecardInfo = self.scorecardInfoArray[0];
                MRPClassicScorecardCell *cell = (MRPClassicScorecardCell *)[self.scorecardTableView rowControllerAtIndex:i];
                MRPHoleModel *hole = [scorecardInfo.holesArray objectAtIndex:i-1];
                cell.round = [MRPRoundData sharedInstance].round;
                [cell configStateTypeForHole:hole];
                [cell configOrderHole:i];
            } else if (i == 10) {
                MRPClassicScorecardCell *cell = [self.scorecardTableView rowControllerAtIndex:i];
                [cell.orderHoleLabel setText:@"OUT"];
                MRPScorecardInfo *scorecardInfo = self.scorecardInfoArray[0];
                [cell.orderHoleLabel setTextColor:[UIColor whiteColor]];
                [cell.scoreLabel setTextColor:[UIColor blackColor]];
                [cell.parViewGroup setBackgroundColor:[UIColor colorWithRed:244./255. green:245./255. blue:246./255. alpha:1]];
                [cell getRoundNumberOfStrokes:scorecardInfo.first9HolesArray];
            } else if (i > 10 && i < 20) {
                MRPScorecardInfo *scorecardInfo = self.scorecardInfoArray[2];
                MRPClassicScorecardCell *cell = (MRPClassicScorecardCell *)[self.scorecardTableView rowControllerAtIndex:i];
                MRPHoleModel *hole = [scorecardInfo.holesArray objectAtIndex:(i-2)%9];
                cell.round = [MRPRoundData sharedInstance].round;
                [cell configStateTypeForHole:hole];
                [cell configOrderHole:i-1];
            } else if (i == 20) {
                MRPClassicScorecardCell *cell = (MRPClassicScorecardCell *)[self.scorecardTableView rowControllerAtIndex:i];
                [cell.orderHoleLabel setText:@"IN"];
                [cell.orderHoleLabel setTextColor:[UIColor whiteColor]];
                [cell.scoreLabel setTextColor:[UIColor blackColor]];
                MRPScorecardInfo *scorecardInfo = self.scorecardInfoArray[2];
                [cell.parViewGroup setBackgroundColor:[UIColor colorWithRed:244./255. green:245./255. blue:246./255. alpha:1]];
                [cell getRoundNumberOfStrokes:scorecardInfo.holesArray];
            } else if (i == 21) {
                MRPClassicScorecardCell *cell = (MRPClassicScorecardCell *)[self.scorecardTableView rowControllerAtIndex:i];
                [cell.orderHoleLabel setText:@"TOTAL"];
                [cell.orderHoleLabel setTextColor:[UIColor whiteColor]];
                [cell.scoreLabel setTextColor:[UIColor blackColor]];
                MRPScorecardInfo *scorecardInfo = self.scorecardInfoArray[2];
                [cell.parViewGroup setBackgroundColor:[UIColor colorWithRed:244./255. green:245./255. blue:246./255. alpha:1]];
                [cell getRoundNumberOfStrokes:scorecardInfo.allHolesArray];
            } else if (i == 22) {
                MRPClassicFooterScoreCardCell *cell = (MRPClassicFooterScoreCardCell *)[self.scorecardTableView rowControllerAtIndex:i];
                cell.delegate = self;
                if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                    [cell.totalPuttsScoreGroup setHidden:YES];
                    [cell.totalPuttsScoreLabel setHidden:YES];
                    [cell.totalPuttsLabel setHidden:YES];
                } else {
                    [cell.totalPuttsScoreGroup setHidden:NO];
                    [cell.totalPuttsScoreLabel setHidden:NO];
                    [cell.totalPuttsLabel setHidden:NO];
                    [cell.totalPuttsScoreLabel setText:[self getTotalPutts]];
                }
            }
        }
    } else {
        row = 9+2+1; // 1 is include HEADER TOTAL, Putts
        [self.scorecardTableView setRowTypes:self.rowTypeArray];
        
        for (int i = 0; i < row; i++) {
            if (i == 0) {
                
            } else if (i < 10) {
                MRPScorecardInfo *scorecardInfo = self.scorecardInfoArray[0];
                MRPClassicScorecardCell *cell = [self.scorecardTableView rowControllerAtIndex:i];
                MRPHoleModel *hole = [scorecardInfo.holesArray objectAtIndex:i-1];
                cell.round = [MRPRoundData sharedInstance].round;
                [cell configStateTypeForHole:hole];
                [cell configOrderHole:i];
            } else if (i == 10) {
                MRPClassicScorecardCell *cell = (MRPClassicScorecardCell *)[self.scorecardTableView rowControllerAtIndex:i];
                [cell.orderHoleLabel setText:@"TOTAL"];
                [cell.orderHoleLabel setTextColor:[UIColor whiteColor]];
                [cell.scoreLabel setTextColor:[UIColor blackColor]];
                MRPScorecardInfo *scorecardInfo = self.scorecardInfoArray[0];
                [cell.parViewGroup setBackgroundColor:[UIColor colorWithRed:244./255. green:245./255. blue:246./255. alpha:1]];
                [cell getRoundNumberOfStrokes:scorecardInfo.allHolesArray];
            } else if (i == 11) {
                MRPClassicFooterScoreCardCell *cell = (MRPClassicFooterScoreCardCell *)[self.scorecardTableView rowControllerAtIndex:i];
                cell.delegate = self;
                if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                    [cell.totalPuttsScoreGroup setHidden:YES];
                    [cell.totalPuttsScoreLabel setHidden:YES];
                    [cell.totalPuttsLabel setHidden:YES];
                } else {
                    [cell.totalPuttsScoreGroup setHidden:NO];
                    [cell.totalPuttsScoreLabel setHidden:NO];
                    [cell.totalPuttsLabel setHidden:NO];
                    [cell.totalPuttsScoreLabel setText:[self getTotalPutts]];
                }
            }
            
        }
    }
}

- (void)table:(WKInterfaceTable *)table didSelectRowAtIndex:(NSInteger)rowIndex {
    MRPHoleModel *selectedHole = [MRPWatchUtils findHoleInRound:[MRPRoundData sharedInstance].round withIndex:[MRPRoundData sharedInstance].round.pausedHoleNumber];
    
    if ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count > 9) {
        if (rowIndex == 0) {
            
        } else if (rowIndex < 10) {
            if (rowIndex-1 != [MRPRoundData sharedInstance].round.pausedHoleNumber) {
                selectedHole.completed = YES;
                
                [MRPWatchUtils saveDataInFile];
            }
            
            [MRPRoundData sharedInstance].round.pausedHoleNumber = rowIndex-1;
            [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationStopUpdateLocation object:nil];
            
            if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:1];
            } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
                if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:1];
                } else {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:1];
                }
            } else {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:1];
            }
        } else if (rowIndex == 10) {
            
        } else if (rowIndex > 10 && rowIndex < 20) {
            if (rowIndex-1 != [MRPRoundData sharedInstance].round.pausedHoleNumber) {
                selectedHole.completed = YES;
                
                [MRPWatchUtils saveDataInFile];
            }
            
            [MRPRoundData sharedInstance].round.pausedHoleNumber = rowIndex-2;
            [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationStopUpdateLocation object:nil];
            
            if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:1];
            } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
                if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:1];
                } else {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:1];
                }
            } else {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:1];
            }
            
        }
    } else {
        if (rowIndex == 0) {
            
        } else if (rowIndex < 10) {
            if (rowIndex-1 != [MRPRoundData sharedInstance].round.pausedHoleNumber) {
                selectedHole.completed = YES;
                
                [MRPWatchUtils saveDataInFile];
            }
            
            [MRPRoundData sharedInstance].round.pausedHoleNumber = rowIndex-1;
            [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationStopUpdateLocation object:nil];
            if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:1];
            } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
                if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:1];
                } else {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:1];
                }
            } else {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:1];
            }
        }
    }
    
    [[MRPWatchRoundManager sharedInstance] roundDidUpdate:[MRPRoundData sharedInstance].round];
}

-(NSString *)getTotalPutts {
    NSInteger totalPutts = 0;
    for (MRPHoleModel *hole in [MRPRoundData sharedInstance].round.selectedTee.holesArray) {
        totalPutts += hole.puttsNumber;
    }
    return [NSString stringWithFormat:@"%d", (int)totalPutts];
}

-(void)endRoundClassic {
    if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:2];
    } else {
        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:2];
    }
}
@end
