//
//  MRPClassicFooterScoreCardCell.h
//  MyTaylorMadeOnCourse
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 11/05/2022.
//  Copyright © 2022 TaylorMade Golf. All rights reserved.
//

#import <Foundation/Foundation.h>
@import WatchKit;

@class MRPClassicFooterScoreCardCell;

@protocol MRPClassicFooterScoreCardCellDelegate

@optional
-(void)endRoundClassic;

@end

@interface MRPClassicFooterScoreCardCell : NSObject

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *totalPuttsLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup  *totalPuttsScoreGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *totalPuttsScoreLabel;


@property (weak, nonatomic) id <MRPClassicFooterScoreCardCellDelegate>          delegate;

@end

