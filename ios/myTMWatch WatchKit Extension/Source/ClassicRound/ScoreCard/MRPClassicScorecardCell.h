//
//  MRPClassicScorecardCell.h
//  MyTaylorMadeOnCourse
//
//  Created by HienNN on 11/05/2022.
//  Copyright © 2022 TaylorMade Golf. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "MRPHoleModel.h"
#import "MRPRoundModel.h"
@import WatchKit;

@interface MRPClassicScorecardCell : NSObject

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *scoreLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *orderHoleLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup  *parImageViewGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup  *parViewGroup;

@property (strong, nonatomic) MRPRoundModel                         *round;

- (void)configStateTypeForHole:(MRPHoleModel *)hole;
- (void)configOrderHole:(NSInteger)order;
- (void)getRoundNumberOfStrokes:(NSArray *)holesArray;

@end

