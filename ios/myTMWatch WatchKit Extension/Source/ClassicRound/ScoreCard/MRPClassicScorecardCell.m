//
//  MRPClassicScorecardCell.m
//  MyTaylorMadeOnCourse
//
//  Created by HienN<PERSON> on 11/05/2022.
//  Copyright © 2022 TaylorMade Golf. All rights reserved.
//

#import "MRPClassicScorecardCell.h"
#import "MRPWatchUtils.h"
#import "MRPConstants.h"

@implementation MRPClassicScorecardCell

- (void)configStateTypeForHole:(MRPHoleModel *)hole {
    if (hole.completed) {
        NSInteger score = 0;
        if (self.round.inprogress) {
            score = hole.score;
        } else {
            if (hole.strokes.count == 0) {
                score = hole.score;
            } else {
                score = hole.strokes.count;
            }
        }
        if(score == 0) {
            self.scoreLabel.text = @"";
            [self.parImageViewGroup setBackgroundImage:nil];
        } else {
            self.scoreLabel.text = [NSString stringWithFormat:@"%ld", (long)score];
            if(score == hole.par+1) {
                [self.parImageViewGroup setBackgroundImage:[UIImage imageNamed:@"ic_ParBogey"]];
            } else if(score >= hole.par+2) {
                [self.parImageViewGroup setBackgroundImage:[UIImage imageNamed:@"ic_ParDoubleBogey"]];
            } else if(score == hole.par-1) {
                [self.parImageViewGroup setBackgroundImage:[UIImage imageNamed:@"ic_parBirdie"]];
            } else if(score <= hole.par-2) {
                [self.parImageViewGroup setBackgroundImage:[UIImage imageNamed:@"ic_parDoubleAgle"]];
            } else if(score == hole.par) {
                [self.parImageViewGroup setBackgroundImage:nil];
            }
        }
    }
}

- (void)getRoundNumberOfStrokes:(NSArray *)holesArray {
    long totalStrokes = 0;
    for(MRPHoleModel *hole in holesArray) {
        if (hole.completed) {
            if (hole.strokes.count == 0) {
                totalStrokes += hole.score;
            } else {
                totalStrokes += hole.strokes.count;
            }
        }
    }
    if(totalStrokes == 0) {
        self.scoreLabel.text = @"";
    } else {
        NSString * totalText = [NSString stringWithFormat:@"%ld", (long)totalStrokes];
        self.scoreLabel.text = totalText;
    }
}

- (void)configOrderHole:(NSInteger)order {
    [self.orderHoleLabel setText:[MRPWatchUtils getOrdinalStringFromInteger:order]];
}

@end
