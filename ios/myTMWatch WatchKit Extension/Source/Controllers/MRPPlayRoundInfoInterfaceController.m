//
//  MRPPlayRoundInfoInterfaceController.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/19/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPPlayRoundInfoInterfaceController.h"
#import "MRPErrorInterfaceController.h"
#import "MRPCourseModel.h"
#import "MRPRoundModel.h"
#import "Mantle.h"
#import "MRPWatchApiHandler.h"

@interface MRPPlayRoundInfoInterfaceController ()

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup  *mainGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup  *loadingGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceImage  *indicatorImage;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *courseNameLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *holeNumberLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *parNumberLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *teeNameLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *distanceTeeLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *courseAddressLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *typeCourseLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *modeCourseLabel;

@property (strong, nonatomic) MRPCourseModel                        *course;
@property (strong, nonatomic) MRPRoundModel                         *round;

@property (assign, nonatomic) BOOL                                  isEditMode;
@property (assign, nonatomic) BOOL                                  showAdvance;

@end

@implementation MRPPlayRoundInfoInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    self.course = (MRPCourseModel *)context;
    
    [self getShowAdvanced];

    [self initRound];
    
    [self initElement];
}

- (void)willActivate {
    [super willActivate];
}

- (void)didDeactivate {
    [super didDeactivate];
}

- (void)getShowAdvanced {
  NSDictionary *userInfoDic = [kUserDefaultsWatch objectForKey:kUserDefaultsUserInfo];
  if ([userInfoDic[@"canPlayAdvancedRound"] isEqual:@1]) {
    self.showAdvance = YES;
  } else {
    [self showLoading];
    [[MRPWatchApiHandler sharedInstance] getShowAdvanceWithSuccess:^(BOOL showAdvance) {
      self.showAdvance = showAdvance;
      [self stopLoading];
    } andFailure:^(NSError *error) {
      self.showAdvance = NO;
      [self stopLoading];
      NSLog(@"getShowAdvanceWithSuccess%@", error.localizedDescription);
    }];
  }
}

- (void)initElement {
    [self.courseNameLabel setText:self.course.name];
    [self.courseAddressLabel setText:self.course.address];
    [self setupUIData];
    [self setTitle:@" "];
}

- (void)initRound {
    if(!self.isEditMode) {
        self.round = [MRPRoundModel new];
        // TODO: Check logic
//        [MRPUtils initLocalHoleIds:self.round];
        self.round.course = self.course;
        self.round.gameType = @0;
        self.round.roundDate = [NSDate date];
        self.round.playedOn = [NSDate date];
        self.round.roundId = [NSNumber numberWithInteger:self.round.roundDate.timeIntervalSince1970];
        self.round.userId = [kUserDefaultsWatch objectForKey:kUserDefaultsLoggedUserId];
        self.round.completed = [NSNumber numberWithBool:NO];
        self.round.roundType = @"Practice";
        self.round.roundMode = @"Classic";
        self.round.courseName = self.course.name;
        
        self.round.mapId = kCourseTypeiGolf;
        self.round.igolfCourseId = self.course.igolfCourseId;
        
        self.round.courseId = [self.course.courseId stringValue];
        
        NSInteger count = 0;
        
        NSInteger imagesIndex = -1;
        for(MRPTeeModel *tee in self.round.course.teeArray) {
            if([tee.isDefault boolValue]) {
                self.round.selectedTee = tee;
            } else if([tee.teeName isEqualToString:@"images"]) {
                imagesIndex = count;
            }
            count++;
        }
        if(imagesIndex >= 0 && imagesIndex < self.round.course.teeArray.count) {
//            [self.round.course.teeArray removeObjectAtIndex:imagesIndex];
        }
    }
    
    self.round.roundMode = kRoundModeClassic;
    self.round.modeIndex = [NSNumber numberWithInteger:ModeTypeClassic];
}

- (void)setupUIData {
    NSString *holeNumber = [NSString stringWithFormat:@"%@",self.course.numberOfHoles];
    NSString *parNumber = [NSString stringWithFormat:@"%@",self.round.selectedTee.totalPar];
    
    
    NSDictionary *userInfoDic = [kUserDefaultsWatch objectForKey:kUserDefaultsUserInfo];
    NSString *distanceTee;
    NSString *unitText;
    if ([userInfoDic[@"unitInfo"] isEqualToString:@"yards"]) {
        unitText = @"YDS";
    } else {
        unitText = @"M";
    }
    
    distanceTee = [NSString stringWithFormat:@"%ld %@",(long)[MRPWatchUtils getConvertedValueForUnit:self.round.selectedTee.totalYards.integerValue], unitText];
    
    [self.holeNumberLabel setText:holeNumber];
    [self.parNumberLabel setText:parNumber];
    [self.teeNameLabel setText:self.round.selectedTee.teeNameDisplay];
  
    BOOL isValidTee = (self.round.selectedTee.teeName.length > 0 &&
                       ![self.round.selectedTee.teeName isEqualToString:@"null"] &&
                       self.round.selectedTee.totalYards > 0 &&
                       distanceTee != nil);

    self.distanceTeeLabel.hidden = !isValidTee;
    if (isValidTee) {
        self.distanceTeeLabel.text = distanceTee;
    }
}

- (void)showLoading {
    [self.mainGroup setHidden:YES];
    [self.loadingGroup setHidden:NO];
    [self.indicatorImage startActivityIndicator];
}

- (void)stopLoading {
    [self.mainGroup setHidden:NO];
    [self.loadingGroup setHidden:YES];
    [self.indicatorImage stopActivityIndicator];
}

- (IBAction)showListType:(id)sender {
    [self presentControllerWithName:@"listTypeInterfaceController" context:self];
}

- (IBAction)showListMode:(id)sender {
    [self presentControllerWithName:@"listModeInterfaceController" context:self];
}

- (IBAction)showListTee:(id)sender {
  //if the default tee is not N/A, then show the list tee to select
  if(![self.round.course.defaultTee isEqual:@"N/A"]) {
    [self presentControllerWithName:@"listTeeInterfaceController" context:@{@"context"  : self,
                                                                            @"round"    : self.round}];
  }
}

- (IBAction)touchupInsidePlayRound {
  [self showLoading];
  NSDictionary *userInfoDic = [kUserDefaultsWatch objectForKey:kUserDefaultsUserInfo];
  if ([userInfoDic[@"canPlayAdvancedRound"] isEqual:@1]) {
    [self requestDefaultClub];
  } else {
    if ([self.round.roundMode isEqual: @"Advanced"]) {
      [[MRPWatchApiHandler sharedInstance] getShowAdvanceWithSuccess:^(BOOL showAdvance) {
        if (!showAdvance) {
          [self stopLoading];
          [self showAlert:@"This feature requires a TaylorMade paid subscription \n Go to TaylorMade Plans"];
        } else {
          [self requestDefaultClub];
        }
      } andFailure:^(NSError *error) {
        [self stopLoading];
        if ([error.localizedDescription containsString:@"unauthorized"]) {
          [MRPWatchUtils reloadRootControllerWithName:@"interfaceController"];
        } else {
          [self presentControllerWithName:@"errorInterfaceController"
                                  context:@{@"title"          : @"",
                                            @"description"    : error.localizedDescription}];
        }
        NSLog(@"getShowAdvanceWithSuccess%@", error.localizedDescription);
      }];
    } else {
      [self requestDefaultClub];
    }
  }
}

- (void)requestDefaultClub {
    [self requestCreateRound];
}

- (void)requestCreateRound {
    self.round.inprogress = YES;
    [[MRPWatchApiHandler sharedInstance] submitEmptyRound:self.round success:^(NSNumber *roundId) {
        
        [self stopLoading];
        
        MRPTeeModel *selectTee = [[MRPTeeModel alloc] init];
        selectTee = self.round.selectedTee;
        self.round.course = nil;
        self.round.selectedTee = selectTee;
        self.round.roundId = roundId;
        self.round.pausedHoleNumber = 0;
        NSError *error;
        NSDictionary *roundDic = [MTLJSONAdapter JSONDictionaryFromModel:self.round error:&error];
        
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:roundDic
                                                           options:0 // Pass 0 if you don't care about the readability of the generated string
                                                             error:&error];
        NSString *roundString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        
        [MRPWatchUtils createOrUpdateRoundFile:roundString];
        
        [MRPRoundData sharedInstance].round = [MTLJSONAdapter modelOfClass:[MRPRoundModel class] fromJSONDictionary:roundDic error:&error];
        
        if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
            [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:1];
        } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
            if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:1];
            } else {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:1];
            }
        } else {
            [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:1];
        }
        
    } failure:^(NSError *error) {
        [self stopLoading];
        if ([error.localizedDescription containsString:@"unauthorized"]) {
            [MRPWatchUtils reloadRootControllerWithName:@"interfaceController"];
        } else {
            [self presentControllerWithName:@"errorInterfaceController"
                                    context:@{@"title"          : @"",
                                              @"description"    : error.localizedDescription}];
        }
        
    }];
}

#pragma mark - List Type Delegate
- (void)didSelectType:(NSString *)type {
    [self.typeCourseLabel setText:type];
    self.round.roundType = type;
}

#pragma mark - List Mode Delegate
- (void)didSelectMode:(NSString *)mode {
    if ([mode isEqual: @"Advanced"] && !self.showAdvance) {
        [self showAlert:@"This feature requires a TaylorMade paid subscription \n Go to TaylorMade Plans"];
        return;
    }
    [self.modeCourseLabel setText:mode];
    self.round.roundMode = mode;
}

#pragma mark - List Tee Delegate
- (void)didSelectTee:(MRPTeeModel *)tee {
    self.round.selectedTee = tee;
    NSString *distanceTee = [NSString stringWithFormat:@"%@ YDS",self.round.selectedTee.totalYards];
    [self.teeNameLabel setText:self.round.selectedTee.teeName];
    BOOL isValidTee = (self.round.selectedTee.teeName.length > 0 &&
                       ![self.round.selectedTee.teeName isEqualToString:@"null"] &&
                       self.round.selectedTee.totalYards > 0 &&
                       distanceTee != nil);

    self.distanceTeeLabel.hidden = !isValidTee;
    if (isValidTee) {
        self.distanceTeeLabel.text = distanceTee;
    }
    
    [self setupUIData];
}

- (void)showAlert:(NSString *)error {
    [self presentControllerWithName:@"errorInterfaceController"
                            context:@{@"title"          : @"",
                                      @"description"    : error}];
}

@end
