//
//  MRPErrorInterfaceController.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/20/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPErrorInterfaceController.h"

@interface MRPErrorInterfaceController ()

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel *titleLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel *descriptionLabel;
@end

@implementation MRPErrorInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    [self.titleLabel setText:context[@"title"]];
    [self.descriptionLabel setText:context[@"description"]];
    // Configure interface objects here.
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}

@end



