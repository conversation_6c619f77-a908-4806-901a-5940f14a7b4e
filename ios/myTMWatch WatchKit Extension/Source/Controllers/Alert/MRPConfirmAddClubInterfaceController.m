//
//  MRPConfirmAddClubInterfaceController.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 8/8/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPConfirmAddClubInterfaceController.h"

@interface MRPConfirmAddClubInterfaceController ()

@property (weak, nonatomic) id <MRPConfirmAddClubDelegate>                delegate;

@end

@implementation MRPConfirmAddClubInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    self.delegate = context;
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}
- (IBAction)dismissAlert {
    [self dismissController];
    
    [self.delegate selectedClub:NO];
}

- (IBAction)addClub {
    [self dismissController];
    
    [self.delegate selectedClub:YES];
}
@end



