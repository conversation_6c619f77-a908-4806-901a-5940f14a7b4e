//
//  MRPAddShotInterfaceController.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/25/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPAddShotInterfaceController.h"
#import <CoreLocation/CoreLocation.h>
#import <MapKit/MapKit.h>
#import "MRPListClubInterfaceController.h"
#import "MRPWatchRoundManager.h"
#import "MRPRoundLieModel.h"

@interface MRPAddShotInterfaceController ()

@property (nonatomic, strong) CLLocationManager                     *locationManager;
@property (nonatomic, strong) CLLocation                            *currentLocation;
@property (strong, nonatomic) NSArray                               *activeClubs;
@property (strong, nonatomic) NSMutableArray                        *locations;

@property (assign, nonatomic) NSInteger                             pausedHoleNumber;
@property (assign, nonatomic) NSInteger                             improvementAccuracyToGiveUpOn;
@property (assign, nonatomic) NSInteger                             pickerIndex;
@property (assign, nonatomic) float                                 middleDistance;
@property (assign, nonatomic) BOOL                                  deferredLocationUpdates;
@property (assign, nonatomic) NSString                              *roundMode;

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *middleGreenDistanceTextLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *frontGreenDistanceTextLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *backGreenDistanceTextLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *holeNumberLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *parNumberLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *scoreNumberLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *previousShotLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *addShotButton;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton *selectHole;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfacePicker *holePicker;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup  *prevGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup  *scoreGroup;

//@property (strong, nonatomic) MRPRoundModel                         *round;

@property (strong, nonatomic) MRPHoleModel                          *selectedHole;
@property (strong, nonatomic) MRPFeatureModel                       *holeFeature;
@property (strong, nonatomic) MRPFeatureModel                       *teeFeature;
@property (strong, nonatomic) MRPFeatureModel                       *greenFeature;

@end

@implementation MRPAddShotInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    self.locations = [[NSMutableArray alloc] init];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(stopUpdateLocation) name:kNotificationStopUpdateLocation object:nil];
    
    [self setupDataForScreen];
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
    
    [self configLocationManager];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}

- (void)willDisappear {
    [super willDisappear];
}

- (void)stopGetCurrentLocation {
    self.currentLocation = [self getCorrectLocation];
    [self calculateDistanceFromCurrentLocation];
    [self.locations removeAllObjects];
}

- (void)setupDataForScreen {
    
    self.pausedHoleNumber = [MRPRoundData sharedInstance].round.pausedHoleNumber;
    self.selectedHole = [MRPWatchUtils findHoleInRound:[MRPRoundData sharedInstance].round withIndex:[MRPRoundData sharedInstance].round.pausedHoleNumber];
    self.roundMode = [MRPRoundData sharedInstance].round.roundMode;
    [self drawHole:self.selectedHole];
    [self getClubs];
    
    [MRPWatchRoundManager sharedInstance].playingRound = [MRPRoundData sharedInstance].round;
    [MRPWatchRoundManager sharedInstance].playingHole = self.selectedHole;
    [self configDataCourse];
}

- (NSInteger)getClubIndex {
    NSInteger clubIndex;
    MRPLocation2DModel *currentLocation = [[MRPLocation2DModel alloc] initWithLatitude:[NSNumber numberWithDouble:self.currentLocation.coordinate.latitude]
                                                     andLongitude:[NSNumber numberWithDouble:self.currentLocation.coordinate.longitude]];
    NSString *startingLie = [MRPWatchUtils getLieForCoordinate:[currentLocation getCoordinateLocation2D] fromHole:self.selectedHole];
    
    NSArray *clubsData = [kUserDefaultsWatch objectForKey:kUserDefaultsListClub];
    NSArray *clubs = [MTLJSONAdapter modelsOfClass:[MRPClubsModel class]
                                     fromJSONArray:clubsData error:nil];
    float distanceGreen = self.middleDistance;
    if (self.selectedHole.strokes.count == 0 && self.selectedHole.par != 3) {
        clubIndex = 0;
    } else if ([[startingLie lowercaseString] isEqualToString:@"green"]) {
        clubIndex = [MRPWatchUtils getPutterIndex:clubs andDistance:distanceGreen];
    } else {
        if(self.selectedHole.par == 3) {
            clubIndex = (clubs.count/2);
            
            NSInteger selectedClubIndex = [MRPWatchUtils getClubIndex:clubs andDistance:distanceGreen];
            if (selectedClubIndex != -1) {
                clubIndex = selectedClubIndex;
            }
        } else if(self.selectedHole.par == 4 || self.selectedHole.par == 5) {
            if(clubs.count <= 2){
                clubIndex = 0;
            }else{
                clubIndex = (clubs.count/2);
                NSInteger selectedClubIndex = [MRPWatchUtils getClubIndex:clubs andDistance:distanceGreen];
                if (selectedClubIndex != -1) {
                    clubIndex = selectedClubIndex;
                }
            }
        } else {
            clubIndex = 0;
        }
    }
    
//    [self.backGreenDistanceTextLabel setText:[NSString stringWithFormat:@"%d",clubIndex]];
    
    return clubIndex;
}
bool isSelect = true;
- (IBAction)touchupInsideAddShot {
    if([self.roundMode isEqualToString:kRoundModeAdvanced]){
        [self addAdvancedShot];
    } else if ([self.roundMode isEqualToString:kRoundModeClassic]) {
        [self addClassicShot];
    } else {
        if(isSelect){
            isSelect = false;
            [self addBasicShot];
            [self performSelector:@selector(setSelectButton) withObject:nil afterDelay:0.3];
        }
    }
}

- (void) setSelectButton{
    isSelect = true;
}

- (void) addClassicShot {
    NSInteger holeNumber = self.selectedHole.holeNumber;
    [self presentControllerWithName:@"AddHoleClassicInterfaceController" context:@{@"context"  : self, @"selectedHole": self.selectedHole, @"classicModeScoresOnly": @([self checkTrackGroup]), @"isFirst": @([self checkFirst])} ];
    [MRPRoundData sharedInstance].round.pausedHoleNumber = holeNumber;
}

- (void) addBasicShot{
    NSInteger holeNumber = self.selectedHole.holeNumber;
    NSInteger coursePar = self.selectedHole.par;
    NSInteger courseScore = self.selectedHole.score;
    NSLog(@"MRPRoundData: %@, hole %@", [NSString stringWithFormat:@"%ld", (long)courseScore], [NSString stringWithFormat:@"%ld", (long)coursePar]);
    MRPLocation2DModel *currentLocation = [[MRPLocation2DModel alloc] initWithLatitude:[NSNumber numberWithDouble:self.currentLocation.coordinate.latitude]
                                                                          andLongitude:[NSNumber numberWithDouble:self.currentLocation.coordinate.longitude]];
    
    [self presentControllerWithName:@"selectScoreInterfaceController" context:@{@"context"  : self, @"holeNumber": [NSNumber numberWithInt:holeNumber], @"coursePar": [NSNumber numberWithInt:coursePar], @"courseScore": [NSNumber numberWithInt:courseScore]} ];
    [MRPRoundData sharedInstance].round.pausedHoleNumber = holeNumber;
}

- (void) addAdvancedShot{
    MRPLocation2DModel *currentLocation = [[MRPLocation2DModel alloc] initWithLatitude:[NSNumber numberWithDouble:self.currentLocation.coordinate.latitude]
                                                                          andLongitude:[NSNumber numberWithDouble:self.currentLocation.coordinate.longitude]];
    NSString *startingLie = [MRPWatchUtils getLieForCoordinate:[currentLocation getCoordinateLocation2D] fromHole:self.selectedHole];
    BOOL isGreen = [[startingLie lowercaseString] isEqualToString:@"green"];
    [self presentControllerWithName:@"listClubInterfaceController" context:@{@"index" : [NSNumber numberWithInt:[self getClubIndex]],
                                                                             @"isGreen" : @(isGreen),
                                                                             @"delegate" : self}];
}

- (IBAction)touchupInsideSelectHole {
//    NSInteger holes = [MRPRoundData sharedInstance].round.selectedTee.holesArray.count;
//    [self presentControllerWithName:@"listHoleInterfaceController" context:@{@"numberHole" : [NSNumber numberWithInteger:holes],
//                                                                             @"delegate" : self}];
    
    if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:0];
    } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
        if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
            [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:0];
        } else {
            [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:0];
        }
    } else {
        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:0];
    }
}

- (IBAction)pickerChanged:(NSInteger)value {
    self.pickerIndex = value+1;
}

-(void)pickerDidSettle:(WKInterfacePicker *)picker{
    if (self.pickerIndex-1 != [MRPRoundData sharedInstance].round.pausedHoleNumber) {
        self.selectedHole.completed = YES;
        
        [MRPWatchUtils saveDataInFile];
    }
    
    [MRPRoundData sharedInstance].round.pausedHoleNumber = self.pickerIndex-1;
    [self setupDataForScreen];
    [self calculateDistanceFromCurrentLocation];
}

- (void)stopUpdateLocation {
    [self.locationManager stopUpdatingLocation];
    self.locationManager.delegate = nil;
    self.locationManager = nil;
}

- (void)getClubs {
    NSError *error;
    NSArray *clubsData = [kUserDefaultsWatch objectForKey:kUserDefaultsListClub];
    self.activeClubs = [MTLJSONAdapter modelsOfClass:[MRPClubsModel class]
                                     fromJSONArray:clubsData error:&error];
    
}

- (void)configLocationManager {
    // Config location manager
    if (!self.locationManager) {
        self.locationManager = [[CLLocationManager alloc] init];
        self.locationManager.desiredAccuracy = kCLLocationAccuracyBest;
        [self.locationManager setDelegate:self];
        
        if (@available(watchOS 4.0, *)) {
            self.locationManager.activityType = CLActivityTypeFitness;
            self.locationManager.allowsBackgroundLocationUpdates = YES;
        }
        
        self.locationManager.distanceFilter = kCLDistanceFilterNone;
        
        if ([self.locationManager respondsToSelector:@selector(requestAlwaysAuthorization)]) {
            [self.locationManager requestAlwaysAuthorization];
        }
        
        [self.locationManager startUpdatingLocation];
    }
}

- (void)drawHole:(MRPHoleModel *)hole {
    
    for (MRPFeatureModel *feature in hole.features.featureArray) {
        if([feature.property.type isEqualToString:@"green"]) {
            self.greenFeature = feature;
        }
        if([feature.property.type isEqualToString:@"tee"]) {
            self.teeFeature = feature;
        }
        if([feature.property.type isEqualToString:@"hole-boundary"]) {
            if(!self.holeFeature) {
                self.holeFeature = feature;
            } else if([MRPWatchUtils regionArea:feature.geometry.coordinates] > [MRPWatchUtils regionArea:self.holeFeature.geometry.coordinates]){
                self.holeFeature = feature;
            }
        }
    }
}

- (void)configDataCourse {
    NSString *titleAddShotButton = [NSString stringWithFormat:@"ADD HOLE SCORE"];
    NSString *score = [NSString stringWithFormat:@"%ld", (long)self.selectedHole.score];
    if(score.intValue > 0){
        titleAddShotButton = [NSString stringWithFormat:@"EDIT HOLE SCORE"];
    }
    if([self.roundMode isEqualToString:kRoundModeAdvanced]){
        [self.prevGroup setHidden:NO];
        [self.scoreGroup setVerticalAlignment:WKInterfaceObjectVerticalAlignmentTop];
        titleAddShotButton = [NSString stringWithFormat:@"ADD %@ SHOT",[MRPWatchUtils getOrdinalStringFromInteger:self.selectedHole.strokes.count+1]];
        score = [NSString stringWithFormat:@"%ld", (long)self.selectedHole.strokes.count];
    }else{
        [self.scoreGroup setVerticalAlignment:WKInterfaceObjectVerticalAlignmentCenter];
        [self.prevGroup setHidden:YES];
        score = [self calculatorScoreToPar];
    }
    [self.addShotButton setText:[titleAddShotButton uppercaseString]];
    [self.holeNumberLabel setText:[NSString stringWithFormat:@"%ld",(long)[MRPRoundData sharedInstance].round.pausedHoleNumber+1]];
    [self.parNumberLabel setText:[NSString stringWithFormat:@"%ld", (long)self.selectedHole.par]];
    [self.scoreNumberLabel setText: score];
    
    NSInteger hole = 9;
    if ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count > 9) {
        hole = 18;
    }
    NSMutableArray *items = [[NSMutableArray alloc] init];
    for (int i = 0; i < hole; i++) {
        WKPickerItem *item = [[WKPickerItem alloc] init];
        item.title = [NSString stringWithFormat:@"%d",i+1];
        [items addObject:item];
    }
    [self.holePicker setItems:items];
    [self.holePicker setSelectedItemIndex:[MRPRoundData sharedInstance].round.pausedHoleNumber];
    [self.holePicker resignFocus];
    NSInteger numberHole = [MRPRoundData sharedInstance].round.pausedHoleNumber+1;
    if (
        ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count < 10 && [MRPRoundData sharedInstance].round.pausedHoleNumber == 9) ||
        ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count < 19 && [MRPRoundData sharedInstance].round.pausedHoleNumber== 18)) {
            numberHole = [MRPRoundData sharedInstance].round.pausedHoleNumber;
    }
    [self.selectHole setTitle:[NSString stringWithFormat:@"%ld",(long)numberHole]];
}

- (void)calculateDistanceFromCurrentLocation {
    MRPRoundModel *round = [MRPRoundData sharedInstance].round;
    if(round.pausedHoleNumber < round.selectedTee.holesArray.count){
        CLLocation *greenMiddleLocation = [[CLLocation alloc] initWithLatitude:[round.selectedTee.holesArray[round.pausedHoleNumber].greenMiddle.latitude floatValue] longitude:[round.selectedTee.holesArray[round.pausedHoleNumber].greenMiddle.longitude floatValue]];
        CLLocation *greenFrontLocation = [[CLLocation alloc] initWithLatitude:[round.selectedTee.holesArray[round.pausedHoleNumber].greenFront.latitude floatValue] longitude:[round.selectedTee.holesArray[round.pausedHoleNumber].greenFront.longitude floatValue]];
        CLLocation *greenBackLocation = [[CLLocation alloc] initWithLatitude:[round.selectedTee.holesArray[round.pausedHoleNumber].greenBack.latitude floatValue] longitude:[round.selectedTee.holesArray[round.pausedHoleNumber].greenBack.longitude floatValue]];
        
        CLLocationDistance distanceMiddle = [self.currentLocation distanceFromLocation:greenMiddleLocation];
        CLLocationDistance distanceFront = [self.currentLocation distanceFromLocation:greenFrontLocation];
        CLLocationDistance distanceBack = [self.currentLocation distanceFromLocation:greenBackLocation];

        self.middleDistance = [MRPWatchUtils formatMaxNumberDistance:distanceMiddle];
        
        [self.middleGreenDistanceTextLabel setText:[NSString stringWithFormat:@"%.0f", [MRPWatchUtils formatMaxNumberDistance:distanceMiddle]]];
        
        // TODO:
    //    MRPLocation2DModel *greenFront1Location = [[MRPLocation2DModel alloc] initWithLatitude:[NSNumber numberWithDouble:self.currentLocation.coordinate.latitude]
    //                                                                          andLongitude:[NSNumber numberWithDouble:self.currentLocation.coordinate.longitude]];
    //    NSString *startingLie = [MRPWatchUtils getLieForCoordinate:[greenFront1Location getCoordinateLocation2D] fromHole:self.selectedHole];
    //    [self.frontGreenDistanceTextLabel setText:startingLie];
        
        [self.frontGreenDistanceTextLabel setText:[NSString stringWithFormat:@"%.0f", [MRPWatchUtils formatMaxNumberDistance:distanceFront]]];
        [self.backGreenDistanceTextLabel setText:[NSString stringWithFormat:@"%.0f", [MRPWatchUtils formatMaxNumberDistance:distanceBack]]];

        if (self.selectedHole.strokes.count < 1) {
            [self.previousShotLabel setText:@""];
        } else {
            MRPStrokeModel *stroke = (MRPStrokeModel *)[self.selectedHole.strokes lastObject];

            MRPClubsModel *previousClubs;
            for (MRPClubsModel *club in self.activeClubs) {
              if ([club.clubId isEqualToString: stroke.clubId]) {
                    previousClubs = club;
                    break;
                }
            }            
            NSString *previousStrokeName = ([MRPWatchUtils getClubSymbol:previousClubs]) ? [MRPWatchUtils getClubSymbol:previousClubs] : @"Penalty";
            CLLocationCoordinate2D location = [[stroke.coordinates lastObject] getCoordinateLocation2D];
            CLLocation *currentShot = [[CLLocation alloc] initWithLatitude:location.latitude longitude:location.longitude];
            CLLocationDistance distancePreviousShot = [self.currentLocation distanceFromLocation:currentShot];
            [self.previousShotLabel setText:[NSString stringWithFormat:@"%@ - %.0f", previousStrokeName, [MRPWatchUtils formatMaxNumberDistance:distancePreviousShot]]];
        }
    }
}

- (void)addStrokeWithClub:(MRPClubsModel *)club penalty:(BOOL)penalty putter:(BOOL)putter numberClubs:(NSInteger)numberClubs {

    MRPLocation2DModel *strokeLocation;
    NSString *startingLie = @"";
    if (putter) {
        strokeLocation = [[MRPLocation2DModel alloc] initWithLatitude:[NSNumber numberWithInteger:0]
                                                         andLongitude:[NSNumber numberWithInteger:0]];
        startingLie = @"green";
    }else {
        strokeLocation = [[MRPLocation2DModel alloc] initWithLatitude:[NSNumber numberWithDouble:self.currentLocation.coordinate.latitude]
                                                         andLongitude:[NSNumber numberWithDouble:self.currentLocation.coordinate.longitude]];
        startingLie = [MRPWatchUtils getLieForCoordinate:[strokeLocation getCoordinateLocation2D] fromHole:self.selectedHole];
    }
    
    // create new stroke
    MRPStrokeModel *stroke = [[MRPStrokeModel alloc] init];
    [stroke.coordinates addObject:strokeLocation];
    stroke.clubId = club.clubId;
    stroke.penalty = [NSNumber numberWithBool:penalty];
    stroke.shotAt = [NSDate date];
    stroke.pKey = [MRPWatchUtils getUniqueStrokeId];
    stroke.startingLie = startingLie;
    stroke.startingLieSubtype = @"Normal";
    stroke.difficult = [NSNumber numberWithBool:NO];
    stroke.recovery = [NSNumber numberWithBool:NO];
    
    [self updateHole:self.selectedHole withStroke:stroke removeStroke:NO numberClubs:numberClubs putter:putter];
}

- (void)updateHole:(MRPHoleModel *)hole withStroke:(MRPStrokeModel *)stroke removeStroke:(BOOL)removeStroke numberClubs:(NSInteger)numberClubs putter:(BOOL)putter {
    for (int i = 0; i < numberClubs; i++) {
        if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeBasic]) {
            if (![MRPRoundData sharedInstance].round.inprogress) {
                [MRPWatchUtils updateStrokeForHole:hole withStroke:stroke removedStroke:removeStroke];
            }
        } else {
            [MRPWatchUtils updateScoreForHole:hole withStroke:stroke removedStroke:removeStroke];
        }
        
        [self updateStrokesOrder];
        
        for (int i = 0; i < [MRPRoundData sharedInstance].round.selectedTee.holesArray.count; i++) {
            MRPHoleModel *hole = [MRPRoundData sharedInstance].round.selectedTee.holesArray[i];
            if (hole.holeNumber == self.selectedHole.holeNumber) {
                hole = self.selectedHole;
                break;
            }
        }
    }
    
    [self calculateDistanceFromCurrentLocation];
    
    NSString *titleAddShotButton = [NSString stringWithFormat:@"ADD HOLE SCORE"];
    if([self.roundMode isEqualToString:kRoundModeAdvanced]){
        titleAddShotButton = [NSString stringWithFormat:@"ADD %@ SHOT",[MRPWatchUtils getOrdinalStringFromInteger:self.selectedHole.strokes.count+1]];
    }
    [self.addShotButton setText:[titleAddShotButton uppercaseString]];
    
    if (!putter) {
        // Update total score
        int score = 0;
        for (MRPHoleModel *hole in [MRPRoundData sharedInstance].round.selectedTee.holesArray) {
            if (hole.holeNumber == self.selectedHole.holeNumber) {
                if (hole.strokes.count > 0) {
                    score += hole.strokes.count;
                }
            }
        }
        
        [self.scoreNumberLabel setText:[NSString stringWithFormat:@"%ld", (long)score]];
        [MRPRoundData sharedInstance].round.totalScore = [NSString stringWithFormat:@"%d", score];
    } else {
        NSInteger index = 1;
        if ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count > 9) {
            if ([MRPRoundData sharedInstance].round.pausedHoleNumber == 17) {
                index = 2;
            } else {
                [[MRPWatchRoundManager sharedInstance] roundDidUpdate:[MRPRoundData sharedInstance].round];
                [MRPRoundData sharedInstance].round.pausedHoleNumber++;
            }
        } else {
            if ([MRPRoundData sharedInstance].round.pausedHoleNumber == 8) {
                index = 2;
            } else {
                [[MRPWatchRoundManager sharedInstance] roundDidUpdate:[MRPRoundData sharedInstance].round];
                [MRPRoundData sharedInstance].round.pausedHoleNumber++;
            }
        }
        
        self.selectedHole.completed = YES;
        
        [self stopUpdateLocation];
        if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
            [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:index];
        } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
            if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
            } else {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:index];
            }
        } else {
            [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
        }
    }
    
    [MRPWatchUtils saveDataInFile];
}

- (void)updateBasicHole:(MRPHoleModel *)hole withStroke:(MRPStrokeModel *)stroke removeStroke:(BOOL)removeStroke numberClubs:(NSInteger)numberClubs putter:(BOOL)putter {
    for (int i = 0; i < numberClubs; i++) {
        if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeBasic]) {
            if (![MRPRoundData sharedInstance].round.inprogress) {
                [MRPWatchUtils updateStrokeForHole:hole withStroke:stroke removedStroke:removeStroke];
            }
        } else {
            [MRPWatchUtils updateScoreForHole:hole withStroke:stroke removedStroke:removeStroke];
        }
        
//        [self updateStrokesOrder];
        
        for (int i = 0; i < [MRPRoundData sharedInstance].round.selectedTee.holesArray.count; i++) {
            MRPHoleModel *hole = [MRPRoundData sharedInstance].round.selectedTee.holesArray[i];
            if (hole.holeNumber == self.selectedHole.holeNumber) {
                hole = self.selectedHole;
                break;
            }
        }
    }
   
    
    NSString *titleAddShotButton = [NSString stringWithFormat:@"ADD HOLE SCORE"];
    if([self.roundMode isEqualToString:kRoundModeAdvanced]){
        titleAddShotButton = [NSString stringWithFormat:@"ADD %@ SHOT",[MRPWatchUtils getOrdinalStringFromInteger:self.selectedHole.strokes.count+1]];
    }
    [self.addShotButton setText:[titleAddShotButton uppercaseString]];
    
    if (!putter) {
        // Update total score
        int score = 0;
        for (MRPHoleModel *hole in [MRPRoundData sharedInstance].round.selectedTee.holesArray) {
            if (hole.holeNumber == self.selectedHole.holeNumber) {
                if (hole.strokes.count > 0) {
                    score += hole.strokes.count;
                }
            }
        }
        
        [self.scoreNumberLabel setText:[NSString stringWithFormat:@"%ld", (long)score]];
        [MRPRoundData sharedInstance].round.totalScore = [NSString stringWithFormat:@"%d", score];
//    } else {
        NSInteger index = 1;
        if ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count > 9) {
            if ([MRPRoundData sharedInstance].round.pausedHoleNumber == 18) {
                index = 2;
                self.selectedHole.completed = YES;
                
                [self stopUpdateLocation];
                
                if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:index];
                } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
                    if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
                    } else {
                        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:index];
                    }
                } else {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
                }
            }
        } else {
            if ([MRPRoundData sharedInstance].round.pausedHoleNumber == 9) {
                index = 2;
                self.selectedHole.completed = YES;
                
                [self stopUpdateLocation];
                
                if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:index];
                } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
                    if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
                    } else {
                        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:index];
                    }
                } else {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
                }
            }
        }
        BOOL inprogress = [MRPRoundData sharedInstance].round.inprogress;
        [[MRPWatchApiHandler sharedInstance] editRound:[MRPRoundData sharedInstance].round success:^(MRPRoundModel *roundResponse, NSDictionary *dict) {

            [MRPWatchUtils updateRound:[MRPRoundData sharedInstance].round roundSubmit:dict withRoundResponse:roundResponse];
            
            if (!inprogress) {//user finish play new round this mean all round data has been submited to server
                [MRPWatchUtils roundSubmited:[MRPRoundData sharedInstance].round];
                
                [MRPWatchUtils removeRoundFile];
            } else {
                [MRPWatchUtils saveDataInFile];
            }
            
        } failure:^(NSError *error) {
            if (!inprogress) {
                [MRPWatchRoundManager sharedInstance].playingRound = [MRPRoundData sharedInstance].round;
                [MRPWatchRoundManager sharedInstance].playingHole = [MRPWatchUtils findHoleInRound:[MRPRoundData sharedInstance].round withIndex:[MRPRoundData sharedInstance].round.pausedHoleNumber];
                for(MRPHoleModel *hole in [MRPRoundData sharedInstance].round.selectedTee.holesArray) {
                    if (hole.holeNumber == hole.holeNumber) {
                        hole.playing = YES;
                        break;
                    }
                    
                }
            }
        }];
        if([MRPRoundData sharedInstance].round.pausedHoleNumber < [MRPRoundData sharedInstance].round.selectedTee.holesArray.count){
            [self calculateDistanceFromCurrentLocation];
            [self setupDataForScreen];
        }
    }
    
    [MRPWatchUtils saveDataInFile];
}

- (void)updateStrokesOrder {
    // Make sure that strokes have they preceding one
    
    BOOL lieAssigned = NO;
    
    for (int i = 0; i < self.selectedHole.strokes.count; i++) {
        MRPStrokeModel *currentStroke = [self.selectedHole.strokes objectAtIndex:i];
        currentStroke.shotNumber = [NSNumber numberWithInt:(i + 1)];
        if (i == 0) {
            if (self.selectedHole.strokes.count == 1) {
                double distance = [MRPWatchUtils distanceFromPointA:[[currentStroke.coordinates lastObject] getCoordinateLocation2D]
                                                           toPointB:[self.selectedHole.pinLocation getCoordinateLocation2D]];
                [MRPWatchUtils updateStroke:currentStroke shotDistance:[NSNumber numberWithDouble:distance]];
            } else {
                
                if ((i + 1) < self.selectedHole.strokes.count) {
                    MRPStrokeModel *nextStroke = [self.selectedHole.strokes objectAtIndex:(i + 1)];
                    double distance = [MRPWatchUtils distanceFromPointA:[[currentStroke.coordinates lastObject] getCoordinateLocation2D]
                                                               toPointB:[[nextStroke.coordinates lastObject] getCoordinateLocation2D]];
                    [MRPWatchUtils updateStroke:currentStroke shotDistance:[NSNumber numberWithDouble:distance]];
                }
            }
            
            [MRPWatchUtils updateStroke:currentStroke precedingStroke:nil];
            
            // set first stroke Lie to GREEN
            if (![currentStroke.penalty boolValue] && !lieAssigned) {
                NSString *lieName = self.teeFeature.property.label;
                MRPRoundLieModel *lie = [[MRPRoundLieModel alloc] initWithName:lieName andSelected:YES andSuboption:NO];
                MRPRoundLieModel *lieSubtype = [[MRPRoundLieModel alloc] initWithName:currentStroke.startingLieSubtype andSelected:YES andSuboption:YES];
                [MRPWatchUtils updateStroke:currentStroke withStartingLie:lie andStartingLieSubtype:lieSubtype];
                
                lieAssigned = YES;
            }
            
        } else {
            
            if ([currentStroke.penalty boolValue]) {
                [MRPWatchUtils updateStroke:currentStroke precedingStroke:nil];
            } else {
                
                if ((i + 1) < self.selectedHole.strokes.count) {
                    for (int x = (i + 1); x < self.selectedHole.strokes.count; x++) {
                        
                        MRPStrokeModel *nextStroke = [self.selectedHole.strokes objectAtIndex:x];
                        
                        if (![nextStroke.penalty boolValue]) {
                            double distance = [MRPWatchUtils distanceFromPointA:[[currentStroke.coordinates lastObject] getCoordinateLocation2D]
                                                                       toPointB:[[nextStroke.coordinates lastObject] getCoordinateLocation2D]];
                            
                            [MRPWatchUtils updateStroke:currentStroke shotDistance:[NSNumber numberWithDouble:distance]];
                            
                            break;
                        }
                    }
                } else if ((i + 1) == self.selectedHole.strokes.count) {
                    // last shot so distance to PIN
                    double distance = [MRPWatchUtils distanceFromPointA:[[currentStroke.coordinates lastObject] getCoordinateLocation2D]
                                                               toPointB:[self.selectedHole.pinLocation getCoordinateLocation2D]];
                    [MRPWatchUtils updateStroke:currentStroke shotDistance:[NSNumber numberWithDouble:distance]];
                }
                
                MRPStrokeModel *previousStroke = [self.selectedHole.strokes objectAtIndex:(i - 1)];
                
                if ([previousStroke.penalty boolValue]) {
                    
                    [MRPWatchUtils updateStroke:currentStroke precedingStroke:nil];
                    
                } else {
                    [MRPWatchUtils updateStroke:currentStroke precedingStroke:previousStroke];
                    
                    if (!lieAssigned) {
                        NSString *lieName = self.teeFeature.property.label;
                        MRPRoundLieModel *lie = [[MRPRoundLieModel alloc] initWithName:lieName andSelected:YES andSuboption:NO];
                        MRPRoundLieModel *lieSubtype = [[MRPRoundLieModel alloc] initWithName:currentStroke.startingLieSubtype andSelected:YES andSuboption:YES];
                        [MRPWatchUtils updateStroke:currentStroke withStartingLie:lie andStartingLieSubtype:lieSubtype];
                        
                        lieAssigned = YES;
                    }
                }
            }
        }
    }
}

- (CLLocation *)getCorrectLocation {
    CLLocation *bestLocation;
    MRPRoundModel *round = [MRPRoundData sharedInstance].round;
    CLLocation *greenMiddleLocation = [[CLLocation alloc] initWithLatitude:[round.selectedTee.holesArray[round.pausedHoleNumber].greenMiddle.latitude floatValue] longitude:[round.selectedTee.holesArray[round.pausedHoleNumber].greenMiddle.longitude floatValue]];
    CLLocationDistance distanceMiddle = [self.currentLocation distanceFromLocation:greenMiddleLocation];
    
    float minDistance = MAXFLOAT;
    for (CLLocation *location in self.locations) {
        CLLocationDistance distance = [location distanceFromLocation:greenMiddleLocation];
        if (fabs(distance - distanceMiddle) < minDistance) {
            minDistance = fabs(distance - distanceMiddle);
            bestLocation = location;
        }
        
    }
    
    return bestLocation;
}

- (void)updateAccuracyDistance {
    [self calculateDistanceFromCurrentLocation];
    [self.locations removeAllObjects];
}

#pragma mark - Location manager delegate
- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations {
    if ([locations count] == 0) {
        // error
        return;
    }
    
    CLLocation *mostRecentLocation = locations.lastObject;
    
    // TODO:
    // Vantri
    // Fake GPS for Thao Testing
//    CLLocation *temp = locations.lastObject;
//    CLLocation *mostRecentLocation = [[CLLocation alloc] initWithLatitude:temp.coordinate.latitude+0.11881149846 longitude:temp.coordinate.longitude+0.01257618651];
    
    // TODO:
    // Rancho
//    CLLocation *temp = locations.lastObject;
//    CLLocation *mostRecentLocation = [[CLLocation alloc] initWithLatitude:temp.coordinate.latitude+12.119035 longitude:temp.coordinate.longitude-223.073017];
    
    // TODO:
    // Four Seasons Golf Club
//    CLLocation *temp = locations.lastObject;
//    CLLocation *mostRecentLocation = [[CLLocation alloc] initWithLatitude:temp.coordinate.latitude+30.1825053646 longitude:temp.coordinate.longitude-102.443014817];


    NSTimeInterval locationAge = -[mostRecentLocation.timestamp timeIntervalSinceNow];
    if (locationAge > 5.0) return;
    
    if (mostRecentLocation.horizontalAccuracy < 0) return;
    
    self.currentLocation = mostRecentLocation;
    
    [self calculateDistanceFromCurrentLocation];

}

//-(void)locationManager:(CLLocationManager *)manager didFinishDeferredUpdatesWithError:(NSError *)error
//{
//    // 次のDeferred更新の準備
//    self.deferredLocationUpdates = NO;
//
//    // 現在位置を取得
//    self.currentLocation = manager.location;
//}

- (void)locationManager:(CLLocationManager *)manager
       didFailWithError:(nonnull NSError *)error
{
    // error
}

#pragma mark - MRPListClubInterface Delegate
- (void)selectClubViewController:(MRPListClubInterfaceController *)viewController didSelectClub:(MRPClubsModel *)club withPenalty:(BOOL)penalty {
    [self addStrokeWithClub:club penalty:penalty putter:NO numberClubs:1];
}

- (void)selectClubViewController:(MRPListClubInterfaceController *)viewController endHole:(BOOL)endHole withIncreaseHoleCount:(BOOL)increase {
    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationStopUpdateLocation object:nil];
    NSInteger index = 1;
    if ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count > 9) {
        if ([MRPRoundData sharedInstance].round.pausedHoleNumber == 17) {
            index = 2;
        } else {
            if (increase) {
                [MRPRoundData sharedInstance].round.pausedHoleNumber++;
                [[MRPWatchRoundManager sharedInstance] roundDidUpdate:[MRPRoundData sharedInstance].round];
            }
        }
    } else {
        if ([MRPRoundData sharedInstance].round.pausedHoleNumber == 8) {
            index = 2;
        } else {
            if (increase) {
                [MRPRoundData sharedInstance].round.pausedHoleNumber++;
                [[MRPWatchRoundManager sharedInstance] roundDidUpdate:[MRPRoundData sharedInstance].round];
            }
        }
    }
    
    self.selectedHole.completed = YES;
    
    [MRPWatchUtils saveDataInFile];
    
    [self stopUpdateLocation];
    
    if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:index];
    } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
        if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
            [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
        } else {
            [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:index];
        }
    } else {
        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
    }
}

- (void)selectClubViewController:(MRPListClubInterfaceController *)viewController didSelectPutter:(MRPClubsModel *)putter numberPutter:(NSInteger)numberClubs {
    [self addStrokeWithClub:putter penalty:NO putter:YES numberClubs:numberClubs];
}

#pragma mark - MRPHoleInterface Delegate
- (void)didSelectHole:(NSInteger)hole {
    if (hole != [MRPRoundData sharedInstance].round.pausedHoleNumber) {
        self.selectedHole.completed = YES;
        
        [MRPWatchUtils saveDataInFile];
    }
    
    [MRPRoundData sharedInstance].round.pausedHoleNumber = hole;
    [self setupDataForScreen];
    [self calculateDistanceFromCurrentLocation];
}

#pragma mark - Select Score Delegate
- (void)didSelectScore:(NSInteger)score {
    self.selectedHole.score = score;
    NSLog(@"MRPRoundData: %@, hole %@", [NSString stringWithFormat:@"%ld", (long)score], [NSString stringWithFormat:@"%ld", (long)self.selectedHole.holeNumber]);
    [MRPRoundData sharedInstance].round.pausedHoleNumber = self.selectedHole.holeNumber;
    self.selectedHole.completed = YES;
    [self updateBasicHole:self.selectedHole withStroke:nil removeStroke:NO numberClubs:0 putter:NO];
}

#pragma mark - MRPAddRoundClassicDelegate

- (void)updateClassicHole:(MRPHoleModel *)hole {
   
    NSString *titleAddShotButton = [NSString stringWithFormat:@"ADD HOLE SCORE"];
    [self.addShotButton setText:[titleAddShotButton uppercaseString]];
    int score = 0;
    for (MRPHoleModel *hole in [MRPRoundData sharedInstance].round.selectedTee.holesArray) {
        if (hole.holeNumber == self.selectedHole.holeNumber) {
            if (hole.strokes.count > 0) {
                score += hole.strokes.count;
            }
        }
    }
    
    [self.scoreNumberLabel setText: [self calculatorScoreToPar]];
    [MRPRoundData sharedInstance].round.totalScore = [NSString stringWithFormat:@"%d", score];

    NSInteger index = 1;
    if ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count > 9) {
        if ([MRPRoundData sharedInstance].round.pausedHoleNumber == 18) {
            index = 2;
            self.selectedHole.completed = YES;
            
            [self stopUpdateLocation];
            
            if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:index];
            } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
                if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
                } else {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:index];
                }
            } else {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
            }
        }
    } else {
        if ([MRPRoundData sharedInstance].round.pausedHoleNumber == 9) {
            index = 2;
            self.selectedHole.completed = YES;
            
            [self stopUpdateLocation];
            
            if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:index];
            } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
                if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
                } else {
                    [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:index];
                }
            } else {
                [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
            }
        }
    }
    BOOL inprogress = [MRPRoundData sharedInstance].round.inprogress;
    [[MRPWatchApiHandler sharedInstance] editRound:[MRPRoundData sharedInstance].round success:^(MRPRoundModel *roundResponse, NSDictionary *dict) {

        [MRPWatchUtils updateRound:[MRPRoundData sharedInstance].round roundSubmit:dict withRoundResponse:roundResponse];
        
        if (!inprogress) {//user finish play new round this mean all round data has been submited to server
            [MRPWatchUtils roundSubmited:[MRPRoundData sharedInstance].round];
            
            [MRPWatchUtils removeRoundFile];
        } else {
            [MRPWatchUtils saveDataInFile];
        }
        
    } failure:^(NSError *error) {
        if (!inprogress) {
            [MRPWatchRoundManager sharedInstance].playingRound = [MRPRoundData sharedInstance].round;
            [MRPWatchRoundManager sharedInstance].playingHole = [MRPWatchUtils findHoleInRound:[MRPRoundData sharedInstance].round withIndex:[MRPRoundData sharedInstance].round.pausedHoleNumber];
            for(MRPHoleModel *hole in [MRPRoundData sharedInstance].round.selectedTee.holesArray) {
                if (hole.holeNumber == hole.holeNumber) {
                    hole.playing = YES;
                    break;
                }
                
            }
        }
    }];
    
    if([MRPRoundData sharedInstance].round.pausedHoleNumber < [MRPRoundData sharedInstance].round.selectedTee.holesArray.count){
        [self calculateDistanceFromCurrentLocation];
        [self setupDataForScreen];
    }
    
    [MRPWatchUtils saveDataInFile];
}

- (void)classicAddScoreOnly:(NSInteger)score {
    NSLog(@"ahihi: %@", self.selectedHole);
    self.selectedHole.score = score;
    [MRPRoundData sharedInstance].round.classicModeScoresOnly = YES;
    [MRPRoundData sharedInstance].round.pausedHoleNumber = self.selectedHole.holeNumber;
    self.selectedHole.completed = YES;
    [self updateClassicHole:self.selectedHole];
    if (![self checkFirst]) {
        NSInteger index = 1;
        if (
            ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count < 10 && [MRPRoundData sharedInstance].round.pausedHoleNumber == 9) ||
            ([MRPRoundData sharedInstance].round.selectedTee.holesArray.count < 19 && [MRPRoundData sharedInstance].round.pausedHoleNumber== 18)) {
                index = 2;
        }
        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:index];
    }
}

- (void)classicAddFirAndGir:(NSInteger)score andPutts:(NSInteger)putts andFw:(NSString *)fw_stats andGr:(NSString *)gr_stats andBunker:(BOOL)isBunker
{
    NSLog(@"ahihi: %@", self.selectedHole);
    self.selectedHole.score = score;
    self.selectedHole.puttsNumber = putts;
    self.selectedHole.fwStats = fw_stats;
    self.selectedHole.grStats = gr_stats;
    self.selectedHole.bunkerHit = isBunker;
    [MRPRoundData sharedInstance].round.classicModeScoresOnly = NO;
    [MRPRoundData sharedInstance].round.pausedHoleNumber = self.selectedHole.holeNumber;
    self.selectedHole.completed = YES;
    [self updateClassicHole:self.selectedHole];
}

- (BOOL)checkTrackGroup {
    for (MRPHoleModel *hole in [MRPRoundData sharedInstance].round.selectedTee.holesArray) {
        if (hole.score > 0) {
            if (hole.grStats != nil) {
                return 0;
            }
            return 1;
        }
    }
    return 0;
}

- (BOOL)checkFirst {
    for (MRPHoleModel *hole in [MRPRoundData sharedInstance].round.selectedTee.holesArray) {
        if (hole.score > 0) {
            return 0;
        }
    }
    return 1;
}

- (NSString *)calculatorScoreToPar {
    float totalScore = 0;
    float totalPar = 0;
    for(MRPHoleModel *hole in [MRPRoundData sharedInstance].round.selectedTee.holesArray) {
        if (hole.score > 0) {
            totalScore += hole.score;
            totalPar += hole.par;
        }
    }
    if(totalScore == 0) {
        return @" -";
    } else if(totalScore == totalPar) {
        return @" E";
    } else if(totalScore - totalPar > 0) {
        return [NSString stringWithFormat:@"+%ld", (long)(totalScore - totalPar)];
    } else {
        return [NSString stringWithFormat:@"%ld", (long)(totalScore - totalPar)];
    }
}
@end
