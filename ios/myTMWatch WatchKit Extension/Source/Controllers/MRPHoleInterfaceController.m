//
//  MRPHoleInterfaceController.m
//  MyRoundPro
//
//  
//  Copyright © 2019 myroundpro. All rights reserved.
//

#import "MRPHoleInterfaceController.h"
#import "MRPHoleCell.h"

@interface MRPHoleInterfaceController ()

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceTable  *holeTableView;

@property (weak, nonatomic) id <MRPListHoleDelegate>        delegate;
@property (assign, nonatomic) NSInteger                     numberHole;

@end

@implementation MRPHoleInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];

    self.delegate = context[@"delegate"];
    self.numberHole = [context[@"numberHole"] integerValue];
    
    //    self.round = [MRPRoundData sharedInstance].round;
    
//    [self initDataFromRound];
    
    [self configTableTee];
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}

//- (void)initDataFromRound {
//    //    NSInteger count = 0;
//
//    NSArray *tee = [self.round.course.teeArray valueForKey:@"self"];
//
//    NSSortDescriptor *sort = [NSSortDescriptor sortDescriptorWithKey:@"totalYards" ascending:NO];
//    self.sortedTee = [tee sortedArrayUsingDescriptors:@[sort]];
//}

- (void)configTableTee {
    
    [self.holeTableView setNumberOfRows:self.numberHole withRowType:@"holeTableRow"];
    
    for (int i = 0; i < self.numberHole; i++) {
        MRPHoleCell *cell = [self.holeTableView rowControllerAtIndex:i];
        [cell.holeNameLabel setText:[NSString stringWithFormat:@"%d", i+1]];
    }
}

- (void)table:(WKInterfaceTable *)table didSelectRowAtIndex:(NSInteger)rowIndex {
//    self.round.selectedTee = [self.sortedTee objectAtIndex:rowIndex];
    [self.delegate didSelectHole:rowIndex];
    [self dismissController];
}

@end



