//
//  MRPListTypeInterfaceController.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 8/9/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPListTypeInterfaceController.h"

@interface MRPListTypeInterfaceController ()

@property (weak, nonatomic) id <MRPListTypeDelegate>    delegate;

@end

@implementation MRPListTypeInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    self.delegate = context;
    
    // Configure interface objects here.
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}

- (IBAction)selectPractice:(id)sender {
    [self.delegate didSelectType:@"Practice"];
    [self dismissController];
}

- (IBAction)selectTournament:(id)sender {
    [self.delegate didSelectType:@"Tournament"];
    [self dismissController];
}
@end



