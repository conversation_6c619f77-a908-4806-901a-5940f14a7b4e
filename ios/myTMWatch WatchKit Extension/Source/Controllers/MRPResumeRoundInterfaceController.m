//
//  MRPResumeRoundInterfaceController.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 9/17/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPResumeRoundInterfaceController.h"

@interface MRPResumeRoundInterfaceController ()

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup  *loadingGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceImage  *indicatorImage;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup  *mainGroup;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel  *resumeRoundLabel;

@end

@implementation MRPResumeRoundInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    [self showLoadingIndicator];
    NSDateFormatter* df = [[NSDateFormatter alloc]init];
    [df setDateFormat:@"yyyy-MM-dd"];
    NSString *dateString = [df stringFromDate:[MRPRoundData sharedInstance].round.playedOn];
    NSString *courseName = [NSString stringWithFormat:@"Resume Round ? \n %@ \n %@", dateString, [MRPRoundData sharedInstance].round.courseName];
    [self.resumeRoundLabel setText:courseName];
    [self stopLoadingIndicator];
    // Configure interface objects here.
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}

- (IBAction)resumeRound {
    if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeAdvanced]) {
        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryInterfaceController"] atPageIndex:1];
    } else if ([[MRPRoundData sharedInstance].round.roundMode isEqualToString:kRoundModeClassic]) {
        if ([MRPRoundData sharedInstance].round.classicModeScoresOnly) {
            [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:1];
        } else {
            [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardClassicInterfaceController", @"addShotInterfaceController", @"roundStatsClassicInterfaceController"] atPageIndex:1];
        }
    } else {
        [MRPWatchUtils reloadRootControllerWithNames:@[@"scorecardInterfaceController", @"addShotInterfaceController", @"summaryBasicInterfaceController"] atPageIndex:1];
    }
}

- (void)showLoadingIndicator {
    [self.loadingGroup setHidden:NO];
    [self.mainGroup setHidden:YES];
    [self.indicatorImage startActivityIndicator];
}

- (void)stopLoadingIndicator {
    [self.loadingGroup setHidden:YES];
    [self.mainGroup setHidden:NO];
    [self.indicatorImage stopActivityIndicator];
}

- (IBAction)cancelRound {
    [self showLoadingIndicator];
    [[MRPWatchApiHandler sharedInstance] deleteRound:[MRPRoundData sharedInstance].round andSuccess:^(NSDictionary *response) {
//        [self stopLoadingIndicator];
        
        [MRPRoundData sharedInstance].round = nil;
        [MRPWatchUtils removeRoundFile];
        
        [MRPWatchUtils reloadRootControllerWithName:@"nearByCourse"];

    } andFailure:^(NSError *error) {
      [self showAlert:error.localizedDescription];
      [self performSelector:@selector(stopLoadingIndicator) withObject:nil afterDelay:0.3];
    }];
}

- (void)showAlert:(NSString *)error {
    [self presentControllerWithName:@"errorInterfaceController"
                            context:@{@"title"          : @"",
                                      @"description"    : error}];
}

@end
