//
//  MRPAboutInterfaceController.m
//  MyRoundPro Watch Extension
//
//  Created by <PERSON><PERSON> on 7/16/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPAboutInterfaceController.h"

@interface MRPAboutInterfaceController ()

@end

@implementation MRPAboutInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    // Configure interface objects here.
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}

@end



