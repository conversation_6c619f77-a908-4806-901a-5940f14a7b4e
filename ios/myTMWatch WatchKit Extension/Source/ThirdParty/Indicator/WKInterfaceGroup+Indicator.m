//
//  WKInterfaceGroup+Indicator.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/13/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "WKInterfaceGroup+Indicator.h"

@implementation WKInterfaceGroup (Indicator)

- (void)startActivityIndicator {
    UIImage *image = [UIImage imageNamed:@"Activity"];
    [self setBackgroundImage:image];
    NSRange range = NSMakeRange(0, 30);
    [self startAnimatingWithImagesInRange:range duration:1 repeatCount:0];
}

- (void)stopActivityIndicator {
    [self stopAnimating];
    [self setBackgroundImage:nil];
}

@end

@implementation WKInterfaceImage (Indicator)

- (void)startActivityIndicator {
//    UIImage *image = [UIImage imageNamed:@"Activity"];
    [self setImageNamed:@"Activity"];
    NSRange range = NSMakeRange(0, 30);
    [self startAnimatingWithImagesInRange:range duration:1 repeatCount:0];
}

- (void)stopActivityIndicator {
    [self stopAnimating];
    [self setImage:nil];
}

@end
