//
//  MRPWatchLocationManager.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 9/17/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPWatchLocationManager.h"

@implementation MRPWatchLocationManager

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static MRPWatchLocationManager *manager;
    dispatch_once(&onceToken, ^{
        manager = [[MRPWatchLocationManager alloc] init];
    });
    return manager;
}

@end
