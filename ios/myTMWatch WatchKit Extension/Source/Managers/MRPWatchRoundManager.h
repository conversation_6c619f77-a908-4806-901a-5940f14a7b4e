//
//  MRPWatchRoundManager.h
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 9/5/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "MRPWatchBaseBackgrounder.h"
#import "MRPRoundModel.h"

@interface MRPWatchRoundManager : MRPWatchBaseBackgrounder

@property (nonatomic, strong) MRPRoundModel *playingRound;
@property (nonatomic, strong) MRPHoleModel *playingHole;

+ (instancetype)sharedInstance;
- (void)roundDidUpdate:(MRPRoundModel *)round;
@end
