//
//  MRPWatchBaseBackgrounder.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 9/5/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPWatchBaseBackgrounder.h"

@interface MRPWatchBaseBackgrounder() {
    dispatch_queue_t _run_queue;
    dispatch_semaphore_t _semaphore;
    BOOL _notifiedDurringSync;
    BOOL _isRunning;
//    AFNetworkReachabilityManager *_reachable;
}
@end
@implementation MRPWatchBaseBackgrounder
- (id)init
{
    self = [super init];
    if (self) {
        _run_queue = dispatch_queue_create([NSStringFromClass([self class]) UTF8String], 0);
        _notifiedDurringSync = NO;
        _semaphore = dispatch_semaphore_create(0);
//        _reachable = [AFNetworkReachabilityManager manager];
//        [_reachable startMonitoring];
    }
    return self;
}

- (void)start {
    if (_isRunning) return;
    _isRunning = YES;
    __weak MRPWatchBaseBackgrounder *wSelf = self;

    dispatch_async(_run_queue, ^{
        while (self.isRunning) {
//            BOOL _hasInternet = [self hasInternet];
            
            if (YES) {
                NSArray *jobs = [self fetchJobs];
                NSInteger numberJob = jobs.count;
                NSInteger numberSuccess = 0;
                NSInteger numberEndJob = 0;
                
                for (id<MRPWatchBackgroundJob> job in jobs) {
                    if ([job execute]) {
                        numberSuccess++;
                    }
                    numberEndJob++;
                    [self syncProgress:(float)numberEndJob / (float)numberJob];
                    if (!self.isRunning) break;
                }
                [self syncJobsFinishedWithNumberSuccess:numberSuccess numberFail:numberJob - numberSuccess];
            } else {
//                [self syncJobsFinishedWithNumberSuccess:0 numberFail:0];
            }
            
            if (self.stopWhenSyncComplete) {
                [self stop];
                break;
            }
            
            if (!self.notifiedDurringSync) {
                dispatch_semaphore_wait(self.semaphore, dispatch_time(DISPATCH_TIME_NOW, [self delayInSeconds] * NSEC_PER_SEC));
            }
            _notifiedDurringSync = NO;
        }
    });
}

-(dispatch_semaphore_t)semaphore {
    return _semaphore;
}

-(BOOL)notifiedDurringSync {
    return _notifiedDurringSync;
}

- (BOOL)isRunning {
    return _isRunning;
}

- (NSTimeInterval)delayInSeconds {
    return 10*60;
}
//- (BOOL)hasInternet {
//
//    if (_reachable.reachable) {
//        NSLog(@"Has Intenet");
//        return YES;
//    } else {
//        NSLog(@"Don't has Intenet");
//        return NO;
//    }
//}

- (void)stop {
    if (!_isRunning) return;
    _isRunning = NO;
    _notifiedDurringSync = NO;
    dispatch_semaphore_signal(_semaphore);
}

- (void)notify {
    _notifiedDurringSync = YES;
    dispatch_semaphore_signal(_semaphore);
}
- (void)syncProgress:(float)progress {
    
}
//Sub class overrided functions
- (void)syncJobsFinishedWithNumberSuccess:(NSInteger)numberSuccess numberFail:(NSInteger)numberFail {
    
}

- (NSArray *)fetchJobs {
    return nil;
}

@end
