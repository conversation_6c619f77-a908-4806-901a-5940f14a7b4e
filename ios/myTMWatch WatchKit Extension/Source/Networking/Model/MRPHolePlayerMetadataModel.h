//
//  MRPHolePlayerMetadataModel.h
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/18/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import <Mantle/Mantle.h>
//#import <Realm/Realm.h>

@interface MRPHolePlayerMetadataModel : MTLModel <MTLJSONSerializing>

@property (strong, nonatomic) NSNumber/* <RLMInt> */ *lowestHeartbeat;
@property (strong, nonatomic) NSNumber/* <RLMInt> */ *averageHeartbeat;
@property (strong, nonatomic) NSNumber/* <RLMInt> */ *peakHeartbeat;
@property (strong, nonatomic) NSNumber/* <RLMInt> */ *caloriesBurned;
@property (strong, nonatomic) NSNumber/* <RLMInt> */ *shotsRemoved;

@end
