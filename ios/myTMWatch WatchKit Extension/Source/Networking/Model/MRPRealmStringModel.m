//
//  MRPRealmStringModel.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/18/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPRealmStringModel.h"

@implementation MRPRealmStringModel

-(instancetype)initWithString:(NSString *)string
{
    if(self = [super init]) {
        self.value = string;
    }
    return self;
}

+ (NSDictionary *)JSONKeyPathsByPropertyKey {
    return @{
             @"value" : @"value"
             };
}

+ (NSValueTransformer *)valueJSONTransformer {
    return [MTLJSONAdapter arrayTransformerWithModelClass:[NSString class]];

//    return [MTLJSONAdapter dictionaryTransformerWithModelClass:MRPStrokeModel.class];
    
//    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
//
//        return value;
//    }];
}

@end
