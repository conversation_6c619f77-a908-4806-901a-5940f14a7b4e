//
//  MRPGeometryModel.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/18/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPGeometryModel.h"

@implementation MRPGeometryModel

+ (NSDictionary *)JSONKeyPathsByPropertyKey {
    return @{
             @"type" : @"type",
             @"coordinates" : @"coordinates"
             };
}

+ (NSValueTransformer *)coordinatesJSONTransformer {
    
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        NSMutableArray *array = [NSMutableArray array];
        if ([value isKindOfClass:[NSArray class]]) {
            if ([[value firstObject] isKindOfClass:[NSArray class]]) {
                for(NSArray *teeboxArray in value) {
                    for (NSArray *teeboxs in teeboxArray) {
                        MRPTeeboxBoundaryModel *teeBox = [[MRPTeeboxBoundaryModel alloc] initWithLatitude:[teeboxs lastObject] andLongitude:[teeboxs firstObject]];
                        [array addObject:teeBox];
                    }
                }
            }  else if ([[value firstObject] isKindOfClass:[NSDictionary class]]) {
                for(NSDictionary *teeboxDict in value) {
                    MRPTeeboxBoundaryModel *teeBox = [[MRPTeeboxBoundaryModel alloc] initWithLatitude:teeboxDict[@"latitude"] andLongitude:teeboxDict[@"longitude"]];
                    [array addObject:teeBox];
                }
            } else {
                for(MRPTeeboxBoundaryModel *teebox in value) {
                    [array addObject:teebox];
                }
            }
            
            return array;
        }
        
        return value;
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        NSMutableArray *array = [NSMutableArray array];
        if ([value isKindOfClass:[NSArray class]]) {
            if ([[value firstObject] isKindOfClass:[NSArray class]]) {
                for(NSArray *teeboxArray in value) {
                    NSDictionary *dict = @{
                                           @"latitude" : [teeboxArray firstObject],
                                           @"longitude" : [teeboxArray lastObject]
                                           };
                    [array addObject:dict];
                    
                }
            } else if ([[value firstObject] isKindOfClass:[NSDictionary class]]) {
                for(NSDictionary *teeboxDict in value) {
                    NSDictionary *dict = @{
                                           @"latitude" : teeboxDict[@"latitude"],
                                           @"longitude" : teeboxDict[@"longitude"]
                                           };
                    [array addObject:dict];
                }
            } else {
                for(MRPTeeboxBoundaryModel *teebox in value) {
                    NSDictionary *dict = @{
                                           @"latitude" : teebox.latitude,
                                           @"longitude" : teebox.longitude
                                           };
                    [array addObject:dict];
                }
            }
            
            return array;
        }
        
        return value;
    }];
}


@end
