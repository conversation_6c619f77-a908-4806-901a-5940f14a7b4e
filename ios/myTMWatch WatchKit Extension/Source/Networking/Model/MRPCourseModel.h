//
//  MRPCourseModel.h
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/17/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import <Mantle/Mantle.h>
//#import <Realm/Realm.h>
#import "MRPRealmStringModel.h"
#import "MRPTeeModel.h"

@interface MRPCourseModel : MTLModel <MTLJSONSerializing>

@property (strong, nonatomic) NSNumber /* <RLMInt> */                          *courseId;
@property (strong, nonatomic) NSString                                  *igolfCourseId;
@property (strong, nonatomic) NSNumber/* <RLMInt> */                          *countryId;
@property (strong, nonatomic) NSString                                  *name;
@property (strong, nonatomic) NSString                                  *courseType;
@property (strong, nonatomic) NSNumber/* <RLMInt> */                          *numberOfHoles;
@property (strong, nonatomic) NSString                                  *address;
@property (strong, nonatomic) NSNumber/* <RLMFloat> */                        *latitude;
@property (strong, nonatomic) NSNumber/* <RLMFloat> */                        *longitude;
@property (strong, nonatomic) NSString                                  *timezone;
@property (strong, nonatomic) NSString                                  *phoneNumber;
//@property (strong, nonatomic) NSArray<MRPRealmStringModel*>             *teeNames;
@property (strong, nonatomic) NSArray<NSString *>                       *teeNames;
@property (strong, nonatomic) NSString                                  *defaultTee;
//@property (strong, nonatomic) NSArray<MRPTee*><MRPTee>                 *teeArray;
@property (strong, nonatomic) NSArray<MRPTeeModel*>                     *teeArray;
@property (strong, nonatomic) NSDate                                    *updateDate;
@property (strong, nonatomic) NSDate                                    *modifiedDate;
@property (strong, nonatomic) NSNumber/* <RLMInt> */                          *layoutHoles;

// iGolf
// courseGolfType = 1 => old API
// courseGolfType = 2 => iGolf
@property (strong, nonatomic) NSNumber/* <RLMInt> */                          *courseGolfType;

@end
