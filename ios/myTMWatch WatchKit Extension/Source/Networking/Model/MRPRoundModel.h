//
//  MRPRoundModel.h
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/19/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import <Mantle/Mantle.h>
//#import <Realm/Realm.h>
#import "MRPTeeModel.h"
#import "MRPCourseModel.h"
#import "MRPRoundPlayerMetadataModel.h"
#import "MRPCourseConditionModel.h"

@interface MRPRoundModel : MTLModel <MTLJSONSerializing, NSCopying>

@property (strong, nonatomic) NSNumber/* <RLMInt> */                 *roundId;
//@property (strong, nonatomic) NSString                          *generated_by;
@property (strong, nonatomic) NSNumber/* <RLMInt>   */               *userId;
@property (strong, nonatomic) NSNumber/* <RLMBool>    */             *completed;
@property (strong, nonatomic) NSNumber/* <RLMBool>      */           *isEditingActive;
@property (strong, nonatomic) NSString                          *courseId;
@property (strong, nonatomic) NSString                          *teeName;
@property (strong, nonatomic) NSString                          *teeNameDisplay;
@property (strong, nonatomic) NSNumber/* <RLMBool>        */         *deletedAt;
@property (strong, nonatomic) NSDate                            *playedOn;
@property (strong, nonatomic) NSNumber/* <RLMInt>       */           *coursePar;
@property (strong, nonatomic) NSString                          *totalScore;
@property (strong, nonatomic) NSDate                            *createdAt;
@property (strong, nonatomic) NSString                          *roundType;
@property (strong, nonatomic) NSString                          *courseName;
@property (strong, nonatomic) NSDate                            *lockedAt;
@property (strong, nonatomic) NSNumber/* <RLMInt>  */                *modeIndex;
@property (strong, nonatomic) NSString                          *roundMode;
@property (strong, nonatomic) NSDate                            *deleted;
@property (strong, nonatomic) MRPRoundPlayerMetadataModel       *playerMetadata;
@property (strong, nonatomic) MRPCourseConditionModel           *courseConditions;
@property (strong, nonatomic) MRPCourseModel                    *course;
@property (copy, nonatomic) MRPTeeModel                       *selectedTee;
@property (strong, nonatomic) NSArray<MRPHoleModel*>            *holesArray;
@property (strong, nonatomic) NSNumber/* <RLMInt> */                 *gameType;
@property (strong, nonatomic) NSDate                            *roundDate;
@property (strong, nonatomic) NSDate                            *pausedDate;
@property (strong, nonatomic) NSNumber/* <RLMInt> */                 *currentSeconds;
@property (strong, nonatomic) NSDate                            *startDate;
@property (strong, nonatomic) NSNumber/* <RLMBool>  */               *sendError;
@property (strong, nonatomic) NSNumber/* <RLMBool>    */             *isRoundSubmited;
//@property (strong, nonatomic) NSNumber<RLMBool>                 *isLatestRound;
//@property (strong, nonatomic) NSNumber<RLMBool>                 *isRoundShowInRecentRounds;
@property (strong, nonatomic) NSNumber/* <RLMBool>     */            *statsCompleted;
@property (strong, nonatomic) NSDate                            *submitedDate;
@property (strong, nonatomic) NSString                          *userTimezone;
@property (nonatomic) NSInteger                                 pausedHoleNumber;
@property (assign) BOOL                                         inprogress;
@property (assign) BOOL                                         classicModeScoresOnly;
//- (MRPRound *)makeCopy;

// iGolf
@property (strong, nonatomic) NSString                          *mapId;
@property (strong, nonatomic) NSString                          *igolfCourseId;

- (BOOL)needSubmit;

@end
