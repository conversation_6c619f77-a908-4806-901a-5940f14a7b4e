//
//  MRPHoleModel.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/18/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPHoleModel.h"

@implementation MRPHoleModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.strokes = [[NSMutableArray alloc] init];
        self.pinLocation = [[MRPTeeboxBoundaryModel alloc] init];
    }
    return self;
}

+ (NSDictionary *)JSONKeyPathsByPropertyKey {
    return @{
             @"holeNumber" : @"hole_number",
             @"par" : @"par",
             @"yards" : @"yards",
             @"handicap" : @"handicap",
             @"teeboxBoundaryArray" : @"teebox_boundary",
             @"greenFront" : @"green_front",
             @"greenMiddle" : @"green_middle",
             @"greenBack" : @"green_back",
             @"features" : @"features",
             @"score" : @"total_score",
             @"strokes" : @"strokes",
             @"pinLocation" : @"pin_location",
             @"holeId" : @"id",
             @"shotsRemoved" : @"shots_removed",
             @"lastModifiedDate" : @"last_modified_date",
             @"lastSentDate" : @"last_sent_date",
             @"completed" : @"completed",
             @"fwStats" : @"fw_stats",
             @"grStats" : @"gr_stats",
             @"puttsNumber" : @"putts_number",
             @"bunkerHit" : @"bunker_hit"
             };
}

+ (NSValueTransformer *)playerMetadataJSONTransformer {
    return [MTLJSONAdapter dictionaryTransformerWithModelClass:MRPHolePlayerMetadataModel.class];
}

+ (NSDateFormatter *)dateFormatter {
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    dateFormatter.dateFormat = @"yyyy-MM-dd hh:mm:ss";
    return dateFormatter;
}

+ (NSValueTransformer *)transformerDate {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter dateFromString:value];
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter stringFromDate:value];
    }];
}

+ (NSValueTransformer *)lastModifiedDateJSONTransformer {
    return [self transformerDate];
}

+ (NSValueTransformer *)lastSentDateJSONTransformer {
    return [self transformerDate];
}

+ (NSValueTransformer *)pinLocationJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        if ([value isKindOfClass:[MRPTeeboxBoundaryModel class]]) {
            return value;
        } else if ([value isKindOfClass:[NSDictionary class]]) {
            MRPTeeboxBoundaryModel *model = [[MRPTeeboxBoundaryModel alloc] initWithLatitude:value[@"latitude"] andLongitude:value[@"longitude"]];
            return model;
        } else {
            MRPTeeboxBoundaryModel *model = [[MRPTeeboxBoundaryModel alloc] initWithLatitude:[value firstObject] andLongitude:[value lastObject]];
            return model;
        }
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        if ([value isKindOfClass:[MRPTeeboxBoundaryModel class]]) {
            MRPTeeboxBoundaryModel *tem = (MRPTeeboxBoundaryModel *)value;
            NSDictionary *dict = [NSDictionary dictionary];
            if ([tem.latitude integerValue] != 0) {
                dict = @{
                         @"latitude" : tem.latitude,
                         @"longitude" : tem.longitude
                         };
            } else {
                dict = @{
                         @"latitude" : @"",
                         @"longitude" : @""
                         };
            }
            
            return dict;
        } else {
            NSDictionary *dict = [NSDictionary dictionary];
            if ([value isKindOfClass:[NSArray class]]) {
                dict = @{
                         @"latitude" : [value firstObject],
                         @"longitude" : [value lastObject]
                         };
            } else if ([value isKindOfClass:[NSDictionary class]]) {
                dict = @{
                         @"latitude" : value[@"latitude"],
                         @"longitude" : value[@"longitude"]
                         };
            } else {
                dict = @{
                         @"latitude" : @"",
                         @"longitude" : @""
                         };
            }
            return dict;
        }
    }];
}

+ (NSValueTransformer *)greenFrontJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        if ([value isKindOfClass:[MRPTeeboxBoundaryModel class]]) {
            return value;
        } else if ([value isKindOfClass:[NSDictionary class]]) {
            return [[MRPTeeboxBoundaryModel alloc] initWithLatitude:value[@"latitude"] andLongitude:value[@"longitude"]];
        } else {
            return [[MRPTeeboxBoundaryModel alloc] initWithLatitude:[value firstObject] andLongitude:[value lastObject]];
        }
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        if ([value isKindOfClass:[MRPTeeboxBoundaryModel class]]) {
            MRPTeeboxBoundaryModel *tem = (MRPTeeboxBoundaryModel *)value;
            NSDictionary *dict = [NSDictionary dictionary];
            if ([tem.latitude integerValue] != 0) {
                dict = @{
                         @"latitude" : tem.latitude,
                         @"longitude" : tem.longitude
                         };
            } else {
                dict = @{
                         @"latitude" : @"",
                         @"longitude" : @""
                         };
            }
            
            return dict;
        } else {
            NSDictionary *dict = [NSDictionary dictionary];
            if ([value isKindOfClass:[NSArray class]]) {
                dict = @{
                         @"latitude" : [value firstObject],
                         @"longitude" : [value lastObject]
                         };
            } else if ([value isKindOfClass:[NSDictionary class]]) {
                dict = @{
                         @"latitude" : value[@"latitude"],
                         @"longitude" : value[@"longitude"]
                         };
            } else {
                dict = @{
                         @"latitude" : @"",
                         @"longitude" : @""
                         };
            }
            return dict;
        }
    }];
}

+ (NSValueTransformer *)greenMiddleJSONTransformer {
    
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        if ([value isKindOfClass:[MRPTeeboxBoundaryModel class]]) {
            return value;
        } else if ([value isKindOfClass:[NSDictionary class]]) {
            return [[MRPTeeboxBoundaryModel alloc] initWithLatitude:value[@"latitude"] andLongitude:value[@"longitude"]];
        } else {
            return [[MRPTeeboxBoundaryModel alloc] initWithLatitude:[value firstObject] andLongitude:[value lastObject]];
        }
        
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        if ([value isKindOfClass:[MRPTeeboxBoundaryModel class]]) {
            MRPTeeboxBoundaryModel *tem = (MRPTeeboxBoundaryModel *)value;
            NSDictionary *dict = [NSDictionary dictionary];
            if ([tem.latitude integerValue] != 0) {
                dict = @{
                         @"latitude" : tem.latitude,
                         @"longitude" : tem.longitude
                         };
            } else {
                dict = @{
                         @"latitude" : @"",
                         @"longitude" : @""
                         };
            }
            
            return dict;
        } else {
            NSDictionary *dict = [NSDictionary dictionary];
            if ([value isKindOfClass:[NSArray class]]) {
                dict = @{
                         @"latitude" : [value firstObject],
                         @"longitude" : [value lastObject]
                         };
            } else if ([value isKindOfClass:[NSDictionary class]]) {
                dict = @{
                         @"latitude" : value[@"latitude"],
                         @"longitude" : value[@"longitude"]
                         };
            } else {
                dict = @{
                         @"latitude" : @"",
                         @"longitude" : @""
                         };
            }
            //            return [MTLJSONAdapter modelOfClass:[MRPTeeboxBoundaryModel class]
            //                             fromJSONDictionary:dict error:&error];
            return dict;
        }
    }];
}

+ (NSValueTransformer *)greenBackJSONTransformer {

    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        if ([value isKindOfClass:[MRPTeeboxBoundaryModel class]]) {
            return value;
        } else if ([value isKindOfClass:[NSDictionary class]]) {
            return [[MRPTeeboxBoundaryModel alloc] initWithLatitude:value[@"latitude"] andLongitude:value[@"longitude"]];
        } else {
            return [[MRPTeeboxBoundaryModel alloc] initWithLatitude:[value firstObject] andLongitude:[value lastObject]];
        }
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        if ([value isKindOfClass:[MRPTeeboxBoundaryModel class]]) {
            MRPTeeboxBoundaryModel *tem = (MRPTeeboxBoundaryModel *)value;
            NSDictionary *dict = [NSDictionary dictionary];
            if ([tem.latitude integerValue] != 0) {
                dict = @{
                         @"latitude" : tem.latitude,
                         @"longitude" : tem.longitude
                         };
            } else {
                dict = @{
                         @"latitude" : @"",
                         @"longitude" : @""
                         };
            }
            
            return dict;
        } else {
            NSDictionary *dict = [NSDictionary dictionary];
            if ([value isKindOfClass:[NSArray class]]) {
                dict = @{
                         @"latitude" : [value firstObject],
                         @"longitude" : [value lastObject]
                         };
            } else if ([value isKindOfClass:[NSDictionary class]]) {
                dict = @{
                         @"latitude" : value[@"latitude"],
                         @"longitude" : value[@"longitude"]
                         };
            } else {
                dict = @{
                         @"latitude" : @"",
                         @"longitude" : @""
                         };
            }
            
            return dict;
        }
    }];
}

+ (NSValueTransformer *)featuresJSONTransformer {
    return [MTLJSONAdapter dictionaryTransformerWithModelClass:MRPFeaturesModel.class];
}

+ (NSValueTransformer *)teeboxBoundaryArrayJSONTransformer {
    
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        NSMutableArray *array = [NSMutableArray array];
        if ([value isKindOfClass:[NSArray class]]) {
            if ([[value firstObject] isKindOfClass:[NSArray class]]) {
                for(NSArray *teeboxArray in value) {
                    MRPTeeboxBoundaryModel *teeBox = [[MRPTeeboxBoundaryModel alloc] initWithLatitude:[teeboxArray lastObject] andLongitude:[teeboxArray firstObject]];
                    [array addObject:teeBox];
                }
            } else if ([[value firstObject] isKindOfClass:[NSDictionary class]]) {
                for(NSDictionary *teeboxDict in value) {
                    MRPTeeboxBoundaryModel *teeBox = [[MRPTeeboxBoundaryModel alloc] initWithLatitude:teeboxDict[@"latitude"] andLongitude:teeboxDict[@"longitude"]];
                    [array addObject:teeBox];
                }
            } else {
                for(MRPTeeboxBoundaryModel *teebox in value) {
                    [array addObject:teebox];
                }
            }
            
            return array;
        }
        
        return value;
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        NSMutableArray *array = [NSMutableArray array];
        if ([value isKindOfClass:[NSArray class]]) {
            if ([[value firstObject] isKindOfClass:[NSArray class]]) {
                for(NSArray *teeboxArray in value) {
                    NSDictionary *dict = @{
                                           @"latitude" : [teeboxArray firstObject],
                                           @"longitude" : [teeboxArray lastObject]
                                           };
                    [array addObject:dict];
                    
                }
            } else if ([[value firstObject] isKindOfClass:[NSDictionary class]]) {
                for(NSDictionary *teeboxDict in value) {
                    NSDictionary *dict = @{
                                           @"latitude" : teeboxDict[@"latitude"],
                                           @"longitude" : teeboxDict[@"longitude"]
                                           };
                    [array addObject:dict];
                }
            } else {
                for(MRPTeeboxBoundaryModel *teebox in value) {
                    NSDictionary *dict;
                    if ([teebox.latitude integerValue] != 0) {
                        dict = @{
                                 @"latitude" : teebox.latitude,
                                 @"longitude" : teebox.longitude
                                 };
                    } else {
                        dict = @{
                                 @"latitude" : @"",
                                 @"longitude" : @""
                                 };
                    }
                    [array addObject:dict];
                }
            }
            
            return array;
        }
        
        return value;
    }];
    
}

+ (NSValueTransformer *)strokesJSONTransformer {
    return [MTLJSONAdapter arrayTransformerWithModelClass:[MRPStrokeModel class]];
}

//+ (MRPTeeboxBoundaryModel *)createTeeBoxBoundary:(id)value {
//    if ([value isKindOfClass:[MRPTeeboxBoundaryModel class]]) {
//        return value;
//    } else {
//
//        return [MTLJSONAdapter dictionaryTransformerWithModelClass:MRPTeeboxBoundaryModel.class];
//
//        NSDictionary *teeboxDic = @{
////                                    @"coordinate" : @{
//                                            @"latitude" : [value objectAtIndex:0],
//                                            @"longitude" : [value objectAtIndex:1]
////                                            }
//                                    };
//        NSError *error;
//        MRPTeeboxBoundaryModel *teexbox = [MTLJSONAdapter modelOfClass:MRPTeeboxBoundaryModel.class fromJSONDictionary:teeboxDic error:&error];
//        //[[MRPTeeboxBoundaryModel alloc] initWithLatitude:(NSNumber*)[value objectAtIndex:0] andLongitude:(NSNumber*)[value objectAtIndex:1]];
//        return teexbox;
//    }
//}


- (void)updateStroke:(MRPStrokeModel *)stroke; {
    //    NSInteger index = [self.strokes indexOfObject:stroke];
    //    [self.strokes replaceObjectAtIndex:index withObject:stroke];
}

- (void)removeStroke:(MRPStrokeModel *)stroke {
//    NSInteger index = [self.strokes indexOfObject:stroke];
//    [self.strokes removeObjectAtIndex:index];
}

- (BOOL)hasChange {
    return
    self.lastModifiedDate != nil &&
    (self.lastSentDate == nil || [self.lastModifiedDate compare:self.lastSentDate] == NSOrderedDescending);
}

- (void)updateChange {
    self.lastModifiedDate = [NSDate date];
}

@end
