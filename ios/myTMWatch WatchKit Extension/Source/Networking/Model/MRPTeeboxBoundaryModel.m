//
//  MRPTeeboxBoundaryModel.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/18/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPTeeboxBoundaryModel.h"
#import <MapKit/MapKit.h>

@implementation MRPTeeboxBoundaryModel

-(instancetype)initWithLatitude:(NSNumber *)latitude andLongitude:(NSNumber *)longitude
{
    if(self = [super init]) {
        self.latitude = latitude;
        self.longitude = longitude;
    }
    return self;
}

- (CLLocationCoordinate2D)getCoordinateLocation2D {
    return CLLocationCoordinate2DMake([self.latitude doubleValue], [self.longitude doubleValue]);
//    return self.location.coordinate;
}


+ (NSDictionary *)JSONKeyPathsByPropertyKey {
    return @{
             @"latitude" : @"latitude",
             @"longitude" : @"longitude",
             
//             @"location": @[@"latitude", @"longitude"]
             };
}

//+ (NSValueTransformer *)locationJSONTransformer {
//    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
//        NSString *latitude = value[@"latitude"];
//        NSString *longitude = value[@"longitude"];
//        CLLocation *location = [[CLLocation alloc] initWithLatitude:[latitude doubleValue] longitude:[longitude doubleValue]];
//        ;
//        return location;
//    }];
//}

@end
