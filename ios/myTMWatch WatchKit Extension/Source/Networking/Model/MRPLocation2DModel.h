//
//  MRPLocation2DModel.h
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/18/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import <Mantle/Mantle.h>
//#import <Realm/Realm.h>
#import <CoreLocation/CoreLocation.h>

@interface MRPLocation2DModel : MTLModel <MTLJSONSerializing>

@property (nonatomic, copy) NSNumber/* <RLMDouble> */ *latitude;
@property (nonatomic, copy) NSNumber/* <RLMDouble> */ *longitude;

//- (instancetype)initWithDictionary:(NSDictionary *)dictionaryValue error:(NSError *__autoreleasing *)error;

//@property (strong, nonatomic) CLLocation            *location;

- (instancetype)initWithLatitude:(NSNumber *)latitude andLongitude:(NSNumber *)longitude;

- (CLLocationCoordinate2D)getCoordinateLocation2D;

@end
