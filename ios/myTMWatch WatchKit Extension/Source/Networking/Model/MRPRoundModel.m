//
//  MRPRoundModel.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/19/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPRoundModel.h"

@implementation MRPRoundModel

+ (NSDictionary *)JSONKeyPathsByPropertyKey {
    return @{
             @"roundId" : @"id",
             @"courseId" : @"course_id",
             @"courseName" : @"course_name",
             @"playedOn" : @"played_on",
             @"totalScore" : @"total_score",
             @"coursePar" : @"course_par",
             @"completed" : @"completed",
             @"holesArray" : @"holes",
             @"playerMetadata" : @"player_metadata",
             @"teeName" : @"tee_name",
             @"teeNameDisplay" : @"tee_name_display",
             @"userTimezone" : @"user_timezone",
             @"roundType" : @"round_type",
             @"roundMode" : @"round_mode",
             @"statsCompleted" : @"stats_completed",
             @"mapId" : @"map_id",
             @"igolfCourseId" : @"igolf_course_id",
             @"deleted": @"deleted",
             // Append
             @"userId" : @"user_id",
             @"isEditingActive" : @"is_editing_active",
             @"deletedAt" : @"deleted_at",
             @"createdAt" : @"created_at",
             @"lockedAt" : @"locked_at",
             @"modeIndex" : @"mode_index",
             @"course" : @"course",
             @"selectedTee" : @"selected_tee",
             @"gameType" : @"game_type",
             @"roundDate" : @"round_date",
             @"pausedDate" : @"paused_date",
             @"currentSeconds" : @"current_seconds",
             @"startDate" : @"start_date",
             @"sendError" : @"send_error",
             @"isRoundSubmited" : @"is_round_submited",
             @"submitedDate" : @"submited_date",
             @"pausedHoleNumber" : @"paused_hole_number",
             @"inprogress" : @"inprogress",
             @"classicModeScoresOnly" : @"classicModeScoresOnly"
             };
}

+ (NSDateFormatter *)dateFormatter {
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setTimeZone:[NSTimeZone timeZoneWithName:@"UTC"]];
    dateFormatter.dateFormat = @"yyyy-MM-dd'T'HH:mm:ss'Z'";
    return dateFormatter;
}

+ (NSValueTransformer *)deletedJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter dateFromString:value];
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter stringFromDate:value];
    }];
}

+ (NSValueTransformer *)userIdJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return value;
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return value;
    }];
}

+ (NSValueTransformer *)createdAtJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter dateFromString:value];
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter stringFromDate:value];
    }];
}

+ (NSValueTransformer *)lockedAtJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter dateFromString:value];
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter stringFromDate:value];
    }];
}

+ (NSValueTransformer *)roundDateJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter dateFromString:value];
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter stringFromDate:value];
    }];
}

+ (NSValueTransformer *)pausedDateJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter dateFromString:value];
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter stringFromDate:value];
    }];
}

+ (NSValueTransformer *)startDateJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter dateFromString:value];
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter stringFromDate:value];
    }];
}

+ (NSValueTransformer *)submitedDateJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter dateFromString:value];
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter stringFromDate:value];
    }];
}

+ (NSValueTransformer *)playedOnJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        NSDate *date = [self.dateFormatter dateFromString:value];
        return date;
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter stringFromDate:value];
    }];
}

+ (NSValueTransformer *)playerMetadataJSONTransformer {
    return [MTLJSONAdapter dictionaryTransformerWithModelClass:[MRPRoundPlayerMetadataModel class]];
}

+ (NSValueTransformer *)courseJSONTransformer {
    return [MTLJSONAdapter dictionaryTransformerWithModelClass:[MRPCourseModel class]];
}

+ (NSValueTransformer *)selectedTeeJSONTransformer {
    return [MTLJSONAdapter dictionaryTransformerWithModelClass:MRPTeeModel.class];
}

+ (NSValueTransformer *)holesArrayJSONTransformer {
    return [MTLJSONAdapter arrayTransformerWithModelClass:[MRPHoleModel class]];
}

- (BOOL)needSubmit {
    if (self.inprogress == NO && self.isRoundSubmited.boolValue == NO) return YES;
    
    for (MRPHoleModel *hole in self.selectedTee.holesArray) {
        if (hole.hasChange) {
            return YES;
        }
    }
    return NO;
}

- (instancetype)copyWithZone:(NSZone *)zone {
  MRPRoundModel *copy = [[[self class] allocWithZone:zone] init];
  if(copy) {
    copy.roundId = [self roundId];
    copy.deletedAt = [self deletedAt];
    copy.roundMode = [self roundMode];
    copy.igolfCourseId = [self igolfCourseId];
    copy.mapId = [self mapId];
    copy.course = [self.course copy];
    copy.inprogress = [self inprogress];
    copy.roundType = [self roundType];
    copy.completed = [self completed];
    copy.startDate = [self startDate];
    copy.currentSeconds = [self currentSeconds];
    copy.courseName = [self courseName];
    copy.courseId = self.courseId;
    copy.coursePar = [self coursePar];
    copy.totalScore = [self totalScore];
    copy.playedOn = [self playedOn];
    copy.userTimezone = [self userTimezone];
    copy.userId = [self userId];
    copy.teeName = [self teeName];
    copy.teeNameDisplay = [self teeNameDisplay];
    copy.isEditingActive = [self isEditingActive];
    copy.isRoundSubmited = [self isRoundSubmited];
    copy.createdAt = [self createdAt];
    copy.statsCompleted = [self statsCompleted];
    copy.roundDate = [self roundDate];
    copy.submitedDate = [self submitedDate];
    copy.pausedDate = [self pausedDate];
    copy.courseConditions = [self.courseConditions copy];
    copy.playerMetadata = [self.playerMetadata copy];
    copy.lockedAt = [self lockedAt];
    copy.modeIndex = [self modeIndex];
    copy.gameType = [self gameType];
    copy.sendError = [self sendError];
    copy.classicModeScoresOnly = [self classicModeScoresOnly];
    
    copy.selectedTee = [self.selectedTee copy];
  }
  
  return copy;
}

@end
