//
//  MRPTeeboxBoundaryModel.h
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/18/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

// https://stackoverflow.com/questions/35111630/combine-multiple-keys-into-single-property-using-mantle

#import <Mantle/Mantle.h>
//#import <Realm/Realm.h>
#import <CoreLocation/CoreLocation.h>

@interface MRPTeeboxBoundaryModel : MTLModel <MTLJSONSerializing>

@property (strong, nonatomic) NSNumber/* <RLMDouble> */ *latitude;
@property (strong, nonatomic) NSNumber/* <RLMDouble> */ *longitude;

//@property (strong, nonatomic) CLLocation            *location;

-(instancetype)initWithLatitude:(NSNumber*)latitude andLongitude:(NSNumber*)longitude;
- (CLLocationCoordinate2D)getCoordinateLocation2D;

@end
