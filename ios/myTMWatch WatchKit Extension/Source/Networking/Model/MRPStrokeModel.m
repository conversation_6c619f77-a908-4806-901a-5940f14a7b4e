
//  MRPStrokeModel.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/18/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPStrokeModel.h"

@implementation MRPStrokeModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.coordinates = [[NSMutableArray alloc] init];
    }
    return self;
}

+ (NSDictionary *)JSONKeyPathsByPropertyKey {
    return @{
             @"clubId" : @"club_id",
             @"distanceToPin" : @"distance_to_pin",
             @"endingLie" : @"ending_lie",
             @"startingLie" : @"starting_lie",
             @"coordinates" : @"coordinates",
             @"shotAt" : @"shot_at",
             @"shotNumber" : @"shot_number",
             @"shotDistance" : @"shot_distance",
             @"strokeId" : @"id"
             };
}

+ (NSValueTransformer *)precedingStrokeJSONTransformer {
    return [MTLJSONAdapter dictionaryTransformerWithModelClass:MRPStrokeModel.class];
}

+ (NSDateFormatter *)dateFormatter {
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    dateFormatter.dateFormat = @"yyyy-MM-dd hh:mm:ss";
    return dateFormatter;
}

+ (NSValueTransformer *)shotAtJSONTransformer {
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter dateFromString:value];
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        return [self.dateFormatter stringFromDate:value];
    }];
}

+ (NSValueTransformer *)coordinatesJSONTransformer {
//    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
//        NSMutableArray *array = [NSMutableArray array];
//        if ([value isKindOfClass:[NSArray class]]) {
//            if([(NSArray*)value count] == 2) {
//                MRPLocation2DModel *location = [[MRPLocation2DModel alloc] initWithLatitude:(NSNumber*)[value objectAtIndex:0] andLongitude:(NSNumber*)[value objectAtIndex:1]];
//                [array addObject:location];
//            } else {
//                return value;
//            }
//        } else {
//            [array addObject:value];
//        }
//
//
//        return array;
//    }];
    
    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        NSMutableArray *array = [NSMutableArray array];
        if ([value isKindOfClass:[NSArray class]]) {
            if ([[value firstObject] isKindOfClass:[NSArray class]]) {
                for(NSArray *teeboxArray in value) {
                    MRPLocation2DModel *locationModel = [[MRPLocation2DModel alloc] initWithLatitude:[teeboxArray firstObject] andLongitude:[teeboxArray lastObject]];
                    [array addObject:locationModel];
                }
            } else if ([[value firstObject] isKindOfClass:[NSDictionary class]]) {
                for(NSDictionary *teeboxDict in value) {
                    MRPLocation2DModel *locationModel = [[MRPLocation2DModel alloc] initWithLatitude:teeboxDict[@"latitude"] andLongitude:teeboxDict[@"longitude"]];
                    [array addObject:locationModel];
                }
            } else {
                for(MRPLocation2DModel *locationModel in value) {
                    [array addObject:locationModel];
                }
            }
            
            return array;
        }
        
        return value;
    } reverseBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
        NSMutableArray *arrValue = [NSMutableArray array];
        
        for (MRPLocation2DModel *locationModel in value) {
            if ([locationModel isKindOfClass:[NSDictionary class]]) {
//                NSDictionary *tem = (NSDictionary *)locationModel;
//                NSDictionary *dict = @{
//                                       @"latitude" : tem@["latitude"],
//                                       @"longitude" : tem@["longitude"]
//                                       };
                [arrValue addObject:locationModel];
            } else {
                NSDictionary *dict = @{
                                       @"latitude" : locationModel.latitude,
                                       @"longitude" : locationModel.longitude
                                       };
                [arrValue addObject:dict];
            }
            
        }
        
        return arrValue;
    }];
    
//    return [MTLValueTransformer transformerUsingForwardBlock:^id(id value, BOOL *success, NSError *__autoreleasing *error) {
//        NSMutableArray *array = [NSMutableArray array];
//        NSMutableArray *arrayDict = [NSMutableArray array];
//        BOOL isArray = NO;
//        for(NSArray *tmpArray in value) {
//            if ([tmpArray isKindOfClass:[NSArray class]]) {
//                isArray = YES;
//                //                NSError *error;
//                NSDictionary *dict = [NSDictionary dictionary];
//                if ([tmpArray isKindOfClass:[NSArray class]]) {
//                    dict = @{
//                             @"latitude" : [tmpArray firstObject],
//                             @"longitude" : [tmpArray lastObject]
//                             };
//                } else {
//                    dict = @{
//                             @"latitude" : value[@"latitude"],
//                             @"longitude" : value[@"longitude"]
//                             };
//                }
//                [arrayDict addObject:dict];
//            }
//        }
//        
//        for(NSArray *tmpArray in value) {
//            if ([tmpArray isKindOfClass:[NSArray class]]) {
//                
//            } else {
//                [array addObject:tmpArray];
//            }
//        }
//        
//        if (isArray) {
//            NSError *error;
//            //            return [MTLJSONAdapter modelsOfClass:[MRPTeeboxBoundaryModel class] fromJSONArray:arrayDict error:&error];
//            return arrayDict;
//        }
//        
//        return array;
//    }];
}

@end
