//
//  MRPWatchApiHandler.h
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/16/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <AFNetworking/AFNetworking.h>
#import "MRPCourseModel.h"
#import "MRPRoundModel.h"
#import "MRPHoleModel.h"
#import "MRPClubsModel.h"

@interface MRPWatchApiHandler : NSObject

@property (strong, nonatomic) AFHTTPSessionManager      *manager;
@property (nonatomic, retain) NSString                  *customerAPIKey;
@property (nonatomic, retain) NSString                  *customerSecretKey;

// Functions
+ (MRPWatchApiHandler*)sharedInstance;

- (void)submitEmptyRound:(MRPRoundModel *)round success:(void (^)(NSNumber *roundId))completionBlock failure:(void (^)(NSError *))failureBlock;

//- (void)editRound:(MRPRoundModel *)round success:(void (^)(MRPRoundModel *roundResponse))completionBlock failure:(void (^)(NSError *))failureBlock;

- (void)editRound:(MRPRoundModel *)round success:(void (^)(MRPRoundModel *roundResponse, NSDictionary *dict))completionBlock failure:(void (^)(NSError *))failureBlock;

- (void)deleteRound:(MRPRoundModel *)round andSuccess:(void (^)(NSDictionary *response))completionBlock andFailure:(void(^)(NSError *error))failureBlock;

-(void)getClubStatsWithParameters:(NSMutableDictionary *)parameters success:(void (^)(NSArray *clubStats))completionBlock andFailure:(void (^)(NSError *error))failureBlock;

- (NSURLSessionDataTask *)searchCoursesWithLocation:(CLLocation*)location andGolfNameSearch:(NSString*)searchString withCompletionBlock:(void (^)(NSMutableArray *responseArray))completionBlock andFailure:(void(^)(NSError *error))failureBlock;

- (void)getCourseTeeDetailWithCourseID:(NSString *)courseID numberOfHoles:(NSNumber *) numberOfHoles  withCompletionBlock:(void (^)(MRPCourseModel *))completionBlock andFailure:(void (^)(NSError *))failureBlock;

- (void)getCourseScorecardDetailsWithCourseID:(MRPCourseModel *)course withCompletionBlock:(void (^)(MRPCourseModel *))completionBlock andFailure:(void (^)(NSError *))failureBlock;

- (void)getCourseGPSVectorWithCourse:(MRPCourseModel *)course withCompletionBlock:(void (^)(MRPCourseModel *))completionBlock andFailure:(void (^)(NSError *))failureBlock;

- (void)getCourseGPSDetailWithCourse:(MRPCourseModel *)course withCompletionBlock:(void (^)(NSArray *))completionBlock andFailure:(void (^)(NSError *))failureBlock;

- (void)getShowAdvanceWithSuccess:(void (^)(BOOL showAdvance))completionBlock andFailure:(void (^)(NSError *error))failureBlock;

@end
