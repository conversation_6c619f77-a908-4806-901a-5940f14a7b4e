//
//  TMConstants.h
//  TaylorMade
//
//  Created by <PERSON><PERSON> on 8/11/16.
//  Copyright © 2016 taylormade. All rights reserved.
//
// Thuc Luu 3/13/18: add link to Support Page

#import <Foundation/Foundation.h>

// Enum In Cell
typedef enum {
    GameProfileMoreCellType = 0,
    AccountMoreCellType,
    ManagePlayerMoreCellType,
//    WatchTutorialMoreCellType,
//    WatchMultiplayerCellType,
//    WatchAppleWatchCellType,
    FAQMoreCellType,
    ContactUsMoreCellType,
    TermAndPrivacyMoreCellType,
    LogoutMoreCellType
} MoreCellType;

typedef enum {
    ShowNowMoreCellType = 0,
    FindTeeMoreCellType
} StoreCellType;

typedef enum {
    Win = 0,
    Draw,
    Lose
} GameStatus;

typedef enum {
    BaseRoundDetailCellType = 0,
    DateRoundDetailCellType,
    TeeRoundDetailCellType,
    GameTypeRoundDetailCellType,
    GameModeTitleRoundDetailCellType,
    MultiplayerRoundDetailCellType,
    AdvanceRoundDetailCellType,
    BasicRoundDetailCellType,
    SetupRoundDetailCellType
} RoundDetailCellType;

// Value
typedef enum {
    StrokePlayMultiplayerType = 0,
    MatchPlayMultiplayerType,
    SkinsMultiplayerType
} MultiplayerType;

typedef enum {
    ClubTypeIdDriver = 1,
    ClubTypeIdIron,
    ClubTypeIdFairway,
    ClubTypeIdHybrid,
    ClubTypeIdPutter,
    ClubTypeIdWedge,
    ClubTypeIdBall
} ClubTypeId;

typedef enum {
    RegisterInfoHomeCourse = 1,
    RegisterInfoPostalCode,
    RegisterInfoCountry,
    RegisterInfoBirthday
} RegisterInfo;

typedef enum {
    FooterMenuActiveMyGame = 0,
    FooterMenuActiveStats = 1,
    FooterMenuActiveAddRound = 2,
    FooterMenuActiveClubs = 3,
    FooterMenuActiveMore = 4
} FooterMenuActive;

typedef enum {
    GameTypePractice,
    GameTypeTournament
} GameType;

typedef enum {
    ModeTypeMultiPlayer = 0,
    ModeTypeAdvanced,
    //ModeTypeBasic,
    ModeTypeClassic
} ModeType;

typedef enum {
    MenuItemScorecard,
    MenuItemPauseRound,
    MenuItemEndRound,
    MenuItemDiscardRound,
    MenuItemSaveAndBack,
    MenuItemCancel
} MenuItem;

typedef enum {
    DrivingStatsTypeAll,
    DrivingStatsTypeDriver,
    DrivingStatsType3WD,
    DrivingStatsTypeOther
} DrivingStatsType;

typedef enum {
    AddClubProgressSelectClubs,
    AddClubProgressAddDetails,
    AddClubProgressDefineSpecifications
} AddClubProgress;

typedef enum {
    ClubDetailsTypeLoft,
    ClubDetailsTypeShaftFlex,
    ClubDetailsTypeShaftLength,
    ClubDetailsTypeFaceLieAdjustment,
    ClubDetailsTypeFaceLoftAdjustment,
    ClubDetailsTypeNone
} ClubDetailsType;

typedef enum {
    ClubTypeActive,
    ClubTypeInActive,
    ClubTypeNone
} ClubType;

typedef enum {
    AcionHandleClubTypeAdd,
    AcionHandleClubTypeActive,
    AcionHandleClubTypeInActive,
    AcionHandleClubTypeDelete,
    AcionHandleClubTypeCompare,
    AcionHandleClubTypeNone
} AcionHandleClubType;

typedef NS_ENUM(NSInteger, ClubStatType) {
    DistanceAverage,
    ShotsPerRound,
    StrokeGained,
    PercentageGreenHit
};

typedef enum {
    paused = 0,
    pending = 1,
    completed = 2,
} RoundProgress;

//#if !DEBUG
//#define NSLog(...)
//#endif

/*------------debugging------------*/
#ifdef DEBUG
#define DLog(...) NSLog(@"%s(%p) %@", __PRETTY_FUNCTION__, self, [NSString stringWithFormat:__VA_ARGS__])
#else
#define DLog(...)
#endif

#define iGolf   1

#define IS_IPHONE (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone)
#define IS_IPHONE_SE (IS_IPHONE && [[UIScreen mainScreen] bounds].size.height == 568.0)
#define IS_IPHONE_6 (IS_IPHONE && [[UIScreen mainScreen] bounds].size.height == 667.0)
#define IS_IPHONE_6_PLUS (IS_IPHONE && [[UIScreen mainScreen] bounds].size.height == 736.0)

#define degreesToRadians(x)                             (M_PI * x / 180.0)
#define radiansToDegrees(x)                             (x * 180.0 / M_PI)

// Common In App
#define kMiniType                                       @"application/pdf"
#define kRequestNewCourseFileName                       @"request_new_course"
#define kMyRoundProSupportEmail                         @"<EMAIL>"
#define kCourseUpdateEmail                              @"<EMAIL>"

#define NUMBEROFCOLUMNS                                 [[[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultsNumberHole] intValue]

// Fonts
#define kFontRobotoRegular                              @"Roboto-Regular"
#define kFontRobotoMedium                               @"Roboto-Medium"
#define kFontRobotoBold                                 @"Roboto-Bold"
#define kFontClubhausRegular                            @"ClubHaus-Regular"
#define kFontClubhausBold                               @"ClubHaus-Bold"
#define kFontClubhausOblique                            @"ClubHaus-Oblique"
#define kFontClubhausBoldOblique                        @"ClubHaus-BoldOblique"
#define kFontIcons                                      @"icomoon"
#define kFontIcons2                                     @"TMMRPSprint2"
#define kFontIcons3                                     @"TMMRPSprint5"
#define kFontIcons4                                     @"ICOMOON2"
#define kFontNotches                                    @"notches"
#define kFontDINCondensedBold                           @"DINCondensed-Bold"
#define kFontHelvetica                                  @"Helvetica"
#define kFontHelveticaBold                              @"Helvetica-Bold"
#define kFontSFProRegular                               @"SFPro-Regular"
#define kFontSFProBold                                  @"SFPro-Bold"
#define kFontSFProSemiBold                              @"SFPro-Semibold"
#define kFontTitleLabel                                 [UIFont fontWithName:kFontRobotoBold size:26]
#define kFontNewTitleLabel                              [UIFont fontWithName:kFontRobotoBold size:29]
#define kFontCompareTitleLabel                          [UIFont fontWithName:kFontRobotoBold size:12]

// Urls

#define kUrlBase                                        [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultsBaseURL]
#define kUrlBaseMyTM                                    [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultsBaseURLMyTM]

#define kUrlBaseDev                                     @"https://staging-azure.myroundpro.com/"
#define kUrlBaseProd                                    @"https://myroundpro.com/"

#define kAuth0Domain                                    [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultsAuth0Domain]
#define kAuth0DomainDev                                 @"auth-stage.taylormadegolf.com"
#define kAuth0DomainProd                                @"auth.taylormadegolf.com"

#define kAuth0ClientID                                  [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultsAuth0ClientID]
#define kAuth0ClientIDDev                               @"MSYzqGgfH9w7NHpGCz6F1KjuYfrNKnDR"
#define kAuth0ClientIDProd                              @"BhTXpaOsrnYWTktmVUKrDvuffXzD4omN"

#define kUrlMyTMDev                                     @"https://mytaylormadedev.azurewebsites.net"
#define kUrlMyTMProd                                    @"https://app-prd-mytaylormade-app-001.azurewebsites.net"

//#define kUrlBaseDev                                     @"https://staging-v2.myroundpro.com/"
//#define kUrlBaseProd                                    @"https://myroundpro.com/"

//#define kUrlBase                                        @"https://myroundpro.com/"
//#define kUrlBase                                        @"https://staging-v2.myroundpro.com/"
//#define kUrlBase                                        @"http:192.168.2.28:3000/"


#define kClientDev                                      @"cff4a6465a84e8f7a30a7f6ea6243e4768690cac9ce8a5161017dce55637e056"
#define kClientProd                                     @"322e230d08c4fff101792e5aa93029cae10ef307d2d395738e1f39b11cb559c8"

//#if DEVELOPMENT
//#define kClientID                                       @"cff4a6465a84e8f7a30a7f6ea6243e4768690cac9ce8a5161017dce55637e056"
//#else
//#define kClientID                                       @"322e230d08c4fff101792e5aa93029cae10ef307d2d395738e1f39b11cb559c8"
//#endif

#define kUrlPrivacyPolicy                               [NSString stringWithFormat:@"%@privacy?locale=en-", kUrlBase]
#define kUrlTermsAndConditions                          [NSString stringWithFormat:@"%@terms?locale=en-", kUrlBase]
#define kUrlPrivacyPolicyUS                             @"https://www.taylormadegolf.com/shared-customer-service-customer-legal/privacy.html?lang=en_US"
#define kUrlTermsAndConditionsUS                        @"https://www.taylormadegolf.com/shared-customer-service-customer-legal/terms-and-conditions.html?lang=en_US"
#define kUrlFAQ                                         [NSString stringWithFormat:@"%@faq?hide_login=true", kUrlBase]
#define kUrlSupport                                     [NSString stringWithFormat:@"%@support?hide_login=true", kUrlBase]
#define kUrlForgotPassword                              [NSString stringWithFormat:@"%@users/password/new?hide_login=true", kUrlBase]

#define kEventFacebookCompletedRegistration             @"facebook_event_completed_registration"
#define kEventFacebookCompletedLogin                    @"facebook_event_completed_login"
#define kEventFacebookCompletedRoundCreation            @"facebook_event_completed_round_creation"

#define kEventCompletedRegistration                     @"event_completed_registration"
#define kEventCompletedLogin                            @"event_completed_login"
#define kEventCompletedRoundCreation                    @"event_completed_round_creation"


#define kUrlLogin                                       @"users/sign_in.json"
#define kUrlLogout                                      @"api/users/revoke_token"
#define kUrlRegister                                    @"users.json"
#define kUrlDeactive                                    @"api/users/"
#define kUrlRounds                                      @"mrp/update-round/"
#define kUrlDeleteRounds                                @"play/round/"
#define kUrlPlayCourses                                 @"play/courses?"
#define kUrlPlayCoursesDetail                           @"play/courses/details/"
#define kUrlApiUsers                                    @"api/users/"
#define kUrlContact                                     @"api/v2/contact/"
#define kUrlClubsInBag1                                 @"api/users/"
#define kUrlClubsInBag2                                 @"/clubs"
#define kUrlClubsInactive                               @"&inactive=true"
#define kUrlCourse                                      @"api/courses/detail/"
#define kUrlClubs                                       @"api/clubs/"
#define kUrlSearchCourse                                @"api/courses?"
#define kUrlRecentRounds1                               @"api/users/"
#define kUrlRecentRounds2                               @"/rounds"
#define kUrlActiveClub                                  @"/clubs/active"
#define kUrlDeleteClub                                  @"/clubs/activate?deactivate=true"
#define kUrlChangePassword                              @"user/update_password.json"
#define kUrlResetPasswordUrl                            @"users/password.json"
#define kUrlResetPasswordContactLink                    @"users/password/new"
#define kUrlStatsDrivingDispersion                      @"/stats/driving/dispersion"
#define kUrlStatsDriving                                @"/stats/driving"
#define kUrlStatsOverall                                @"/stats/overall"
#define kUrlStatsOverallLastRound                       @"/stats/overall?last_round=true"
#define kUrlStatsOverallRound                           @"/stats/overall?round_ids="
#define kUrlStatsClub                                   @"/stats/club"
#define kUrlStatsPutting                                @"/stats/putting"
#define kUrlStatsApproach                               @"/stats/approach"
#define kUrlStatsApproachProximity                      @"/stats/approach/proximity"
#define kUrlStatsShort                                  @"/stats/short"
#define kUrlStatsShortProximity                         @"/stats/short/proximity"
#define kUrlTaylorMadeBase                              @"https://my.taylormadegolf.com/api/"
#define kUrlClubBrands                                  @"headModels/brands/clubs/"
#define kUrlClubModels1                                 @"headModels/brand/"
#define kUrlClubModels2                                 @"/clubType/"
#define kUrlBallModels                                  @"/ballModels/"
#define kUrlUserInfoFromCDM                             @"users/login_by_access_token.json"
#define kUrlApiIGolf                                    @"api/igolf/"

// Balls - MyTM
#define kClientIdMyTM                                   @"040c8293-395e-4727-af3a-9798bc60e96c"
#define kMyTMBallId                                     @"e57525b5-50fb-491b-12c8-08d548424d09"
#define kUrlSSOLoginMyTM                                @"/v1/auth/sso/login"
#define kUrlRefreshTokenMyTM                            @"/v1/auth/token/refresh"
#define kUrlBrandsBall                                  @"/v1/witb/brands-by-club-category"
#define kUrlModelsBall                                  @"/v1/witb/models-by-brand"

#define kVimeoVideoId                                   @"201430183"
#define kVimeoGettingStartedId                          @"543679070"
#define kVimeoAppleWatchId                              @"354690029"
#define kVimeoMultiplayerId                             @"543681165"

#define kPubkey  @"-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDI2bvVLVYrb4B0raZgFP60VXY\ncvRmk9q56QiTmEm9HXlSPq1zyhyPQHGti5FokYJMzNcKm0bwL1q6ioJuD4EFI56D\na+70XdRz1CjQPQE3yXrXXVvOsmq9LsdxTFWsVBTehdCmrapKZVVx6PKl7myh0cfX\nQmyveT/eqyZK1gYjvQIDAQAB\n-----END PUBLIC KEY-----"
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************


// Colors
#define kColorToolBar                                   [UIColor colorWithRed:250.0/255.0 green:250.0/255.0 blue:250.0/255.0 alpha:1.0]
#define kColorRed                                       [UIColor colorWithRed:204.0/255.0 green:0.0/255.0 blue:0.0/255.0 alpha:1.0]
#define kColorDarkRed                                   [UIColor colorWithRed:107.0/255.0 green:15.0/255.0 blue:21.0/255.0 alpha:1.0]
#define kColorBlack                                     [UIColor colorWithRed:51.0/255.0 green:51.0/255.0 blue:51.0/255.0 alpha:1.0]
#define kColorDarkGray                                  [UIColor colorWithRed:153.0/255.0 green:153.0/255.0 blue:153.0/255.0 alpha:1.0]
#define kColorBlackGray                                 [UIColor colorWithRed:100.0/255.0 green:100.0/255.0 blue:100.0/255.0 alpha:1.0]
#define kColorLightGray                                 [UIColor colorWithRed:238.0/255.0 green:238.0/255.0 blue:238.0/255.0 alpha:1.0]
#define kColorLightWeightGray                           [UIColor colorWithRed:248.0/255.0 green:248.0/255.0 blue:248.0/255.0 alpha:1.0]
#define kColorSeparatorColor                            [UIColor colorWithRed:51.0/255.0 green:51.0/255.0 blue:51.0/255.0 alpha:0.1]
#define kColorButtonGrayColor                           [UIColor colorWithRed:140.0/255.0 green:140.0/255.0 blue:140.0/255.0 alpha:1.0]
#define kColorSearchInactiveButton                      [UIColor colorWithWhite:153.0/255.0 alpha:0.6]
#define kColorStatsDarkGreen                            [UIColor colorWithRed:57.0/255.0 green:77.0/255.0 blue:38.0/255.0 alpha:1.0]
#define kColorStatsLightGreen                           [UIColor colorWithRed:87.0/255.0 green:113.0/255.0 blue:68.0/255.0 alpha:1.0]
#define kColorStatsGreen                                [UIColor colorWithRed:54.0/255.0 green:180.0/255.0 blue:37.0/255.0 alpha:1.0]
#define kColorLightGrayValue                            [UIColor colorWithRed:190.0/255.0 green:190.0/255.0 blue:190.0/255.0 alpha:1.0]

#define kColorTeamBlue                                  [UIColor colorWithRed:24.0/255.0 green:129.0/255.0 blue:254.0/255.0 alpha:1]
#define kColorTeamBlueBlur                              [UIColor colorWithRed:220.0/255.0 green:236.0/255.0 blue:255.0/255.0 alpha:1]
#define kColorTeamRed                                   [UIColor colorWithRed:255.0/255.0 green:59.0/255.0 blue:48.0/255.0 alpha:1]
#define kColorTeamRedBlur                               [UIColor colorWithRed:249.0/255.0 green:217.0/255.0 blue:221.0/255.0 alpha:1]

#define kColorTextFieldBlack                            [UIColor colorWithRed:0.0/255.0 green: 0.0/255.0 blue:0.0/255.0 alpha:1.0]
#define kColorTextFieldGreen                            [UIColor colorWithRed:52.0/255.0 green: 199.0/255.0 blue:89.0/255.0 alpha:1.0]
#define kColorCardViewBackground                        @"3A4E55"
#define kColorTextFieldError                            [UIColor colorWithRed:255.0/255.0 green: 59.0/255.0 blue:48.0/255.0 alpha:1.0]
#define kColorTextGray                                  [UIColor colorWithRed:140.0/255.0 green: 140.0/255.0 blue:145.0/255.0 alpha:1.0]
#define kColorTextGreen                                 [UIColor colorWithRed:52.0/255.0 green: 199.0/255.0 blue:89.0/255.0 alpha:1.0]
#define kColorBackgroundGray                            [UIColor colorWithRed:245.0/255.0 green: 246.0/255.0 blue:247.0/255.0 alpha:1.0]
#define kColorStastGraphGreen                           [UIColor colorWithRed:0.0/255.0 green: 190.0/255.0 blue:75.0/255.0 alpha:1.0]
#define kColorStastGraphRed                             [UIColor colorWithRed:255.0/255.0 green: 59.0/255.0 blue:48.0/255.0 alpha:1.0]
#define kColorStastGraphWhite                           [UIColor colorWithRed:255.0/255.0 green: 255.0/255.0 blue:255.0/255.0 alpha:1.0]
#define kColorBorderBlack                               [UIColor colorWithRed:0.0/255.0 green: 0.0/255.0 blue:0.0/255.0 alpha:1.0]
#define kColorBorderGray                                [UIColor colorWithRed:60.0/255.0 green: 60.0/255.0 blue:67.0/255.0 alpha:0.33]
#define kColorPrimaryBlack                              [UIColor colorWithRed:0.0/255.0 green: 0.0/255.0 blue:0.0/255.0 alpha:1.0]
#define kColorGrayNew                                   [UIColor colorWithRed:60.0/255.0 green: 60.0/255.0 blue:67.0/255.0 alpha:0.33]
#define kColorGreen34C759                               [UIColor colorFromHexString: @"34C759"]
#define kColorGray                                      [UIColor colorFromHexString: @"8C8C91"]
#define kColorGray8C8C91                                [UIColor colorFromHexString: @"8C8C91"]
#define kColorlightGrayF5F6F7                           [UIColor colorWithRed:245.0/255.0 green: 246.0/255.0 blue:247.0/255.0 alpha:1]
#define kColorBlurField                                 [UIColor colorWithRed:255.0/255.0 green:255.0/255.0 blue:255.0/255.0 alpha:0.33]
#define kColorGrayLabel                                 [UIColor colorWithRed:140.0/255.0 green:140.0/255.0 blue:140.0/255.0 alpha:1.0]
#define kColorGrayScore                                 [UIColor colorFromHexString: @"8D8D92"]
#define kColorGrayIcon                                  [UIColor colorWithRed:168.0/255.0 green:168.0/255.0 blue:168.0/255.0 alpha:1]
#define kColorGreen00BE4B                               [UIColor colorFromHexString: @"00BE4B"]
#define kColorRough                                     [UIColor colorFromHexString: @"D54545"]
#define kColorHazard                                    [UIColor colorFromHexString: @"48B1E0"]
#define kColorBunker                                    [UIColor colorFromHexString: @"D2C242"]
#define kColorGreenSegmented                            [UIColor colorWithRed:39.0/255.0 green:174.0/255.0 blue:96.0/255.0 alpha:1]
#define kColorGraySegmented                             [UIColor colorWithRed:140.0/255.0 green:139.0/255.0 blue:143.0/255.0 alpha:1]

#define kColorInActiveCell                              [UIColor colorWithRed:235.0/255.0 green: 235.0/255.0 blue:235.0/255.0 alpha:1]
#define kColorSelectedCell                              [UIColor colorWithRed:52.0/255.0 green: 199.0/255.0 blue:89.0/255.0 alpha:1]
#define kColorWhite                                     [UIColor whiteColor]
#define kColorGrayE9E9EE                                [UIColor colorFromHexString: @"E9E9EE"]
#define kColorGrayE5E6E7                                [UIColor colorFromHexString: @"E5E6E7"]
#define kColorBlack262628                               [UIColor colorFromHexString: @"262628"]
#define kColorRedFF453A                                 [UIColor colorFromHexString: @"FF453A"]
#define kColorGrayEFEFEF                                [UIColor colorFromHexString: @"EFEFEF"]
#define kColorGrayBDBDBD                                [UIColor colorFromHexString: @"BDBDBD"]
#define kColorBlue2D9CDB                                [UIColor colorFromHexString: @"2D9CDB"]
#define kColorGray8C8B8F                                [UIColor colorFromHexString: @"8C8B8F"]
#define kColorGrayEBEBEB                                [UIColor colorFromHexString: @"EBEBEB"]
#define kColorGrayF4F5F6                                [UIColor colorFromHexString: @"F4F5F6"]
#define kColorGreen3ABA56                               [UIColor colorFromHexString: @"3ABA56"]
#define kColorGreen3ABA5620                             [UIColor colorWithRed:58.0/255.0 green: 186.0/255.0 blue:86.0/255.0 alpha:0.2]
#define kColorRedFF0000                                 [UIColor colorFromHexString: @"FF0000"]

// Screen Name
#define kScreenNameWelcome                              @"Welcome Screen"
#define kScreenNameBirthdaySignUp                       @"Birthday Sign Up Screen"
#define kScreenNameNameSignUp                           @"Name Sign Up Screen"
#define kScreenNameEmailSignUp                          @"Email Sign Up Screen"
#define kScreenNamePasswordSignUp                       @"Password Sign Up Screen"
#define kScreenNameFinishSignUp                         @"Finish Sign Up Screen"
#define kScreenNameLogin                                @"Login Screen"
#define kScreenNameForgotPassword                       @"Forgot Password Screen"
#define kScreenNameTermsOfService                       @"Terms Of Service Screen"
#define kScreenNamePrivacyPolicy                        @"Privacy Policy Screen"

#define kScreenNameMyGame                               @"My Game Screen"
#define kScreenNameRoundOverView                        @"Round Overview Screen"

#define kScreenNameMore                                 @"More Screen"
#define kScreenNameStore                                @"Store Screen"
#define kScreenNameGameProfile                          @"Game Profile Screen"
#define kScreenNameAccount                              @"Account Screen"
#define kScreenNameWatchTutorial                        @"Watch Tutorial Screen"
#define kScreenNameWatchMultiplayer                     @"Watch Multiplayer Demo Screen"
#define kScreenNameWatchAppleWatch                      @"Watch Apple Watch Demo Screen"
#define kScreenNameFAQ                                  @"FAQ's Screen"
#define kScreenNameContactUs                            @"Contact Us Screen"
#define kScreenNameLogout                               @"Logout Screen"
#define kScreenNameShopNow                              @"Shop Now"
#define kScreenNameFindTeeTime                          @"Tee Time"

#define kScreenNameActiveClubs                          @"Active Clubs Screen"
#define kScreenNameInactiveClubs                        @"Inactive Clubs Screen"
#define kScreenNameDetailClub                           @"Detail Club Screen"
#define kScreenNameStatsClub                            @"Stats Club Screen"
#define kScreenNameAddClub                              @"Add Club Screen"
#define kScreenNameAddClubDetail                        @"Add Club Detail Screen"
#define kScreenNameSelectBrand                          @"Select Brand Screen"
#define kScreenNameSelectModel                          @"Select Model Screen"
#define kScreenNameConfirmClub                          @"Confirm Club Screen"
#define kScreenNameDefineSpecification                  @"Define Specification Screen"
#define kScreenNameCompareClubs                         @"Compare Clubs Screen"
#define kScreenNameClubComparison                       @"Club Comparison Screen"
#define kScreenNameActivateClubs                        @"Activate Clubs Screen"
#define kScreenNameDeactivateClubs                      @"Deactivate Clubs Screen"
#define kScreenNameDeleteClubs                          @"Delete Clubs Screen"

#define kScreenNameNearByCourses                        @"Nearby Courses Screen"
#define kScreenNameRecentCourses                        @"Recent Courses Screen"
#define kScreenNameSetUpRound                           @"Set Up Round Screen"
#define kScreenNameSelectTee                            @"Select Tee Screen"
#define kScreenNameSelectGameType                       @"Select Game Type Screen"
#define kScreenNameSelectMode                           @"Select Mode Screen"
#define kScreenNameSelectDate                           @"Select Date Screen"
#define kScreenNamePlayRound                            @"Play Round Screen"
#define kScreenNameMap                                  @"Map Screen"
#define kScreenNameReviewMap                            @"Review Map Screen"
#define kScreenNameSelectClub                           @"Select Club Screen"
#define kScreenNameSelectLieType                        @"Select Lie Type Screen"
#define kScreenNameScorecard                            @"Scorecard Screen"
#define kScreenNameRoundSummary                         @"Round Summary Screen"
#define kScreenNameReportCourseIssue                    @"Report Course Issue Screen"
#define kScreenNameRequestNewCourse                     @"Request New Course Screen"
#define kScreenNameRecentPlayer                         @"Recent Player Screen"

#define kScreenNameStats                                @"Stats Screen"
#define kScreenNameFilterByDate                         @"Filter By Date Screen"
#define kScreenNameFilterByRound                        @"Filter By Round Screen"
#define kScreenNameFilterByCourse                       @"Filter By Course Screen"
#define kScreenNameFilterByRoundType                    @"Filter By Round Type Screen"
#define kScreenNameFilterByRoundMode                    @"Filter By Round Mode Screen"
#define kScreenNameBaseline                             @"Baseline Screen"
#define kScreenNamePuttingStats                         @"Putting Stats Screen"
#define kScreenNameShortStats                           @"Short Stats Screen"
#define kScreenNameApproachStats                        @"Approach Stats Screen"
#define kScreenNameDrivingStats                         @"Driving Stats Screen"
#define kScreenNameStatsByClubDistance                  @"Stats by Club Distance Screen"
#define kScreenNameStatsByClubShots                     @"Stats by Club Shots Screen"
#define kScreenNameStatsByClubStrokeGained              @"Stats by Club Strokes Gained Screen"
#define kScreenNameStatsByClubGreen                     @"Stats by Club % Green Screen"

// Segues
#define kSegueRegistrationSecondStep                    @"registrationSecondStepSegueIdentifier"
#define kSegueRegistrationLastStep                      @"registrationLastStepSegueIdentifier"
#define kSegueFromClubTypeToClubSubtype                 @"fromClubTypeToClubSubtypeSegueIdentifier"
#define kSegueFromSelectClubToClubs                     @"fromSelectClubToClubSegueIdentifier"
#define kSegueFromClubToStatsCompareSegue               @"fromClubToStatsCompareSegue"
#define kSegueFromRoundToGameSettings                   @"fromRoundToGameSettingsSegue"
#define kSegueFromRegisterToLatestRound                 @"fromRegisterToLatestRoundSegue"
#define kSegueFromLatestRoundToAddClub                  @"fromLatestRoundToAddClubSegue"
#define kSegueFromFinishClubSetupToAddBrand             @"fromFinishClubSetupToAddBrandSegue"
#define kSegueFromClubBrandToModel                      @"fromClubBrandToModelSegue"
#define kSegueFromClubModelsToSummary                   @"fromClubModelsToClubSummarySegue"
#define kSegueFromClubsToEditClub                       @"fromClubsToEditClubSegue"
#define kSegueFromClubsToDetailClub                     @"fromClubsToDetailClubSegue"
#define kSegueFromMapToSelectClub                       @"fromMapToSelectClubSegue"
#define kSegueFromSearchCourseToRoundDetails            @"fromSearchCourseToRoundDetailsSegue"
#define kSegueFromSearchCourseToNewRoundDetails         @"fromSearchCourseToNewRoundDetailsSegue"
#define kSegueFromRoundDetailsToSelectTee               @"fromRoundDetailsToSelectTeeSegue"
#define kSegueFromRoundDetailsToMaps                    @"fromRoundDetailsToMapsSegue"
#define kSegueFromRoundSelectGameToMaps                 @"fromRoundSelectGameToMapsSegue"
#define kSegueFromPlayRoundToMaps                       @"fromPlayRoundToMapSegue"
#define kSegueFromMapsToLie                             @"fromMapsToLieSegue"
#define kSegueFromMapsToRoundSummary                    @"fromMapsToRoundSummarySegue"
#define kSegueFromMapsToRoundScorecard                  @"fromMapsToRoundScorecardSegue"
#define kSegueFromRoundDetailsToRoundStart              @"fromRoundDetailsToRoundStartSegue"
#define kSegueFromMultiplayerToRoundStart               @"fromMultiplayerToRoundStartSegue"
#define kSegueFromMultiplayerToGamePlay                 @"fromMultiplayerToGamePlaySegue"
#define kSegueFromMultiplayerToSelectGame               @"fromMultiplayerToSelectGameSegue"
#define kSegueFromRoundDetailsToListMultiplayer         @"fromRoundDetailsToListMultiplayerSegue"
#define kSegueFromFinishClubSetupToDefineSpecification  @"fromFinishClubSetupToDefineSpecificationSegue"
#define kSegueFromStatsToBaselineSettingSegue           @"baselineSettingSegue"
#define kSegueFromStatsToRoundModeFilterSegue           @"roundModeFilterSegue"
#define kSegueFromStatsToRoundTypeFilterSegue           @"roundTypeFilterSegue"
#define kSegueFromStatsToRoundFilterSegue               @"roundFilterSegue"
#define kSegueFromStatsToDateFilterSegue                @"dateFilterSegue"
#define kSegueFromMoreToAccountSegue                    @"accountSegue"
#define kSegueFromMoreToManagePlayerSegue               @"managePlayerSegue"
#define kSegueFromListToMultiPlayerDetailSegue          @"fromListToMultiPlayerDetailSegue"

// Controllers
#define kStoryboardRegisterVC                           @"registerViewControllerIdentifier"
#define kStoryboardStatsClubVC                          @"statsClubViewControllerIdentifier"
#define kStoryboardEditClubVC                           @"editClubViewControllerIdentifier"
#define kStoryboardDetailClubContainerVC                @"detailClubContainerViewControllerIdentifier"
#define kStoryboardSettingsGameVC                       @"settingsGameViewControllerIdentifier"
#define kStoryboardAddClubVC                            @"addClubViewControllerIdentifier"
#define kStoryboardHandleActionClubVC                   @"handleActionClubViewController"
#define kStoryboardCompareClubsVC                       @"compareClubsViewController"
#define kStoryboardStatsCompareVC                       @"statsCompareViewController"
#define kStoryboardEditOptionClub                       @"editOptionClubViewController"
#define kStoryboardLatestRoundVC                        @"latestRoundViewControllerIdentifier"
#define kStoryboardMoreVC                               @"moreViewControllerIdentifier"
#define kStoryboardStoreVC                              @"storeViewControllerIdentifier"
#define kStoryboardStatsVC                              @"statsViewControllerIdentifier"
#define kStoryboardClubsVC                              @"clubsViewControllerIdentifier"
#define kStoryboardLoginVC                              @"loginViewControllerIdentifier"
#define kStoryboardBrandVC                              @"brandViewControllerIdentifier"
#define kStoryboardSearchCourseVC                       @"searchCourseViewControllerIdentifier"
#define kStoryboardRoundContinueVC                      @"roundContinueViewControllerIdentifier"
#define kStoryboardWebViewControllerVC                  @"webViewControllerIdentifier"
#define kStoryboardWebKitControllerVC                   @"webKitControllerIdentifier"
#define kStoryboardConfirmToSVC                         @"confirmTosViewControllerIdentifier"
#define kStoryboardContactVC                            @"contactViewControllerIdentifier"
#define kStoryboardRoundGetStartedVC                    @"roundGetStartedViewControllerIdentifier"
#define kStoryboardRoundDetailsVC                       @"roundDetailsViewControllerIdentifier"
#define kStoryboardDrivingStatsVC                       @"drivingStatsViewControllerIdentifier"
#define kStoryboardApproachStatsVC                      @"approachStatsViewControllerIdentifier"
#define kStoryboardApproachStatsClassicVC               @"approachStatsClassicViewControllerIdentifier"
#define kStoryboardPuttingStatsVC                       @"puttingStatsViewControllerIdentifier"
#define kStoryboardMapsVC                               @"mapsViewControllerIdentifier"
#define kStoryboardClubModelsVC                         @"clubModelsViewControllerIdentifier"
#define kStoryboardRoundOverviewVC                      @"roundOverviewViewControllerIdentifier"
#define kStoryboardRoundSummaryVC                       @"roundSummaryViewControllerIdentifier"
#define kStoryboarRoundScorecardVC                      @"roundScorecardViewControllerIdentifier"
#define kStoryboarListMultiplayerVC                     @"listMultiplayerViewControllerIdentifier"
#define kStoryboardRecentPlayerVC                       @"recentPlayersViewControllerIdentifier"
#define kStoryboardEditPlayerVC                         @"editMultiPlayerViewControllerIdentifier"
#define kRoundTypeFilterVC                              @"kRoundTypeFilter"
#define kRoundModeFilterVC                              @"kRoundModeFilter"
//Cell identifier
#define kCellheaderScoreCellIdentifier                  @"headerScoreCellIdentifier"
// User Defaults
#define kAppGroupName                                   @"group.com.taylormadegolf.mtmplus"
#define kUserDefaultsFacebookAds                        @"USER_DEFAULTS_FACEBOOK_ADS"
#define kUserDefaultsRememberLogin                      @"USER_DEFAULTS_REMEMBER_LOGIN"
#define kUserDefaultsRememberLoginWatch                 @"USER_DEFAULTS_REMEMBER_LOGIN_WATCH"
#define kUserDefaultsLoggedUserId                       @"USER_DEFAULTS_LOGGED_USER_ID"
#define kUserDefaultsUnit                               @"USER_DEFAULTS_UNIT"
#define kUserDefaultsGenderOfCourse                     @"USER_DEFAULTS_GENDER_OF_COURSE"
#define kUserDefaultsCourseOutlineDisabled              @"USER_DEFAULTS_COURSE_OUTLINE_DISABLED"
#define kUserDefaultsRangeFinderDisabled                @"USER_DEFAULTS_RANGE_FINDER_DISABLED"
#define kUserDefaultsRangefinderTooltipShowed           @"USER_DEFAULTS_RANGEFINDER_TOOLTIP_SHOWED"
#define kUserDefaultsHoleOverlayTooltipShowed           @"USER_DEFAULTS_HOLEOVERLAY_TOOLTIP_SHOWED"
#define kUserDefaultsMarkerMovementTooltipShowed        @"USER_DEFAULTS_MARKERMOVEMENT_TOOLTIP_SHOWED"
#define kUserDefaultsRoundAdded                         @"USER_DEFAULTS_ROUND_ADDED"
#define kUserDefaultsToken                              @"USER_DEFAULTS_TOKEN"
#define kUserDefaultsUserID                             @"USER_DEFAULTS_USER_ID"
#define kUserDefaultsBaseURL                            @"USER_DEFAULTS_BASE_URL"
#define kUserDefaultsClientID                           @"USER_DEFAULTS_CLIENT_ID"
#define kUserDefaultsListClub                           @"USER_DEFAULTS_LIST_CLUB"
#define kUserDefaultsUserInfo                           @"USER_DEFAULTS_USER_INFO"
#define kUserDefaultsCurrentRound                       @"USER_DEFAULTS_CURRENT_ROUND"
#define kUserDefaultsNumberHole                         @"USER_DEFAULTS_NUMBER_HOLE"
#define kUserDefaultsNumberCompleteHole                 @"USER_DEFAULTS_NUMBER_COMPLETE_HOLE"
#define kUserDefaultsNeedRepeatReview                   @"USER_DEFAULTS_NEED_REPEAT_REVIEW"
#define kUserDefaultsDateRepeatReview                   @"USER_DEFAULTS_DATE_REPEAT_REVIEW"
#define kUserDefaultsEncryptedData                      @"USER_DEFAULTS_ENCRYPTED_DATA"
#define kUserDefaultsAuth0Domain                        @"USER_DEFAULTS_AUTH0_DOMAIN"
#define kUserDefaultsAuth0ClientID                      @"USER_DEFAULTS_AUTH0_CLIENT_ID"
#define kUserDefaultsRefreshToken                       @"USER_DEFAULTS_REFRESH_TOKEN"
#define kUserDefaultsExpiresIn                          @"USER_DEFAULTS_EXPIRES_IN"


// UserDefaults for Balls
#define kUserDefaultsBaseURLMyTM                        @"USER_DEFAULTS_BASE_URL_MyTM"
#define kUserDefaultsAuth0AccessToken                   @"USER_DEFAULTS_AUTH0_ACCESS_TOKEN"
#define kUserDefaultsMyTMIdToken                        @"USER_DEFAULTS_MYTM_ID_TOKEN"
#define kUserDefaultsMyTMRefreshToken                   @"USER_DEFAULTS_MYTM_REFRESH_TOKEN"

#define kNumberCompleteHoleForReview                    3

// Club Family
#define kClubFamilyDriver                               @"driver"
#define kClubFamilyFairway                              @"fairway"
#define kClubFamilyHybrid                               @"hybrid"
#define kClubFamilyIron                                 @"iron"
#define kClubFamilyWedge                                @"wedge"
#define kClubFamilyPutter                               @"putter"
#define kClubFamilyBall                                 @"ball"   /* TL 09/28/2019 treat ball as a club type -> an item in the bag */

// Constants
#define kGeneratedByWatch                               @"Apple Watch"
//#define kGeneratedByWatch                               @"Samsung Gear S3"
#define kStrokesGainedPro                               @"Pro"
#define kHandednessLeft                                 @"left"
#define kHandednessRight                                @"right"
#define kStrokeScratch                                  @"Scratch"
#define kStrokePro                                      @"Pro"
#define kStroke5                                        @"5"
#define kStroke10                                       @"10"
#define kStroke15                                       @"15"
#define kStroke20                                       @"20"
#define kUnitYards                                      @"yards"
#define kUnitFeet                                       @"feet"
#define kUnitMeters                                     @"meters"
#define kGenderMale                                     @"male"
#define kGenderFemale                                   @"female"
#define kiGolfGenderMale                                @"men"
#define kiGolfGenderFemale                              @"wmn"
#define kTaylorMadeManufacturerString                   @"                       | ";
#define kBelgradeAdaId                                  @"202492"
#define kCourseTypeiGolf                                @"iGolf"
#define kCourseTypeMRP                                  @"MRP"
#define kRoundModeSimple                                @"Simple"
#define kRoundModeBasic                                 @"Basic"
#define kRoundModeClassic                               @"Classic"
#define kRoundModeAdvanced                              @"Advanced"
#define kRoundModeMultiplayer                           @"Multiplayer"
#define kMultiplayerTypeStroke                          @"StrokePlay"
#define kMultiplayerTypeSkins                           @"Skins"
#define kMultiplayerTypeMatch                           @"MatchPlay"
//#define kBelgradeAdaLatitude                            @"-0.0360268"
//#define kBelgradeAdaLongitude                           @"0.078377"
#define kBelgradeAdaLatitude                            @"0.019449"
#define kBelgradeAdaLongitude                           @"0.058489"
//#define kBelgradeMAdaLatitude                           @"-0.03885909"
//#define kBelgradeMAdaLongitude                          @"0.08239093"
#define kPaddingBottomiPhoneX                           34
#define kPaddingTopiPhoneX                              8
#define kPaddingTopSafeArea                             20
#define kInvalidOrNullValue                             @"- -"

// Notifications
#define kNotificationShowError                          @"NOTIFICATION_SHOW_ERROR"
#define kNotificationUpdateClub                         @"NOTIFICATION_UPDATE_CLUB"
#define kNotificationUpdateImageClub                    @"NOTIFICATION_UPDATE_IMAGE_CLUB"
#define kNotificationCourseOutlineRedraw                @"NOTIFICATION_COURSE_OUTLINE_REDRAW"
#define kNotificationOpenHolePreview                    @"NOTIFICATION_OPEN_HOLE_PREVIEW"
#define kNotificationUpdateScoreSimpleMode              @"NOTIFICATION_UPDATE_SCORE_SIMPLE_MODE"
#define kNotificationShowProgressHUD                    @"NOTIFICATION_SHOW_PROGRESS_HUD"
#define kNotificationDisplayError                       @"NOTIFICATION_DISPLAY_ERROR"
#define kNotificationUpdateRecentRounds                 @"NOTIFICATION_UPDATE_RECENT_ROUNDS"
#define kNotificationIncludeStatsChanged                @"NOTIFICATION_INCLUDE_STATS_CHANGED"
#define kNotificationRoundEdited                        @"NOTIFICATION_ROUND_EDITED"
#define kNotificationStopTrackingLocation               @"NOTIFICATION_STOP_TRACKING_LOCATION"
#define kNotificationTrackingLocation                   @"NOTIFICATION_TRACKING_LOCATION"
#define kNotificationUpdateSettingButton                @"NOTIFICATION_UPDATE_SETTING_BUTTON"

// Define function
#define postNotify(_notificationName, _obj, _userInfoDictionary) [[NSNotificationCenter defaultCenter] postNotificationName: _notificationName object: _obj userInfo: _userInfoDictionary];
#define addObserver(_notificationName, _observer, _observerSelector, _obj) [[NSNotificationCenter defaultCenter] addObserver:_observer selector:@selector(_observerSelector) name:_notificationName object: _obj];
#define removeObserver(_observer) [[NSNotificationCenter defaultCenter] removeObserver: _observer];
#define initTapGesture(_target, _observerSelector) [[UITapGestureRecognizer alloc] initWithTarget:_target action:@selector(_observerSelector)];

//#define IS_IPHONEX (([[UIScreen mainScreen] bounds].size.height-812)?NO:YES)
#define HAS_SAFEAREA [MRPUtils hasTopSafeArea]
#define TOP_PADDING_ERROR [MRPUtils topAdjustPaddingInsetSafeArea]
#define HEIGHT_ERROR ([MRPUtils hasTopSafeArea]) ? 74 + TOP_PADDING_ERROR : 74
#define IS_IOS11orHIGHER ([[[UIDevice currentDevice] systemVersion] floatValue] >= 11.0)

//Icon
#define kIconCheckMarkActive                            @"checkMarkActiveNew"
#define kIconCheckMarkInActive                          @"checkmarkInActiveNew"

//Deep link
#if DEV
#define BASE_MY_TM_URL                                  @"3sshf.test-app.link"
#else
#define BASE_MY_TM_URL                                  @"3sshf.app.link"
#endif
#define ROUND_SCREEN_NAME                               @"add round"
#define STATS_SCREEN_NAME                               @"stats"
#define EDIT_ROUND_SCREEN_NAME                          @"edit_round"
#define DEEPLINK_KEY_MAIN_SCREEN                        @"main_screen"
#define DEEPLINK_KEY_ROUND_ID                           @"round_id"
#define DEEPLINK_KEY_ADD_ROUND                          @"$marketing_title"

//Classic
#define FW_HIT                                          @"fw_hit"
#define FW_LEFT                                         @"fw_missed_left"
#define FW_RIGHT                                        @"fw_missed_right"

#define GR_LEFT                                         @"gr_missed_left"
#define GR_RIGHT                                        @"gr_missed_right"
#define GR_HIT                                          @"gr_hit"
#define GR_LONG                                         @"gr_missed_long"
#define GR_SHORT                                        @"gr_missed_short"
#define Classic_Fairway                                 @"Fairway"
#define Classic_Fairway_Par3                            @"Fairway N/A (Par 3)"
