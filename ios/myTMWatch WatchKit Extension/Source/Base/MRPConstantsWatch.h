//
//  MRPConstantsWatch.h
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 7/19/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#ifndef MRPConstantsWatch_h
#define MRPConstantsWatch_h

//typedef enum {
//    ModeTypeAdvanced,
//    ModeTypeBasic
//} ModeType;

#define kAppGroupName                                   @"group.com.taylormadegolf.mtmplus"
#define kCourseTypeiGolf                                @"iGolf"
#define kRoundModeAdvanced                              @"Advanced"

#define kUrlWatchBase                                   [kUserDefaultsWatch objectForKey:kUserDefaultsBaseURL]
#define mTMToken                                        [kUserDefaultsWatch objectForKey:kUserDefaultsToken]
#define kClientID                                       [kUserDefaultsWatch objectForKey:kUserDefaultsClientID]
#define mTMRefreshToken                                       [kUserDefaultsWatch objectForKey:kUserDefaultsRefreshToken]
#define mTMExpiresIn                                       [kUserDefaultsWatch objectForKey:kUserDefaultsExpiresIn]

// User Defaults
#define kUserDefaultsWatch                              [[NSUserDefaults alloc] initWithSuiteName:kAppGroupName]
#define kUserDefaultsFacebookAds                        @"USER_DEFAULTS_FACEBOOK_ADS"
#define kUserDefaultsRememberLogin                      @"USER_DEFAULTS_REMEMBER_LOGIN"
#define kUserDefaultsLoggedUserId                       @"USER_DEFAULTS_LOGGED_USER_ID"
#define kUserDefaultsUnit                               @"USER_DEFAULTS_UNIT"
#define kUserDefaultsGenderOfCourse                     @"USER_DEFAULTS_GENDER_OF_COURSE"
#define kUserDefaultsCourseOutlineDisabled              @"USER_DEFAULTS_COURSE_OUTLINE_DISABLED"
#define kUserDefaultsRangeFinderDisabled                @"USER_DEFAULTS_RANGE_FINDER_DISABLED"
#define kUserDefaultsRangefinderTooltipShowed           @"USER_DEFAULTS_RANGEFINDER_TOOLTIP_SHOWED"
#define kUserDefaultsHoleOverlayTooltipShowed           @"USER_DEFAULTS_HOLEOVERLAY_TOOLTIP_SHOWED"
#define kUserDefaultsMarkerMovementTooltipShowed        @"USER_DEFAULTS_MARKERMOVEMENT_TOOLTIP_SHOWED"
#define kUserDefaultsRoundAdded                         @"USER_DEFAULTS_ROUND_ADDED"
#define kUserDefaultsToken                              @"USER_DEFAULTS_TOKEN"
#define kUserDefaultsBaseURL                            @"USER_DEFAULTS_BASE_URL"

//#define kNotificationUpdateRoundFromScoreCard           @"NOTIFICATION_UPDATE_ROUND_FROM_SCORECARD"
//#define kNotificationUpdateRoundFromAddShot             @"NOTIFICATION_UPDATE_ROUND_FROM_ADD_SHOT"
#define kNotificationStopUpdateLocation                 @"NOTIFICATION_STOP_UPDATE_LOCATION"


#endif /* MRPConstantsWatch_h */
