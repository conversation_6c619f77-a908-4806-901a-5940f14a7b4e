//
//  MRPScorecardCell.m
//  MyRoundPro
//
//  Created by <PERSON><PERSON> on 8/6/18.
//  Copyright © 2018 myroundpro. All rights reserved.
//

#import "MRPScorecardCell.h"
#import "MRPWatchUtils.h"
#import "MRPConstants.h"

@implementation MRPScorecardCell

- (void)configStateTypeForHole:(MRPHoleModel *)hole {
    if (hole.completed) {
        NSInteger score = 0;
        if (self.round.inprogress) {
            //        self.editButton.hidden = NO;
            score = hole.score;
        } else {
            if (hole.strokes.count == 0) {
                score = hole.score;
            } else {
                score = hole.strokes.count;
            }
        }
        if(score == 0) {
            self.scoreLabel.text = @"";
            [self.parImageViewGroup setBackgroundImage:nil];
        } else {
            self.scoreLabel.text = [NSString stringWithFormat:@"%ld", (long)score];
            if(score == hole.par+1) {
                [self.parImageViewGroup setBackgroundImage:[UIImage imageNamed:@"Icon_ParBogey"]];
            } else if(score >= hole.par+2) {
                [self.parImageViewGroup setBackgroundImage:[UIImage imageNamed:@"Icon_ParDoubleBogey"]];
            } else if(score == hole.par-1) {
                [self.parImageViewGroup setBackgroundImage:[UIImage imageNamed:@"Icon_ParBirdie"]];
            } else if(score <= hole.par-2) {
                [self.parImageViewGroup setBackgroundImage:[UIImage imageNamed:@"Icon_ParDoubleAgle"]];
            } else if(score == hole.par) {
                [self.parImageViewGroup setBackgroundImage:nil];
            }
        }
    }
}

- (void)getRoundNumberOfStrokes:(NSArray *)holesArray {
    long totalStrokes = 0;
    for(MRPHoleModel *hole in holesArray) {
        if (hole.completed) {
            if (hole.strokes.count == 0) {
                totalStrokes += hole.score;
            } else {
                totalStrokes += hole.strokes.count;
            }
        }
    }
    if(totalStrokes == 0) {
        self.scoreLabel.text = @"";
    } else {
        UIFont *font = [UIFont fontWithName:kFontDINCondensedBold size:24];
        NSString * totalText = [NSString stringWithFormat:@"%ld", (long)totalStrokes];
        NSAttributedString *textAttribute = [[NSAttributedString alloc] initWithString : totalText attributes : @{NSFontAttributeName : font}];
        [self.scoreLabel setAttributedText:textAttribute];
        [self.parImageViewGroup setContentInset:UIEdgeInsetsMake(8, 0, 0, 0)];
    }
}

- (void)configOrderHole:(NSInteger)order {
    [self.orderHoleLabel setText:[MRPWatchUtils getOrdinalStringFromInteger:order]];
}

@end
