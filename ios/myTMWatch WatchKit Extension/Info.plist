<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSExtension</key>
	<dict>
		<key>NSExtensionAttributes</key>
		<dict>
			<key>WKAppBundleIdentifier</key>
			<string>com.taylormadegolf.mytaylormadeplus.ios.watchkitapp</string>
		</dict>
		<key>NSExtensionPointIdentifier</key>
		<string>com.apple.watchkit</string>
	</dict>
	<key>UIAppFonts</key>
	<array>
		<string>SFPro.ttf</string>
		<string>notches.ttf</string>
		<string>Roboto-Bold.ttf</string>
		<string>Roboto-Regular.ttf</string>
		<string>ClubHaus-Bold.ttf</string>
		<string>ClubHaus-BoldOblique.ttf</string>
		<string>ClubHaus-Oblique.ttf</string>
		<string>ClubHaus-Regular.ttf</string>
		<string>icomoon.ttf</string>
		<string>ICOMOON2.ttf</string>
		<string>TMMRPSprint2.ttf</string>
		<string>tmmrpsprint5.ttf</string>
		<string>DIN-Condensed-Bold.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>location</string>
	</array>
</dict>
</plist>
