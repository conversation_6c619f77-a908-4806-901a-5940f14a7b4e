git_url("**************:weareenvoy/mytaylormade-certs.git")

storage_mode("git")

type("development") # The default type, can be: appstore, adhoc, enterprise or development

username "<EMAIL>" #This will be the git username
shallow_clone true
force_for_new_devices true
ENV["FASTLANE_PASSWORD"] = "hYU$55DGF2$34K5hjgwewr" #Password to access git repo.
ENV["MATCH_PASSWORD"] = "123456" #Password for the .p12 files saved in git repo.
# ENV["FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"] = "ocvy-jaas-lzhz-elwl" //envoy
ENV["FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"] = "tfzj-truf-rdsu-gnpe" # <EMAIL>



# app_identifier(["tools.fastlane.app", "tools.fastlane.app2"])
# username("<EMAIL>") # Your Apple Developer Portal username

# For all available options run `fastlane match --help`
# Remove the # in the beginning of the line to enable the other options

# The docs are available on https://docs.fastlane.tools/actions/match
