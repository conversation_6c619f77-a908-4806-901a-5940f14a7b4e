import {Platform} from 'react-native';
import {subscribeIAP} from '../requests/payments';
import moment from 'moment';
import {
  getSwingCredentials,
  registerSwingPlayer,
} from '../requests/swing-index';

export const sortPurchases = purchases => {
  return purchases?.sort((a, b) => {
    // Convert timestamps to seconds
    const timestampA = a?.transactionDate / 1000;
    const timestampB = b?.transactionDate / 1000;
    return moment.unix(timestampB).isBefore(moment.unix(timestampA))
      ? -1
      : moment.unix(timestampB).isAfter(moment.unix(timestampA))
      ? 1
      : 0;
  });
};

export const subscribePurchase = async (
  purchase,
  isPurchasing,
  isCallSwingIndex,
) => {
  try {
    // Make request to check purchase receipt
    let request = await subscribeIAP(
      purchase?.transactionReceipt,
      Platform.OS === 'android' ? purchase?.signatureAndroid : '',
    );
    let isChampionProduct =
      purchase.productId === 'champion_monthly' ||
      purchase.productId === 'champion_annual' ||
      purchase.productId === 'level_one_monthly' ||
      purchase.productId === 'level_one_annual';
    let isLegendProduct =
      purchase.productId === 'legend_monthly' ||
      purchase.productId === 'legend_annual' ||
      purchase.productId === 'level_two_monthly' ||
      purchase.productId === 'level_two_annual';
    // Set interval to check receipt until it is processed
    if (Platform.OS === 'ios' && !request?.receiptProcessed) {
      const subscribeInterval = setInterval(async () => {
        request = await subscribeIAP(
          purchase?.transactionReceipt,
          Platform.OS === 'android' ? purchase?.signatureAndroid : '',
        );
        if (request?.receiptProcessed) {
          if (
            (isChampionProduct || isLegendProduct) &&
            isPurchasing &&
            isCallSwingIndex
          ) {
            await registerSwingPlayer();
            await getSwingCredentials();
          }
          clearInterval(subscribeInterval);
        }
      }, 30000);
    } else if (
      request?.receiptProcessed &&
      (isChampionProduct || isLegendProduct) &&
      isPurchasing &&
      isCallSwingIndex
    ) {
      await registerSwingPlayer();
      await getSwingCredentials();
    }
  } catch (error) {
    throw error;
  }
};

export const displayCurrency = number => {
  const with2Decimals = number?.toString().match(/^-?\d+(?:\.\d{0,2})?/)[0];
  return with2Decimals;
};
