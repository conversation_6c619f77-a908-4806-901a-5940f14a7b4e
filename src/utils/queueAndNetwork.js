import NetInfo from '@react-native-community/netinfo';
import {t} from 'i18next';
import {completeHole} from 'requests/add-round';
import {queueFactory} from 'utils/queue/Queue';
import {
  getInprogressHoleSubmitArray,
  setInprogressHoleSubmitArray,
} from './commonVariable';
import {
  deleteDataRound,
  getRoundAndHole,
  submitHoleOffline,
} from './realmHelper';
import {showErrorToast} from './toast';

let subscribe = null;
let queue = null;
let isConnect = false;
export const monitorNetworkAndSyncRoundData = () => {
  // Cancel any existing network listeners
  unsubscribe();

  // Register new network state listener
  subscribe = NetInfo.addEventListener(async state => {
    if (state.isInternetReachable && !isConnect) {
      // When internet becomes available
      isConnect = true;
      // Fetch latest data
      const data = await getRoundAndHole();
      if (data) {
        // Update rounds with new data
        createWorkUpdateRound(data, true);
      }
    } else if (!state.isInternetReachable) {
      // When internet becomes unavailable
      isConnect = false;
    }
  });
};
const unsubscribe = () => {
  if (subscribe) {
    subscribe();
    subscribe = null;
  }
};

export const isConnectedNetwork = () => {
  return isConnect;
};

const initQueue = async () => {
  if (queue) {
    return queue;
  }
  queue = await queueFactory();
  addWorkUpdateRound();
  return queue;
};

const addWorkUpdateRound = () => {
  if (queue) {
    queue.addWorker('update-round', async (id, payload) => {
      try {
        let {completed, inprogress, ...roundPayload} = payload?.round;

        const response = await completeHole(payload?.roundId, roundPayload);
        if (response) {
          let inprogressHoleArray = getInprogressHoleSubmitArray();
          //Remove the hole data with holeId when the submitting was completed successfully
          inprogressHoleArray = inprogressHoleArray.filter(
            item => item.id !== payload?.holeId,
          );
          setInprogressHoleSubmitArray(inprogressHoleArray);
          await deleteDataRound();
        }
      } catch (error) {
        const reCheckConnect = isConnectedNetwork();
        if (!reCheckConnect) {
          await submitHoleOffline(payload);
        }
      }
    });
  }
};

export const createWorkUpdateRound = async (data, isReconnect = false) => {
  if (!queue) {
    await initQueue();
  }
  const isConnected = isConnectedNetwork();
  if (!isConnected) {
    await submitHoleOffline(data);
  } else if (queue) {
    if (!isReconnect) {
      //only add the hole data to inprogressHoleArray when internet is available
      let inprogressHoleArray = getInprogressHoleSubmitArray();
      //Add the hole data to inprogressHole array
      inprogressHoleArray.push({
        holes: {...data?.round.holes?.[0]},
        id: data?.holeId,
      });
    }
    queue.createJob('update-round', data);
  }
};

export const removeWorkUpdateRound = () => {
  if (queue) {
    queue.flushQueue('update-round');
  }
};

export const showToastErrorInternet = () => {
  showErrorToast({
    error: {},
    title: t('play.no_internet'),
  });
};
