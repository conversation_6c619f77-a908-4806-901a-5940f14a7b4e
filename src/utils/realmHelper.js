import Realm from 'realm';
import { getModePlayed } from './commonVariable';
export let realm: Realm = null;
export const initRealm = async () => {
  if (realm) {
    return realm;
  }
  realm = await Realm.open({
    path: 'AddRoundRealm',
    schema: [RoundData, HoleData, JobSchema, PlayerData, CoursePlaying],
    schemaVersion: 8,
  });
  return realm;
};

const RoundData = {
  name: 'Round',
  properties: {
    _id: 'string',
    round: 'string',
    roundId: 'int',
  },
  primaryKey: '_id',
};
const HoleData = {
  name: 'Hole',
  properties: {
    _id: 'string',
    hole: 'string',
  },
  primaryKey: '_id',
};
const CoursePlaying = {
  name: 'CoursePlaying',
  properties: {
    id: 'int',
    courseGPS: 'string?',
    courseDetail: 'string?',
    teeSelected: 'string?',
    scoreCardCourse: 'string?',
    roundData: 'string?',
    statsClub: 'string?',
    listMarkers: 'string?',
    bagList: 'string?',
    selectHole: 'int',
    clubs: 'string?',
    isFinder: {type: 'bool', default: true},
    isPracticeMode: {type: 'bool', default: false},
    listHoleEdited: 'string?',
    mapMode: 'string?',
  },
  primaryKey: 'id',
};
const JobSchema = {
  name: 'Job',
  primaryKey: 'id',
  properties: {
    id: 'string', // UUID.
    name: 'string', // Job name to be matched with worker function.
    payload: 'string', // Job payload stored as JSON.
    data: 'string', // Store arbitrary data like "failed attempts" as JSON.
    priority: 'int', // -5 to 5 to indicate low to high priority.
    active: {type: 'bool', default: false}, // Whether or not job is currently being processed.
    timeout: 'int', // Job timeout in ms. 0 means no timeout.
    created: 'date', // Job creation timestamp.
    failed: 'date?', // Job failure timestamp (null until failure).
  },
};
const PlayerData = {
  name: 'Player',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'string',
    initials: 'string?',
    strokes: 'int',
    createAt: 'date',
  },
};

//Add hole data to realm table for submitting the round data when the internet is available
export const submitHoleOffline = async dataSubmit => {
  if (!realm) {
    await initRealm();
  }
  if (realm && dataSubmit) {
    const roundData = realm.objectForPrimaryKey(
      'Round',
      dataSubmit?.round?.igolf_course_id,
    );
    realm.write(() => {
      if (roundData) {
        // update
        roundData.round = JSON.stringify({...dataSubmit?.round, holes: []});
      } else {
        // add
        const roundParse = JSON.stringify({...dataSubmit?.round, holes: []});
        realm.create('Round', {
          _id: dataSubmit?.round?.igolf_course_id,
          round: roundParse,
          roundId: dataSubmit?.roundId,
        });
      }
      dataSubmit?.round?.holes?.forEach(_val => {
        const holeData = realm.objectForPrimaryKey(
          'Hole',
          _val.number + dataSubmit?.round?.igolf_course_id,
        );
        if (holeData) {
          // update
          holeData.hole = JSON.stringify(_val);
        } else {
          // add
          realm.create('Hole', {
            _id: _val.number + dataSubmit?.round?.igolf_course_id,
            hole: JSON.stringify(_val),
          });
        }
      });
    });
  }
};

export const getRoundAndHole = async () => {
  if (!realm) {
    await initRealm();
  }
  if (realm) {
    const roundData = realm.objects('Round');
    const holeData = realm.objects('Hole');
    if (roundData?.length > 0 && holeData?.length > 0) {
      const roundParse = JSON.parse(roundData[0].round);
      let holes = [];
      for (let i = 0; i < holeData?.length; i++) {
        const holeParse = JSON.parse(holeData[i].hole);
        holes = [...holes, holeParse];
      }
      return {
        roundId: roundData[0].roundId,
        round: {...roundParse, holes},
      };
    }
  }
  return null;
};

export const deleteDataRound = async () => {
  if (!realm) {
    await initRealm();
  }

  if (realm) {
    const round = realm.objects('Round');
    if (round?.length > 0) {
      realm.write(() => {
        realm.delete(realm.objects('Round'));
        realm.delete(realm.objects('Hole'));
      });
    }
  }
};

export const addUpdatePlayer = async data => {
  if (!realm) {
    await initRealm();
  }
  let player;
  if (realm) {
    realm.write(() => {
      player = realm.objects('Player').filtered(`id == "${data.id}"`)[0];
      if (player) {
        player.name = data.name;
        player.initials = data.initials;
        player.strokes = data.strokes;
      } else {
        realm.create('Player', data);
      }
    });
  }
};

export const getAllPlayer = async () => {
  if (!realm) {
    await initRealm();
  }
  if (realm) {
    const player = realm.objects('Player');
    return JSON.parse(JSON.stringify(player));
  }
};

export const deletePlayer = async id => {
  if (!realm) {
    await initRealm();
  }
  if (realm) {
    realm.write(() => {
      const player = realm.objects('Player').filtered(`id == "${id}"`)[0];
      realm.delete(player);
    });
  }
};

export const saveCoursePlaying = async dataCache => {
  if (!realm) {
    await initRealm();
  }
  if (realm && dataCache) {
    try {
      const courseData = realm.objectForPrimaryKey(
        'CoursePlaying',
        dataCache.idRound,
      );
      realm.write(() => {
        if (courseData) {
          // update
          if (dataCache?.roundData) {
            courseData.roundData = JSON.stringify({
              ...dataCache?.roundData,
              round_mode: getModePlayed(),
            });
          }
          if (dataCache?.listMarkers) {
            courseData.listMarkers = JSON.stringify(dataCache?.listMarkers);
          }
          if (dataCache?.selectHole) {
            courseData.selectHole = dataCache.selectHole;
          }
          if (dataCache?.isFinder) {
            courseData.isFinder = dataCache.isFinder;
          }
          courseData.isPracticeMode = dataCache.isPracticeMode === true;
          if (dataCache?.clubs) {
            courseData.clubs = JSON.stringify(dataCache?.clubs);
          }
          if (dataCache?.listHoleEdited) {
            courseData.listHoleEdited = JSON.stringify(
              dataCache?.listHoleEdited,
            );
          }
          if (dataCache?.mapMode !== undefined) {
            courseData.mapMode = dataCache.mapMode;
          }
        } else {
          // add
          const courseGPS = JSON.stringify(dataCache?.courseGPS);
          const courseDetail = JSON.stringify(dataCache?.courseDetail);
          const teeSelected = JSON.stringify(dataCache?.teeSelected);
          const scoreCardCourse = JSON.stringify(dataCache?.scoreCardCourse);
          const statsClub = dataCache?.statsClub
            ? JSON.stringify(dataCache?.statsClub)
            : '';
          const bagList = dataCache?.bagList
            ? JSON.stringify(dataCache?.bagList)
            : '';
          const roundData = JSON.stringify({
            ...dataCache?.roundData,
            round_mode: getModePlayed(),
          });
          const listMarkers = dataCache?.listMarkers
            ? JSON.stringify(dataCache?.listMarkers)
            : '';
          const listHoleEdited = dataCache?.listHoleEdited
            ? JSON.stringify(dataCache?.listHoleEdited)
            : '';

          const selectHole = dataCache?.selectHole;
          const clubs = dataCache?.clubs
            ? JSON.stringify(dataCache?.clubs)
            : '';
          const isFinder = dataCache?.isFinder;
          const isPracticeMode = dataCache?.isPracticeMode;
          const mapMode = dataCache?.mapMode || '';
          const dataParse = {
            id: dataCache.idRound,
            courseGPS,
            courseDetail,
            teeSelected,
            scoreCardCourse,
            statsClub,
            bagList,
            roundData,
            listMarkers,
            selectHole,
            listHoleEdited,
            clubs,
            isFinder,
            isPracticeMode,
            mapMode,
          };
          realm.create('CoursePlaying', dataParse);
        }
      });
    } catch (error) {
      console.log('Error', error);
    }
  }
};

export const getCoursePlaying = async () => {
  if (!realm) {
    await initRealm();
  }
  if (realm) {
    const courseInfo = realm.objects('CoursePlaying');
    if (courseInfo?.length > 0) {
      const data = courseInfo[0];
      return {
        idRound: data?.id,
        courseGPS: JSON.parse(data?.courseGPS),
        courseDetail: JSON.parse(data?.courseDetail),
        teeSelected: JSON.parse(data?.teeSelected),
        scoreCardCourse: JSON.parse(data?.scoreCardCourse),
        statsClub: data?.statsClub ? JSON.parse(data?.statsClub) : null,
        bagList: data?.bagList ? JSON.parse(data?.bagList) : [],
        roundData: JSON.parse(data?.roundData),
        listMarkers: data?.listMarkers ? JSON.parse(data?.listMarkers) : null,
        selectHole: data?.selectHole,
        listHoleEdited: data?.listHoleEdited
          ? JSON.parse(data?.listHoleEdited)
          : [],
        isFinder: data?.isFinder,
        isPracticeMode: data?.isPracticeMode,
        clubs: data?.clubs ? JSON.parse(data?.clubs) : null,
        mapMode: data?.mapMode || '',
      };
    }
    return null;
  }
  return null;
};

export const deleteCoursePlaying = async () => {
  if (!realm) {
    await initRealm();
  }
  if (realm) {
    realm.write(() => {
      realm.delete(realm.objects('CoursePlaying'));
    });
  }
};
