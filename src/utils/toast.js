import Toast from 'react-native-toast-message';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ENVIRONMENTS} from '../config/env';

export const showToast = ({
  ref,
  type = 'error',
  message,
  subText,
  onHide,
  topOffset,
}) => {
  const toastMessage = ref ? ref.current : Toast;
  toastMessage.show({
    type,
    topOffset: topOffset ? topOffset : hp('5%'),
    text1: message,
    text2: subText,
    onHide: onHide,
  });
};

export const showErrorToast = async ({
  ref,
  error,
  title,
  description,
  onHide,
  topOffset,
}) => {
  const env = await AsyncStorage.getItem('env');
  const toastMessage = ref ? ref.current : Toast;
  const errorMessage =
    ((error?.response?.data?.errorMessage ||
      error?.response?.data?.message ||
      error?.response?.data?.fault?.message) &&
    env === ENVIRONMENTS.PROD
      ? description
      : error?.response?.data?.errorMessage ||
        error?.response?.data?.message ||
        error?.response?.data?.fault?.message) || description;

  toastMessage.show({
    type: 'error',
    topOffset: topOffset ? topOffset : hp('5%'),
    text1: title,
    text2: errorMessage,
    onHide: onHide,
  });
};
