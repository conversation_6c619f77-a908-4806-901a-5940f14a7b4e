import {getBagData} from 'screens/MyBag/controller/ControllerMyBag';
import {getIdToken} from './user';

export const getUserInfoForWatch = async user => {
  const idToken = await getIdToken();
  const golferProfile = user?.golferProfile;
  return {
    iGolfHomeCourseID: golferProfile?.iGolfCourseId || '',
    acceptedPrivacyOn: user?.acceptedPrivacyOn || '',
    acceptedTermsOn: user?.acceptedTermsOn || '',
    avatar: user?.avatar || '',
    birthday: user?.dob || '',
    country: user?.userCountry || '',
    createdAt: user?.createdAt || '',
    email: user?.email || '',
    gender: user?.gender || '',
    handed: golferProfile?.handed || '',
    handicap:
      golferProfile?.newHandicap?.tmCalculatedHandicap ||
      golferProfile?.newHandicap?.userInputHandicap ||
      '',
    userId: user.id || '',
    name: user?.lastName || '',
    first_name: user?.firstName || '',
    unitInfo: user?.measurementUnits?.toLowerCase?.() || 'yards',
    strokesGainedBaseline: golferProfile?.strokesGainedBaseline || '',
    token: idToken,
    home_course_name: golferProfile?.homeCourse || '',
  };
};

export const getClubInfoForWatch = (clubCategories, bagList, playService) => {
  try {
    return (
      getBagData(true, clubCategories, bagList, playService, true)
        .flatMap?.(item => item.contentByStatus)
        .map?.(item => {
          return {
            club_family: item?.clubFamily?.name || '',
            club_type: item?.clubType?.type || '',
            disabled: item?.disable,
            face_lie_adjustment: item?.faceLoftAdjustment?.value || '',
            face_loft_adjustment: item?.faceLoftAdjustment?.value || '',
            id: item?.id || '',
            in_bag: item?.inBag,
            loft: item?.loft?.value || '',
            manufacturer: item?.manufacturer?.name || '',
            modelname: item?.modelName?.name || '',
            shaft_flex: item?.shaftFlex?.value || '',
            self: '',
            user: '',
          };
        }) || []
    );
  } catch (error) {
    console.log('error get club for watch', error?.message);
  }
};
