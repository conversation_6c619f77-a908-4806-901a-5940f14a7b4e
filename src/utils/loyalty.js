import {initialStateLoyalty, updateLoyaltyData} from 'reducers/loyalty';
import {
  getUserTier,
  getUserPoints,
  getTierList,
  getUserCompletedActions,
} from 'requests/loyalty';
import {getLoyaltyActions, getRewardProducts} from 'requests/rewards';
import {useSelector} from 'react-redux';
import {GA_setUserProperty} from './googleAnalytics';
import {CACHE_KEY, checkMainCountry} from './constant';

export const updateRewardProducts = async (dispatch, country) => {
  try {
    const isMainCountry = checkMainCountry(country);
    if (isMainCountry) {
      const rewardProducts = await getRewardProducts();
      dispatch(
        updateLoyaltyData({
          rewardProducts:
            rewardProducts && rewardProducts?.length > 0 ? rewardProducts : [],
        }),
      );
    }
  } catch (error) {
    dispatch(
      updateLoyaltyData({
        rewardProducts: [],
      }),
    );
  }
};

export const updateUserLoyaltyData = async (
  dispatch,
  userCountry,
  loyaltyState,
  appCacheVersions,
) => {
  try {
    const isMainCountry = checkMainCountry(userCountry);
    if (!isMainCountry) {
      return;
    }
    const currentLoyaltyActionsCacheVersion =
      appCacheVersions?.features?.find?.(
        item => item.key === CACHE_KEY.REWARD_LOYALTY_ACTIONS,
      )?.version;
    let apiRequests = [
      getUserTier(),
      getUserPoints(),
      getTierList(),
      getUserCompletedActions(),
    ];
    if (loyaltyState && appCacheVersions) {
      const isLoyaltyDataValid =
        loyaltyState?.loyaltyActions?.version ===
          currentLoyaltyActionsCacheVersion &&
        loyaltyState?.loyaltyActions?.country === appCacheVersions?.country &&
        currentLoyaltyActionsCacheVersion != null;
      if (!isLoyaltyDataValid) {
        apiRequests.push(getLoyaltyActions({country: userCountry}));
      }
    }
    let [tier, points, tierAll, completedActions, loyaltyActions] =
      await Promise.all(apiRequests);
    if (completedActions) {
      dispatch(
        updateLoyaltyData({
          completedActions,
        }),
      );
    } else {
      dispatch(
        updateLoyaltyData({
          completedActions: [],
        }),
      );
    }
    GA_setUserProperty(
      'loyalty_member_level',
      tier?.currentTier || initialStateLoyalty.tier?.currentTier,
    );
    GA_setUserProperty(
      'points_balance',
      (points?.availablePoints || initialStateLoyalty.points?.availablePoints) +
        '',
    );
    dispatch(
      updateLoyaltyData({
        tier: tier?.currentTier ? tier : initialStateLoyalty.tier,
        points: points?.totalSpent >= 0 ? points : initialStateLoyalty.points,
        tierAll: tierAll?.tierDetail
          ? tierAll?.tierDetail
          : initialStateLoyalty.tierAll,
      }),
    );
    if (loyaltyActions) {
      dispatch(
        updateLoyaltyData({
          loyaltyActions: {
            data: loyaltyActions?.length > 0 ? loyaltyActions : [],
            country: userCountry,
            version: currentLoyaltyActionsCacheVersion,
          },
        }),
      );
    }
  } catch (error) {
    dispatch(updateLoyaltyData(initialStateLoyalty));
  }
};
