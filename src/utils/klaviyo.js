import axios from 'axios';
import {getConfig, ENVIRONMENTS} from 'config/env';
import {Platform} from 'react-native';
import {get} from 'lodash';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {checkMainCountry, COUNTRY_CODE} from '../utils/constant';
import {getCountry} from 'utils/commonVariable';

export const addPushDeviceToken = async (email, token, userCountry) => {
  if (!checkMainCountry(userCountry)) {
    return;
  }
  const apiPublicKey = await getConfig('PUBLIC_KEY_KLAVIYO');
  const url = `https://a.klaviyo.com/client/push-tokens?company_id=${apiPublicKey}`;

  const data = {
    type: 'push-token',
    attributes: {
      token: token,
      platform: Platform.OS,
      vendor: Platform.OS === 'ios' ? 'apns' : 'fcm',
      profile: {
        data: {
          type: 'profile',
          attributes: {
            email: email,
          },
        },
      },
    },
  };

  const options = {
    method: 'POST',
    headers: {
      accept: 'application/json',
      revision: '2024-07-15',
      'content-type': 'application/json',
    },
    data: {data: data},
  };

  try {
    const response = await axios(url, options);
  } catch (error) {
    console.error('Error pushing device token:', error);
  }
};

export const sendUserToKlaviyo = async (payload, userCountry) => {
  try {
    if (!checkMainCountry(userCountry)) {
      return;
    }
    let env = await AsyncStorage.getItem('env');
    let listID = 'TCdg9x';
    if (getCountry() === COUNTRY_CODE.CAN) {
      if (env === ENVIRONMENTS.PROD) {
        listID = 'Xx6Sed';
      } else {
        listID = 'TCdg9x';
      }
    } else {
      if (env === ENVIRONMENTS.PROD) {
        listID = 'XAZyNM';
      } else {
        listID = 'TCdg9x';
      }
    }
    const private_key = await getConfig('PRIVATE_KEY_KLAVIYO');
    const request = await axios({
      url: `https://a.klaviyo.com/api/profile-subscription-bulk-create-jobs/`,
      method: 'POST',
      headers: {
        accept: 'application/json',
        revision: '2023-02-22',
        'content-type': 'application/json',
        Authorization: `Klaviyo-API-Key ${private_key}`,
      },
      data: {
        data: {
          type: 'profile-subscription-bulk-create-job',
          attributes: {
            list_id: listID,
            custom_source: 'app',
            subscriptions: [
              {
                //channels: {email: ['MARKETING'], sms: ['MARKETING']},
                email: payload,
                // phone_number: '+15005550006',
                // profile_id: '01GDDKASAP8TKDDA2GRZDSVP4H',
              },
            ],
          },
        },
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
