import { checkMainCountry } from './constant';
import {isOtherPayment, openOtherPayment} from './user';

export const navigateToDeepLink = async (
  deepLink,
  navigate,
  setDeepLink,
  permission,
  user,
) => {
  if (deepLink.includes('redirectUrl:')) {
    navigate('WebView', {
      screen: 'WebView',
      params: {
        uri: deepLink.replace('redirectUrl:', ''),
        canGoBack: true,
      },
    });
    setDeepLink('');
    return;
  }
  // Navigate to screen associated with deep link
  switch (deepLink) {
    case 'gameProfile':
      navigate('Profile', {screen: 'MyGameProfile'});
      setDeepLink('');
      break;
    case 'fittings':
      if (checkMainCountry(user?.userCountry)) {
        navigate('Fittings', {screen: 'Fittings'});
      }
      setDeepLink('');
      break;
    case 'mybag':
      navigate('MyBag', {screen: 'MyBagScreens'});
      setDeepLink('');
      break;
    case 'postScore':
      navigate('ScoresStats', {screen: 'ScoreAddCourse'});
      setDeepLink('');
      break;
    case 'club-recommender':
      if (checkMainCountry(user?.userCountry)) {
        navigate('ClubRecommender', {screen: 'ClubWelcome'});
      }
      setDeepLink('');
      break;
    case 'compare-club':
      navigate('MyBag', {screen: 'CompareClubs'});
      setDeepLink('');
      break;
    case 'launchHomeScreen':
    case 'handicap':
      navigate('Home');
      setDeepLink('');
      break;
    case 'shop':
      navigate('App', {
        screen: 'ShopTabScreen',
      });
      setDeepLink('');
      break;
    case 'play':
    case 'viewScores':
    case 'stats':
      navigate('App', {screen: 'Play'});
      setDeepLink('');
      break;
    case 'usga':
      if (checkMainCountry(user?.userCountry)) {
        navigate('Profile', {screen: 'HandicapUSGA'});
      }
      setDeepLink('');
      break;
    case 'address':
      navigate('ResetPassword', {screen: 'ShippingAddress'});
      setDeepLink('');
      break;
    case 'referral-email':
      navigate('Profile', {screen: 'ReferralInputEmail'});
      setDeepLink('');
      break;
    case 'profile':
      navigate('Profile');
      setDeepLink('');
      break;
    case 'articles':
    case 'drills':
      navigate('ClubHouse');
      setDeepLink('');
      break;
    case 'rewards':
      navigate('Rewards');
      setDeepLink('');
      break;
    case 'tour-trash':
    case 'perks':
      navigate('Rewards');
      setDeepLink('');
      break;
    case 'quiz':
      navigate('IntroQuiz', {
        screen: 'UserQuiz',
      });
      setDeepLink('');
      break;
    default:
      break;
  }
};
