import React from 'react';
import {View, StyleSheet, Image, Platform, ImageBackground} from 'react-native';
const {moderateScale} = require('react-native-size-matters');
import {
  heightPercentageToDP,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import Text from 'components/Text/Text';

const {
  GAME_TYPE_MULTIPLAYER,
  MATCH_TEAM_NAME,
} = require('screens/PlayCourseMap/DataSubmitDefault');
const {
  getSkins,
  getStrokes,
  getMatchScores,
  getMatchScoresDetail,
  getSkinsDetail,
  getStrokesDetail,
} = require('./scorecard');
const BackgroundWinner = require('assets/imgs/background-winner.png');
const BackgroundMatchWinner = require('assets/imgs/bg-match-winner.png');
import MedalOneIcon from 'assets/imgs/medal-one.svg';
import IconSadFace from 'assets/imgs/icon-sad-face.svg';
import appStyles from '../styles/global';

export const renderWinnerCompleteBoard = (
  scoreDetail,
  isDetailScreen = false,
) => {
  const gameType = scoreDetail?.multiplayer_game_type;
  const gamePlayers = isDetailScreen
    ? scoreDetail?.player
    : scoreDetail?.players;
  if (gameType === GAME_TYPE_MULTIPLAYER.SKINS) {
    let skins = getSkins(gamePlayers, scoreDetail?.holes, isDetailScreen);
    skins?.sort((a, b) => {
      return b.skin - a.skin;
    });
    return (
      <View style={styles.winnerBoard}>
        {skins?.map((item, index) => {
          return (
            <ImageBackground
              style={styles.rowPlayerScore}
              source={index === 0 ? BackgroundWinner : undefined}
            >
              <View style={[appStyles.row, styles.innerRowPlayer]}>
                <View style={styles.playerPlaceView}>
                  {index === 0 ? (
                    <MedalOneIcon />
                  ) : (
                    <View style={styles.placeCircle}>
                      <Text black style={styles.prizeText}>
                        {index + 1}
                      </Text>
                    </View>
                  )}
                </View>
                <View style={[appStyles.row, styles.skinScoreView]}>
                  <Text black style={styles.textSkin}>
                    {item.skin}
                  </Text>
                </View>
                <Text
                  style={[
                    styles.textPlayerName,
                    {color: index === 0 ? 'rgba(17, 17, 17, 1)' : '#8C8B8F'},
                  ]}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {item.player?.player_name}
                </Text>
              </View>
            </ImageBackground>
          );
        })}
      </View>
    );
  }
  if (gameType === GAME_TYPE_MULTIPLAYER.STROKE) {
    let strokes = getStrokes(gamePlayers, scoreDetail?.holes, isDetailScreen);
    strokes?.sort((a, b) => {
      return a.stroke - b.stroke;
    });
    return (
      <View style={styles.winnerBoard}>
        {strokes?.map((item, index) => {
          return (
            <ImageBackground
              style={styles.rowPlayerScore}
              source={index === 0 ? BackgroundWinner : undefined}
            >
              <View style={[appStyles.row, styles.innerRowPlayer]}>
                <View style={styles.playerPlaceView}>
                  {index === 0 ? (
                    <MedalOneIcon />
                  ) : (
                    <View style={styles.placeCircle}>
                      <Text black style={styles.prizeText}>
                        {index + 1}
                      </Text>
                    </View>
                  )}
                </View>
                <View style={[appStyles.row, styles.strokeScoreView]}>
                  <Text black style={styles.textSkin}>
                    {item.hole_score}
                  </Text>
                  <Text black style={styles.textNetStroke}>
                    {item.stroke}
                  </Text>
                </View>
                <Text
                  style={[
                    styles.textPlayerName,
                    {color: index === 0 ? 'rgba(17, 17, 17, 1)' : '#8C8B8F'},
                  ]}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {item.player?.player_name}
                </Text>
              </View>
            </ImageBackground>
          );
        })}
      </View>
    );
  }
  if (gameType === GAME_TYPE_MULTIPLAYER.MATCH) {
    const matchScores = getMatchScores(scoreDetail?.holes, isDetailScreen);
    const matchScore = matchScores[matchScores?.length - 1];
    const teamOne =
      gamePlayers?.filter(player => player.team === MATCH_TEAM_NAME.TEAM1) ||
      [];
    const teamTwo =
      gamePlayers?.filter(player => player.team === MATCH_TEAM_NAME.TEAM2) ||
      [];
    const hasOnlyTwoPlayers = gamePlayers?.length === 2 ? true : false;
    const hasNoTeamWin =
      (matchScore.skipHole === 0 && !matchScore.teamWin) ||
      matchScore.scoreTeam1 === matchScore.scoreTeam2;
    if (!matchScore?.teamWin) {
      matchScore.teamWin = matchScore.tempTeamWin;
      matchScore.gameWinScore = Math.abs(
        matchScore.scoreTeam1 - matchScore.scoreTeam2,
      );
    }

    return (
      <View>
        {[...Array(2)].map((team, index) => {
          const currentTeam = index === 0 ? teamOne : teamTwo;
          return (
            <View style={[styles.winnerBoard, appStyles.row]}>
              <View
                style={{
                  backgroundColor: index === 0 ? '#FFCC00' : '#3E4D54',
                  width: 10,
                }}
              />
              <View style={{flex: 1, justifyContent: 'center'}}>
                {currentTeam?.map(item => {
                  return (
                    <View
                      style={[
                        styles.rowPlayerScore,
                        {borderBottomWidth: currentTeam?.length > 1 ? 1 : 0},
                      ]}
                    >
                      <Text
                        style={[
                          appStyles.black,
                          styles.textPlayerName,
                          {marginLeft: 20},
                        ]}
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {item.player_name}
                      </Text>
                    </View>
                  );
                })}
              </View>
              {hasNoTeamWin ? (
                <View
                  style={[
                    appStyles.hCenter,
                    appStyles.vCenter,
                    styles.loseTeam,
                  ]}
                >
                  <Text
                    style={[appStyles.sm, {textAlign: 'center', marginTop: 8}]}
                  >
                    play.complete_round.all_square
                  </Text>
                </View>
              ) : currentTeam[0].team === matchScore.teamWin ? (
                renderWinnerMatch(matchScore.gameWinScore, hasOnlyTwoPlayers)
              ) : (
                renderLoseMatch()
              )}
            </View>
          );
        })}
      </View>
    );
  }
};

export const renderWinnerMatch = (score, hasOneWinner = false) => {
  return (
    <ImageBackground
      style={[appStyles.hCenter, appStyles.vCenter, styles.winnerRightBox]}
      source={BackgroundMatchWinner}
    >
      <MedalOneIcon />
      <Text style={[appStyles.black, appStyles.smm]}>
        {hasOneWinner ? 'Winner!' : 'Winners!'}
      </Text>
      <Text style={[appStyles.black, styles.textGameMatchWinScore]}>
        {score}
      </Text>
    </ImageBackground>
  );
};
export const renderLoseMatch = () => {
  return (
    <View style={[appStyles.hCenter, appStyles.vCenter, styles.loseTeam]}>
      <IconSadFace />
      <Text
        style={[
          appStyles.black,
          appStyles.xs,
          {textAlign: 'center', marginTop: 8},
        ]}
      >
        play.complete_round.better_luck_next_time
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  winnerBoard: {
    marginHorizontal: moderateScale(10),
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#ffffff',
    marginTop: 10,
  },
  rowPlayerScore: {
    flexDirection: 'row',
    alignItems: 'center',
    height: moderateScale(57),
    borderBottomColor: '#EBEBEB',
    borderBottomWidth: 1,
    backgroundColor: '#ffffff',
  },
  playerPlaceView: {
    width: 34,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeCircle: {
    borderWidth: 1.5,
    borderRadius: moderateScale(9),
    width: moderateScale(18),
    height: moderateScale(18),
    justifyContent: 'center',
    alignItems: 'center',
  },
  prizeText: {
    textAlign: 'center',
    textAlignVertical: 'center',
    fontSize: 11.5,
    fontWeight: '400',
    position: 'absolute',
  },
  textSkin: {
    fontWeight: '700',
    fontSize: 24,
    lineHeight: 28.64,
  },
  textNetStroke: {
    fontWeight: '400',
    fontSize: 17,
    marginLeft: 4,
    lineHeight: 20.29,
    paddingBottom: 2,
  },
  textPlayerName: {
    marginLeft: 6,
    fontWeight: '700',
    fontSize: 24,
    maxWidth: wp(65),
  },
  textGameMatchWinScore: {
    fontSize: 17,
    letterSpacing: 3,
    fontWeight: '700',
  },
  loseTeam: {
    width: moderateScale(95),
    paddingHorizontal: 15,
    borderLeftWidth: 1,
    borderColor: '#EBEBEB',
    minHeight: 100,
  },
  innerRowPlayer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  skinScoreView: {
    alignItems: 'flex-end',
    justifyContent: 'center',
    minWidth: 40,
  },
  strokeScoreView: {
    alignItems: 'flex-end',
    justifyContent: 'center',
    minWidth: 72,
  },
  winnerRightBox: {
    minHeight: 100,
    minWidth: 100,
  },
});
