import { s } from 'react-native-size-matters';
import {MATCH_TEAM_NAME} from 'screens/PlayCourseMap/DataSubmitDefault';
import {SYMBOL} from 'utils/constant';

export const COLOR_TEAM_FIRST = '#FFCC00';
export const COLOR_TEAM_SECOND = '#3E4D54';
export const UNDEFINED_VALUE = SYMBOL.DOUBLE_DASH;

export const getStyleFromTeam = team => {
  const backgroundColor =
    team === MATCH_TEAM_NAME.TEAM1 ? COLOR_TEAM_FIRST : COLOR_TEAM_SECOND;
  const triangleColor =
    team === MATCH_TEAM_NAME.TEAM1
      ? 'rgba(0, 0, 0, 0.1)'
      : 'rgba(255, 255, 255, 0.2)';
  const textColor = team === MATCH_TEAM_NAME.TEAM2 ? '#ffffff' : '#000000';
  return {backgroundColor, triangleColor, textColor};
};
export const getSkins = (players, holesScore, isDetailScreen = false) => {
  let initSkins = players?.map(player => {
    return {player, skin: 0};
  });
  let stackScore = 1;
  for (const scores of holesScore) {
    const playerScores = scores.player_score;
    if (isDetailScreen) {
      if (playerScores?.length > 0) {
        const holeWinner = playerScores.reduce((prev, cur) =>
          cur.adjusted_score < prev.adjusted_score ? cur : prev,
        );
        const isHoleWin = playerScores.filter(
          player => player.adjusted_score === holeWinner.adjusted_score,
        );
        const isDraw = isHoleWin?.length > 1;
        if (!isDraw) {
          initSkins?.forEach(element => {
            if (element.player.player_id === isHoleWin[0].player_id) {
              element.skin = element.skin + stackScore;
              stackScore = 1;
            }
          });
        } else {
          stackScore += 1;
        }
      }
    } else {
      if (playerScores) {
        const holeWinner = playerScores.reduce((prev, cur) =>
          cur.adjusted_score < prev.adjusted_score ? cur : prev,
        );
        const isHoleWin = playerScores.filter(
          player => player.adjusted_score === holeWinner.adjusted_score,
        );
        const isDraw = isHoleWin?.length > 1;
        if (!isDraw) {
          initSkins?.forEach(element => {
            if (element.player.player_id === isHoleWin[0].player_id) {
              element.skin = element.skin + stackScore;
              stackScore = 1;
            }
          });
        } else {
          stackScore += 1;
        }
      }
    }
  }
  return initSkins;
};

export const getStrokes = (players, holesScore, isDetailScreen = false) => {
  let strokes = players.map(player => {
    return {player, stroke: 0, hole_score: 0};
  });
  for (const scores of holesScore) {
    const playerScores = scores.player_score;
    if (isDetailScreen) {
      if (playerScores?.length > 0) {
        playerScores.map(player => {
          strokes.forEach(element => {
            if (element.player.player_id === player.player_id) {
              element.stroke = element.stroke + player.adjusted_score;
              element.hole_score = element.hole_score + player.hole_score;
            }
          });
        });
      }
    } else {
      if (playerScores) {
        playerScores.map(player => {
          strokes.forEach(element => {
            if (element.player.player_id === player.player_id) {
              element.stroke = element.stroke + player.adjusted_score;
              element.hole_score = element.hole_score + player.hole_score;
            }
          });
        });
      }
    }
  }
  return strokes;
};

export const getMatchScores = (holesScore, isDetailScreen = false) => {
  let scoreTeam1 = 0;
  let scoreTeam2 = 0;
  let initMatchScores = [];
  let skipHole = 0;
  let teamWin = null;
  let tempTeamWin = null;
  let gameWinScore = '';
  for (const scores of holesScore) {
    const playerScores = scores.player_score;
    const holeNumber = scores.number;
    if (isDetailScreen) {
      if (playerScores?.length > 0) {
        const holeWinScore = playerScores.reduce((prev, cur) =>
          cur.adjusted_score < prev.adjusted_score ? cur : prev,
        );
        const isHoleWin = playerScores.filter(
          player => player.adjusted_score === holeWinScore.adjusted_score,
        );
        let isDraw = isHoleWin?.length > 1;
        if (isHoleWin?.length === 2) {
          if (isHoleWin[0].team === isHoleWin[1].team) {
            isDraw = false;
          } else {
            isDraw = true;
          }
        }
        let holeWin = isDraw ? null : isHoleWin[0].team;
        if (holeWin === MATCH_TEAM_NAME.TEAM1) {
          scoreTeam1 += 1;
        }
        if (holeWin === MATCH_TEAM_NAME.TEAM2) {
          scoreTeam2 += 1;
        }
        let diffScore = Math.abs(scoreTeam1 - scoreTeam2);
        if (diffScore > 0) {
          tempTeamWin =
            scoreTeam1 > scoreTeam2
              ? MATCH_TEAM_NAME.TEAM1
              : MATCH_TEAM_NAME.TEAM2;
        }
        if (!teamWin) {
          teamWin =
            diffScore > skipHole + 18 - holeNumber
              ? scoreTeam1 > scoreTeam2
                ? MATCH_TEAM_NAME.TEAM1
                : MATCH_TEAM_NAME.TEAM2
              : null;
          if (teamWin) {
            const teamLoseMaxScore = skipHole + 18 - holeNumber;
            gameWinScore = `${diffScore}&${teamLoseMaxScore}`;
          }
        }
        initMatchScores.push({
          winScore: holeWinScore.adjusted_score,
          scoreTeam1,
          scoreTeam2,
          isDraw,
          teamWin,
          skipHole,
          diffScore,
          gameWinScore,
          tempTeamWin,
        });
      } else {
        if (!teamWin) {
          skipHole += 1;
        }
        initMatchScores.push({
          scoreTeam1,
          scoreTeam2,
          teamWin,
          skipHole,
          gameWinScore,
          tempTeamWin,
        });
      }
    } else {
      if (playerScores) {
        const holeWinScore = playerScores.reduce((prev, cur) =>
          cur.adjusted_score < prev.adjusted_score ? cur : prev,
        );
        const isHoleWin = playerScores.filter(
          player => player.adjusted_score === holeWinScore.adjusted_score,
        );
        let isDraw = isHoleWin?.length > 1;
        if (isHoleWin?.length === 2) {
          if (isHoleWin[0].team === isHoleWin[1].team) {
            isDraw = false;
          } else {
            isDraw = true;
          }
        }
        let holeWin = isDraw ? null : isHoleWin[0].team;
        if (holeWin === MATCH_TEAM_NAME.TEAM1) {
          scoreTeam1 += 1;
        }
        if (holeWin === MATCH_TEAM_NAME.TEAM2) {
          scoreTeam2 += 1;
        }
        let diffScore = Math.abs(scoreTeam1 - scoreTeam2);
        if (diffScore > 0) {
          tempTeamWin =
            scoreTeam1 > scoreTeam2
              ? MATCH_TEAM_NAME.TEAM1
              : MATCH_TEAM_NAME.TEAM2;
        }
        if (!teamWin) {
          teamWin =
            diffScore > skipHole + 18 - holeNumber
              ? scoreTeam1 > scoreTeam2
                ? MATCH_TEAM_NAME.TEAM1
                : MATCH_TEAM_NAME.TEAM2
              : null;
          if (teamWin) {
            const teamLoseMaxScore = skipHole + 18 - holeNumber;
            gameWinScore = `${diffScore}&${teamLoseMaxScore}`;
          }
        }
        initMatchScores.push({
          winScore: holeWinScore.adjusted_score,
          scoreTeam1,
          scoreTeam2,
          isDraw,
          teamWin,
          skipHole,
          diffScore,
          gameWinScore,
          tempTeamWin,
        });
      } else {
        if (!teamWin) {
          skipHole += 1;
        }
        initMatchScores.push({
          scoreTeam1,
          scoreTeam2,
          teamWin,
          skipHole,
          gameWinScore,
          tempTeamWin,
        });
      }
    }
  }
  return initMatchScores;
};
export const getStat = (holesScore, playerId) => {
  let score = 0;
  let par3 = [];
  let par4 = [];
  let par5 = [];
  let par3avg,
    par4avg,
    par5avg,
    parTotal = 0;
  for (const scores of holesScore) {
    if (playerId) {
      //Multiplayer Player
      const playerScores = scores.player_score;
      const par = scores.par;
      if (playerScores) {
        playerScores.map(player => {
          if (playerId === player.player_id) {
            score += player.hole_score;
            parTotal += par;
            if (par === 3) {
              par3.push(player.hole_score);
            }
            if (par === 4) {
              par4.push(player.hole_score);
            }
            if (par === 5) {
              par5.push(player.hole_score);
            }
          }
        });
      }
    } else {
      //Single Player
      const playerScore = scores.score;
      if (playerScore) {
        const par = scores.par;
        score += playerScore;
        parTotal += par;
        if (par === 3) {
          par3.push(playerScore);
        }
        if (par === 4) {
          par4.push(playerScore);
        }
        if (par === 5) {
          par5.push(playerScore);
        }
      }
    }
  }
  par3avg = par3?.length
    ? par3.reduce((a, b) => a + b, 0) / par3?.length
    : null;
  par4avg = par4?.length
    ? par4.reduce((a, b) => a + b, 0) / par4?.length
    : null;
  par5avg = par5?.length
    ? par5.reduce((a, b) => a + b, 0) / par5?.length
    : null;
  return {score, par3avg, par4avg, par5avg, parTotal};
};
export const calculateOutInTotal = (
  holesScore,
  teeSelected,
  scoreCardDefault,
  isMultiplayer,
  isMatchPlay,
  scoreDetail,
) => {
  const scoreHoleMultiplayer = (start, end, hole1To9) => {
    let scores = hole1To9?.length ? hole1To9 : [];
    let totalScorePlayers = scoreDetail?.players?.map(player => {
      return {hole_score: 0, adjustedScore: 0, ...player};
    });
    for (let i = start - 1; i < end; i++) {
      const hcp = scoreCardDefault?.hcpHole?.[i] || 0;
      const {players, newTotalScores} = formatMultiPlayerScore(
        holesScore[i].score ? holesScore[i].player_score : null,
        hcp,
        totalScorePlayers,
      );

      totalScorePlayers =
        newTotalScores?.length > 0 ? newTotalScores : totalScorePlayers;

      const item = {
        ...holesScore[i],
        hcp,
        yds: teeSelected?.ydsHole?.[i],
        players,
      };
      scores.push(item);
      if (i === end - 1) {
        const outOrIn = {
          number: end === 18 ? 'IN' : 'OUT',
          yds:
            end === 18
              ? teeSelected?.yds1To18 - teeSelected?.yds1To9
              : teeSelected?.yds1To9,
          par: end === 18 ? scoreCardDefault.parIn : scoreCardDefault.parOut,
          players: totalScorePlayers,
        };

        scores.push(outOrIn);
      }
    }
    return scores;
  };
  const handleTotalMultiPlayer = (scoreOut, scoreIn) => {
    let totalScorePlayers = scoreDetail?.players?.map(player => {
      const playerScoreOut = scoreOut.players.find(
        item => item?.player_id === player?.player_id,
      );
      const playerScoreIn = scoreIn.players.find(
        item => item?.player_id === player?.player_id,
      );
      const hole_score =
        (playerScoreOut?.hole_score || 0) + (playerScoreIn?.hole_score || 0);
      const adjustedScore =
        (playerScoreOut?.adjustedScore || 0) +
        (playerScoreIn?.adjustedScore || 0);

      return {
        hole_score: hole_score,
        adjustedScore: adjustedScore,
        ...player,
      };
    });
    return {
      number: 'TOTAL',
      yds: teeSelected?.ydsTotal || 0,
      par: scoreCardDefault.parTotal,
      players: totalScorePlayers,
    };
  };

  const formatMultiPlayerScore = (playerScores, hcp, totalScorePlayers) => {
    let players = [];
    let newTotalScores = [];
    players = scoreDetail?.players?.map(player => {
      const playerScore = playerScores?.find(
        pscore => pscore.player_id === player.player_id,
      );
      let strokeGaind = 1;
      // Strokes - HCP < 0 ;	0 <= Strokes - HCP <= 17 ;	Strokes - HCP >= 18 -> 0;1;2
      if (player.stroke_received - hcp < 0) {
        strokeGaind = 0;
      }
      if (player.stroke_received - hcp >= 18) {
        strokeGaind = 2;
      }
      const adjustedScore = (playerScore?.hole_score || 0) - strokeGaind;
      let formatedPlayer = {
        ...player,
        strokeGaind,
      };
      if (playerScores) {
        formatedPlayer = {
          ...formatedPlayer,
          ...playerScore,
          adjustedScore,
        };
        let total = totalScorePlayers.find(
          tscore => tscore.player_id === player.player_id,
        );
        total = {
          ...formatedPlayer,
          hole_score: total.hole_score + formatedPlayer.hole_score || 0,
          adjustedScore:
            total.adjustedScore + formatedPlayer.adjustedScore || 0,
        };
        newTotalScores.push(total);
      }

      return formatedPlayer;
    });
    return {players, newTotalScores};
  };

  const scoreHoleStartToEnd = (start, end, hole1To9) => {
    let scores = hole1To9?.length ? hole1To9 : [];
    let put = 0;
    let score = 0;
    let countFw = 0;
    let countGr = 0;
    let fw = 0;
    let gr = 0;
    for (let i = start - 1; i < end; i++) {
      const oldScoreToPar = hole1To9?.length
        ? i === 9
          ? scores[i - 1]?.scoreToPar
          : scores[i]?.scoreToPar
        : scores[i - 1]?.scoreToPar;
      const item = {
        ...holesScore[i],
        hcp:
          scoreCardDefault?.hcpHole?.[i] == null
            ? UNDEFINED_VALUE
            : scoreCardDefault?.hcpHole?.[i],
        yds: teeSelected?.ydsHole?.length > 0 ? teeSelected?.ydsHole?.[i] : 0,
        scoreToPar: getScoreToPar(oldScoreToPar, holesScore[i]),
      };
      scores.push(item);
      put += holesScore[i].putts_number || 0;
      score += holesScore[i].score || 0;
      countFw += holesScore[i].score > 0 && holesScore[i].fw_stats ? 1 : 0;
      countGr += holesScore[i].score > 0 && holesScore[i].gr_stats ? 1 : 0;
      fw += holesScore[i].fw_stats?.includes('hit') ? 1 : 0;
      gr += holesScore[i].gr_stats?.includes('hit') ? 1 : 0;
    }
    const outOrIn = {
      number: end === 9 ? 'OUT' : 'IN',
      yds:
        end === 18
          ? teeSelected?.yds1To18 - teeSelected?.yds1To9
          : teeSelected?.yds1To9,
      par: end === 18 ? scoreCardDefault.parIn : scoreCardDefault.parOut,
      putts_number: put,
      score: score,
      grPercent: countGr > 0 ? Math.round((gr * 100) / countGr) : null,
      fwPercent: countFw > 0 ? Math.round((fw * 100) / countFw) : null,
      countGr,
      countFw,
      fw,
      gr,
    };
    scores.push(outOrIn);
    return scores;
  };
  let scores = [];
  if (holesScore?.length >= 9) {
    const hole1To9 = isMultiplayer
      ? scoreHoleMultiplayer(1, 9)
      : scoreHoleStartToEnd(1, 9);
    scores = scores.concat(hole1To9);
  }
  if (holesScore?.length === 18) {
    const hole9To18 = isMultiplayer
      ? scoreHoleMultiplayer(10, 18, scores)
      : scoreHoleStartToEnd(10, 18, scores);
    scores = hole9To18;
    if (isMultiplayer) {
      if (isMatchPlay) {
        scores.push(scores[9]);
      }
      const total = handleTotalMultiPlayer(scores[9], scores[19]);
      scores.push(total);
    } else {
      const grTotal = scores[9].gr + scores[19].gr;
      const fwTotal = scores[9].fw + scores[19].fw;
      const countFwTotal = scores[9].countFw + scores[19].countFw;
      const countGrTotal = scores[9].countGr + scores[19].countGr;
      const total = {
        number: 'TOTAL',
        yds: teeSelected?.ydsTotal || 0,
        par: scoreCardDefault.parTotal,
        putts_number: scores[9].putts_number + scores[19].putts_number,
        score: scores[9].score + scores[19].score,
        grPercent: countGrTotal
          ? Math.round((grTotal * 100) / countGrTotal)
          : null,
        fwPercent: countFwTotal
          ? Math.round((fwTotal * 100) / countFwTotal)
          : null,
      };
      scores.push(total);
    }

    return scores;
  }
  const total = {
    ...scores[9],
    number: 'TOTAL',
    yds: teeSelected?.ydsTotal || 0,
  };
  if (holesScore?.length === 9) {
    scores.pop(); // Remove OUT column if course has 9 holes
  }
  scores.push(total);
  return scores;
};

export const getTruncatedNameWinnerMatch = (
  playerName1,
  playerName2,
  maxLength = 38,
) => {
  let name = `${playerName1} Wins`;
  let hasTwoPlayersWin = false;
  if (playerName2) {
    name = `${playerName1} & ${playerName2} Win`;
    hasTwoPlayersWin = true;
  }
  if (name?.length > maxLength) {
    if (hasTwoPlayersWin) {
      name = `${playerName1.substring(
        0,
        playerName1?.length > playerName2?.length ? 15 : 10,
      )}... & ${playerName2.substring(
        0,
        playerName2?.length > playerName1?.length ? 15 : 10,
      )}... Win`;
    } else {
      name = `${playerName1.substring(0, 30)}... Wins`;
    }
  }
  return name;
};

export const prepareScoreCard = (roundDetail, tee) => {
  const holesData = [];
  try {
    let notPar3_1to9Count = 0;
    let fw_1To9HitCount = 0;
    let fw_10To18HitCount = 0;

    let notPar3_10to18Count = 0;
    let gr_1To9HitCount = 0;
    let gr_10To18HitCount = 0;

    let holePlayedFrom1To9 = 0;
    let holePlayedFrom10To18 = 0;

    roundDetail?.holes.forEach((item, index) => {
      holesData.push({...item, yds: tee?.ydsHole?.[index] || 0});
      if (!item.totalScore) {
        return;
      }
      if (item.holeNumber <= 9) {
        holePlayedFrom1To9++;
        if (item.par !== 3) {
          notPar3_1to9Count++;
          if (item.fwStats === 'fw_hit') {
            fw_1To9HitCount++;
          }
        }
        if (item.grStats === 'gr_hit') {
          gr_1To9HitCount++;
        }
      } else {
        holePlayedFrom10To18++;
        if (item.par !== 3) {
          notPar3_10to18Count++;
          if (item.fwStats === 'fw_hit') {
            fw_10To18HitCount++;
          }
        }
        if (item.grStats === 'gr_hit') {
          gr_10To18HitCount++;
        }
      }
    });
    let outFw9 =
      Math.round(parseFloat(fw_1To9HitCount / notPar3_1to9Count) * 100) || 0;
    let outGr9 =
      Math.round(parseFloat(gr_1To9HitCount / holePlayedFrom1To9) * 100) || 0;
    let out1To9Data = roundDetail?.holes.reduce((result, item) => {
      if (item.holeNumber <= 9) {
        return {
          holeNumber: 'OUT',
          yds: tee?.yds1To9 || 0,
          fwStats: notPar3_1to9Count > 0 ? `${outFw9}%` : UNDEFINED_VALUE,
          grStats: holePlayedFrom1To9 > 0 ? `${outGr9}%` : UNDEFINED_VALUE,
          handicap: '', //(result.handicap || 0) + item.handicap
          par: (result.par || 0) + item.par,
          puttsNumber: (result.puttsNumber || 0) + item.puttsNumber,
          totalScore: (result.totalScore || 0) + item.totalScore,
        };
      } else {
        return result;
      }
    }, 0);
    let outFw18 =
      Math.round(parseFloat(fw_10To18HitCount / notPar3_10to18Count) * 100) ||
      0;
    let outGr18 =
      Math.round(parseFloat(gr_10To18HitCount / holePlayedFrom10To18) * 100) ||
      0;
    let out10To18Data = roundDetail?.holes.reduce((result, item) => {
      if (item.holeNumber > 9) {
        return {
          holeNumber: 'IN',
          yds: tee?.yds10To18 || 0,
          fwStats: notPar3_10to18Count > 0 ? `${outFw18}%` : UNDEFINED_VALUE,
          grStats: holePlayedFrom10To18 > 0 ? `${outGr18}%` : UNDEFINED_VALUE,
          handicap: '',
          par: (result.par || 0) + item.par,
          puttsNumber: (result.puttsNumber || 0) + item.puttsNumber,
          totalScore: (result.totalScore || 0) + item.totalScore,
        };
      } else {
        return result;
      }
    }, 0);

    let totalFwPrcnt =
      Math.round(
        parseFloat(
          (fw_1To9HitCount + fw_10To18HitCount) /
            (notPar3_1to9Count + notPar3_10to18Count),
        ) * 100,
      ) || 0;
    let totalGrPrcnt =
      Math.round(
        parseFloat(
          (gr_1To9HitCount + gr_10To18HitCount) /
            (holePlayedFrom1To9 + holePlayedFrom10To18),
        ) * 100,
      ) || 0;

    let totalData = roundDetail?.holes.reduce((result, item) => {
      return {
        holeNumber: 'TOTAL',
        yds: tee?.yds1To18 || 0,
        fwStats:
          notPar3_1to9Count > 0 || notPar3_10to18Count > 0
            ? `${totalFwPrcnt}%`
            : UNDEFINED_VALUE,
        grStats:
          holePlayedFrom1To9 > 0 || holePlayedFrom10To18 > 0
            ? `${totalGrPrcnt}%`
            : UNDEFINED_VALUE,
        handicap: '',
        par: (result.par || 0) + item.par,
        puttsNumber: (result.puttsNumber || 0) + item.puttsNumber,
        totalScore: (result.totalScore || 0) + item.totalScore,
      };
    }, 0);
    if (roundDetail?.holes?.length >= 9) {
      holesData.splice(9, 0, out1To9Data);
    }
    if (roundDetail?.holes?.length >= 18) {
      holesData.push(out10To18Data);
    }
    holesData.push(totalData);
    return holesData;
  } catch (error) {
    console.log(error);
    return [];
  }
};

export const getScoreToPar = (scoreToPar, hole2) => {
  if (hole2.score) {
    if (scoreToPar) {
      return hole2?.score - hole2?.par + scoreToPar;
    }

    return hole2?.score - hole2?.par;
  }
  return scoreToPar;
};
