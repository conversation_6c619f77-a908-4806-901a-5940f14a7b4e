import * as Keychain from 'react-native-keychain';
import {generateSecureRandom} from 'react-native-securerandom';
import binaryToBase64 from 'react-native/Libraries/Utilities/binaryToBase64';
const CryptoJS = require('crypto-js');
// Unique non-sensitive ID which we use to save the store password
const encryptionKey = 'MYTM_ENCRYPTION';

export const getEncryptionKey = async () => {
  try {
    // Check for existing credentials
    const existingCredentials = await Keychain.getGenericPassword();
    if (existingCredentials) {
      return {isFresh: false, key: existingCredentials.password};
    }

    // Generate new credentials based on random string
    const randomBytes = await generateSecureRandom(32);
    if (!randomBytes) {
      throw new Error('Error generating a secure random key buffer');
    }

    const randomBytesString = binaryToBase64(randomBytes);
    if (!randomBytesString) {
      throw new Error('Error converting secure random key buffer');
    }

    const hasSetCredentials = await Keychain.setGenericPassword(
      encryptionKey,
      randomBytesString,
    );
    if (hasSetCredentials) {
      return {isFresh: true, key: randomBytesString};
    }
    throw new Error('Error setting the generic password on Keychain');
  } catch (error) {
    throw new Error(error);
  }
};

export const encryptionData = (data, key) => {
  let Sha256 = CryptoJS.SHA256;
  let Hex = CryptoJS.enc.Hex;
  let Utf8 = CryptoJS.enc.Utf8;
  let Base64 = CryptoJS.enc.Base64;
  let AES = CryptoJS.AES;

  let secret_key = key + '-key';
  let secret_iv = key + '-iv';

  let keySHA = Sha256(secret_key).toString(Hex).substr(0, 32);
  let iv = Sha256(secret_iv).toString(Hex).substr(0, 16);

  // Encryption
  let output = AES.encrypt(data, Utf8.parse(keySHA), {
    iv: Utf8.parse(iv),
  })?.toString();

  let outputB64 = Utf8.parse(output).toString(Base64);

  return outputB64.replace(/=/g, '-');
};

export const decryptionData = (data, key) => {
  let Sha256 = CryptoJS.SHA256;
  let Hex = CryptoJS.enc.Hex;
  let Utf8 = CryptoJS.enc.Utf8;
  let AES = CryptoJS.AES;
  let Base64 = CryptoJS.enc.Base64;
  const replaceChars = data.replace(/-/g, '=');
  let outputUTF8 = Base64.parse(replaceChars).toString(Utf8);
  let secret_key = key + '-key';
  let secret_iv = key + '-iv';

  let keySHA = Sha256(secret_key).toString(Hex).substr(0, 32);
  let iv = Sha256(secret_iv).toString(Hex).substr(0, 16);
  const decrypted = AES.decrypt(outputUTF8, Utf8.parse(keySHA), {
    iv: Utf8.parse(iv),
  }).toString(Utf8);
  return decrypted;
};
