import React from 'react';
import {View, Image, Platform, TouchableOpacity} from 'react-native';
import Text from 'components/Text/Text';
import appStyles from '../../styles/global';
import Button from 'components/Button';
import BgButton from 'assets/imgs/bg_button.svg';
import IconCrown from 'assets/imgs/ic_crown.svg';
import IconBookmark from 'assets/imgs/ic_bookmark.svg';

const GoalsComponent = ({
  title,
  description,
  functionClick = () => {},
  imageUrl,
  buttonTitle,
  buttonLoading,
  isFreePermission,
}) => {
  return (
    <View
      style={[
        appStyles.mTSm,
        appStyles.white,
        appStyles.mHXs,
        {borderRadius: 8, backgroundColor: '#FFFFFF'},
      ]}
    >
      <Image
        source={imageUrl}
        style={{
          width: '100%',
          height: 150,
          borderTopLeftRadius: 8,
          borderTopRightRadius: 8,
        }}
      />
      <View style={appStyles.row}>
        <View style={{paddingHorizontal: 15, paddingTop: 13, flex: 2}}>
          <Text style={[{fontSize: 17, fontWeight: '700', color: '#000000'}]}>
            {title}
          </Text>
          <Text style={[appStyles.mTSm, {fontSize: 13, color: '#000000'}]}>
            {description}
          </Text>
          <View style={appStyles.row}>
            {isFreePermission ? (
              <TouchableOpacity
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                  marginBottom: 20,
                }}
                onPress={functionClick}
              >
                <BgButton />
                <Text
                  style={{
                    fontSize: 13,
                    position: 'absolute',
                    fontWeight: 'bold',
                    paddingHorizontal: 10,
                    color: '#000000',
                  }}
                >
                  plan.upgrade_for_access_low
                </Text>
              </TouchableOpacity>
            ) : (
              <Button
                style={[
                  {
                    height: 36,
                    minWidth: 140,
                    borderRadius: 22,
                    paddingHorizontal: 0,
                    paddingVertical: 0,
                    marginTop: 10,
                    marginBottom: 20,
                  },
                ]}
                text={buttonTitle}
                textStyle={{
                  fontSize: 13,
                  fontWeight: 'bold',
                  paddingHorizontal: 25,
                }}
                loading={buttonLoading}
                numberOfLines={1}
                loadingMode="dark"
                backgroundColor="black"
                borderColor="black"
                onPress={() => functionClick()}
                textColor="white"
                centered
              />
            )}
            <View style={appStyles.flex} />
          </View>
        </View>
        {isFreePermission && (
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              paddingTop: 8,
              marginTop: -0.5,
            }}
          >
            <View style={{position: 'absolute'}}>
              <IconBookmark />
            </View>
            <IconCrown />
            <Text
              style={{
                fontSize: 8,
                color: '#000000',
                textAlign: 'center',
                marginTop: Platform.OS === 'ios' ? 4 : 0,
              }}
            >
              plan.champion_only
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};
export default GoalsComponent;
