import {useState, useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {createStore} from '../../utils/store';
import {ALREADY_FORCE_LOGOUT} from 'utils/constant';
import { clearAsyncStorage } from 'utils/asyncStorage';

const StoreGate = ({encryptionKey, children}) => {
  const [hasData, setHasData] = useState(false);

  useEffect(() => {
    (async () => {
      setHasData(await AsyncStorage.getItem('persist:root'));
    })();
  }, []);

  // hasData hasn't been set, so don't return anything
  if (hasData === false) {
    return null;
  }

  // If the encryption key is fresh, we need to flush AsyncStorage
  if (encryptionKey.isFresh && hasData !== null) {
    clearAsyncStorage();
    AsyncStorage.setItem(ALREADY_FORCE_LOGOUT, 'true');
  }

  // Create redux store and persistor
  const {store, persistor} = createStore(encryptionKey.key);

  return children(store, persistor);
};

export default StoreGate;
