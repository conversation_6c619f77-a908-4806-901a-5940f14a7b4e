import React from 'react';
import {
  View,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  Image,
} from 'react-native';
import PropTypes from 'prop-types';
import Icon from 'react-native-vector-icons/FontAwesome5';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

import Text from '../Text';

import appStyles from 'styles/global';
import styles from './styles';
import {GREY} from 'config';
import IcChecked from 'assets/imgs/ic_checked.svg';
import IcUnchecked from 'assets/imgs/ic_unchecked.svg';
import {moderateScale} from 'react-native-size-matters';

const CheckButton = ({
  style,
  text,
  textColor,
  textSize,
  backgroundColor,
  borderColor,
  centered,
  onPress,
  disabled,
  loading,
  loadingMode,
  DINbold,
  rightIcon,
  textStyle,
  subText,
  isChecked,
  value,
}) => {
  return (
    <TouchableOpacity
      style={[
        appStyles.pHSm,
        appStyles.vCenter,
        styles.button,
        {backgroundColor, borderColor},
      ].concat(style)}
      onPress={() => onPress(value)}
      disabled={disabled}
    >
      {loading ? (
        <ActivityIndicator color={loadingMode === 'dark' ? 'white' : 'black'} />
      ) : (
        <View style={[appStyles.row, appStyles.hCenter]}>
          <Text
            style={[
              centered ? appStyles.textCenter : appStyles.textLeft,
              {color: textColor, flex: 1},
              textSize && appStyles[textSize],
              Platform.OS === 'ios' && DINbold && {marginBottom: -5},
            ].concat(textStyle)}
            DINbold={DINbold}
          >
            {text}
          </Text>
          {isChecked != null &&
            (isChecked === true ? <IcChecked /> : <IcUnchecked />)}
        </View>
      )}
    </TouchableOpacity>
  );
};

CheckButton.propTypes = {
  text: PropTypes.string,
  textColor: PropTypes.string,
  backgroundColor: PropTypes.string,
  borderColor: PropTypes.string,
  centered: PropTypes.bool,
  onPress: PropTypes.func,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  DINbold: PropTypes.bool,
  isChecked: PropTypes.any,
};

CheckButton.defaultProps = {
  text: '',
  textColor: 'black',
  backgroundColor: 'transparent',
  borderColor: '',
  centered: false,
  onPress: () => {},
  disabled: false,
  loading: false,
  DINbold: false,
};

export default CheckButton;
