import React, {useState, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  Dimensions,
  Image,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {moderateScale} from 'react-native-size-matters';
import moment from 'moment';
import {connect, useSelector} from 'react-redux';
import {getProductDetails, removeProductFromBasket} from 'requests/ecom';
import {updateCurrentBasket} from 'reducers/basket';
import Text from '../Text';
import LoadingOverlay from '../LoadingOverlay';
import appStyles from 'styles/global';
import {GREEN} from 'config';
import clubSilhouettes from 'json/club-silhouettes';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import analytics from '@react-native-firebase/analytics';
import {NOT_APPLICABLE, SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';
import silhouettesShaftLogo from 'assets/imgs/silhouettes-shaft.jpg';
import silhouettesGripLogo from 'assets/imgs/silhouettes-grip.jpg';
import {allowIronTTBData, getSKUForProduct} from 'utils/clubs';
import IcDeleteProduct from 'assets/imgs/ic_delete_product.svg';
import QuantitySection from 'components/QuantitySection';
import {canProductAttributeShown, checkHiddenAttribute} from 'utils/shop';
import { getCurrencySymbol } from 'utils/commonVariable';
const URL =
  'https://www.taylormadegolf.com/on/demandware.static/-/Sites-tmag-master-catalog/default/zoom/';
const URL_LOAD_IMG =
  'https://www.taylormadegolf.com/on/demandware.static/-/Sites-tmag-custom-catalog-us/default/images/OracleConfigurator/';
const LABEL_RIGHT_SPACE = '  ';
const LABEL_DATE_RIGHT_SPACE = ' ';
const ProductItem = ({
  status,
  statusText,
  statusIcon,
  statusColor,
  trialStarted,
  isPDP,
  name,
  price,
  productId,
  macroCode,
  itemId,
  endDate,
  isEnded,
  customNodes,
  isBasket,
  updateCurrentBasket,
  isTTBItem,
  atp,
  itemData,
  quantity,
  styleColorTitle,
  sizeImage = 300,
  moveToShop,
  maxQty,
  setQuantity,
}) => {
  const basket = useSelector(state => state.basket);
  const [details, setDetails] = useState(null);
  const [viewMore, setViewMore] = useState(false);
  const [loadOverlay, setLoadOverlay] = useState(false);
  const configurations = customNodes
    ? customNodes?.selectedNodes?.filter(node => {
        return (
          node?.name === 'Model' ||
          node?.name === 'Gender' ||
          node?.name === 'Hand' ||
          node?.name === 'Lie Adjustment' ||
          node?.name === 'Loft Adjustment' ||
          node?.name === 'Loft' ||
          node?.name === 'Shaft Material' ||
          node?.name === 'Shaft Vendor' ||
          node?.name === 'Shaft Model' ||
          node?.name === 'Shaft Flex' ||
          node?.name === 'Tipping Adjustment' ||
          node?.name === 'Length Adjustment' ||
          node?.name === 'Grip Vendor' ||
          node?.name === 'Grip Model' ||
          node?.name === 'Grip Wraps' ||
          node?.name === 'Grip Logo'
        );
      })
    : [];
  const headModel = configurations?.find(node => {
    return node?.name === 'Model';
  });
  const shaftModel = configurations?.find(node => {
    return node?.name === 'Shaft Model';
  });
  const gripModel = configurations?.find(node => {
    return node?.name === 'Grip Model';
  });
  const handleTextBold =
    statusText?.indexOf('-') > -1 ? statusText.split('-') : statusText;

  useEffect(() => {
    (async () => {
      try {
        // Make request to get product details from sfcc
        const productDetails = await getProductDetails(macroCode || productId);
        setDetails(productDetails);
      } catch (error) {
        throw error;
      }
    })();
  }, []);

  const checkClubType = () => {
    switch (productId) {
      case 'TM_DRIVER_MODEL':
        return 'Driver';
      case 'TM_FAIRWAY_MODEL':
        return 'Fairway';
      case 'TM_IRONS_MODEL':
        return 'Iron';
      case 'TM_RESCUE_MODEL':
        return 'Rescue';
      case 'TM_PUTTER_MODEL':
        return 'Putter';
      default:
        return null;
    }
  };

  const renderProductConfigurations = () => {
    return configurations
      ?.filter(config => config?.name !== 'Model')
      .map((config, i) => {
        const count = viewMore ? configurations.length : 2;
        if (i <= count) {
          return (
            <View
              key={config.name + '-' + i}
              style={[
                (productId === 'TM_IRONS_MODEL' &&
                  config.name === 'Shaft Vendor') ||
                (productId === 'TM_DRIVER_MODEL' &&
                  config.name === 'Shaft Model') ||
                config.name === 'Grip Vendor'
                  ? appStyles.mTXs
                  : {},
              ]}
            >
              <Text
                key={config?.option?.rtId}
                style={[appStyles.xs, styleColorTitle]}
              >
                {config?.name}
                {LABEL_RIGHT_SPACE}
                <Text style={[appStyles.grey, appStyles.xs]}>
                  {config?.option?.name || config?.option?.text}
                </Text>
              </Text>
              {(productId === 'TM_DRIVER_MODEL' &&
                config?.name === 'Loft' &&
                atp?.head) ||
              (productId === 'TM_IRONS_MODEL' &&
                config?.name === 'Loft Adjustment' &&
                atp?.head) ? (
                <Text style={[appStyles.xs, styleColorTitle]}>
                  {t('productitem.head_availability_date')}
                  {LABEL_DATE_RIGHT_SPACE}
                  <Text style={[appStyles.grey, appStyles.xs]}>
                    {moment(atp?.head, 'YYYY-MM-DD').format('L')}
                  </Text>
                </Text>
              ) : null}
              {config?.name === 'Length Adjustment' && atp?.shaft ? (
                <Text style={[appStyles.xs, styleColorTitle]}>
                  {t('productitem.shaft_availability_date')}
                  {LABEL_DATE_RIGHT_SPACE}
                  <Text style={[appStyles.grey, appStyles.xs]}>
                    {moment(atp?.shaft, 'YYYY-MM-DD').format('L')}
                  </Text>
                </Text>
              ) : null}
              {config?.name === 'Grip Logo' && atp?.grip ? (
                <Text style={[appStyles.xs, styleColorTitle]}>
                  {t('productitem.grip_availability_date')}
                  {LABEL_DATE_RIGHT_SPACE}
                  <Text style={[appStyles.grey, appStyles.xs]}>
                    {moment(atp?.grip, 'YYYY-MM-DD').format('L')}
                  </Text>
                </Text>
              ) : null}
            </View>
          );
        }
      });
  };

  const getProductName = () => {
    if (headModel) {
      const clubType = checkClubType();
      if (clubType === 'Iron') {
        const ironName = allowIronTTBData[headModel?.option?.name]?.name;
        if (ironName) {
          return ironName;
        }
      }
      return headModel?.option?.name;
    } else {
      return name;
    }
  };

  const getProductImage = () => {
    const clubType = checkClubType();
    console.log('clubType ', clubType, headModel);
    if (details?.image_groups) {
      return {
        uri: details?.image_groups[0]?.images[0]?.link,
      };
    } else if (
      headModel?.option?.info?.imageUrlLarge ||
      headModel?.option?.info?.imageUrl ||
      headModel?.option?.info?.imageURL
    ) {
      return {
        uri:
          headModel?.option?.info?.imageUrlLarge ||
          headModel?.option?.info?.imageUrl ||
          headModel?.option?.info?.imageURL,
      };
    }

    const sku = getSKUForProduct?.(headModel?.option?.name, clubType);
    if (sku) {
      return {
        uri: `https://www.taylormadegolf.com/on/demandware.static/-/Sites-tmag-master-catalog/default/zoom/${sku}_zoom_D.jpg`,
      };
    }
    if (clubType) {
      return clubSilhouettes?.find(silhouette => silhouette?.type === clubType)
        .image;
    } else {
      return clubSilhouettes?.find(silhouette => silhouette?.type === 'Driver')
        .image;
    }
  };

  const getModelImage = (modelImage, type) => {
    return modelImage?.length
      ? {
          uri: URL_LOAD_IMG + `${type}/${modelImage}.jpg`,
        }
      : type === 'Shafts'
      ? silhouettesShaftLogo
      : silhouettesGripLogo;
  };

  const GA_removeFromCart = (item, index) => {
    const listProduct = [];
    listProduct.push({
      item_id: item.product_id,
      item_name: item.item_text,
      item_category: NOT_APPLICABLE,
      item_brand: 'TaylorMade Golf',
      item_list_name: 'Drops',
      item_location_id: 'cart', //optional
      item_variant: details?.c_tm_comfil_size || NOT_APPLICABLE,
      index: index, //e.g 4
      price: item.price, //e.g 123.22
      quantity: item.quantity, //e.g 2
    });
    analytics().logEvent('remove_from_cart', {
      app_screen_name: 'cart', //e.g home, my orders
      screen_type: SCREEN_CLASS.SHOP, //e.g checkout, pdp, plp, account
      page_name: 'cart', //e.g home, my orders
      page_type: SCREEN_TYPES.TTB, //e.g basket, home, order
      page_category: SCREEN_TYPES.TTB,
      currency: 'USD',
      items: listProduct,
    });
  };

  const removeProduct = async () => {
    setLoadOverlay(true);
    try {
      const itemIndex = basket?.product_items?.findIndex(
        item => item.item_id === itemId,
      );
      // Make request to remove product from sfcc basket
      const currentBasket = await removeProductFromBasket({
        basketId: basket?.basket_id,
        resourceState: basket._resource_state,
        itemId,
      });
      GA_removeFromCart(itemData, itemIndex);
      // Update basket in redux
      updateCurrentBasket(currentBasket);
      setLoadOverlay(false);
    } catch (error) {
      setLoadOverlay(false);
      showToast({
        type: 'error',
        message: t('product.item.fore_left'),
        subText: t('product.item.could_not_remove_that_product'),
      });
    }
  };

  const handleTextUnderline = t('productitem.how_hitting_it_underline').split(
    '-',
  );

  const renderProductOptions = () => {
    if (
      !details?.variation_values ||
      !details?.variation_attributes ||
      !details
    ) {
      return null;
    }
    const displayData = {};
    Object.keys(details?.variation_values).forEach(key => {
      const attribute = details?.variation_attributes.find(
        item => item.id === key,
      );

      let isHiddenAttribute = checkHiddenAttribute(details, attribute);
      if (isHiddenAttribute) {
        return;
      }
      const option = attribute.values.find(
        opt => opt.value === details?.variation_values[key],
      );
      displayData[attribute?.name] = option?.name;
    });

    return (
      <>
        {Object.keys(displayData).map(key => {
          return (
            <Text style={[appStyles.xs, appStyles.mTXxs, styleColorTitle]}>
              {`${key}  `}
              <Text style={[appStyles.grey, appStyles.xs]}>
                {displayData[key]}
              </Text>
            </Text>
          );
        })}
      </>
    );
  };

  return (
    <View style={appStyles.mBSm}>
      {loadOverlay ? <LoadingOverlay transparent={loadOverlay} /> : null}
      <View
        style={{
          position: 'relative',
          backgroundColor: isTTBItem ? '#FFFFFF' : 'transparent',
          borderRadius: 8,
        }}
      >
        <Image
          source={getProductImage()}
          style={[
            appStyles.fullWidth,
            {
              height: moderateScale(sizeImage),
              backgroundColor: '#FFFFFF',
              borderRadius: 8,
            },
          ]}
          resizeMode={
            headModel?.option?.info || details?.image_groups
              ? 'cover'
              : 'contain'
          }
        />
        {statusText ? (
          <View
            style={[
              appStyles.row,
              appStyles.hCenter,
              appStyles.pill,
              appStyles.pLSm,
              appStyles.mSm,
              {position: 'absolute', backgroundColor: statusColor},
            ]}
          >
            {statusIcon}
            <Text
              style={[
                appStyles.white,
                appStyles.xs,
                appStyles.mLSm,
                {fontWeight: Platform.OS === 'ios' ? '600' : 'bold'},
              ]}
            >
              {!Array.isArray(handleTextBold) ? statusText : handleTextBold[0]}
              {Array.isArray(handleTextBold) && (
                <Text style={[{fontWeight: 'normal'}, appStyles.xs]}>
                  {handleTextBold[1]}
                </Text>
              )}
            </Text>
          </View>
        ) : null}

        {shaftModel ? (
          <Image
            source={getModelImage(shaftModel?.option?.image, 'Shafts')}
            style={[
              appStyles.fullWidth,
              appStyles.mVSm,
              {height: moderateScale(20), borderRadius: isTTBItem ? 0 : 5},
            ]}
          />
        ) : null}

        {gripModel ? (
          <Image
            source={getModelImage(gripModel?.option?.image, 'Grips')}
            style={[
              appStyles.fullWidth,
              appStyles.mBSm,
              {height: moderateScale(20), borderRadius: 5},
            ]}
          />
        ) : null}
      </View>
      <View style={appStyles.mHSm}>
        {trialStarted && isPDP ? (
          <View style={[appStyles.row, appStyles.hCenter, appStyles.mTSm]}>
            <View style={[appStyles.row, appStyles.hCenter]}>
              <Icon name="clock" color={GREEN} size={moderateScale(12)} />
              <Text style={[appStyles.xs, appStyles.green, appStyles.mLSm]}>
                Trial {isEnded ? 'Ended' : 'Ends'} on{LABEL_DATE_RIGHT_SPACE}
                {moment(endDate).format('L')}
              </Text>
            </View>
          </View>
        ) : null}
        <Text
          style={[
            appStyles.bold,
            isTTBItem ? appStyles.mds : appStyles.md,
            appStyles.mVSm,
            styleColorTitle,
          ]}
        >
          {getProductName()}
        </Text>
        {quantity && isTTBItem ? (
          <Text style={[appStyles.xs, styleColorTitle]}>
            {t('overview.product_quantity')}
            {LABEL_RIGHT_SPACE}
            <Text style={[appStyles.xs, appStyles.lightGrey]}>{quantity}</Text>
          </Text>
        ) : null}
        {price ? (
          <Text style={[appStyles.xs, appStyles.mTXxs, styleColorTitle]}>
            {isTTBItem
              ? t('overview.price_to_purchase_ttb')
              : t('overview.price')}
            {LABEL_RIGHT_SPACE}
            <Text style={[appStyles.xs, appStyles.lightGrey]}>
              {getCurrencySymbol(basket?.currency)}
              {price}
            </Text>
          </Text>
        ) : null}
        {renderProductOptions()}
        {configurations?.length ? (
          <View>
            {renderProductConfigurations()}
            <TouchableOpacity onPress={() => setViewMore(!viewMore)}>
              <Text style={[appStyles.xs, styleColorTitle]}>
                {!viewMore ? '+ View More' : '- View Less'}
              </Text>
            </TouchableOpacity>
          </View>
        ) : null}
        {isBasket && atp?.model ? (
          <Text style={[appStyles.grey, appStyles.mTSm, appStyles.xs]}>
            {t('overview.est_ship_date')}
            {LABEL_DATE_RIGHT_SPACE}
            <Text style={[appStyles.black, appStyles.xs, styleColorTitle]}>
              {moment(atp?.model, 'YYYY-MM-DD').format('L')}
            </Text>
          </Text>
        ) : null}
        {isBasket ? (
          <View style={[appStyles.mTSm, appStyles.row, appStyles.hCenter]}>
            {!isTTBItem ? (
              <QuantitySection
                maxQty={maxQty}
                quantity={quantity}
                setQuantity={qty => setQuantity(itemId, qty)}
                // renderTextLimit={renderTextLimit}
              />
            ) : null}
            <TouchableOpacity
              onPress={removeProduct}
              style={{borderRadius: 50, borderWidth: 1, borderColor: '#8C8B8F'}}
            >
              <IcDeleteProduct />
            </TouchableOpacity>
          </View>
        ) : null}
        {!isPDP &&
          status !== 'INITIALIZED' &&
          status !== 'TRIAL_COMING' &&
          status !== 'MISMATCHED_COMPONENT' &&
          status !== 'PRODUCT_RECEIVED' &&
          !moveToShop && (
            <Text style={[appStyles.xs, appStyles.grey, appStyles.mTSm]}>
              {t('productitem.how_hitting_it')}
            </Text>
          )}
        {!isPDP &&
          status !== 'INITIALIZED' &&
          status !== 'TRIAL_COMING' &&
          status !== 'MISMATCHED_COMPONENT' &&
          status !== 'PRODUCT_RECEIVED' &&
          moveToShop && (
            <Text
              style={[
                appStyles.md,
                appStyles.grey,
                appStyles.mTSm,
                {fontWeight: 'bold'},
              ]}
            >
              {handleTextUnderline[0]}
              <TouchableOpacity onPress={moveToShop}>
                <Text
                  style={[
                    {
                      textDecorationLine: 'underline',
                      fontWeight: 'bold',
                      marginBottom: Platform.OS === 'ios' ? -3 : -5,
                    },
                    appStyles.md,
                    appStyles.grey,
                  ]}
                >
                  {handleTextUnderline[1]}
                </Text>
              </TouchableOpacity>
            </Text>
          )}
      </View>
    </View>
  );
};

const mapDispatchToProps = {updateCurrentBasket};

export default connect(null, mapDispatchToProps)(ProductItem);
