import React from 'react';
import {View, Platform, Image, TouchableOpacity} from 'react-native';

import Text from '../Text';

import appStyles from '../../styles/global';
import {BLUE_LIGHT, GREY} from '../../config';
import RadioButton from '../RadioButton';
import {moderateScale} from 'react-native-size-matters';
import logoTaylor from '../../../assets/imgs/WITBTMLogo.png';
import AntDesign from 'react-native-vector-icons/AntDesign';
import * as Animatable from 'react-native-animatable';

const ClubItem = ({item, onCheck, checked, isAddDetail, onPressItem}) => {
  const onPressCheck = value => {
    onCheck(value);
  };
  const onPress = value => {
    if (onPressItem) {
      onPressItem(value);
    }
  };
  return (
    <Animatable.View animation={'fadeIn'} key={item?.id}>
      <View style={[appStyles.whiteBg, appStyles.pHSm]}>
        <Text
          style={[appStyles.xs, appStyles.mTSm, appStyles.mBXs, {color: GREY}]}
        >
          {item?.name?.toUpperCase()}
        </Text>
      </View>
      {item?.items?.map((value, index) => (
        <View
          key={`${index}`}
          style={[
            {backgroundColor: BLUE_LIGHT},
            item?.items?.length - 1 === index && appStyles.pBSm,
          ]}
        >
          <TouchableOpacity
            style={[appStyles.row, appStyles.hCenter, appStyles.pRSm]}
            onPress={() => onPress(value)}
            activeOpacity={1}
          >
            <View
              style={[
                appStyles.pHXs,
                appStyles.blackBg,
                appStyles.mSm,
                appStyles.hCenter,
                appStyles.vCenter,
                {minWidth: 60, height: 60, borderRadius: 8, marginRight: '3%'},
              ]}
            >
              <Text
                style={[
                  appStyles.white,
                  appStyles.lg,
                  Platform.OS === 'ios' && {marginBottom: -5},
                ]}
                DINbold
              >
                {value.type}
              </Text>
            </View>
            {isAddDetail ? (
              value?.brand?.name ? (
                <View
                  style={[appStyles.row, appStyles.flex, appStyles.hCenter]}
                >
                  {value?.brand?.name === 'TaylorMade' ? (
                    <Image
                      style={{
                        width: moderateScale(100),
                        height: moderateScale(20),
                        resizeMode: 'contain',
                        alignSelf: 'flex-end',
                      }}
                      source={logoTaylor}
                    />
                  ) : (
                    <Text>{value?.brand && value.brand.name}</Text>
                  )}
                  <Text
                    style={[appStyles.flex, appStyles.mRXs]}
                    numberOfLines={1}
                  >
                    {value?.model && ` | ${value?.model?.name}`}
                  </Text>
                </View>
              ) : (
                <Text style={[appStyles.flex, appStyles.grey, appStyles.sm]}>
                  club.item.select_brand_and_model
                </Text>
              )
            ) : (
              <Text style={[appStyles.flex, appStyles.sm]}>{value?.name}</Text>
            )}

            {isAddDetail ? (
              <AntDesign name="right" color={GREY} size={20} />
            ) : (
              <RadioButton
                checked={checked[value?.id]}
                setChecked={() => onPressCheck(value)}
              />
            )}
          </TouchableOpacity>
          <View style={[{height: 0.8, backgroundColor: GREY + '50'}]} />
        </View>
      ))}
    </Animatable.View>
  );
};

export default ClubItem;
