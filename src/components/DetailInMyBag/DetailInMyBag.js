/* eslint-disable react-hooks/exhaustive-deps */
import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  ScrollView,
  StatusBar,
  Image,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {connect, useSelector} from 'react-redux';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import analytics from '@react-native-firebase/analytics';

import {
  getAllClubLofts,
  getAllClubShaftFlex,
  getAllClubShaftLength,
  getAllClubLieAdjustments,
  getAllClubLoftAdjustments,
  updateClubinBag,
  deleteClubinBag,
  getStatsClub,
  getStatsClubPutter,
} from 'requests/accounts';
import {updateBagList} from 'reducers/myBag';
import {showToast} from 'utils/toast';

import FormItem from 'components/FormItem';
import Selector from 'components/Selector';
import Button from 'components/Button';
import Text from 'components/Text';

import appStyles from 'styles/global';
import clubSilhouettes from 'json/club-silhouettes';
import AndroidNumberPicker from 'components/AndroidNumberPicker/AndroidNumberPicker';
import {
  CLUB_NAME,
  formatClubType,
  GA_EVENT_NAME,
  KEY_DATA_PUTTER,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_TYPES,
  SELECTOR_TYPE,
} from 'utils/constant';
import {t, use} from 'i18next';
import RowHaveLogo from 'components/StatsInMyBag/RowHaveLogo';
import DistanceStats from 'components/StatsInMyBag/DistanceStats';
import Icon from 'react-native-vector-icons/FontAwesome';
import IcCompare from 'assets/imgs/ic_compare.svg';
import IcDeleteClub from 'assets/imgs/ic_delete_club.svg';
import StrokesGainedHelp from 'components/StatsInMyBag/StrokesGainedHelp';
import GraphPutterBar from 'components/Graph/GraphPutterBar';
import GraphPutterPoint from 'components/Graph/GraphPutterPoint';
import ModalStrokesGained from 'components/ModalStrokesGained/ModalStrokesGained';
import {GA_logEvent} from 'utils/googleAnalytics';

const DetailInMyBag = ({navigation, route, clubDetail, updateBagList}) => {
  const categoryId = clubDetail?.categoryId;
  const hasLoft = clubDetail?.clubCategory !== CLUB_NAME.PUTTER ? true : false;
  const canEditLoft =
    clubDetail.clubCategory === CLUB_NAME.DRIVER ? true : false;
  const hasFaceLoftLieAdj =
    clubDetail?.clubCategory !== CLUB_NAME.IRONS ||
    clubDetail?.clubCategory !== CLUB_NAME.WEDGE
      ? true
      : false;
  const isBall = clubDetail?.clubCategory === CLUB_NAME.BALL ? true : false;
  const isPutter = clubDetail?.clubCategory === CLUB_NAME.PUTTER ? true : false;
  const [allClubLofts, setAllClubLofts] = useState([]);
  const [allClubShaftFlex, setAllClubShaftFlex] = useState([]);
  const [allClubShaftLength, setAllClubShaftLength] = useState([]);
  const [allClubLieAdjustments, setAllClubLieAdjustments] = useState([]);
  const [allClubLoftAdjustments, setAllClubLoftAdjustments] = useState([]);

  const [loft, setLoft] = useState();
  const [shaftFlex, setShaftFlex] = useState();
  const [shaftLength, setShaftLength] = useState();
  const [faceLieAdjustment, setFaceLieAdjustment] = useState();
  const [faceLoftAdjustment, setFaceLoftAdjustment] = useState();
  const [clubStatus, setClubStatus] = useState();
  const [brand, setBrand] = useState();
  const [model, setModel] = useState();

  const [toggledLoft, setToggledLoft] = useState(false);
  const [toggledShaftFlex, setToggledShaftFlex] = useState(false);
  const [toggledShaftLength, setToggledShaftLength] = useState(false);
  const [toggledLieAdjustments, setToggledLieAdjustments] = useState(false);
  const [toggledLoftAdjustments, setToggledLoftAdjustments] = useState(false);

  const [loading, setLoading] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const sheetLoftRef = useRef(null);
  const sheetFlexRef = useRef(null);
  const sheetLengthRef = useRef(null);
  const sheetLieAdjustmentRef = useRef(null);
  const sheetLoftAdjustmentRef = useRef(null);
  const user = useSelector(state => state?.user);
  const bagList = useSelector(state => state?.myBag?.bagList || []);
  const units = user?.golferProfile?.measurementUnits || 'yards';
  const [dataClub, setDataClub] = useState(null);
  const typeOfClub = () => {
    const club = bagList?.find(value => value.id === clubDetail?.id);
    if (club) {
      return formatClubType(club?.clubType?.type || club?.clubFamily?.name);
    }
    return '';
  };
  const [isValuesChanged, setValuesChanged] = useState(false);
  const [isAllValuesSaved, setAllValuesSaved] = useState(false);

  useEffect(() => {
    if (!isBall) {
      callAllClubLofts();
    }
  }, []);

  useEffect(() => {
    setLoft(clubDetail?.loft ? `${clubDetail?.loft}` : '');
    setShaftFlex(clubDetail?.shaftFlex ? clubDetail?.shaftFlex : '');
    setShaftLength(clubDetail?.shaftLength ? clubDetail?.shaftLength : '');
    setFaceLieAdjustment(
      clubDetail?.faceLieAdjustment ? clubDetail?.faceLieAdjustment : '',
    );
    setFaceLoftAdjustment(
      clubDetail?.faceLoftAdjustment ? clubDetail?.faceLoftAdjustment : '',
    );
    setClubStatus(clubDetail?.inBag);
  }, []);

  const isGreenOrFairway = name => {
    return (
      name === CLUB_NAME.DRIVER ||
      name === CLUB_NAME.HYBRID ||
      name === CLUB_NAME.HYBRID_REAL ||
      name === CLUB_NAME.FAIRWAY
    );
  };
  const changeDetailProp = (prop, value) => {
    switch (prop) {
      case 'brand':
        setBrand(value);
        break;
      case 'model':
        setModel(value);
        break;
      case 'loft':
        setLoft(value);
        break;
      case 'shaftFlex':
        setShaftFlex(value);
        break;
      case 'shaftLength':
        setShaftLength(value);
        break;
      case 'faceLieAdjustment':
        setFaceLieAdjustment(value);
        break;
      case 'faceLoftAdjustment':
        setFaceLoftAdjustment(value);
        break;
      case 'clubStatus':
        setClubStatus(value);
        break;
      default:
        break;
    }
    setValuesChanged(true);
    setAllValuesSaved(false);
  };

  const changeLoft = value => {
    changeDetailProp('loft', value);
  };

  const changeShaftFlex = value => {
    changeDetailProp('shaftFlex', value);
  };

  const changeShaftLength = value => {
    changeDetailProp('shaftLength', value);
  };

  const changeFaceLieAdjustment = value => {
    changeDetailProp('faceLieAdjustment', value);
  };

  const changeFaceLoftAdjustment = value => {
    changeDetailProp('faceLoftAdjustment', value);
  };

  const changeClubStatus = value => {
    changeDetailProp('clubStatus', value);
  };

  const getDataClubs = async () => {
    const params = `club=${
      isGreenOrFairway(clubDetail?.clubCategory) ? 'fairway' : 'green'
    }&units=${units}`;

    const response = await getStatsClub(clubDetail?.id, params);
    setDataClub(response);
  };

  const getDataClubsPutter = async () => {
    const response = await getStatsClubPutter(clubDetail?.id, `units=${units}`);
    setDataClub(response);
  };

  const callAllClubLofts = async () => {
    setLoading(true);
    try {
      if (clubDetail?.clubCategory === CLUB_NAME.PUTTER) {
        await getDataClubsPutter();
      } else await getDataClubs();
      const allLofts = hasLoft ? getAllClubLofts(categoryId) : [];
      const allFlex = getAllClubShaftFlex();
      const allLength = getAllClubShaftLength(categoryId);
      const allLieAdjustments = getAllClubLieAdjustments();
      const allLoftAdjustments = getAllClubLoftAdjustments();
      const allData = await Promise.all([
        allLofts,
        allFlex,
        allLength,
        allLieAdjustments,
        allLoftAdjustments,
      ]);
      setAllClubLofts(allData[0]);
      setAllClubShaftFlex(allData[1]);
      setAllClubShaftLength(allData[2]);
      setAllClubLieAdjustments(allData[3]);
      setAllClubLoftAdjustments(allData[4]);
    } catch (error) {
      showToast({
        type: 'error',
        message: t('mybagadd.bag_room_needs_organizing'),
        subText: t('mybag.add_specs.we_have_our_top_pro_on_it'),
      });
    } finally {
      setLoading(false);
    }
  };

  const updateClub = async () => {
    setLoading(true);
    try {
      const loftValueUpdate =
        loft && !isBall
          ? allClubLofts.filter(element => element.value === loft)
          : null;
      const results = await updateClubinBag(
        true,
        clubDetail?.id,
        brand?.id || clubDetail.brandId,
        brand ? model?.id : clubDetail?.modelId,
        clubStatus,
        {
          loftId: loftValueUpdate,
        },
        {
          shaftFlexId:
            shaftFlex && !isBall
              ? allClubShaftFlex.filter(element => element.value === shaftFlex)
              : null,
        },
        {
          shaftLengthId:
            shaftLength && !isBall
              ? allClubShaftLength.filter(
                  element => element.value === shaftLength,
                )
              : null,
        },
        {
          faceLieAdjustmentId:
            faceLieAdjustment && !isBall
              ? allClubLieAdjustments.filter(
                  element => element.value === faceLieAdjustment,
                )
              : null,
        },
        {
          faceLoftAdjustmentId:
            faceLoftAdjustment && !isBall
              ? allClubLoftAdjustments.filter(
                  element => element.value === faceLoftAdjustment,
                )
              : null,
        },
      );
      trackingGoogleAnalytics();
      updateBagList(results);
      const titleChange = `${clubDetail.clubCategory} ${
        model?.name || clubDetail.modelName
          ? `| ${model?.name || clubDetail.modelName} ${loft || ''}`
          : ''
      }`;
      navigation.setOptions({
        title: titleChange,
      });
      setValuesChanged(false);
      setAllValuesSaved(true);
    } catch (error) {
      showToast({
        type: 'error',
        message: t('An_error_occurred_saving_club_details'),
      });
    } finally {
      setLoading(false);
    }
  };

  const trackingGoogleAnalytics = isDelete => {
    try {
      let event = clubStatus
        ? GA_EVENT_NAME.ACTIVATE_CLUB
        : GA_EVENT_NAME.DEACTIVATE_CLUB;
      if (isDelete) {
        event = GA_EVENT_NAME.DELETE_CLUB;
      }
      GA_logEvent(event, {
        screen_type: SCREEN_TYPES.WITB,
        page_name: PAGE_NAME.ACCOUNT_WITB_CLUB_DETAIL_INFO,
        page_type: SCREEN_TYPES.WITB,
        page_category: PAGE_CATEGORY.ACCOUNT_WITB_CLUB_DETAIL,
        club_active: clubStatus ? 'true' : 'false',
        club_type: clubDetail?.clubCategory,
        club_brand: clubDetail?.manufacturer,
        club_model: clubDetail?.modelName,
        club_loft: loft,
        club_shaft_flex: shaftFlex,
        club_shaft_length: shaftLength,
        club_face_lie_adjustment: faceLieAdjustment,
        club_face_loft_adjustment: faceLoftAdjustment,
      });
    } catch (error) {
      console.log('analytics error');
    }
  };

  const disableClub = async () => {
    setDeleting(true);
    setLoading(true);
    try {
      const results = await deleteClubinBag(clubDetail.id);
      trackingGoogleAnalytics(true);
      updateBagList(results);
    } catch (error) {
      showToast({
        type: 'error',
        message: t('mybag.club_details.cleaning_out_the_bag_room'),
        subText: t('mybag.club_details.try_deleting_your_club_again'),
      });
    } finally {
      setDeleting(false);
      setLoading(false);
      navigation.navigate('MyBagScreens');
    }
  };

  const toggleBottomSheets = value => {
    //Toggling the visibility state of the bottom sheet
    let allRefs = [
      sheetFlexRef,
      sheetLengthRef,
      sheetLieAdjustmentRef,
      sheetLoftAdjustmentRef,
    ];

    if (hasLoft) {
      allRefs = [];
      allRefs.push(sheetLoftRef);
      allRefs.push(sheetFlexRef);
      allRefs.push(sheetLengthRef);
    }

    if (isPutter) {
      allRefs = [];
      allRefs.push(sheetLengthRef);
    }

    if (hasFaceLoftLieAdj) {
      allRefs = [];
      allRefs.push(sheetLoftRef);
      allRefs.push(sheetFlexRef);
      allRefs.push(sheetLengthRef);
      allRefs.push(sheetLieAdjustmentRef);
      allRefs.push(sheetLoftAdjustmentRef);
    }

    for (const item of allRefs) {
      if (item.current === value.current) {
        item?.current?.snapTo(0);
      } else {
        item?.current?.snapTo(1);
      }
    }
  };

  const alertCheckForDelete = () => {
    return Alert.alert(
      `${t('mybag.club_details.want_to_delete')} ${
        isBall ? t('mybag.club_details.ball') : t('mybag.club_details.club')
      }?`,
      '',
      [
        {
          text: t('mybag.club_details.cancel'),
          onPress: () => {},
          style: 'cancel',
        },
        {text: t('mybag.club_details.ok'), onPress: () => disableClub()},
      ],
    );
  };

  const alertCheckForSave = () => {
    return Alert.alert(t('mybag.club_details.save_your_changes'), '', [
      {
        text: t('mybag.club_details.cancel'),
        onPress: () => {},
        style: 'cancel',
      },
      {text: t('mybag.club_details.ok'), onPress: () => updateClub()},
    ]);
  };

  const getClubImage = () => {
    const silhouette = clubSilhouettes.find(
      club => club.categoryId === categoryId,
    )?.image;
    return clubDetail.manufacturer === 'TaylorMade' && clubDetail.image
      ? {uri: clubDetail.image}
      : silhouette;
  };

  const formatTitle = selectorType => {
    switch (selectorType) {
      case SELECTOR_TYPE.LOFT:
        return t('my.bag.add_specs.select_loft');
      case SELECTOR_TYPE.SHAFT_FLEX:
        return t('my.bag.add_specs.select_shaft_flex');
      case SELECTOR_TYPE.SHAFT_LENGTH:
        return t('my.bag.add_specs.select_shaft_length');
      case SELECTOR_TYPE.FACE_LIE_ADJUSTMENT:
        return t('my.bag.add_specs.select_lie_adjustment');
      case SELECTOR_TYPE.FACE_LOFT_ADJUSTMENT:
        return t('my.bag.add_specs.select_loft_adjustment');
    }
    return null;
  };

  const onCompareClubPress = () => {
    navigation.push('CompareClubs', {clubToCompare: clubDetail});
  };

  const sameFormItemProps = {
    disabled: loading,
    bottomBorder: true,
    hasIconArrowRight: true,
  };

  const renderStrokesGainedView = dataOverall => {
    let strokeGainedValue =
      dataOverall?.strokesGained != null
        ? `${Math.round10(dataOverall?.strokesGained, -1).toFixed(2)}`
        : '--';
    let avgDistanceValue =
      dataOverall?.avgDistance != null
        ? `${Math.round10(dataOverall?.avgDistance, -1).toFixed(0)}`
        : '--';
    let fairwayHitValue =
      dataOverall?.fairwaysHit != null
        ? `${Math.round10(dataOverall?.fairwaysHit, -1).toFixed(0)}%`
        : '--';
    return (
      <View
        style={[
          appStyles.row,
          appStyles.spaceBetween,
          styles.strokeGainedBoxContainer,
        ]}
      >
        <View style={[appStyles.column, styles.strokeChildBox, {flex: 0.36}]}>
          <Text
            style={[
              appStyles.alignCenter,
              {fontSize: 13, color: '#8C8B8F', marginBottom: 14},
            ]}
          >
            stats.driving.strokes_gained
          </Text>
          <View style={{flexDirection: 'row', justifyContent: 'center'}}>
            {strokeGainedValue > 0 ? (
              <View
                style={[
                  appStyles.alignCenter,
                  Platform.OS === 'android' ? appStyles.mTXs : appStyles.mBSm,
                  {marginRight: 1},
                ]}
              >
                <Icon name="plus" color={'#3ABA56'} size={12} />
              </View>
            ) : null}
            <Text
              DINbold
              style={[
                appStyles.alignCenter,
                {
                  fontWeight: '500',
                  fontSize: 26,
                  color:
                    strokeGainedValue < 0
                      ? '#FF0000'
                      : strokeGainedValue >= 0
                      ? '#3ABA56'
                      : '#3E4D54',
                },
              ]}
            >
              {strokeGainedValue}
            </Text>
          </View>
        </View>
        <View style={[appStyles.column, styles.strokeChildBox, {flex: 0.33}]}>
          <Text
            style={[
              appStyles.alignCenter,
              {fontSize: 13, color: '#8C8B8F', marginBottom: 14},
            ]}
          >
            stats.driving.avg_distance
          </Text>
          <Text
            DINbold
            style={[appStyles.alignCenter, {fontSize: 26, color: '#3E4D54'}]}
          >
            {avgDistanceValue}
          </Text>
        </View>
        <View style={[appStyles.column, styles.strokeChildBox, {flex: 0.31}]}>
          <Text
            style={[
              appStyles.alignCenter,
              {fontSize: 13, color: '#8C8B8F', marginBottom: 14},
            ]}
          >
            scores.stats.fairways_hit
          </Text>
          <Text
            DINbold
            style={[
              appStyles.alignCenter,
              {fontWeight: '500', fontSize: 26, color: '#3ABA56'},
            ]}
          >
            {fairwayHitValue}
          </Text>
        </View>
      </View>
    );
  };

  const onShowModal = () => {
    setIsVisible(true);
  };
  const onHideModal = () => {
    setIsVisible(false);
  };
  const renderBox = (title, value) => {
    const text = value
      ? value > 0
        ? `+${value.toFixed(2)}`
        : value < 0
        ? `${value.toFixed(2)}`
        : value.toFixed(2)
      : '0.00';
    return (
      <View
        key={title}
        style={[
          {
            minWidth: wp('25%'),
            height: wp('25%'),
            borderRadius: 10,
            backgroundColor: '#3A4E55',
          },
          appStyles.pXs,
          appStyles.hCenter,
        ]}
      >
        <Text style={[appStyles.xs, appStyles.white]}>{title}</Text>
        <View style={[appStyles.center]}>
          <Text DINbold style={[appStyles.xl, appStyles.white]}>
            {text}
          </Text>
        </View>
      </View>
    );
  };

  const renderPutter = () => {
    return (
      <View style={[appStyles.flex]}>
        <View
          style={[
            appStyles.flex,
            appStyles.whiteBg,
            appStyles.pSm,
            {paddingTop: 0},
          ]}
        >
          <StrokesGainedHelp onPress={onShowModal} />
          <View
            style={[
              appStyles.row,
              appStyles.flex,
              {justifyContent: 'space-around'},
            ]}
          >
            {renderBox(
              'my_bag.detail.stats.per_round',
              dataClub?.strokes_gained,
            )}
            {renderBox(
              'my_bag.detail.stats.per_shot',
              dataClub?.strokes_gained_for_shots,
            )}
          </View>
        </View>
        <GraphPutterPoint
          itemNameFirst={t('my_bag.detail.stats.your_avg').toUpperCase()}
          itemNameSecond={t('my_bag.detail.stats.pro_avg').toUpperCase()}
          measurementUnits={units}
          dataPutterFirst={dataClub}
          dataPutterSecond={dataClub}
          keyHoleFirst={KEY_DATA_PUTTER.HOLE_PER}
          keyHoleSecond={KEY_DATA_PUTTER.HOLE_PRO_PER}
          keyPerFirst={KEY_DATA_PUTTER.ROUND_NUM}
          keyPerSecond={KEY_DATA_PUTTER.ROUND_PRO_NUMBER}
        />
        <StrokesGainedHelp onPress={onShowModal} />
        <GraphPutterBar measurementUnits={units} data={dataClub} />
      </View>
    );
  };

  return (
    <View style={appStyles.flex}>
      <ScrollView>
        <View style={[appStyles.flex, appStyles.spaceBetween]}>
          <StatusBar barStyle="dark-content" />
          <View>
            <View style={[appStyles.whiteBg, appStyles.pTMd]}>
              <Image
                style={[styles.cardBodyImg, appStyles.alignCenter]}
                source={getClubImage()}
              />
            </View>

            <View
              style={[
                appStyles.pSm,
                appStyles.hr,
                appStyles.hrTop,
                appStyles.row,
                appStyles.whiteBg,
              ]}
            >
              <Text style={appStyles.mRSm}>
                {isBall
                  ? t('my_bag.club.detail.ball')
                  : t('my_bag.detail.brand')}
              </Text>
              <RowHaveLogo
                brand={brand?.name || clubDetail.manufacturer}
                model={brand ? model?.name : clubDetail.modelName}
              />
            </View>
            {!isBall ? (
              <View>
                {hasLoft ? (
                  <FormItem
                    label={t('my_bag.add_specs.supporting_copy.loft')}
                    value={loft ? loft : ''}
                    onPress={() => {
                      toggleBottomSheets(sheetLoftRef);
                      setToggledLoft(!toggledLoft);
                    }}
                    {...sameFormItemProps}
                    disabled={!canEditLoft}
                    hasIconArrowRight={canEditLoft}
                  />
                ) : null}
                {!isPutter && (
                  <FormItem
                    label={t('my_bag.add_specs.supporting_copy.shaft_flex')}
                    value={shaftFlex ? shaftFlex : 'Select'}
                    onPress={() => {
                      toggleBottomSheets(sheetFlexRef);
                      setToggledShaftFlex(!toggledShaftFlex);
                    }}
                    {...sameFormItemProps}
                  />
                )}
                <FormItem
                  label={t('my_bag.add_specs.supporting_copy.shaft_length')}
                  value={shaftLength ? shaftLength : 'Select'}
                  onPress={() => {
                    toggleBottomSheets(sheetLengthRef);
                    setToggledShaftLength(!toggledShaftLength);
                  }}
                  {...sameFormItemProps}
                />
                {!isPutter && hasFaceLoftLieAdj && (
                  <FormItem
                    label={t(
                      'my_bag.add_specs.supporting_copy.face_lie_adjustment',
                    )}
                    value={faceLieAdjustment ? faceLieAdjustment : 'Select'}
                    onPress={() => {
                      toggleBottomSheets(sheetLieAdjustmentRef);
                      setToggledLieAdjustments(!toggledLieAdjustments);
                    }}
                    {...sameFormItemProps}
                  />
                )}
                {!isPutter && hasFaceLoftLieAdj && (
                  <FormItem
                    label={t(
                      'my_bag.add_specs.supporting_copy.face_loft_adjustment',
                    )}
                    value={faceLoftAdjustment ? faceLoftAdjustment : 'Select'}
                    onPress={() => {
                      toggleBottomSheets(sheetLoftAdjustmentRef);
                      setToggledLoftAdjustments(!toggledLoftAdjustments);
                    }}
                    {...sameFormItemProps}
                  />
                )}
              </View>
            ) : null}
            <FormItem
              label={`${t('my_bag.club.detail.active')} ${
                isBall ? t('my_bag.club.detail.ball') : t('Club')
              }`}
              toggle
              currentStatus={clubStatus}
              setStatus={changeClubStatus}
              {...sameFormItemProps}
            />
            <View
              style={{
                marginTop: 20,
              }}
            >
              <TouchableOpacity
                onPress={alertCheckForDelete}
                disabled={loading}
                style={[
                  appStyles.viewShadow,
                  appStyles.row,
                  styles.buttonDelete,
                ]}
              >
                <Text style={[appStyles.pSm, appStyles.red, appStyles.hCenter]}>
                  {deleting
                    ? `${t('my_bag.club.detail.deleting')} ${
                        isBall
                          ? t('my_bag.club.detail.ball')
                          : t('my_bag.club.detail.club')
                      }`
                    : `${t('my_bag.club.detail.delete')} ${
                        isBall
                          ? t('my_bag.club.detail.ball')
                          : t('my_bag.club.detail.club')
                      }`}
                </Text>
                <IcDeleteClub style={{marginRight: 23}} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
      {(isValuesChanged || isAllValuesSaved) && (
        <View
          style={[
            appStyles.whiteBg,
            appStyles.pTSm,
            appStyles.pBMd,
            appStyles.pHXs,
          ]}
        >
          <Button
            text={
              !isValuesChanged && isAllValuesSaved
                ? 'common.changes_saved'
                : 'common.save_changes'
            }
            textColor="white"
            backgroundColor={
              !isValuesChanged && isAllValuesSaved ? '#3ABA56' : 'black'
            }
            leftTextIcon={!isValuesChanged && isAllValuesSaved ? '􀆅' : ''}
            leftTextIconStyle={{marginRight: 10}}
            borderColor={
              !isValuesChanged && isAllValuesSaved ? '#3ABA56' : 'black'
            }
            onPress={alertCheckForSave}
            loadingMode={'dark'}
            centered
            style={[appStyles.xl]}
            DINbold
            disabled={!isValuesChanged && isAllValuesSaved}
          />
        </View>
      )}
      {hasLoft && Platform.OS === 'ios' && (
        <Selector
          ref={sheetLoftRef}
          type="number"
          value={loft}
          options={allClubLofts}
          onChange={changeLoft}
          toggled={toggledLoft}
          setToggled={setToggledLoft}
        />
      )}
      {hasLoft && Platform.OS === 'android' && (
        <AndroidNumberPicker
          dataType={SELECTOR_TYPE.LOFT}
          selectorType=""
          value={loft}
          options={allClubLofts}
          setValue={changeLoft}
          visible={toggledLoft}
          onClose={() => setToggledLoft(false)}
          title={formatTitle('')}
        />
      )}
      {!isPutter && Platform.OS === 'ios' && (
        <Selector
          selectorType="shaftFlex"
          ref={sheetFlexRef}
          type="number"
          value={shaftFlex}
          options={allClubShaftFlex}
          onChange={changeShaftFlex}
          toggled={toggledShaftFlex}
          setToggled={setToggledShaftFlex}
        />
      )}
      {!isPutter && Platform.OS === 'android' && (
        <AndroidNumberPicker
          dataType={SELECTOR_TYPE.SHAFT_FLEX}
          selectorType="shaftFlex"
          value={shaftFlex}
          options={allClubShaftFlex}
          visible={toggledShaftFlex}
          setValue={changeShaftFlex}
          onClose={() => setToggledShaftFlex(false)}
          title={formatTitle('shaftFlex')}
        />
      )}
      {Platform.OS === 'ios' && (
        <Selector
          selectorType="shaftLength"
          ref={sheetLengthRef}
          type="number"
          value={shaftLength}
          options={allClubShaftLength}
          onChange={changeShaftLength}
          toggled={toggledShaftLength}
          setToggled={setToggledShaftLength}
        />
      )}
      {Platform.OS === 'android' && (
        <AndroidNumberPicker
          dataType={SELECTOR_TYPE.SHAFT_LENGTH}
          selectorType="shaftLength"
          value={shaftLength}
          options={allClubShaftLength}
          visible={toggledShaftLength}
          setValue={changeShaftLength}
          onClose={() => setToggledShaftLength(false)}
          title={formatTitle('shaftLength')}
        />
      )}
      {!isPutter && Platform.OS === 'ios' && hasFaceLoftLieAdj && (
        <Selector
          selectorType="faceLie"
          ref={sheetLieAdjustmentRef}
          type="number"
          value={faceLieAdjustment}
          options={allClubLieAdjustments}
          onChange={changeFaceLieAdjustment}
          toggled={toggledLieAdjustments}
          setToggled={setToggledLieAdjustments}
        />
      )}
      {!isPutter && hasFaceLoftLieAdj && Platform.OS === 'android' && (
        <AndroidNumberPicker
          dataType={SELECTOR_TYPE.FACE_LIE_ADJUSTMENT}
          selectorType="faceLie"
          value={faceLieAdjustment}
          options={allClubLieAdjustments}
          setValue={changeFaceLieAdjustment}
          visible={toggledLieAdjustments}
          onClose={() => setToggledLieAdjustments(false)}
          title={formatTitle('faceLie')}
        />
      )}
      {!isPutter && Platform.OS === 'ios' && hasFaceLoftLieAdj && (
        <Selector
          selectorType="faceLoft"
          ref={sheetLoftAdjustmentRef}
          type="number"
          value={faceLoftAdjustment}
          options={allClubLoftAdjustments}
          onChange={changeFaceLoftAdjustment}
          toggled={toggledLoftAdjustments}
          setToggled={setToggledLoftAdjustments}
        />
      )}
      {!isPutter && hasFaceLoftLieAdj && Platform.OS === 'android' && (
        <AndroidNumberPicker
          dataType={SELECTOR_TYPE.FACE_LOFT_ADJUSTMENT}
          selectorType="faceLoft"
          value={faceLoftAdjustment}
          options={allClubLoftAdjustments}
          setValue={changeFaceLoftAdjustment}
          visible={toggledLoftAdjustments}
          onClose={() => setToggledLoftAdjustments(false)}
          title={formatTitle('faceLoft')}
        />
      )}
      {loading && (
        <ActivityIndicator
          style={appStyles.absoluteFill}
          color="black"
          size="large"
        />
      )}
      {isVisible && (
        <View
          style={[
            appStyles.absoluteFill,
            {
              backgroundColor: 'rgba(76, 76, 76, 0.9)',
              justifyContent: 'center',
            },
          ]}
        >
          <ModalStrokesGained visible={isVisible} onClose={onHideModal} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  cardBodyImg: {
    height: undefined,
    aspectRatio: 1,
    width: wp('100%'),
  },
  buttonDelete: {
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 8,
    backgroundColor: 'white',
    marginHorizontal: 10,
    marginBottom: 30,
    height: 60,
  },
  buttonCompare: {
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 8,
    backgroundColor: 'white',
    marginHorizontal: 10,
    marginBottom: 10,
    height: 60,
  },
  strokeGainedBoxContainer: {
    width: '100%',
    marginBottom: 10,
  },
  strokeChildBox: {
    borderRadius: 8,
    ...appStyles.viewShadowLight,
    backgroundColor: 'white',
    paddingVertical: 7,
    paddingHorizontal: 9,
    marginHorizontal: 5,
  },
});

const mapDispatchToProps = {updateBagList};

export default connect(null, mapDispatchToProps)(DetailInMyBag);
