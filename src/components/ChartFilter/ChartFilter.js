import React from 'react';
import {View, TouchableWithoutFeedback} from 'react-native';

import Text from '../Text';

import appStyles from '../../styles/global';

const ChartFilter = ({type, filter, setFilter, options}) => {
  return (
    <View
      style={[
        appStyles.row,
        appStyles.vCenter,
        appStyles.greyBg,
        appStyles.pXs,
        appStyles.pill,
        appStyles.mBSm,
      ]}
    >
      {options.map(filterType => {
        const activeStyle =
          filter === filterType ? [appStyles.blackBg, appStyles.pill] : [];

        return (
          <TouchableWithoutFeedback
            key={filterType}
            onPress={() => setFilter(filterType)}
          >
            <View style={[appStyles.flex, appStyles.pVXs, ...activeStyle]}>
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.xs]}
              >
                {filterType}
              </Text>
            </View>
          </TouchableWithoutFeedback>
        );
      })}
    </View>
  );
};

export default ChartFilter;
