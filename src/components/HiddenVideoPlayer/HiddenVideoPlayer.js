import React, {useCallback, useRef, useState, useImperativeHandle} from 'react';
import Video from 'react-native-video';
import {NativeModules, Platform} from 'react-native';

const {VideoPlayerManager} = NativeModules;

// interface HiddenVideoPlayerProps {
//   videoUri: string;
//   repeat?: boolean;
// }

function HiddenVideoPlayer(props, ref) {
  const [paused, setPaused] = useState(true);
  const videoRef = useRef(null);

  useImperativeHandle(ref, () => ({
    play: () => {
      handlePlayVideo();
    },
    pause: () => {
      setPaused(true);
    },
  }));

  const handlePlayVideo = useCallback(() => {
    if (!props.videoUri) {
      return;
    }
    if (Platform.OS === 'android') {
      return VideoPlayerManager?.showVideoPlayer?.(props.videoUri);
    }
    setPaused(false);
    // @ts-ignore
    videoRef?.current?.presentFullscreenPlayer();
  }, []);

  if (Platform.OS === 'android') {
    return null;
  }

  return (
    <Video
      ref={videoRef}
      source={{uri: props.videoUri}}
      paused={paused}
      repeat={props.repeat}
      style={{width: 0, height: 0}}
    />
  );
}

export default React.forwardRef(HiddenVideoPlayer);
