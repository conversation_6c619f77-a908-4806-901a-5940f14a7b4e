import React from 'react';
import PropTypes from 'prop-types';
import {View} from 'react-native';
import DeviceInfo from 'react-native-device-info';

import TitleLogo from '../../../assets/imgs/logo-sm-mytm.svg';
import BlackLogo from '../../../assets/imgs/logo-sm-mytm-black.svg';
import TabletBlackLogo from '../../../assets/imgs/logo-lg-mytm-black.svg';
import IntroLogo from '../../../assets/imgs/logo-lg-mytm.svg';
import TabletLogo from '../../../assets/imgs/logo-tablet.svg';

const isTablet = DeviceInfo.isTablet();

const Logo = ({intro, style, isBlack}) => {
  return (
    <View style={style}>
      {isBlack && isTablet ? (
        <TabletBlackLogo />
      ) : isBlack ? (
        <BlackLogo />
      ) : isTablet ? (
        <TabletLogo />
      ) : intro ? (
        <IntroLogo />
      ) : (
        <TitleLogo />
      )}
    </View>
  );
};

Logo.propTypes = {
  intro: PropTypes.bool,
};

Logo.defaultProps = {
  intro: false,
};

export default Logo;
