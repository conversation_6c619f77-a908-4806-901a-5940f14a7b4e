import React from 'react';
import PropTypes from 'prop-types';
import {TouchableOpacity} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';

import Text from '../Text';

import CloseIconSm from '../../../assets/imgs/icon-close-sm.svg';
import CloseIconLg from '../../../assets/imgs/icon-close-lg.svg';

import appStyles from '../../styles/global';

const HeaderRightButton = ({
  isLargeIcon,
  text,
  onPress,
  dark,
  isClose,
  disabled = false,
  styleText,
}) => {
  const isTablet = DeviceInfo.isTablet();
  return (
    <TouchableOpacity
      style={{paddingHorizontal: wp('4%')}}
      onPress={onPress}
      disabled={disabled}
    >
      {isClose ? (
        isTablet ? (
          <CloseIconLg />
        ) : isLargeIcon ? (
          <CloseIconLg />
        ) : (
          <CloseIconSm />
        )
      ) : (
        <Text
          style={[
            disabled
              ? appStyles.grey
              : dark
              ? appStyles.black
              : appStyles.white,
            {
              paddingLeft: wp('2%'),
              paddingVertical: wp('1%'),
            },
            styleText,
          ]}
        >
          {text}
        </Text>
      )}
    </TouchableOpacity>
  );
};

HeaderRightButton.propTypes = {
  text: PropTypes.string,
  onPress: PropTypes.func,
  dark: PropTypes.bool,
  isClose: PropTypes.bool,
  isLargeIcon: PropTypes.bool,
};

HeaderRightButton.defaultProps = {
  text: '',
  onPress: () => {},
  dark: false,
  isClose: false,
  isLargeIcon: false,
};

export default HeaderRightButton;
