import React, {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {View} from 'react-native';
import Picker from '@gregfrench/react-native-wheel-picker';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';

import appStyles from '../../styles/global';
import styles from './styles';

const HandicapPicker = ({handicap, addHandicap, maxHandicap, minHandicap}) => {
  // fix case maxHandicap = - 0.1, this design can not select -0.2; -0.3;...
  const calculatedMaxHandicap =
    maxHandicap < 0 && maxHandicap > -1 ? -1.9 : maxHandicap;
  const [wholeNumber, setWholeNumber] = useState(
    handicap ? parseFloat(handicap?.toString().split('.')[0]) : 16,
  );
  const [decimal, setDecimal] = useState(
    handicap && handicap?.toString().split('.')[1]
      ? parseFloat(handicap?.toString().split('.')[1])
      : 0,
  );

  let maxWholeNumber = !isNaN(calculatedMaxHandicap)
    ? parseFloat(calculatedMaxHandicap?.toString().split('.')[0])
    : 54;
  let maxDecimal = !isNaN(calculatedMaxHandicap)
    ? parseFloat(calculatedMaxHandicap?.toString().split('.')[1])
    : 0;
  maxDecimal = maxDecimal || 0;

  const settingMinHandicap = minHandicap || -10;
  if (maxWholeNumber < settingMinHandicap) {
    maxWholeNumber = settingMinHandicap;
  }
  useEffect(() => {
    if (
      wholeNumber === settingMinHandicap ||
      (wholeNumber === maxWholeNumber && decimal > maxDecimal)
    ) {
      addHandicap(parseFloat(wholeNumber));
    } else {
      addHandicap(parseFloat(`${wholeNumber}.${decimal}`));
    }
  }, [wholeNumber, decimal]);

  const getWholeNumberOptions = () => {
    const options = [];
    for (let i = settingMinHandicap; i <= maxWholeNumber; i++) {
      options.push(
        <Picker.Item key={i} label={`${i < 0 ? `+${-i}` : i}`} value={i} />,
      );
    }
    return options;
  };

  const getDecimalOptions = () => {
    const options = [];
    if (wholeNumber < maxWholeNumber) {
      for (let i = 0; i <= 9; i++) {
        options.push(<Picker.Item key={i} label={`.${i}`} value={i} />);
      }
    } else {
      for (let i = 0; i <= maxDecimal; i++) {
        options.push(<Picker.Item key={i} label={`.${i}`} value={i} />);
      }
    }
    return options;
  };
  // wholeNumber === -10 || wholeNumber === maxWholeNumber && maxDecimal === 0
  return (
    <View style={[styles.pickerContainer, appStyles.row]}>
      <Picker
        style={appStyles.flex}
        itemStyle={{color: 'black', fontSize: hp('3%')}}
        selectedValue={wholeNumber}
        onValueChange={value => setWholeNumber(value)}
      >
        {getWholeNumberOptions()}
      </Picker>
      {wholeNumber === settingMinHandicap ||
      (wholeNumber === maxWholeNumber && maxDecimal === 0) ? null : (
        <Picker
          style={appStyles.flex}
          itemStyle={{color: 'black', fontSize: hp('3%')}}
          selectedValue={decimal}
          onValueChange={value => setDecimal(value)}
        >
          {getDecimalOptions()}
        </Picker>
      )}
    </View>
  );
};

HandicapPicker.propTypes = {
  addQuizHandicap: PropTypes.func,
};

HandicapPicker.defaultProps = {
  addQuizHandicap: () => {},
};

export default HandicapPicker;
