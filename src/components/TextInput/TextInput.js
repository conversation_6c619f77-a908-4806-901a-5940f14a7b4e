import React, {useRef} from 'react';
import PropTypes from 'prop-types';
import {View, TextInput, TouchableOpacity, Platform} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome5';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Text from 'components/Text';

import appStyles from '../../styles/global';
import styles from './styles';
import {GREY} from '../../config';

const TextInputComponent = ({
  style,
  placeholder,
  onChangeText,
  defaultValue,
  leftIcon,
  rightIcon,
  clearInput,
  mode,
  secureTextEntry,
  autoCapitalize,
  keyboardType,
  returnKeyType,
  disabled,
  autoCorrect,
  textContentType,
  stylesInput,
  styleLeftIcon,
  placeholderTextColor,
  value,
  onFocus,
  onSubmitEditing,
  inputProps,
  refInput,
  isDoubleText = false,
}) => {
  const inputRef = useRef(null);
  return (
    <View
      style={[
        appStyles.pHMd,
        appStyles.row,
        appStyles.hCenter,
        styles.textInputContainer,
      ].concat(style)}
    >
      {leftIcon ? (
        <Icon
          name={leftIcon}
          color={GREY}
          size={wp('4%')}
          style={appStyles.mRSm}
          {...styleLeftIcon}
        />
      ) : null}
      {value && isDoubleText && (
        <Text size={16} gray style={{
          marginTop: Platform.OS === 'ios' ? 7 : 0,
        }}>
          {placeholder}
        </Text>
      )}

      <TextInput
        style={[
          appStyles.sm,
          appStyles.flex,
          {flexWrap: 'wrap'},
          mode === 'light' ? styles.textInputLight : styles.textInput,
          {...stylesInput},
        ]}
        placeholderTextColor={placeholderTextColor ?? GREY}
        placeholder={placeholder}
        onChangeText={value => onChangeText(value.trimStart())}
        defaultValue={defaultValue}
        secureTextEntry={secureTextEntry}
        autoCapitalize={autoCapitalize}
        keyboardType={keyboardType}
        returnKeyType={returnKeyType || 'done'}
        disabled={disabled}
        underlineColorAndroid="transparent"
        autoCorrect={autoCorrect}
        textContentType={textContentType}
        value={value}
        ref={ref => {
          inputRef.current = ref;
          if (refInput) {
            refInput(ref);
          }
        }}
        onSubmitEditing={onSubmitEditing}
        onFocus={onFocus}
        {...inputProps}
      />

      {/* Clear input text */}
      {rightIcon && defaultValue?.length ? (
        <TouchableOpacity
          style={[appStyles.pLSm, {marginLeft: 'auto'}]}
          onPress={() => {
            inputRef?.current?.clear?.();
            clearInput();
          }}
          disabled={disabled}
        >
          <Icon name={rightIcon} color={GREY} size={wp('4%')} />
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

TextInputComponent.propTypes = {
  placeholder: PropTypes.string,
  onChangeText: PropTypes.func,
  defaultValue: PropTypes.string,
  clearInput: PropTypes.func,
  mode: PropTypes.string,
  secureTextEntry: PropTypes.bool,
  autoCapitalize: PropTypes.string,
  keyboardType: PropTypes.string,
  returnKeyType: PropTypes.string,
  autoCorrect: PropTypes.bool,
};

TextInputComponent.defaultProps = {
  placeholder: '',
  onChangeText: () => {},
  defaultValue: '',
  clearInput: () => {},
  mode: '',
  secureTextEntry: false,
  autoCapitalize: 'sentences',
  keyboardType: 'default',
  autoCorrect: true,
  returnKeyType: null,
};

export default TextInputComponent;
