import React, {
  useMemo,
  useState,
  useEffect,
  useRef,
  useImperativeHandle,
} from 'react';
import {View, TouchableOpacity, Platform, StyleSheet} from 'react-native';
import BottomSheet from 'reanimated-bottom-sheet';
import {
  NOT_APPLICABLE,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_CLASS,
  SCREEN_TYPES,
  SYNC_ROUND_TYPE,
} from 'utils/constant';
import appStyles from 'styles/global';
import Text from 'components/Text';
import moment from 'moment';
import {t} from 'i18next';
import {showErrorToast, showToast} from 'utils/toast';
import ButtonUSGA from 'screens/Scores/components/ButtonUSGA';
import {GA_logCtaEvent, GA_logScreenViewV2} from 'utils/googleAnalytics';
import {getSyncRound3rdParty} from 'utils/commonVariable';
import {checkOverallStatsCompleted} from 'utils/singleRoundStat';
import {getReadableTextFromErrMsg} from 'utils/convert';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/Ionicons';
import Success_Icon from 'assets/imgs/ic_submit_ghin_success.svg';
import Toast from 'react-native-toast-message';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import WheelPickerBottomSheet from 'components/WheelPickerBottomSheet';
import {getGhinRoundData} from 'utils/play';
import {useSelector} from 'react-redux';
import RightArrowIcon from 'assets/imgs/playScore/ic_right_arrow_white.svg';
import FormItem from 'components/FormItem';
import {postRoundToUSGA} from 'requests/play-stats';
import {checkCanPostRoundToUSGA} from 'utils/user';
import {
  isConnectedNetwork,
  showToastErrorInternet,
} from 'utils/queueAndNetwork';

const BottomSheetSubmitGhinConfirm = React.forwardRef(
  (
    {
      currentRoundInfo,
      teeValueDisplay,
      onSuccess,
      onCloseSuccessModal,
      onPressSubmit,
      navigation,
      origin,
      isModalShow,
      onCloseModal,
      isPostWithRound,
      onReloadListUSGA,
      ghinCourseId,
    },
    ref,
  ) => {
    const user = useSelector(state => state.user);
    const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
    const canPostToUSGA = checkCanPostRoundToUSGA(showHideFeatures, user);
    const [loadingSubmitRound, setLoadingSubmitRound] = useState(false);
    const [scoreToParTextWidth, setScoreToParTextWidth] = useState(0);
    let scoreToParValue = currentRoundInfo?.scoreToPar;
    if (scoreToParValue == null) {
      if (currentRoundInfo?.holes?.length > 0) {
        const totalPar = currentRoundInfo.holes.reduce((sum, hole) => {
          if (hole.completeHole) {
            return sum + (hole.par || 0);
          }
          return sum;
        }, 0);
        scoreToParValue = currentRoundInfo?.totalScore - totalPar;
      } else {
        scoreToParValue =
          currentRoundInfo?.totalScore - currentRoundInfo?.coursePar;
      }
    }
    //use when post new score
    const [newRoundIdMyTm, setNewRoundIdMyTM] = useState(null);
    const [showSuccess, setShowSuccess] = useState(null);
    const syncType = getSyncRound3rdParty();
    let commonErrorText = t('ghin.score.error.common_error_title');
    if (syncType === SYNC_ROUND_TYPE.WHS) {
      commonErrorText = t('ghin.score.error.common_error_title_whs');
    }
    const isGolfCanada = syncType === SYNC_ROUND_TYPE.WHS;
    const [toastOpacity, setToastOpacity] = useState(0);
    //Ref
    const toastRef = useRef(null);
    const sheetRef = useRef(null);
    const teesRef = useRef(null);
    const scoreTypeRef = useRef(null);
    const datePlayedRef = useRef(null);

    const [ghinTeeSelected, setGhinTeeSelected] = useState(null);
    const [ghinTeeList, setGhinTeeList] = useState([]);
    const [scoreType, setScoreType] = useState('Away');
    const [datePlayed, setDatePlayed] = useState(
      currentRoundInfo?.playedOn ||
        (isGolfCanada ? moment() : moment().toISOString()),
    );
    const [numberOfHolesPlayed, setNumberOfHolesPlay] = useState(
      currentRoundInfo?.holes?.filter?.(item => item.completeHole)?.length,
    );
    const numPar = currentRoundInfo?.coursePar;

    useEffect(() => {
      if (currentRoundInfo?.holes?.length > 0) {
        setNumberOfHolesPlay(
          currentRoundInfo?.holes?.filter?.(item => item.completeHole)?.length,
        );
      }
    }, [currentRoundInfo?.holes]);

    useEffect(() => {
      if (ghinCourseId && ghinCourseId !== '0') {
        if (canPostToUSGA) {
          getDataGhinCourse(ghinCourseId);
        }
      }
    }, [ghinCourseId]);

    const getDataGhinCourse = async ghinCourseId => {
      try {
        let {ghinTeeList, ghinTee} = await getGhinRoundData(ghinCourseId, user);
        setGhinTeeSelected(ghinTee);
        setGhinTeeList(ghinTeeList);
      } catch (error) {
      } finally {
      }
    };

    const teeRating = useMemo(() => {
      if (ghinTeeSelected) {
        let currentRatingType = 'total';
        let courseGhinRating = ghinTeeSelected?.ratings?.find?.(
          item => item.ratingType?.toLowerCase() === currentRatingType,
        );
        if (courseGhinRating) {
          const teeRatingDisplay = `${courseGhinRating.courseRating || 0} / ${
            courseGhinRating.slopeRating || 0
          } / ${courseGhinRating.courseHandicap || 0}`;
          return teeRatingDisplay ? teeRatingDisplay : '0 / 0 / 0';
        } else {
          return '0 / 0 / 0';
        }
      } else {
        return '0 / 0 / 0';
      }
    }, [ghinTeeSelected]);

    const sheetShowToast = toastConfig => {
      if (toastRef?.current) {
        setToastOpacity(1);
        showToast({
          ...toastConfig,
          topOffset: hp(7),
          ref: toastRef,
          onHide: () => {
            setToastOpacity(0);
          },
        });
      }
    };

    const sheetShowErrorToast = toastConfig => {
      if (toastRef?.current) {
        setToastOpacity(1);
        showErrorToast({
          ...toastConfig,
          topOffset: hp(7),
          ref: toastRef,
          onHide: () => {
            setToastOpacity(0);
          },
        });
      }
    };

    useImperativeHandle(ref, () => ({
      snapTo: index => {
        sheetRef?.current?.snapTo(index);
      },
      showToast: toastConfig => {
        sheetShowToast(toastConfig);
      },
      showErrorToast: toastConfig => {
        sheetShowErrorToast(toastConfig);
      },
    }));

    useEffect(() => {
      if (showSuccess) {
        setTimeout(() => {
          ref.current?.snapTo?.(1);
          setShowSuccess(false);
          onCloseModal?.();
          if (onCloseSuccessModal) {
            onCloseSuccessModal?.();
          } else {
            navigation.navigate('App', {screen: 'Play'});
          }
        }, 2000);
      }
    }, [showSuccess]);

    const showModalSuccess = () => {
      setShowSuccess(true);
    };
    const navigateToConfirmCourseScreen = successResult => {
      navigation?.navigate('GhinConfirmCourse', {
        currentRoundInfo: currentRoundInfo,
        newRoundId: successResult?.roundId || newRoundIdMyTm,
        numPar: numPar,
        teeValueDisplay: teeValueDisplay,
        onSuccess,
        showModalSuccess,
      });
    };

    const navigateToConfirmTeeScreen = successResult => {
      navigation?.navigate('GhinConfirmTee', {
        currentRoundInfo: currentRoundInfo,
        newRoundId: successResult?.roundId || newRoundIdMyTm,
        numPar: numPar,
        teeValueDisplay: teeValueDisplay,
        onSuccess,
        showModalSuccess,
        ghinCourseDetail: {
          courseId: successResult?.ghinCourseId,
        },
      });
    };

    const navigateToConfirm = (errorCode, successResult) => {
      switch (errorCode) {
        case 'ERROR_MAP_GHIN_COURSE':
        case 'ERROR_COURSE_NOT_FOUND':
          navigateToConfirmCourseScreen(successResult);
          break;
        case 'ERROR_MAP_TEE_SET_RATINGS':
        case 'ERROR_TEE_NOT_FOUND':
          navigateToConfirmTeeScreen(successResult);
          break;
        default:
          break;
      }
    };

    const showError = (errorMsg, errorCode) => {
      if (errorCode) {
        let newMsg = errorMsg || '';
        let title = commonErrorText;
        switch (errorCode) {
          case 'ERROR_MAP_NUMBER_HOLES_PLAYED':
            title = t('ghin.score.error.title.invalid_score');
            newMsg = t('ghin.score.error.post_9_score_invalid');
            break;
          default:
            break;
        }
        sheetShowToast({
          type: 'error',
          message: title,
          subText: newMsg,
        });
      } else {
        sheetShowToast({
          type: 'error',
          message: commonErrorText,
          subText: errorMsg,
        });
      }
    };

    const trackGA = () => {
      try {
        let screenName = '';
        let screenClass = '';
        let screenType = '';
        switch (origin) {
          case 'Scores':
            screenName = 'scores - rounds';
            screenType = SCREEN_TYPES.PLAY;
            screenClass = SCREEN_CLASS.STATS;
            break;
          case 'ScoreDetail':
          case 'RoundSummaryPopup':
          case 'RoundOverview':
            screenName = 'round stats detail';
            screenType = SCREEN_TYPES.PLAY;
            screenClass = SCREEN_CLASS.STATS;
            break;
          case 'ScoreAddEditDetails':
            screenName = 'post score - details';
            screenType = SCREEN_TYPES.PLAY;
            screenClass = SCREEN_CLASS.PLAY;
            break;
          default:
            break;
        }
        GA_logCtaEvent(
          'usga_post_score',
          t('ghin.score.cta.post_score_to_usga'),
          screenName,
          screenClass,
          screenType,
        );
        GA_logScreenViewV2(
          PAGE_NAME.POST_SCORE_TO_USGA_SUCCESS,
          PAGE_CATEGORY.PLAY_POST_A_SCORE,
          SCREEN_CLASS.PLAY,
          SCREEN_CLASS.PLAY,
        );
      } catch (error) {}
    };

    const getScoreType = () => {
      try {
        let srcType = '';
        switch (scoreType) {
          case 'Away':
            srcType = 'A';
            break;
          case 'Home':
            srcType = 'H';
            break;
          case 'Comp':
            srcType = 'T';
            break;
          default:
            break;
        }
        return srcType;
      } catch (error) {
        console.log('error', error);
        return '';
      }
    };
    const submitCurrentRoundToUSGA = async () => {
      let submitResult = {};
      let roundScore = currentRoundInfo?.totalScore;

      try {
        let postBy = currentRoundInfo?.generatedBy;
        if (!postBy) {
          postBy = Platform.OS === 'ios' ? 'iOS Device' : 'Android Device';
        }
        let userTimezone = currentRoundInfo?.userTimezone;
        if (!userTimezone) {
          userTimezone = new Date().getTimezoneOffset() / -60;
        }
        // Check if only all front 9 holes (holes 1–9) are completed
        const onlyFrontNineCompleted =
          currentRoundInfo?.holes.filter(holeItem => holeItem.completeHole)
            .length === 9 &&
          currentRoundInfo?.holes
            .slice(0, 9)
            .every(holeItem => holeItem.completeHole);

        // Check if only all back 9 holes (holes 10–18) are completed
        const onlyBackNineCompleted =
          currentRoundInfo?.holes.filter(holeItem => holeItem.completeHole)
            .length === 9 &&
          currentRoundInfo?.holes
            .slice(9, 18)
            .every(holeItem => holeItem.completeHole);

        let dataSubmit = {
          teeName: ghinTeeSelected?.teeSetRatingName,
          datePlayed: datePlayed,
          numberOfHolesPlayed: numberOfHolesPlayed,
          front9Score: onlyFrontNineCompleted ? roundScore : null,
          back9Score: onlyBackNineCompleted ? roundScore : null,
          eighteenHoleScore:
            !onlyFrontNineCompleted && !onlyBackNineCompleted
              ? roundScore
              : null,
          userTimezone: userTimezone,
          generatedBy: postBy,
          roundType: 'Practice',
          roundMode: currentRoundInfo?.roundMode,
          sourceType: getScoreType(),
          ghinCourseId: ghinCourseId,
          ghinCourseName:
            currentRoundInfo?.ghinCourseName || currentRoundInfo?.courseName,
          ghinTeeSetId: ghinTeeSelected?.teeSetRatingId,
          ghinTeeSetName: ghinTeeSelected?.teeSetRatingName,
          postScoreGhin: true,
          roundId: currentRoundInfo?.id,
        };
        // console.log('dataSubmit', dataSubmit);
        submitResult = await postRoundToUSGA(dataSubmit);

        onReloadListUSGA?.();
        return submitResult;
      } catch (error) {
        console.log(error.message);
        sheetShowErrorToast({
          error,
          title: commonErrorText,
          description:
            error.message ||
            'An error occurred saving your score. Please try again',
        });
        return;
      }
    };

    const onSubmitRoundToGhin = async () => {
      // teesRef?.current?.snapTo(0);
      // return;
      const checkNetwork = isConnectedNetwork();
      if (!checkNetwork) {
        showToastErrorInternet();
        return;
      }
      setLoadingSubmitRound(true);
      try {
        trackGA();
        if (currentRoundInfo?.id || newRoundIdMyTm) {
          let isStatsCompleted = await checkOverallStatsCompleted(
            currentRoundInfo?.id || newRoundIdMyTm,
          );
          if (!isStatsCompleted) {
            sheetShowToast({
              type: 'error',
              message: commonErrorText,
              subText: t(
                'ghin.score.error.updating_stats_before_post_score_error',
              ),
            });
            setLoadingSubmitRound(false);
            return;
          }
        }
        let rs;
        if (onPressSubmit) {
          rs = await onPressSubmit?.(newRoundIdMyTm);
        } else {
          rs = await submitCurrentRoundToUSGA();
        }
        if (rs) {
          if (rs.message) {
            const errMsg = getReadableTextFromErrMsg(rs);
            showError(errMsg, rs.errorCode);
            if (syncType === SYNC_ROUND_TYPE.WHS) {
              setNewRoundIdMyTM(rs.roundId);
              closeBottomSheet();
              navigateToConfirm(rs.errorCode, rs);
            }
          } else if (rs.roundGhinId || rs?.score?.id || rs.golfnetRoundId) {
            showModalSuccess();
            onSuccess?.(rs.roundGhinId || rs?.score?.id || rs.golfnetRoundId);
          }
        }
      } catch (error) {
        try {
          const errMsg = getReadableTextFromErrMsg(error);
          showError(errMsg, error?.errorCode);
          if (syncType === SYNC_ROUND_TYPE.WHS) {
            closeBottomSheet();
            navigateToConfirm(error?.errorCode, error);
          }
        } catch (e) {
          showError(error?.message);
        }
      } finally {
        setLoadingSubmitRound(false);
      }
    };

    const closeBottomSheet = () => {
      if (!loadingSubmitRound) {
        ref.current?.snapTo?.(1);
        onCloseModal?.();
      }
    };

    const titleSyncText = useMemo(() => {
      switch (syncType) {
        case SYNC_ROUND_TYPE.USGA:
          return origin === 'SubmitRoundSummary'
            ? 'ghin.score.title.post_by_hole_score_to_usga'
            : 'ghin.score.title.post_score_to_usga';
        case SYNC_ROUND_TYPE.WHS:
          return origin === 'SubmitRoundSummary'
            ? 'ghin.score.title.post_by_hole_score_to_whs'
            : 'ghin.score.title.post_score_to_whs';
        default:
          break;
      }
    }, [origin, syncType]);
    const descriptionText = useMemo(() => {
      switch (syncType) {
        case SYNC_ROUND_TYPE.USGA:
          return t('ghin.score.post_score_description');
        case SYNC_ROUND_TYPE.WHS:
          return t('ghin.score.post_score_description_whs');
        default:
          break;
      }
    }, [syncType]);
    const onOpenModal = type => {
      setTimeout(() => {
        switch (type) {
          case 'SCORE_TYPE':
            scoreTypeRef.current.snapTo(0);
            break;
          case 'TEES_TYPE':
            teesRef.current.snapTo(0);
            break;
          case 'DATE_TYPE':
            datePlayedRef.current.snapTo(0);
            break;
          default:
            break;
        }
      }, 300);
    };

    // Determine the number of holes to display
    const displayHoleNumber = () => {
      // If holes have been played
      if (numberOfHolesPlayed > 0) {
        // If exactly 9 holes have been played
        if (numberOfHolesPlayed === 9) {
          return 9;
        }
        // If other than 9 holes have been played
        else {
          return 18;
        }
      }
      // If no holes have been played yet
      else {
        // Get from round info in order of priority
        return (
          currentRoundInfo?.numberOfHolesPlayed ||
          currentRoundInfo?.standardHoleNumber ||
          currentRoundInfo?.holes?.length
        );
      }
    };

    const renderInputForm = () => {
      return (
        <View>
          <FormItem
            backgroundColor={'transparent'}
            label={t('home.post_score_detail.supporting_copy.tees')}
            value={
              ghinTeeList.length > 0 ? ghinTeeSelected.teeName : NOT_APPLICABLE
            }
            onPress={() => {
              if (ghinTeeList.length > 0) {
                onOpenModal('TEES_TYPE');
              }
            }}
            style={{paddingBottom: 1}}
            labelStyle={{color: 'white', fontSize: 16}}
            valueStyle={{color: '#e6e6e6', fontSize: 16, paddingRight: 0}}
            customRightIcon={
              ghinTeeList.length > 0 ? (
                <View>
                  <RightArrowIcon style={{top: 10}} />
                </View>
              ) : null
            }
          />
          <View
            style={[
              appStyles.pHSm,
              {
                flexDirection: 'row-reverse',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingBottom: 16,
              },
            ]}
          >
            <Text
              size={12}
              style={[
                appStyles.textRight,
                {
                  color: '#e6e6e6',
                  marginRight: ghinTeeList.length > 0 ? 25 : 0,
                },
              ]}
              onPress={() => {
                if (ghinTeeList.length > 0) {
                  onOpenModal('TEES_TYPE');
                }
              }}
            >
              {teeRating}
            </Text>
            <Text size={12} style={[appStyles.textRight]} white>
              {t('ghin.tee_rating_title')}
            </Text>
          </View>
          <View style={styles.fullwidthLineHorizontal} />
          <FormItem
            backgroundColor={'transparent'}
            label={'Score Type'}
            value={scoreType}
            onPress={() => {
              onOpenModal('SCORE_TYPE');
            }}
            labelStyle={{color: 'white', fontSize: 16}}
            valueStyle={{color: '#e6e6e6', fontSize: 16, paddingRight: 0}}
            customRightIcon={<RightArrowIcon />}
            style={{paddingVertical: 16}}
          />
          <View style={styles.fullwidthLineHorizontal} />
          <FormItem
            backgroundColor={'transparent'}
            label={t('home.post_score_detail.supporting_copy.date_played')}
            value={datePlayed && moment(datePlayed).format('ll')}
            onPress={() => {
              onOpenModal('DATE_TYPE');
            }}
            labelStyle={{color: 'white', fontSize: 16}}
            valueStyle={{color: '#e6e6e6', fontSize: 16, paddingRight: 0}}
            customRightIcon={<RightArrowIcon />}
            style={{paddingVertical: 16}}
          />
          <View style={styles.fullwidthLineHorizontal} />
        </View>
      );
    };

    const renderPostScore = () => {
      return (
        <View
          style={[
            appStyles.fullHeight,
            appStyles.hCenter,
            {
              paddingHorizontal: '4%',
              paddingVertical: '6.3%',
              backgroundColor: isGolfCanada
                ? 'rgba(181, 18, 27, 1)'
                : 'rgba(0, 54, 95, 1)',
            },
          ]}
        >
          <View>
            <View
              style={[
                appStyles.row,
                {
                  justifyContent: 'center',
                  marginTop: Platform.OS === 'android' ? -15 : 0,
                  left: scoreToParTextWidth / 2,
                },
              ]}
            >
              <Text Din79Font weight={700} white size={54}>
                {+currentRoundInfo?.totalScore}
              </Text>
              {scoreToParValue != null && (
                <View
                  style={[
                    appStyles.mLXs,
                    appStyles.alignEnd,
                    styles.scoreToParContainer,
                  ]}
                  onLayout={event => {
                    let {width} = event?.nativeEvent?.layout;
                    setScoreToParTextWidth(width);
                  }}
                >
                  <Text Din79Font weight={'800'} black>
                    {scoreToParValue > 0 ? '+' : ''}
                    {scoreToParValue === 0 ? 'E' : scoreToParValue}
                  </Text>
                </View>
              )}
            </View>
            <View style={[appStyles.row, styles.viewHeader]}>
              <Text
                white
                Din79Font
                weight={800}
                size={22}
                style={[styles.courseName]}
                numberOfLines={1}
              >
                {(
                  currentRoundInfo?.ghinCourseName ||
                  currentRoundInfo?.courseName
                )?.toUpperCase()}
              </Text>
            </View>
            <View style={[styles.viewHeader]}>
              <View style={[appStyles.row, {marginVertical: 10}]}>
                <View style={styles.viewHole}>
                  <Text white size={8} style={{fontWeight: '500'}}>
                    Holes
                  </Text>
                  <Text Din79Font white style={styles.textNumber}>
                    {displayHoleNumber()}
                  </Text>
                </View>
                <View style={styles.viewHole}>
                  <Text white size={8} style={{fontWeight: '500'}}>
                    Par
                  </Text>
                  <Text Din79Font white style={styles.textNumber}>
                    {numPar}
                  </Text>
                </View>
                {!isPostWithRound && (
                  <>
                    <View style={styles.lineVertical} />
                    <View style={styles.viewHole}>
                      <Text white size={8} style={{fontWeight: '500'}}>
                        Date
                      </Text>
                      <Text white style={styles.textNumber}>
                        {moment(currentRoundInfo?.playedOn).format(
                          'MM.DD.YYYY',
                        )}{' '}
                      </Text>
                    </View>
                  </>
                )}
              </View>
              {isPostWithRound ? (
                renderInputForm()
              ) : (
                <View style={styles.lineHorizontal} />
              )}
              <Text white style={styles.titlePostScore}>
                {titleSyncText}
              </Text>
              <Text style={styles.descriptionText} numberOfLines={3}>
                {descriptionText}
              </Text>
            </View>
          </View>
          <ButtonUSGA
            textColor="black"
            onPress={onSubmitRoundToGhin}
            centered
            Din79Font
            style={styles.buttonPostScore}
            loading={loadingSubmitRound}
            disabled={loadingSubmitRound}
            textStyle={[appStyles.uppercase, {fontWeight: '700', fontSize: 12}]}
          />
          <TouchableOpacity
            onPress={() => closeBottomSheet()}
            style={styles.viewClose}
          >
            <Icon name={'close-outline'} color={'#000'} size={24} />
          </TouchableOpacity>
        </View>
      );
    };

    const renderSuccessScore = () => {
      return (
        <View
          style={[
            {
              height: 247,
              paddingTop: 22,
              backgroundColor: isGolfCanada
                ? 'rgba(181, 18, 27, 1)'
                : 'rgba(0, 54, 95, 1)',
            },
            appStyles.hCenter,
          ]}
        >
          <Success_Icon />
          <Text style={styles.viewTitleSuccess}>
            {t('ghin.score.title.post_score_success').toUpperCase()}
          </Text>
          <Text gray style={styles.viewContentSuccess}>
            {!getSyncRound3rdParty() === SYNC_ROUND_TYPE.WHS
              ? 'GolfCanada.score.post_score_success_description'
              : 'ghin.score.post_score_success_description'}
          </Text>
        </View>
      );
    };
    return (
      <Modal
        isVisible={isModalShow}
        style={{margin: 0, backgroundColor: 'transparent'}}
        backdropTransitionOutTiming={0}
        onModalHide={() => {
          setToastOpacity(0);
        }}
      >
        <View style={{flex: 1}}>
          <BottomSheet
            ref={sheetRef}
            snapPoints={[showSuccess ? 247 : isPostWithRound ? 550 : 384, 0]}
            borderRadius={46}
            initialSnap={1}
            onCloseEnd={() => {
              if (isModalShow) {
                onCloseModal?.();
              }
            }}
            renderContent={() => {
              return showSuccess ? renderSuccessScore() : renderPostScore();
            }}
          />
        </View>
        <WheelPickerBottomSheet
          ref={teesRef}
          currentValue={ghinTeeSelected}
          options={ghinTeeList}
          onChange={value => {
            setGhinTeeSelected(value);
          }}
          pickerTitle={t('play.select_tee')}
          keyProp={'teeSetRatingId'}
          labelProp={'teeSetRatingName'}
          sheetsProps={{snapPoints: [520, 0]}}
        />
        <WheelPickerBottomSheet
          ref={scoreTypeRef}
          currentValue={scoreType}
          options={['Home', 'Away', 'Comp']}
          onChange={setScoreType}
          pickerTitle={t('play.select_score_type')}
          type={'text'}
          sheetsProps={{snapPoints: [480, 0]}}
        />
        <WheelPickerBottomSheet
          ref={datePlayedRef}
          type={'date'}
          allowFutureDates={false}
          currentValue={datePlayed}
          onChange={value => setDatePlayed(moment(value).toISOString())}
          pickerTitle={t('play.select_date')}
          sheetsProps={{snapPoints: [520, 0]}}
        />
        <Toast style={{opacity: toastOpacity}} ref={toastRef} />
      </Modal>
    );
  },
);
const styles = StyleSheet.create({
  courseName: {
    flex: 1,
    textAlign: 'center',
    letterSpacing: 1.1,
  },
  viewHeader: {
    marginTop: Platform.OS === 'ios' ? 0 : -5,
    alignItems: 'center',
  },
  viewHole: {
    alignItems: 'center',
    marginHorizontal: 20,
  },
  textNumber: {
    fontSize: 22,
    fontWeight: '800',
    marginTop: Platform.OS === 'ios' ? 0 : -5,
    letterSpacing: 1.1,
  },
  titlePostScore: {
    fontSize: 16,
    fontWeight: '700',
    color: 'rgba(140, 139, 143, 1)',
    marginTop: 24,
  },
  descriptionText: {
    fontSize: 16,
    fontWeight: '400',
    textAlign: 'center',
    color: 'rgba(140, 139, 143, 1)',
    marginTop: 10,
  },
  buttonPostScore: {
    width: '100%',
    position: 'absolute',
    bottom: 25,
    height: 40,
    backgroundColor: '#fff',
  },
  scoreToParContainer: {
    borderRadius: 3,
    backgroundColor: '#fff',
    paddingHorizontal: 5,
    paddingVertical: 1,
    marginBottom: Platform.OS === 'ios' ? 12 : 10,
  },
  lineVertical: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(140, 139, 143, 1)',
  },
  lineHorizontal: {
    flexDirection: 'row',
    width: wp(100) - 16,
    height: 1,
    marginTop: 14,
    backgroundColor: 'rgba(140, 139, 143, 1)',
  },
  fullwidthLineHorizontal: {
    flexDirection: 'row',
    width: wp(100),
    height: 1,
    backgroundColor: '#e6e6e6',
  },
  viewClose: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    right: 20,
    top: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
  },
  viewTitleSuccess: {
    fontSize: 22,
    fontWeight: '800',
    color: 'rgba(255, 255, 255, 1)',
    marginTop: 20,
    textAlign: 'center',
    letterSpacing: 1.1,
  },
  viewContentSuccess: {
    fontSize: 16,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 15,
    marginHorizontal: 26,
  },
});
export default BottomSheetSubmitGhinConfirm;
