import React, {useState, useRef} from 'react';
import {View, StyleSheet, Image, Platform, Pressable} from 'react-native';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';
import {moderateScale} from 'react-native-size-matters';

import Text from 'components/Text';

import appStyles from 'styles/global';
import Ionicons from 'react-native-vector-icons/Ionicons';
import WITBLogo from 'assets/imgs/WITBTMLogo.png';
import IconDeactivate from 'assets/imgs/ic_deactivate.png';
import IcChecked from 'assets/imgs/ic_check_square.png';
import IcUnchecked from 'assets/imgs/ic_uncheck_square.png';
import SwipeRow from 'components/SwipeList/SwipeRow';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolation,
  useAnimatedGestureHandler,
  runOnJS,
} from 'react-native-reanimated';
import {CLUB_NAME} from 'utils/constant';
import {PanGestureHandler} from 'react-native-gesture-handler';

const isTablet = DeviceInfo.isTablet();
const CARD_HEIGHT = 86;

const CardItem = props => {
  const {
    club,
    index,
    isActiveClub,
    selectedItems,
    isCompareClubs,
    handleSelectItems,
    clubCategoryId,
    renderOnPress,
    getClubInitials,
    getClubImage,
    updateStatusScroll,
    changeOffsetPageValue,
    updateRef,
    getRefItem,
  } = props;
  const [borderRight, setBorderRight] = useState({id: null});
  const refItemClub = useRef();
  const offsetMove = useSharedValue(70);

  const widthHideItem = isActiveClub ? -160 : -80;
  const itemSelected = selectedItems[club?.id]?.id;

  const animatedMoveView = useAnimatedStyle(() => {
    const trans = interpolate(offsetMove.value, [0, 160], [70, 0], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      transform: [{translateX: trans}],
    };
  });

  const eventHandler = useAnimatedGestureHandler({
    onStart: (event, ctx) => {
      // pressed.value = true;
    },
    onActive: (event, ctx) => {
      if (event.translationX < 0) {
        runOnJS(changeOffsetPageValue)(event.translationX);
      } else if (event.translationX >= 0) {
        runOnJS(changeOffsetPageValue)(0);
      }
    },
    onEnd: (event, ctx) => {
      if (event.translationX > -50) {
        runOnJS(changeOffsetPageValue)(0);
      }
    },
  });

  const canSelectClub = name => {
    switch (Object.values(selectedItems)[0]?.clubFamily?.name) {
      case CLUB_NAME.DRIVER:
      case CLUB_NAME.FAIRWAY:
        return (
          name === CLUB_NAME.DRIVER ||
          name === CLUB_NAME.FAIRWAY ||
          name === CLUB_NAME.HYBRID
        );
      case CLUB_NAME.HYBRID:
        return (
          name === CLUB_NAME.DRIVER ||
          name === CLUB_NAME.FAIRWAY ||
          name === CLUB_NAME.HYBRID ||
          name === CLUB_NAME.IRONS ||
          name === CLUB_NAME.WEDGE
        );
      case CLUB_NAME.IRONS:
      case CLUB_NAME.WEDGE:
        return (
          name === CLUB_NAME.HYBRID ||
          name === CLUB_NAME.IRONS ||
          name === CLUB_NAME.WEDGE
        );
      case CLUB_NAME.PUTTER:
        return name === CLUB_NAME.PUTTER;
      case CLUB_NAME.BALL:
        return name === CLUB_NAME.BALL;
    }
    return false;
  };
  const renderItemCompare = () => {
    return (
      <Animated.View
        key={club?.id + index}
        style={[
          appStyles.row,
          {
            marginLeft: 15,
            marginBottom: 12,
            alignItems: 'center',
          },
        ]}
      >
        <Pressable
          activeOpacity={0.8}
          onPress={() => handleSelectItems(club)}
          style={{marginRight: 5}}
        >
          {itemSelected ? (
            <Image source={IcChecked} style={{width: 26, height: 26}} />
          ) : (
            <Image
              source={IcUnchecked}
              style={{
                width: 26,
                height: 26,
                tintColor:
                  Object.keys(selectedItems).length > 0 &&
                  !canSelectClub(club?.clubFamily?.name)
                    ? '#BDBDBD'
                    : '#111111',
              }}
            />
          )}
        </Pressable>
        {renderAllClub(club, index)}
      </Animated.View>
    );
  };
  const renderAllClub = (item, index) => {
    return (
      <View
        style={[
          {flex: 1, backgroundColor: '#fff', marginLeft: 10, borderRadius: 10},
          (isCompareClubs || Platform.OS === 'android') && appStyles.viewShadow,
        ]}
      >
        <Pressable
          style={[
            styles.cardBody,
            {height: isTablet ? hp('17%') : CARD_HEIGHT},
            {
              //fix swipe padding on IOS
              marginLeft: 10,
              borderRadius:
                !isCompareClubs && borderRight?.id === item?.id ? 0 : 10,
            },
          ]}
          onPress={() =>
            isCompareClubs
              ? handleSelectItems(item)
              : renderOnPress(clubCategoryId, item)
          }
        >
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              flex: 0.2,
            }}
          >
            <Text
              style={[
                styles.club,
                !item?.inBag
                  ? {
                      color: '#8C8B8F',
                      marginTop: 4,
                      marginBottom: Platform.OS === 'android' ? -10 : 0,
                    }
                  : appStyles.black,
              ]}
              DINbold
            >
              {getClubInitials(item)}
            </Text>
            {!item?.inBag && (
              <Text style={{fontSize: 13, color: '#8C8B8F'}}>Inactive</Text>
            )}
          </View>
          <View style={{flex: 0.4, alignItems: 'center'}}>
            <Image style={[styles.cardBodyImg]} source={getClubImage(item)} />
          </View>
          <View
            style={{
              flex: 0.4,
            }}
          >
            {item?.manufacturer?.name === 'TaylorMade' ? (
              <Image
                style={{
                  width: moderateScale(100),
                  height: moderateScale(20),
                  resizeMode: 'contain',
                  alignSelf: 'flex-end',
                }}
                source={WITBLogo}
              />
            ) : (
              <Text
                style={[
                  appStyles.textRight,
                  !item?.manufacturer?.name && appStyles.grey,
                ]}
              >
                {item?.manufacturer && item?.manufacturer?.name}
              </Text>
            )}

            <Text
              style={[
                appStyles.textRight,
                appStyles.mTMd,
                appStyles.grey,
                !item?.modelName?.name && appStyles.xsm,
                {fontSize: 13},
              ]}
            >
              {item?.modelName && item?.modelName?.name}
            </Text>
          </View>
        </Pressable>
      </View>
    );
  };

  if (!isActiveClub && club.inBag) {
    return null;
  }
  if (isCompareClubs) {
    if (changeOffsetPageValue) {
      return (
        <PanGestureHandler
          onGestureEvent={eventHandler}
          activeOffsetX={[-10, 10]}
        >
          {renderItemCompare()}
        </PanGestureHandler>
      );
    }
    return renderItemCompare();
  }

  return (
    <SwipeRow
      ref={refItemClub}
      key={club?.id + index}
      rightOpenValue={widthHideItem}
      friction={8}
      stopRightSwipe={widthHideItem}
      stopLeftSwipe={1}
      previewOpenValue={2}
      swipeGestureBegan={() => {
        //close other swipe
        if (getRefItem()?.id !== club?.id && getRefItem()?.itemSwipe) {
          getRefItem()?.itemSwipe.closeRow();
        }
        //disable scroll
        updateStatusScroll(false);
      }}
      swipeGestureEnded={() => {
        updateStatusScroll(true);
      }}
      onRowClose={() => {
        //reset border view
        if (borderRight?.id) {
          setBorderRight({id: null});
        }
      }}
      onRowOpen={() => {
        //add ref to close when open other swipe
        updateRef(refItemClub.current, club?.id);
      }}
      onSwipeValueChange={swipeData => {
        //swipe right show compare page
        if (
          swipeData.direction === 'right' &&
          !swipeData.isOpen &&
          swipeData.value !== 0
        ) {
          if (swipeData.value > 50) {
            updateStatusScroll(true);
          }
          // changeOffsetPageValue(swipeData.value, club);
        }
        //end swipe right
        if (
          swipeData.direction === 'left' &&
          !swipeData.isOpen &&
          swipeData.value < 50
        ) {
          // changeOffsetPageValue(swipeData.value, club);
        }
        //change border radius
        //Open swipe
        if (
          swipeData.value < widthHideItem / 2 &&
          swipeData.direction === 'left' &&
          !swipeData.isOpen &&
          borderRight?.id === null
        ) {
          setBorderRight({id: club?.id});
        }
        //Close swipe
        if (
          swipeData.value >= widthHideItem / 2 &&
          swipeData.direction === 'right' &&
          swipeData.isOpen &&
          borderRight?.id
        ) {
          setBorderRight({id: null});
        }

        //Open swipe
        if (Math.abs(swipeData.value) <= 160) {
          offsetMove.value = Math.abs(swipeData.value);
        }
      }}
      style={[
        appStyles.viewShadow,
        {
          flex: 1,
          marginBottom: 12,
        },
      ]}
    >
      <Animated.View
        style={[styles.rightSwipeButtons, {width: isActiveClub ? 160 : 80}]}
      >
        {isActiveClub && (
          <Pressable
            style={{flex: 1, zIndex: 2}}
            onPress={() => renderOnPress(clubCategoryId, club, 'DEACTIVATE')}
          >
            <Animated.View
              style={[
                {
                  backgroundColor: '#3E4D54',
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                },
                animatedMoveView,
              ]}
            >
              <Image source={IconDeactivate} />
              <Text white style={{fontSize: 13, marginTop: 4}}>
                my_bag.bag_list.button.deactivate
              </Text>
            </Animated.View>
          </Pressable>
        )}

        <Pressable
          style={{
            backgroundColor: '#FF0000',
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            borderTopRightRadius: 10,
            borderBottomRightRadius: 10,
            zIndex: 1,
          }}
          onPress={() => renderOnPress(clubCategoryId, club, 'DELETE')}
        >
          <Ionicons name={'trash-outline'} size={30} color={'white'} />
          <Text white style={{fontSize: 13, marginTop: 4}}>
            my_bag.bag_list.button.delete
          </Text>
        </Pressable>
      </Animated.View>
      {renderAllClub(club, index)}
    </SwipeRow>
  );
};

const styles = StyleSheet.create({
  cardBody: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    minHeight: isTablet ? hp('17%') : 86,
    paddingLeft: 15,
    paddingRight: 15,
  },
  cardBodyImg: {
    height: isTablet ? hp('16%') : 78,
    aspectRatio: 1,
    width: undefined,
  },
  club: {
    ...Platform.select({
      ios: {
        height: 31,
      },
    }),
    fontSize: 40,
  },
  rightSwipeButtons: {
    alignSelf: 'flex-end',
    height: '100%',
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
    flexDirection: 'row',
  },
});

export default CardItem;
