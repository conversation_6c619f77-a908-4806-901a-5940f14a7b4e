import React from 'react';
import {View, ImageBackground, TouchableWithoutFeedback} from 'react-native';
import {moderateScale} from 'react-native-size-matters';
import analytics from '@react-native-firebase/analytics';

import Text from '../Text';

import appStyles from '../../styles/global';
import {countContentView} from '../../requests/content';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

const FeedTile = ({navigate, index, item, tilesLength}) => {
  const {title, type, heading, backgroundImage, cta, extraData} = item;

  const feedTilePressed = async () => {
    if (!!extraData?.contentId) {
      countContentView(extraData?.contentId, title);
    }
    if (!!extraData?.content?.video_type && !!extraData?.content?.video_id) {
      navigate('VideoArticle', {
        params: {
          item: item,
        },
      });
    } else if (extraData?.contentFormat === 'clubhouseArticles') {
      navigate('FeedArticle', {
        screen: 'FeedArticle',
        params: {
          item: item,
        },
      });
    } else if (extraData?.contentFormat === 'images') {
      navigate('FeedArticle', {
        screen: 'FeedStories',
        params: {
          item: item,
        },
      });
    } else if (extraData?.contentFormat === 'videos') {
      navigate('Video', {
        video: {
          id: item?.extraData?.content?.video_id,
          title: item?.title,
          host: item?.extraData?.content?.video_type,
          contentId: item?.extraData?.content?.id,
          origin: 'AllArticle',
          videoUrl: item?.extraData?.content?.video_url,
          videoProvider: item?.extraData?.content?.dataSource,
        },
      });
    } else if (type === 'MEMBER_ONLY_EXCLUSIVE') {
      navigate('Shop', {
        screen: 'ShopPDP',
        params: {
          productName: item?.product_name || item?.extraData?.product?.name,
          productId: item?.extraData?.productId,
          origin: 'feed',
        },
      });
    } else if (extraData?.contentType === 'Drill') {
      // TODO: Drill
      // navigate('FeedArticle', {
      //   screen: 'FeedArticle',
      //   params: {
      //     item: item,
      //   },
      // });
    }
    if (item?.type === 'video') {
      navigate('Video');
    }

    await analytics().logEvent('vertical_tile_open', {
      id: item?.id,
      type: item?.type,
      name: item?.name,
    });
  };

  return (
    <TouchableWithoutFeedback onPress={feedTilePressed}>
      <View
        style={[
          appStyles.pHSm,
          index === 0 ? appStyles.pTMd : {paddingTop: moderateScale(30)},
          index === tilesLength - 1 ? appStyles.pBMd : {},
        ]}
      >
        <ImageBackground
          source={{
            uri: backgroundImage,
          }}
          imageStyle={{borderTopLeftRadius: 8, borderTopRightRadius: 8}}
          style={[
            appStyles.flex,
            appStyles.spaceBetween,
            appStyles.imageGrayBg,
            {
              height: (wp('86%') * 377) / 325,
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8,
            },
          ]}
          resizeMode={'cover'}
        ></ImageBackground>
        <View
          style={[
            appStyles.pHSm,
            appStyles.whiteBg,
            appStyles.fullWidth,
            {
              // position: 'absolute',
              // bottom: -1,
              borderBottomRightRadius: 8,
              borderBottomLeftRadius: 8,
              shadowOffset: {width: 1, height: 2},
              shadowOpacity: 0.2,
              shadowRadius: 3,
              elevation: 4,
              shadowColor: '#000000',
            },
          ]}
        >
          <Text
            DINbold
            style={[
              appStyles.uppercase,
              {color: '#8c8c91', paddingTop: moderateScale(16)},
            ]}
          >
            {heading}
          </Text>
          <Text
            style={[
              appStyles.black,
              appStyles.bold,
              {
                fontSize: moderateScale(24),
                paddingTop: moderateScale(10),
                paddingBottom: moderateScale(20),
              },
            ]}
          >
            {title}
          </Text>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default FeedTile;
