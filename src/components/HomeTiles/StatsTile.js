import React from 'react';
import {
  Platform,
  View,
  ImageBackground,
  TouchableWithoutFeedback,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';
import {useSelector} from 'react-redux';
import analytics from '@react-native-firebase/analytics';

import Text from '../Text';
import Button from '../../components/Button';

import appStyles from '../../styles/global';
import styles from './styles';

import ScoresBg from '../../../assets/imgs/home-tile-scores_bg.jpg';
import TargetScoreBg from '../../../assets/imgs/home-tile-target-score_bg.jpg';
import { WHITE_OPACITY } from '../../config';

const isTablet = DeviceInfo.isTablet();

const StatsTile = ({navigate, item}) => {
  const homeTiles = useSelector(state => state.home?.homeTiles);
  const statsTile = homeTiles.find(tile => tile.type === item.type);
  const heading = statsTile?.heading || item.heading;
  const title = statsTile?.title;
  const description = statsTile?.description || item.description;
  const message = statsTile?.message || item.message;
  const ctaText = statsTile?.ctaText || item.ctaText;
  const ctaLink = statsTile?.ctaLink || item.ctaLink;
  const backgroundImage = statsTile?.backgroundImage || item.backgroundImage;
  const extraData = statsTile?.extraData || item.extraData;
  const targetScore = statsTile?.extraData?.find(x => x.label === 'Target');
  const countScore = targetScore?.count;

  return (
    <TouchableWithoutFeedback
      onPress={
        item.type === 'AVERAGE_SCORE'
          ? async () => {
              navigate('ScoresStats');
              await analytics().logEvent('horizontal_tile_open', {
                id: item.id,
                type: item.type,
              });
            }
          : () =>
              navigate('Quiz', {
                screen: 'QuizScoreTarget',
                params: {origin: 'homeTiles', isEdit: true},
              })
      }
    >
      <View style={appStyles.pRXs}>
        <Text style={[isTablet ? appStyles.mBXs : appStyles.mBSm, appStyles.black]}>
          {heading}
        </Text>
        <ImageBackground
          source={
            backgroundImage
              ? {uri: backgroundImage}
              : item.type === 'AVERAGE_SCORE'
              ? ScoresBg
              : TargetScoreBg
          }
          imageStyle={{borderRadius: 8}}
          style={[styles.tileContainer, appStyles.pMd]}
        >
          <View style={[appStyles.hCenter, appStyles.fullHeight]}>
            <View style={[appStyles.hCenter, !isTablet ? appStyles.mTLg : {}]}>
              {message?.length ? (
                <Text
                  style={[appStyles.white, appStyles.xxl, appStyles.textCenter]}
                  DINbold
                >
                  {message}
                </Text>
              ) : (
                <>
                  {item.type?.trim() === 'TARGET_SCORE' && !countScore ? (
                    <View style={[appStyles.mHSm]}>
                      <Text
                        style={[
                          appStyles.white,
                          appStyles.lg,
                          {textAlign: 'center'},
                        ]}
                        DINbold
                      >
                        {title || '-'}
                      </Text>
                    </View>
                  ) : (
                    <>
                      <Text style={[appStyles.white, appStyles.xxxxl]} DINbold>
                        {title || '-'}
                      </Text>
                      <Text
                        style={[
                          appStyles.white,
                          appStyles.xs,
                          {marginTop: isTablet ? '-2%' : '-4%'},
                        ]}
                      >
                        {description}
                      </Text>
                    </>
                  )}
                </>
              )}
            </View>
            <View style={[appStyles.hCenter, {marginTop: 'auto'}]}>
              <View style={[appStyles.row, appStyles.spaceAround]}>
                {extraData &&
                  extraData.map(stat => {
                    return (
                      <View
                        key={stat.label}
                        style={[appStyles.flex, appStyles.hCenter]}
                      >
                        <Text style={[appStyles.white, appStyles.lg]} DINbold>
                          {stat.count || '-'}
                        </Text>
                        <Text style={[appStyles.white, appStyles.xs]}>
                          {stat.label}
                        </Text>
                      </View>
                    );
                  })}
              </View>
              <Button
                text={ctaText}
                textColor={'white'}
                textSize={{fontSize: 13}}
                backgroundColor={WHITE_OPACITY}
                borderColor={WHITE_OPACITY}
                centered
                style={[appStyles.mTMd, {height: hp('4.3%'), borderWidth: 0}]}
                textStyle={appStyles.semiBold}
                onPress={
                  item.type === 'AVERAGE_SCORE'
                    ? async () => {
                        navigate('ScoresStats');
                        await analytics().logEvent('horizontal_tile_open', {
                          id: item.id,
                          type: item.type,
                        });
                        await analytics().logEvent('home_cta_open', {
                          name: ctaText,
                        });
                        await analytics().logEvent('cta_open', {
                          name: ctaText,
                        });
                      }
                    : () =>
                        navigate('Quiz', {
                          screen: 'QuizScoreTarget',
                          params: {origin: 'homeTiles', isEdit: true},
                        })
                }
              />
            </View>
          </View>
        </ImageBackground>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default StatsTile;
