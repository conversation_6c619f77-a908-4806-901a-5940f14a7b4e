import React, {useState, useEffect} from 'react';
import {View, TouchableOpacity, StyleSheet, Platform} from 'react-native';
import {moderateScale} from 'react-native-size-matters';
import Text from 'components/Text';
import IconPlusNormal from 'assets/imgs/plus_black.svg';
import IconPlusGrey from 'assets/imgs/plus_grey.svg';
import IconMinusNormal from 'assets/imgs/minus_black.svg';
import IconMinusGrey from 'assets/imgs/minus_grey.svg';

import appStyles from 'styles/global';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';

import {t} from 'i18next';
const QuantitySection = ({quantity, setQuantity, maxQty, renderTextLimit}) => {
  if (!maxQty) {
    maxQty = 1;
  }
  const onMinusQty = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };
  const onPlusQty = () => {
    if (quantity <= maxQty - 1) {
      setQuantity(quantity + 1);
    }
  };
  return (
    <View style={{marginRight: moderateScale(15)}}>
      <View
        style={[
          appStyles.row,
          appStyles.hCenter,
          appStyles.vCenter,
          styles.quantityView,
        ]}
      >
        <TouchableOpacity
          onPress={onMinusQty}
          disabled={quantity === 1}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 25,
            minWidth: 25,
          }}
        >
          {quantity > 1 ? <IconMinusNormal /> : <IconMinusGrey />}
        </TouchableOpacity>
        <View style={appStyles.hCenter}>
          <Text style={[appStyles.greyText, {fontSize: moderateScale(8)}]}>
            {t('shop.pdp_buy.qty')}
          </Text>
          <Text
            black
            DINbold
            style={[
              {
                paddingTop: Platform.OS === 'ios' ? hp(1.4) : hp(0.7),
                fontSize: moderateScale(16),
              },
            ]}
          >
            {quantity}
          </Text>
        </View>
        <TouchableOpacity
          onPress={onPlusQty}
          disabled={quantity === maxQty}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 25,
            minWidth: 25,
          }}
        >
          {quantity <= maxQty - 1 ? <IconPlusNormal /> : <IconPlusGrey />}
        </TouchableOpacity>
      </View>
      {renderTextLimit?.()}
    </View>
  );
};

const styles = StyleSheet.create({
  quantityView: {
    borderRadius: moderateScale(42),
    borderWidth: 1,
    borderColor: '#8C8C90',
    paddingHorizontal: wp('3%'),
    justifyContent: 'space-between',
    minWidth: wp('28%'),
    minHeight: hp('6%'),
    marginBottom: 5,
  },
});

export default QuantitySection;
