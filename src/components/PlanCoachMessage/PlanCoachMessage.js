import React from 'react';
import {View, StyleSheet} from 'react-native';
import {moderateScale} from 'react-native-size-matters';
import Svg, {Path} from 'react-native-svg';
import {connect} from 'react-redux';
import {isTablet} from 'react-native-device-info';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import Text from '../Text';

import appStyles from '../../styles/global';
import FastImage from 'react-native-fast-image/src';
import {t} from 'i18next';
import {useSelector} from 'react-redux';

const PlanCoachMessage = ({
  url, // fake data, after demo, delete it
  planCoach,
  swingIndex,
  planContentStrings,
  type,
  user,
  style,
  planCurrentStep,
  planContentBatch,
}) => {
  const firstName = useSelector(state => state.user?.firstName);

  const getMessage = () => {
    let message = '';
    switch (type) {
      case 'describeShot':
        return `${t('describe_shot.plan_coach_message.okay')} ${firstName}${t(
          'describe_shot.plan_coach_message.title_okay',
        )}`;
      case 'shortPuttInformation':
        return t('short_putt_information.plan_coach_message');
      case 'longPuttInformation':
        return t('long_putt_information.plan_coach_message');
      case 'swingIndex':
        message = planContentStrings?.swing_potential_index?.replace(
          '{{player.firstName}}',
          user?.firstName,
        );
        message = message?.replace(
          '{{spi.currentAvg}}',
          swingIndex?.currentAvg,
        );
        message = message?.replace(
          '{{spi.potentialAvg}}',
          Number.isInteger(swingIndex?.potentialAvg)
            ? swingIndex?.potentialAvg?.toFixed(1)
            : swingIndex?.potentialAvg,
        );
        return message;
      case 'roadMap':
        message = planContentStrings?.roadmap_play_with_the_pros_activated;
        return message;
      case 'relevantLessons':
        // const element = planContentBatch?.swingElement?.find(
        //   element => element?.id === planCurrentStep?.swingElementId,
        // );
        // const swingSubElement = element?.swingSubElements?.find(
        //   element =>
        //     element.id === planCurrentStep?.currentStep?.swingSubElementId,
        // );
        // message = planContentStrings?.lesson_initial?.replace(
        //   '{{swingSubElement.name}}',
        //   swingSubElement?.name,
        // );
        // message = message?.replace('{{swingElement.name}}', element?.name);
        return 'lesson_tap.coach.message';
      case 'goals':
        message = 'coach.message.here_you_can_access';
        return message;
      case 'baseline_down_the_line':
        return 'baseline.message.down_the_line';
      case 'baseline_face_on':
        return 'baseline.message.driver_face_on';
      case 'return_down_the_line':
        return 'coach.message.returned.down_the_line';
      case 'return_face_on':
        return 'coach.message.returned.face_on';
      case 'scramble':
        return 'coach_message.record_single_swing';
      case 'long_putting':
        return 'long_putt_message_coach';
      case 'short_putting':
        return 'short_putt_message_coach';
      case 'coach_ongoing':
        return 'coach_ongoing_message_coach';
      case 'swing_remaint_returned.down_the_line':
        return 'swing_remaint_returned.down_the_line';
      case 'swing_remaint_returned.face_on':
        return 'swing_remaint_returned.face_on';
      case 'swing_maintenance_coach_message':
        return 'swing_maintenance_coach_message';
      case 'quick_fix':
        return 'quick_fix_coach_message';
      default:
        return '';
    }
  };

  return (
    <View
      style={[
        appStyles.pHSm,
        type === 'puttInformation' && appStyles.pBSm,
        appStyles.row,
        appStyles.pVSm,
        {backgroundColor: '#3E4D54'},
        style,
      ]}
    >
      <View style={[appStyles.alignEnd]}>
        <FastImage
          source={{
            uri: url ? url : planCoach?.profileImageUrl,
            cache: FastImage.cacheControl.web,
          }}
          style={[
            appStyles.mBSm,
            {
              width: moderateScale(50),
              height: moderateScale(50),
              borderRadius: 25,
            },
          ]}
        />
        {/* <View
          style={[
            appStyles.whiteBg,
            {
              width: moderateScale(14),
              height: moderateScale(14),
              borderRadius: 100,
              position: 'absolute',
              bottom: 0,
              right: 0,
            },
          ]}
        >
          <Icon name="information-variant" size={moderateScale(14)} />
        </View> */}
      </View>
      <View style={[styles.item, appStyles.mLXs]}>
        <View style={[styles.balloon, appStyles.whiteBg]}>
          <Text
            style={[
              appStyles.black,
              {fontSize: 14, paddingVertical: 3, paddingHorizontal: 3},
            ]}
          >
            {getMessage()}
          </Text>
          <View style={[styles.arrowContainer, styles.arrowLeftContainer]}>
            <Svg
              style={styles.arrowLeft}
              width={moderateScale(15.5, 0.6)}
              height={moderateScale(17.5, 0.6)}
              viewBox="32.484 17.5 15.515 17.5"
              enable-background="new 32.485 17.5 15.515 17.5"
            >
              <Path
                d="M38.484,17.5c0,8.75,1,13.5-6,17.5C51.484,35,52.484,17.5,38.484,17.5z"
                fill={'#fff'}
                x="0"
                y="0"
              />
            </Svg>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  item: {
    // marginVertical: moderateScale(7, 2),
    flexDirection: 'row',
  },
  balloon: {
    maxWidth: isTablet() ? moderateScale(170, 2) : moderateScale(230, 2),
    paddingHorizontal: moderateScale(10, 2),
    paddingTop: moderateScale(5, 2),
    paddingBottom: moderateScale(7, 2),
    borderRadius: 13,
  },
  arrowLeft: {
    left: moderateScale(-6, 0.5),
  },
  arrowContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
    flex: 1,
  },
  arrowLeftContainer: {
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
  },
});

const mapStateToProps = state => ({
  user: state.user,
  planCoach: state.plans.planCoach,
  planContentStrings: state.plans.planContentStrings,
  planContentBatch: state.plans.planContentBatch,
});

export default connect(mapStateToProps, null)(PlanCoachMessage);
