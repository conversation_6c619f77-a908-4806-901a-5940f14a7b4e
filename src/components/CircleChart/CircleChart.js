import React from 'react';
import ProgressCircleCustom from 'components/ProgressCircleCustom';
import {Platform, View, StyleSheet} from 'react-native';
import Text from 'components/Text';

const CircleChart = ({
  value,
  color,
  textBelow,
  backgroundColor,
  chartSize = 108,
  valueTextSize = 34,
  percentTextSize = 16,
  notApplicableSize = 104,
  textBelowSize = 16,
  textBelowStyle = {},
  borderWidth = 7,
  isTextBelowDin79,
  animationDuration = 400,
  borderOpacity = '0.2',
  style= {},
  isShareRound = false,
}) => {
  return value === 0 && !isShareRound ? (
    <View
      style={[
        styles.notApplicableCircle,
        {
          width: notApplicableSize,
          height: notApplicableSize,
          borderRadius: notApplicableSize / 2,
        },
      ]}
    >
      <View
        style={{
          flexDirection: 'row',
        }}
      >
        <Text
          white
          size={valueTextSize}
          Din79Font
          style={{fontWeight: '700', marginLeft: 8}}
        >
          {value}
        </Text>
        <Text
          MonoFont
          size={percentTextSize}
          style={{
            fontWeight: '700',
            marginTop: Platform.OS === 'ios' ? 8 : 4,
          }}
          white
        >
          %
        </Text>
      </View>
      <Text
        white
        size={textBelowSize}
        style={{textAlign: 'center', marginHorizontal: 8, ...textBelowStyle}}
        Din79Font={isTextBelowDin79}
      >
        {textBelow}
      </Text>
    </View>
  ) : (
    <ProgressCircleCustom
      style={style}
      value={value}
      maxValue={100}
      size={chartSize}
      colorProgress={color}
      endText={''}
      duration={animationDuration}
      border={borderWidth}
      Din79Font
      customEndText={
        <Text
          MonoFont
          size={percentTextSize}
          style={{
            fontWeight: '700',
            marginTop: Platform.OS === 'ios' ? 8 : 4,
          }}
          black
        >
          %
        </Text>
      }
      textStyle={{
        fontWeight: '700',
        fontSize: valueTextSize,
      }}
      textBelow={textBelow}
      textBelowSize={textBelowSize}
      textBelowStyle={textBelowStyle}
      backgroundColor={backgroundColor}
      isTextBelowDin79={isTextBelowDin79}
      borderOpacity={borderOpacity}
    />
  );
};

const styles = StyleSheet.create({
  notApplicableCircle: {
    backgroundColor: '#cccccc',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 4,
  },
});

export default CircleChart;
