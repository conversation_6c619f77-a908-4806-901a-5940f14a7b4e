import React from 'react';
import {View, TouchableOpacity, Text, StyleSheet} from 'react-native';
import appStyles from '../../styles/global';

const DescribeShotPicker = ({value, options, onPress}) => {
  return (
    <>
      {options?.map((opt, index) => {
        const arr = Object.values(value);
        if (opt.title === '' && opt.options.length === 1) {
          return (
            <TouchableOpacity
              key={`${index}`}
              onPress={() => onPress(opt.options[0])}
              style={[
                appStyles.hCenter,
                {
                  width: '100%',
                  marginTop: '2%',
                },
              ]}
            >
              <Text
                style={[
                  appStyles.underlined,
                  appStyles.bold,
                  {
                    marginVertical: 15,
                    marginLeft: 10,
                    color: 'black',
                  },
                ]}
              >
                {opt.options[0].name}
              </Text>
            </TouchableOpacity>
          );
        }
        return (
          <View key={index} style={[appStyles.row, {marginTop: '2%'}]}>
            <View
              style={{
                width: '30%',
                paddingLeft: 10,
              }}
            >
              <Text
                style={[appStyles.bold, {marginVertical: 15, color: 'black'}]}
              >
                {opt.title}
              </Text>
            </View>
            <View
              style={[
                appStyles.row,
                opt.options?.length === 3 && appStyles.spaceBetween,
                {
                  width: '70%',
                },
              ]}
            >
              {opt.options.map((item, i) => {
                const selectedItem = arr.find(
                  x => x.type === item.type && x.name === item.name,
                );
                if (item.name) {
                  return (
                    <TouchableOpacity
                      key={i}
                      onPress={() => onPress(item)}
                      style={[
                        appStyles.vCenter,
                        appStyles.hCenter,
                        styles.boxItem,
                        {
                          width: '32%',
                          backgroundColor: selectedItem ? 'black' : 'white',
                        },
                      ]}
                    >
                      <Text
                        style={{
                          color: !selectedItem ? 'black' : 'white',
                        }}
                      >
                        {item.name}
                      </Text>
                    </TouchableOpacity>
                  );
                } else {
                  return <View key={i} style={{width: '32%'}} />;
                }
              })}
            </View>
          </View>
        );
      })}
    </>
  );
};

const styles = StyleSheet.create({
  boxItem: {
    borderRadius: 25,
    borderWidth: 1,
    borderColor: 'black',
  },
});

export default DescribeShotPicker;
