import React, {useRef} from 'react';
import {View, Modal} from 'react-native';
import Toast from 'react-native-toast-message';

import CustomShopSelector from '../CustomShopSelector';

const ClubConfiguratorModal = ({
  toggleSelector,
  setToggleSelector,
  clubConfig,
  model,
  navigate,
  origin,
  orderType,
  isTTB,
  productId,
  setShowToastError,
  isAllowSelectionOtherClub,
  quantity,
}) => {
  const toastRef = useRef(null);
  return (
    <Modal
      transparent={true}
      visible={toggleSelector}
      onRequestClose={() => {}}
    >
      <View
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(0,0,0,0.7)',
        }}
      >
        <Toast ref={toastRef} />
      </View>
      <CustomShopSelector
        toggleSelector={toggleSelector}
        setToggleSelector={setToggleSelector}
        setShowToastError={setShowToastError}
        clubConfig={clubConfig}
        model={model}
        toastRef={toastRef}
        navigate={navigate}
        origin={origin}
        orderType={orderType}
        isTTB={isTTB}
        productId={productId}
        isBottomSheet
        isAllowSelectionOtherClub={isAllowSelectionOtherClub}
        quantity={quantity || 1}
      />
    </Modal>
  );
};

export default ClubConfiguratorModal;
