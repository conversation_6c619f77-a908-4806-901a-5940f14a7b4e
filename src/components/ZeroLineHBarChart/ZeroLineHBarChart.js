import React, {useState} from 'react';
import {View, processColor} from 'react-native';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import {HorizontalBarChart} from 'react-native-charts-wrapper';

import Text from '../Text';
import ChartFilter from '../ChartFilter';

import appStyles from '../../styles/global';
import {LIGHT_GREY} from '../../config';

const TEXT_COLOR = processColor(LIGHT_GREY);
const GRID_COLOR = processColor('rgba(140, 140, 145, 0.3)');
const GREEN_COLOR = processColor('#3DB472');
const RED_COLOR = processColor('#C71A04');

const ZeroLineHBarChart = ({chart, height, preview}) => {
  const coordinates = chart?.chartData || chart?.data;
  const filterOptions = coordinates?.map(data => {
    return data?.option;
  });
  const [filter, setFilter] = useState(filterOptions[0]);
  const chartData = coordinates?.find(data => data?.option === filter);

  const getXAxisLabel = () => {
    return chartData.yAxis
      .map(axis => {
        return axis?.toString();
      })
      .reverse();
  };

  const getValues = () => {
    return chartData.xAxis
      .map(axis => {
        return {
          y: parseFloat(axis),
        };
      })
      .reverse();
  };

  const getColors = () => {
    return chartData.xAxis
      .map(data => {
        if (data > 0) {
          return GREEN_COLOR;
        } else {
          return RED_COLOR;
        }
      })
      .reverse();
  };

  const xAxisLabels = getXAxisLabel();

  return (
    <View style={appStyles.center}>
      {!preview && filterOptions.length > 1 ? (
        <ChartFilter
          type="bar"
          filter={filter}
          setFilter={setFilter}
          options={filterOptions}
        />
      ) : null}
      <View style={[appStyles.row, appStyles.hCenter]}>
        <View style={{width: 15}}>
          <Text
            style={[
              appStyles.xs,
              appStyles.textCenter,
              appStyles.lightGrey,
              {
                transform: [
                  {rotate: '-90deg'},
                  {translateY: -((height || hp('29%')) / 2)},
                ],
                width: height || hp('30%'),
              },
            ]}
          >
            {chart?.yAxisTitle} {chart?.yAxisUnits}
          </Text>
        </View>
        <HorizontalBarChart
          style={[appStyles.flex, {height: height || hp('30%')}]}
          data={{
            dataSets: [
              {
                values: getValues(),
                label: '',
                config: {
                  colors: getColors(),
                  drawValues: false,
                },
              },
            ],
            config: {
              barWidth: 0.3,
            },
          }}
          xAxis={{
            valueFormatter: xAxisLabels,
            labelCount: xAxisLabels?.length,
            position: 'BOTTOM',
            drawGridLines: true,
            gridColor: GRID_COLOR,
            textColor: TEXT_COLOR,
            textSize: hp('1.2%'),
            gridDashedLine: {
              lineLength: 5,
              spaceLength: 3,
              phase: 1,
            },
            axisLineColor: processColor('transparent'),
          }}
          yAxis={{
            left: {enabled: false},
            right: {
              drawLabels: true,
              drawGridLines: true,
              zeroLine: {
                enabled: true,
                lineWidth: 1.5,
                lineColor: processColor('#fff'),
              },
              gridDashedLine: {
                lineLength: 5,
                spaceLength: 3,
                phase: 1,
              },
              gridColor: GRID_COLOR,
              textColor: TEXT_COLOR,
              textSize: hp('1.2%'),
              axisLineColor: processColor('transparent'),
            },
          }}
          chartDescription={{text: ''}}
          legend={{enabled: false}}
          marker={{enabled: false}}
          doubleTapToZoomEnabled={false}
          highlightPerTapEnabled={false}
          drawValueAboveBar={false}
          highlightPerDragEnabled={false}
          pinchZoom={false}
        />
      </View>
      <Text
        style={[
          appStyles.xs,
          appStyles.textCenter,
          appStyles.mTSm,
          appStyles.lightGrey,
        ]}
      >
        {chart?.xAxisTitle} {chart?.xAxisUnits}
      </Text>
    </View>
  );
};

export default ZeroLineHBarChart;
