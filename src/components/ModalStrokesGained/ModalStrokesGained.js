import React from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import Modal from 'react-native-modal';
import Text from '../Text';
import appStyles from '../../styles/global';
import Icon from 'react-native-vector-icons/AntDesign';
import {PRIMARY_COLOR} from '../../config';

const ModalStrokesGained = ({visible, onClose}) => {
  const backdropPress = () => {
    if (onClose) {
      onClose();
    }
  };

  return (
    <View
      style={[
        appStyles.pSm,
        styles.centeredView,
        appStyles.whiteBg,
        appStyles.mHSm,
      ]}
    >
      <View style={[appStyles.row, appStyles.spaceBetween]}>
        <Text style={[appStyles.lg, {marginTop: '8%'}]} DINbold>
          my_bag.detail.stats.strokes_gained
        </Text>
        <TouchableOpacity onPress={backdropPress} style={[appStyles.pHXs]}>
          <Icon name="close" size={17} color={PRIMARY_COLOR} />
        </TouchableOpacity>
      </View>
      <Text style={[appStyles.xs, appStyles.mTSm]}>
        my_bag.detail.stats.strokes_gained_is_new_metric
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    borderRadius: 10,
    paddingVertical: '6%',
  },
  title: {
    marginTop: 8,
    height: 40,
  },
});

export default ModalStrokesGained;
