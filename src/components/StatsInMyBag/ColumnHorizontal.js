/* eslint-disable react-hooks/exhaustive-deps */
import React from 'react';
import {View, Platform} from 'react-native';

import appStyles from '../../styles/global';
import Text from '../Text';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {GREEN_COLUMN, PRIMARY_COLOR} from '../../config';

const widthColumnMax = wp('70%');
const ColumnHorizontal = ({maxValue, currentValue, type}) => {
  const widthColumn = (widthColumnMax / maxValue) * currentValue;
  const value = `${currentValue}${type}`;
  const renderText = (color = 'white') => (
    <Text
      style={[
        appStyles.sm,
        appStyles.pHXs,
        {color},
        Platform.OS === 'ios' && {marginTop: 5},
      ]}
      DINbold
    >
      {value}
    </Text>
  );
  return (
    <View style={[appStyles.row, appStyles.hCenter]}>
      <View
        style={[
          {
            backgroundColor: GREEN_COLUMN,
            height: 32,
            width: widthColumn || 0,
            borderTopRightRadius: 3,
            borderBottomRightRadius: 3,
            alignItems: 'flex-end',
          },
          appStyles.vCenter,
        ]}
      >
        {widthColumn >= 40 && renderText()}
      </View>
      {widthColumn < 40 && currentValue > 0 && renderText(PRIMARY_COLOR)}
    </View>
  );
};

export default ColumnHorizontal;
