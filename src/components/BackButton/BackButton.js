import React from 'react';
import PropTypes from 'prop-types';
import {View, TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';

import Text from '../Text';

import appStyles from '../../styles/global';

const isTablet = DeviceInfo.isTablet();

const BackButton = ({
  color,
  text,
  navigationText,
  onPress,
  closeIcon,
  customIcon,
  disabled = false,
  style,
}) => {
  return (
    <TouchableOpacity
      style={[{paddingLeft: wp('2%')}, style]}
      onPress={onPress}
      disabled={disabled}
    >
      {text ? (
        <Text style={{color}}>{text}</Text>
      ) : (
        <View style={[appStyles.row, appStyles.hCenter]}>
          {customIcon ? (
            customIcon
          ) : (
            <Icon
              name={closeIcon ? 'close-outline' : 'chevron-back'}
              color={color}
              size={isTablet ? wp('3%') : wp('7%')}
            />
          )}
          {navigationText ? (
            <Text style={[{color}]}>{navigationText}</Text>
          ) : null}
        </View>
      )}
    </TouchableOpacity>
  );
};

BackButton.propTypes = {
  color: PropTypes.string,
};

BackButton.defaultProps = {
  color: 'white',
};

export default BackButton;
