import React, {useRef, useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Platform,
  TextInput,
  Keyboard,
} from 'react-native';
import BottomSheet from 'reanimated-bottom-sheet';
import DatePicker from 'react-native-date-picker';

import HeightPicker from '../HeightPicker';
import NumberPicker from '../NumberPicker';
import IOSNumberPicker from '../IOSNumberPicker';
import TextPicker from '../TextPicker';
import HandicapPicker from '../HandicapPicker';
import DescribeShotPicker from 'components/DescribeShot/DescribeShotPicker';
import appStyles from '../../styles/global';

const CustomSelector = React.forwardRef((props, ref) => {
  const inputRef = useRef(null);
  const [valueCurrent, setValueCurrent] = useState(null);
  const [selectedBall, setSelectedBall] = useState({});

  const selectedItem = item => {
    if (item.type === 'other') {
      setSelectedBall({[item.type]: item});
      props.onChange({[item.type]: item});
      ref.current?.snapTo(1);
    } else {
      const selectedOther = Object.values({...selectedBall}).filter(
        x => x.type === 'other',
      );
      if (selectedOther) {
        // delete selectedBall.other;
        const {other, ...boxFields} = {...selectedBall};
        if (item.type !== selectedBall[item.name]?.type) {
          setSelectedBall({...boxFields, [item.type]: item});
        }
      } else {
        if (item.type !== selectedBall[item.name]?.type) {
          setSelectedBall({...selectedBall, [item.type]: item});
        }
      }
    }
  };
  return (
    <BottomSheet
      ref={ref}
      snapPoints={[props.snapPoints == null ? '40%' : props.snapPoints, 0]}
      borderRadius={props.borderRadius == null ? 20 : props.borderRadius}
      initialSnap={1}
      onCloseEnd={() => {
        if (props.type === 'message') {
          inputRef?.current?.blur();
        }
        if (props?.onCloseEnd) {
          props.onCloseEnd();
        }
        if (props?.setToggled) {
          props.setToggled(false);
        }
      }}
      onOpenEnd={() => {
        if (props.type === 'message') {
          inputRef?.current?.focus();
        }
      }}
      renderContent={() => {
        return (
          <View
            style={{
              height: '100%',
              backgroundColor: 'white',
            }}
          >
            <View
              style={[
                appStyles.row,
                appStyles.spaceBetween,
                appStyles.hCenter,
                {width: '100%'},
                props.styleHeader ? props.styleHeader : {},
              ]}
            >
              <View
                style={[appStyles.pLXs, !props?.textCancel && {width: '15%'}]}
              >
                {props?.textCancel && (
                  <TouchableOpacity
                    disabled={props?.disabledCancel}
                    onPress={() => {
                      if (props.type === 'box') {
                        setSelectedBall(props?.value);
                      }
                      props?.handleCancel();
                    }}
                  >
                    <Text
                      style={[
                        appStyles.underlined,
                        props?.styleCancel ? props?.styleCancel : {},
                      ]}
                    >
                      {props.textCancel}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
              {props?.title && (
                <Text
                  style={[
                    props?.styleTitle ? props?.styleTitle : appStyles.pTXs,
                  ]}
                >
                  {props.title}
                </Text>
              )}
              <TouchableOpacity
                onPress={() => {
                  Keyboard.dismiss();
                  if (props.type === 'text') {
                    const value =
                      valueCurrent !== null
                        ? valueCurrent
                        : props.type === 'text'
                        ? props.options?.[0]
                        : null;
                    props.onChange(value);
                  }

                  if (props.type === 'box') {
                    props.onChange(selectedBall);
                  }
                  ref.current?.snapTo(1);
                  if (props?.setToggled) {
                    props.setToggled(false);
                  }
                }}
              >
                <Text
                  style={[
                    appStyles.underlined,
                    appStyles.pRXs,
                    props?.styleDone ? props?.styleDone : appStyles.pTXs,
                  ]}
                >
                  Done
                </Text>
              </TouchableOpacity>
            </View>
            <View
              style={{
                flex: 1,
                alignItems: 'center',
                marginHorizontal: props.type === 'box' ? 10 : 0,
              }}
            >
              {props.type === 'text' ? (
                <TextPicker
                  value={props.value}
                  options={props.options}
                  setValue={setValueCurrent}
                />
              ) : null}
              {props.type === 'date' ? (
                <DatePicker
                  style={[appStyles.flex]}
                  date={
                    props.value?.length
                      ? new Date(props.value)
                      : props.defaultValue && props.defaultValue?.length > 0
                      ? new Date(props.defaultValue)
                      : new Date()
                  }
                  mode="date"
                  maximumDate={props.allowFutureDates ? '' : new Date()}
                  onDateChange={props.onChange}
                  androidVariant="iosClone"
                />
              ) : null}
              {props.type === 'height' ? (
                <HeightPicker
                  height={props.value}
                  addQuizHeight={props.onChange}
                />
              ) : null}
              {props.type === 'number' && Platform.OS === 'ios' ? (
                <IOSNumberPicker
                  dataType={props.dataType}
                  selectorType={props.selectorType}
                  value={props.value}
                  options={props.options}
                  setValue={props.onChange}
                  toggled={props.toggled}
                />
              ) : null}
              {props.type === 'number' && Platform.OS === 'android' ? (
                <NumberPicker
                  dataType={props.dataType}
                  selectorType={props.selectorType}
                  value={props.value}
                  options={props.options}
                  setValue={props.onChange}
                  toggled={props.toggled}
                />
              ) : null}
              {props.type === 'handicap' ? (
                <HandicapPicker
                  handicap={props.value}
                  addHandicap={props.onChange}
                />
              ) : null}
              {props.type === 'message' ? (
                <TextInput
                  onChangeText={props.onChange}
                  autoCorrect={props?.autoCorrect}
                  multiline={props?.multiline}
                  ref={inputRef}
                  maxLength={props.maxLength}
                  textAlignVertical="top"
                  style={[
                    appStyles.full,
                    {
                      padding: 5,
                    },
                  ]}
                  defaultValue={props.value}
                />
              ) : null}
              {props.type === 'box' && props?.options?.length > 0 ? (
                <DescribeShotPicker
                  value={selectedBall}
                  options={props.options}
                  onPress={selectedItem}
                />
              ) : null}
            </View>
          </View>
        );
      }}
    />
  );
});

export default CustomSelector;
