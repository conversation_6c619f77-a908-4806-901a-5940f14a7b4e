import React, {useState} from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import moment from 'moment';

import Text from '../Text';
import Button from '../Button';
import LoadingOverlay from '../LoadingOverlay';

import appStyles from '../../styles/global';
import {getConfig} from '../../config/env';
import {covertFittingsTypeId} from '../../utils/convert';
import {getAuth0AccessToken} from '../../utils/user';
import {t} from 'i18next';
import {useDispatch} from 'react-redux';
import {getCountryDateWithFormat} from 'utils/countries';

const FittingList = ({navigate, fittings, fittingsType}) => {
  const [loadingWebView, setLoadingWebView] = useState(false);
  const dispatch = useDispatch();
  const renderItems = ({item}) => {
    const date = moment(
      item.actualEnd ||
        item.scheduledEnd ||
        item.actualStart ||
        item.scheduledStart,
    ).format(getCountryDateWithFormat('MM.DD.YY'));

    return (
      <View style={[styles.fitting]}>
        <TouchableOpacity
          style={[appStyles.viewShadow]}
          onPress={() => {
            if (fittingsType === 'past') {
              navigate('FittingRecommendations', {
                title: date,
                shotId: item.id,
              });
            }
            if (fittingsType === 'upcoming') {
              navigate('FittingDetails', {
                shotId: item.id,
                type: 'upcoming',
              });
            }
          }}
        >
          <View
            style={[
              appStyles.pVMd,
              {
                backgroundColor: 'white',
                paddingHorizontal: 15,
                paddingVertical: 20,
                borderRadius: 9,
              },
            ]}
          >
            <View style={[appStyles.row]}>
              <Text style={[appStyles.lg, {paddingBottom: 18}]} DINbold>
                {date}
              </Text>
              <Text style={[appStyles.xs, appStyles.grey, appStyles.mLAuto]}>
                {covertFittingsTypeId(item.fittingTypeId)}
              </Text>
            </View>
            <Text style={[appStyles.sm]}>{item.location?.name}</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const navigateToWebView = async () => {
    setLoadingWebView(true);
    const accessToken = await getAuth0AccessToken(dispatch);
    const MFE_URL = await getConfig('MFE_URL');

    navigate('WebView', {
      screen: 'WebView',
      params: {
        title: t('fitting.webview.title.find_event'),
        uri: `${MFE_URL}/events/find?accessToken=${accessToken}&iframe=true`,
        origin: fittingsType === 'past' ? 'FittingHistory' : 'FittingUpcoming',
        canGoBack: true,
      },
    });
    setLoadingWebView(false);
  };

  return (
    <>
      {loadingWebView ? <LoadingOverlay transparent={loadingWebView} /> : null}
      <FlatList
        contentContainerStyle={{
          flex: 1,
          paddingTop: fittings?.length ? hp('3%') : 0,
        }}
        data={fittings}
        renderItem={renderItems}
        keyExtractor={item => item.id}
        ListEmptyComponent={() => {
          return (
            <ImageBackground
              source={require('assets/imgs/Upcoming.jpg')}
              style={[
                appStyles.flex,
                appStyles.hCenter,
                appStyles.pHSm,
                appStyles.spaceBetween,
                {
                  paddingTop: hp('3%'),
                },
              ]}
            >
              <Text
                style={[
                  appStyles.xmd,
                  appStyles.textCenter,
                  appStyles.mBMd,
                  {fontWeight: '500', paddingHorizontal: 36},
                ]}
                DINbold
              >
                {`${t('fitting.list.custom_fitting').toUpperCase()}`}
              </Text>
              <Button
                style={{width: '100%', marginBottom: 44, borderRadius: 42}}
                text="fitting.list.find_a_fitter_near_you"
                backgroundColor="white"
                textColor="black"
                borderColor="transparent"
                onPress={navigateToWebView}
                centered
                textStyle={{fontWeight: '500', fontSize: 18}}
                DINbold
              />
            </ImageBackground>
          );
        }}
      />
    </>
  );
};

const styles = StyleSheet.create({
  fitting: {
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    marginBottom: 10,
  },
});

export default FittingList;
