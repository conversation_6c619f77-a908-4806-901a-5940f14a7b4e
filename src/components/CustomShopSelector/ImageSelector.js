import React, {useState, useEffect, useCallback} from 'react';
import {View, Image, TouchableWithoutFeedback} from 'react-native';
import {moderateScale} from 'react-native-size-matters';
import {ScrollView} from 'react-native-gesture-handler';
import {
  heightPercentageToDP,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';

import Header from './Header';
import Button from '../Button';
import Text from '../Text';

import appStyles from '../../styles/global';
import CustomImageSelectorItem from './CustomImageSelectorItem';
import {convertModelNameToMarcoName} from '../../utils/convert';
import {compareClubDisplayName} from 'utils/clubs';
import { getCurrencySymbol } from 'utils/commonVariable';

const ImageSelectorItem = ({
  imageType,
  option,
  value,
  setValue,
  setUpCharge,
  onLayoutFunction,
}) => {
  const [itemImage, setItemImage] = useState(
    option.image
      ? `https://www.taylormadegolf.com/on/demandware.static/-/Sites-tmag-custom-catalog-us/default/images/OracleConfigurator/${imageType}/${option.image}.jpg`
      : null,
  );

  const getImage = () => {
    return itemImage
      ? {uri: itemImage}
      : imageType === 'Shafts'
      ? require('../../../assets/imgs/silhouettes-shaft.jpg')
      : require('../../../assets/imgs/silhouettes-grip.jpg');
  };

  const selectedContainerStyle =
    value === option.value
      ? [appStyles.blackBg]
      : [{borderWidth: 1, borderColor: '#E6E5EB'}];
  const selectedTextStyle =
    value === option.value ? [appStyles.white] : [appStyles.black];

  const selectItem = () => {
    setValue(option.value);
    setUpCharge(option.upcharge || null);
  };

  const onLayout = event => {
    if (onLayoutFunction) {
      onLayoutFunction(event);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={selectItem} onLayout={onLayout}>
      <View
        style={[
          appStyles.borderRadius,
          appStyles.mBSm,
          appStyles.pVSm,
          ...selectedContainerStyle,
        ]}
      >
        <Text
          style={[appStyles.textCenter, appStyles.mBSm, ...selectedTextStyle]}
        >
          {option.label}{' '}
          {option.upcharge
            ? `(+${getCurrencySymbol()}${option.upcharge})`
            : null}
        </Text>
        <Image
          style={{width: '100%', height: moderateScale(20)}}
          source={getImage()}
          resizeMode="cover"
          onError={() => setItemImage(null)}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

const ImageSelector = ({
  title,
  options = [],
  value,
  setValue,
  setCurrentSelector,
  save,
  loading,
  setUpCharge,
  isBottomSheet,
  origin,
  goback,
  isCustomImage = false,
  clubMacroCodeConfig = [],
  clubType,
  isAllowSelectionOtherClub,
}) => {
  const isTablet = DeviceInfo.isTablet();
  const [initialValue, setInitialValue] = useState(value);
  const [initialPositionDone, setInitialPositionDone] = useState(false);
  const [selectedItemYPos, setSelectedItemYPos] = useState(0);
  const [selectedItemHeight, setSelectedItemHeight] = useState(0);
  const [lastItemYPos, setLastItemYPos] = useState(0);
  const [scrollViewHeight, setScrollViewHeight] = useState(0);
  const [lastItemHeight, setLastItemHeight] = useState(0);
  const [contentOffset, setContentOffset] = useState(0);
  const imageType = title === 'Shaft Model' ? 'Shafts' : 'Grips';
  let selectedIndex = options?.findIndex(item => item.value === initialValue);
  if (selectedIndex < 0) {
    selectedIndex = 0;
  }
  const onLayoutFunction = useCallback(
    (event, option, index) => {
      if (initialValue && !initialPositionDone) {
        let {width, height, x, y} = event.nativeEvent.layout;
        if (initialValue === option?.value) {
          setSelectedItemYPos(y);
          setSelectedItemHeight(height + hp(4));
        }
        if (index === options?.length - 1) {
          setLastItemYPos(y);
          setLastItemHeight(height + hp(4));
        }
      }
    },
    [initialValue, initialPositionDone],
  );

  useEffect(() => {
    if (
      selectedItemYPos > 0 &&
      selectedItemHeight > 0 &&
      lastItemYPos > 0 &&
      lastItemHeight > 0 &&
      scrollViewHeight > 0 &&
      !initialPositionDone
    ) {
      //if the selected item's y position is inside the last page, so set the contentOffset to the last page (prevent bug BOTTOM GAP on IOS)
      let selectedItemYCenterPos =
        selectedItemYPos - scrollViewHeight / 2 + selectedItemHeight / 2;
      let lastPagePosition = lastItemYPos + lastItemHeight - scrollViewHeight;
      let yPos =
        selectedItemYCenterPos > lastPagePosition
          ? lastPagePosition < 0
            ? 0
            : lastPagePosition
          : selectedItemYCenterPos < selectedItemHeight //if contentOffSet is less than 1 item in the list, it can be set to 0 (because scroll is negligible)
          ? 0
          : selectedItemYCenterPos;
      setContentOffset(yPos);
      setInitialPositionDone(true);
    }
  }, [
    selectedItemYPos,
    selectedItemHeight,
    lastItemYPos,
    lastItemHeight,
    scrollViewHeight,
    initialPositionDone,
  ]);

  console.log('options', options, clubMacroCodeConfig);
  return (
    <View
      style={[
        {
          height:
            origin === 'clubRecommender'
              ? hp('50%')
              : isBottomSheet
              ? hp('100%')
              : hp('60%'),
          backgroundColor: isBottomSheet ? 'white' : '#F5F6F7',
          paddingBottom: 20,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
        },
      ]}
    >
      <Header
        title={title}
        goBack={() =>
          initialValue !== value ? goback() : setCurrentSelector(null)
        }
      />
      <ScrollView
        style={appStyles.flex}
        contentOffset={
          isCustomImage === false
            ? {
                y: contentOffset,
              }
            : undefined
        }
        onLayout={event => {
          let {width, height} = event.nativeEvent.layout;
          setScrollViewHeight(height);
        }}
      >
        <View
          style={[
            appStyles.pHSm,
            appStyles.mTMd,
            isCustomImage && appStyles.row,
            isCustomImage ? {flexWrap: 'wrap', justifyContent: 'center'} : {},
            appStyles.whiteBg,
          ]}
        >
          {options.map((option, index) => {
            const modelName = convertModelNameToMarcoName(option?.value);
            const optionMacroCode = clubMacroCodeConfig?.options?.find(x =>
              compareClubDisplayName(x.desc?.[0], modelName),
            );

            if (isCustomImage) {
              let nameMacroCode = optionMacroCode?.name;
              // update right SKU for Kalea Premier
              if (
                nameMacroCode === 'TC131' &&
                optionMacroCode?.desc?.length > 0 &&
                optionMacroCode?.desc?.[0] === 'Kalea Premier'
              ) {
                nameMacroCode = 'TA057';
              }
              if (
                nameMacroCode === 'TC132' &&
                optionMacroCode?.desc?.length > 0 &&
                optionMacroCode?.desc?.[0] === 'Kalea Premier Fwy'
              ) {
                nameMacroCode = 'TA085';
              }
              return (
                <CustomImageSelectorItem
                  key={option.label}
                  clubType={clubType}
                  isAllowSelectionOtherClub={isAllowSelectionOtherClub}
                  imageType={imageType}
                  option={option}
                  value={value}
                  setValue={setValue}
                  clubMacroCodeConfig={clubMacroCodeConfig}
                  nameMacroCode={nameMacroCode}
                />
              );
            }
            return (
              <ImageSelectorItem
                key={option.label}
                imageType={imageType}
                option={option}
                value={value}
                setValue={setValue}
                setUpCharge={setUpCharge}
                onLayoutFunction={event =>
                  onLayoutFunction(event, option, index)
                }
              />
            );
          })}
        </View>
      </ScrollView>
      <View
        style={[
          appStyles.pHSm,
          appStyles.pBMd,
          appStyles.pTSm,
          isBottomSheet || (!isBottomSheet && isTablet)
            ? {marginBottom: '12%'}
            : {},
        ]}
      >
        <Button
          text={isBottomSheet ? 'common.next' : 'common.save'}
          textColor={'white'}
          backgroundColor={'black'}
          borderColor={'black'}
          disabled={loading || !value}
          onPress={save}
          loading={loading}
          loadingMode="dark"
          centered
          DINbold
        />
      </View>
    </View>
  );
};

export default ImageSelector;
