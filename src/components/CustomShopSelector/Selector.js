import React, {useState, useEffect, useCallback} from 'react';
import {View, ScrollView} from 'react-native';
import {moderateScale} from 'react-native-size-matters';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';

import Header from './Header';
import Button from '../Button';

import appStyles from '../../styles/global';
import {GREY} from '../../config';
import {convertCustomShopText} from '../../utils/convert';

const Selector = ({
  title,
  options,
  value,
  setValue,
  setCurrentSelector,
  save,
  loading,
  fullWidth,
  isBottomSheet,
  origin,
  goback,
}) => {
  const isTablet = DeviceInfo.isTablet();
  const [initialValue, setInitialValue] = useState(value);
  const [initialPositionDone, setInitialPositionDone] = useState(false);
  const [selectedItemYPos, setSelectedItemYPos] = useState(0);
  const [lastItemYPos, setLastItemYPos] = useState(0);
  const [selectedItemHeight, setSelectedItemHeight] = useState(0);
  const [scrollViewHeight, setScrollViewHeight] = useState(0);
  const [lastItemHeight, setLastItemHeight] = useState(0);
  const [contentOffset, setContentOffset] = useState(0);

  const onLayoutFunction = useCallback(
    (event, option, index) => {
      if (initialValue && !initialPositionDone) {
        let {width, height, x, y} = event.nativeEvent.layout;
        if (initialValue === option?.value) {
          setSelectedItemYPos(y);
          setSelectedItemHeight(height + hp(4));
        }
        if (index === options?.length - 1) {
          setLastItemYPos(y);
          setLastItemHeight(height + hp(4));
        }
      }
    },
    [initialValue, initialPositionDone]
  );

  useEffect(() => {
    if (
      selectedItemYPos > 0 &&
      selectedItemHeight > 0 &&
      lastItemYPos > 0 &&
      lastItemHeight > 0 &&
      scrollViewHeight > 0 &&
      !initialPositionDone
    ) {
      //if the selected item's y position is inside the last page, so set the contentOffset to the last page (prevent bug BOTTOM GAP on IOS)
      let selectedItemYCenterPos =
        selectedItemYPos - scrollViewHeight / 2 + selectedItemHeight / 2;
      let lastPagePosition = lastItemYPos + lastItemHeight - scrollViewHeight;
      let yPos =
        selectedItemYCenterPos > lastPagePosition
          ? lastPagePosition < 0
            ? 0
            : lastPagePosition
          : selectedItemYCenterPos < selectedItemHeight //if contentOffSet is less than 1 item in the list, it can be set to 0 (because scroll is negligible)
          ? 0
          : selectedItemYCenterPos;
      setContentOffset(yPos);
      setInitialPositionDone(true);
    }
  }, [
    selectedItemYPos,
    selectedItemHeight,
    lastItemYPos,
    lastItemHeight,
    scrollViewHeight,
    initialPositionDone,
  ]);
  return (
    <View
      style={[
        {
          height:
            origin === 'clubRecommender'
              ? hp('50%')
              : isBottomSheet
              ? hp('100%')
              : hp('60%'),
          width: '100%',
          backgroundColor: isBottomSheet ? 'white' : '#F5F6F7',
          paddingBottom: 20,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
        },
      ]}
    >
      <Header
        title={title}
        goBack={() =>
          initialValue !== value ? goback() : setCurrentSelector(null)
        }
      />
      <ScrollView
        style={appStyles.flex}
        contentOffset={{
          y: contentOffset,
        }}
        onLayout={event => {
          let {width, height} = event.nativeEvent.layout;
          setScrollViewHeight(height);
        }}
      >
        <View
          style={[
            appStyles.pHSm,
            appStyles.mTMd,
            appStyles.flex,
            appStyles.row,
            {flexWrap: 'wrap'},
          ]}
        >
          {options.map((option, index) => {
            return (
              <Button
                key={option.label}
                style={[
                  {
                    width: fullWidth ? '100%' : wp('44%'),
                    height: moderateScale(38),
                  },
                  appStyles.mRXs,
                  appStyles.mBSm,
                ]}
                text={convertCustomShopText(option.label)}
                textColor={value === option.value ? 'white' : 'black'}
                backgroundColor={value === option.value ? 'black' : 'white'}
                borderColor={value === option.value ? 'black' : GREY}
                onPress={() => setValue(option.value)}
                textSize="xs"
                centered
                onLayoutFunction={event =>
                  onLayoutFunction(event, option, index)
                }
              />
            );
          })}
        </View>
      </ScrollView>
      <View
        style={[
          appStyles.pHSm,
          appStyles.pBMd,
          appStyles.pTSm,
          isBottomSheet || (!isBottomSheet && isTablet)
            ? {marginBottom: '12%'}
            : {},
        ]}
      >
        <Button
          text={isBottomSheet ? 'common.next' : 'common.save'}
          textColor={'white'}
          backgroundColor={'black'}
          borderColor={'black'}
          disabled={loading || !value}
          onPress={save}
          loading={loading}
          loadingMode="dark"
          centered
          DINbold
        />
      </View>
    </View>
  );
};

export default Selector;
