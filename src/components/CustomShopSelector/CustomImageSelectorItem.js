import React, {useEffect, useState} from 'react';
import {View, Image, TouchableWithoutFeedback, StyleSheet} from 'react-native';
import Text from '../Text';
import appStyles from '../../styles/global';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import FastImage from 'react-native-fast-image/src';
import {getPlaceholderImageOfClubModel} from 'utils/clubs';

const CustomImageSelectorItem = ({
  imageType,
  option,
  value,
  setValue,
  setUpCharge,
  nameMacroCode,
  clubType,
  isAllowSelectionOtherClub,
}) => {
  const [itemImage, setItemImage] = useState(
    option.image
      ? `https://www.taylormadegolf.com/on/demandware.static/-/Sites-tmag-custom-catalog-us/default/images/OracleConfigurator/${imageType}/${option.image}.jpg`
      : nameMacroCode
      ? `https://www.taylormadegolf.com/on/demandware.static/-/Sites-tmag-master-catalog/default/zoom/${nameMacroCode}_zoom_D.jpg`
      : null,
  );

  useEffect(() => {
    if (clubType === 'TM_IRONS_MODEL' && option?.nameMacroCode) {
      setItemImage(
        `https://www.taylormadegolf.com/on/demandware.static/-/Sites-tmag-master-catalog/default/zoom/${option?.nameMacroCode}_zoom_D.jpg`,
      );
    }
  }, []);

  const getImage = () => {
    return itemImage
      ? {uri: itemImage, cache: FastImage.cacheControl.web}
      : getPlaceholderImageOfClubModel(clubType);
  };

  const selectedContainerStyle =
    value === option.value
      ? [{backgroundColor: 'black'}]
      : [{borderWidth: 1, borderColor: 'black'}];
  const selectedTextStyle =
    value === option.value ? [appStyles.white] : [appStyles.black];

  const selectedImageStyle =
    value === option.value ? [styles.containerSelected] : [];

  const selectItem = () => {
    setValue(option.value);
  };

  return (
    <TouchableWithoutFeedback onPress={selectItem}>
      <View
        style={[
          appStyles.borderRadius,
          appStyles.mBSm,
          appStyles.mHXs,
          appStyles.column,
          ...selectedContainerStyle,
          {width: wp('40%')},
        ]}
      >
        <View
          style={[
            appStyles.borderTopRadius,
            ...selectedImageStyle,
            styles.imageContainer,
          ]}
        >
          <FastImage
            style={[appStyles.fullWidth, appStyles.fullHeight]}
            source={getImage()}
            resizeMode="cover"
            onError={() => setItemImage(null)}
          />
        </View>
        <Text
          style={[
            appStyles.textCenter,
            appStyles.pHXs,
            appStyles.mVMd,
            appStyles.xs,
            ...selectedTextStyle,
          ]}
        >
          {option?.desc?.[0] || option?.value}
        </Text>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  containerSelected: {
    borderWidth: 1,
    borderColor: 'black',
  },
  imageContainer: {
    width: '100%',
    height: 170,
    paddingTop: 10,
    backgroundColor: 'white',
  },
});

export default CustomImageSelectorItem;
