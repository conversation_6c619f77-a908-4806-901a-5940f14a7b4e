import React from 'react';
import {View, TouchableWithoutFeedback} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome5';
import DeviceInfo from 'react-native-device-info';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

import Text from '../Text';

import appStyles from '../../styles/global';
import { t } from 'i18next';

const CustomShopHeader = ({title, goBack}) => {
  const isTablet = DeviceInfo.isTablet();
  let headerTitle = '';
  if (title) {
    if (
      (title + '')
        .trim()
        .toUpperCase()
        .startsWith(t('config_club.choose')?.toUpperCase())
    ) {
      headerTitle = title;
    } else {
      headerTitle = `${t('config_club.choose')} ${title}`;
    }
  }
  return (
    <View style={[appStyles.row, appStyles.hCenter, appStyles.pHSm]}>
      {goBack ? (
        <TouchableWithoutFeedback onPress={goBack}>
          <Icon
            style={appStyles.pRSm}
            name="chevron-left"
            size={isTablet ? wp('3%') : wp('5%')}
            color="black"
          />
        </TouchableWithoutFeedback>
      ) : null}
      <Text style={[appStyles.bold, appStyles.md]}>{headerTitle}</Text>
    </View>
  );
};

export default CustomShopHeader;
