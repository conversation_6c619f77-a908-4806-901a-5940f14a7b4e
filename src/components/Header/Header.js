import React, {useMemo} from 'react';
import PropTypes from 'prop-types';
import {
  View,
  TouchableOpacity,
  Platform,
  StyleSheet,
  Image,
} from 'react-native';
import {connect, useSelector} from 'react-redux';
import DeviceInfo from 'react-native-device-info';
import analytics from '@react-native-firebase/analytics';

import Logo from '../Logo';
import Text from '../Text';

import CloseIconSm from 'assets/imgs/icon-close-sm.svg';
import CloseDarkIconSm from 'assets/imgs/icon-close-dark-sm.svg';
import CloseIconLg from 'assets/imgs/icon-close-lg.svg';
import CloseDarkIconLg from 'assets/imgs/icon-close-dark-lg.svg';
import CloseProfile from 'assets/imgs/icon_close_profile.svg';
import IconCart from 'assets/imgs/ic_cart.png';

import appStyles from 'styles/global';
import styles from './styles';
import {moderateScale} from 'react-native-size-matters';
import {isOtherPayment, openOtherPayment} from 'utils/user';
import {getFontSize} from 'utils/constant';
import {GREEN_STATS, WHITE} from 'config';

const isTablet = DeviceInfo.isTablet();

const Header = ({
  isLogo,
  isClose,
  title,
  titleColor,
  close,
  closeDark,
  user,
  navigate,
  showGoPro,
  upgradeButtonStyle,
  upgradeButtonTextStyle,
  profileBG,
  profileTextColor,
  isShop,
  basket,
  isBlack,
  headerStyle,
  iconProps,
  type,
  titleStyle,
  smallHeader,
  haveLogo,
  headerProfile,
}) => {
  const totalNotificationUnread = useSelector(
    state => state.home?.totalNotificationUnread,
  );
  const permissions = useSelector(state => state?.app?.permissions);
  const allProductsQuantity = useMemo(() => {
    const totalQuantity = basket?.product_items?.reduce?.(
      (total, item) => total + item.quantity,
      0,
    );
    return totalQuantity || 0;
  }, [basket]);
  return (
    <View style={[appStyles.row, appStyles.hCenter, headerStyle]}>
      {haveLogo && <Logo isBlack={isBlack} />}
      {isLogo ? (
        <Logo isBlack={isBlack} />
      ) : (
        <Text
          style={[
            appStyles.lg,
            titleColor,
            titleStyle,
            {fontSize: getFontSize(40)},
          ]}
          DINbold={titleStyle?.fontWeight === 'bold' ? false : true}
        >
          {title}
        </Text>
      )}
      {isClose ? (
        <TouchableOpacity
          style={[
            appStyles.mLAuto,
            type !== 'from_feature_splash' && appStyles.mBLg,
            smallHeader && appStyles.mBMd,
            style.iconContainer,
            headerProfile && {marginBottom: '4%'},
          ]}
          onPress={close}
        >
          {headerProfile ? (
            <CloseProfile />
          ) : closeDark ? (
            isTablet ? (
              <CloseDarkIconLg {...iconProps} />
            ) : (
              <CloseDarkIconSm {...iconProps} />
            )
          ) : type !== 'from_feature_splash' ? (
            <CloseIconLg {...iconProps} />
          ) : (
            <CloseIconSm {...iconProps} />
          )}
        </TouchableOpacity>
      ) : (
        <View
          style={[
            appStyles.row,
            appStyles.mLAuto,
            appStyles.spaceEnd,
            appStyles.hCenter,
          ]}
        >
          {!permissions?.myTMSubscriptionLevel && showGoPro ? (
            <TouchableOpacity
              style={[appStyles.mRSm]}
              onPress={() => {
                if (isOtherPayment(permissions)) {
                  openOtherPayment();
                  return;
                }
              }}
            >
              <View
                style={[
                  appStyles.blackBg,
                  // appStyles.pBMd,
                  appStyles.pHMd,
                  styles.upgradeButton,
                  upgradeButtonStyle,
                  {paddingVertical: '11%', paddingHorizontal: '10%'},
                ]}
              >
                <Text
                  style={[
                    appStyles.textCenter,
                    appStyles.white,
                    appStyles.bold,
                    appStyles.xs,
                    upgradeButtonTextStyle,
                  ]}
                >
                  profile.cta
                </Text>
              </View>
            </TouchableOpacity>
          ) : isShop ? (
            <TouchableOpacity
              onPress={async () => {
                navigate('Shop', {screen: 'ShopBasket'});
                await analytics().logEvent('shop_cta_open', {
                  name: 'Cart button',
                });
                await analytics().logEvent('cta_open', {
                  name: 'Cart button',
                });
              }}
            >
              <View
                style={[
                  appStyles.mRLg,
                  appStyles.hCenter,
                  appStyles.vCenter,
                  {minWidth: styles.cartButton.width},
                ]}
              >
                <Image
                  source={IconCart}
                  style={{
                    ...styles.cartButton,
                    position: 'absolute',
                    tintColor:
                      basket?.product_items?.length > 0
                        ? GREEN_STATS
                        : '#8C8B8F',
                  }}
                />
                <Text
                  style={[
                    appStyles.xxs,
                    appStyles.textCenter,
                    {
                      marginBottom: 5,
                      marginLeft: 3,
                      color:
                        basket?.product_items?.length > 0 ? GREEN_STATS : WHITE,
                    },
                  ]}
                >
                  {allProductsQuantity}
                </Text>
              </View>
            </TouchableOpacity>
          ) : null}

          <TouchableOpacity onPress={() => navigate('Profile')}>
            <View
              style={[
                Platform.OS === 'ios' ? appStyles.pTMd : {},
                profileBG,
                appStyles.vCenter,
                styles.profileButton,
              ]}
            >
              {totalNotificationUnread > 0 && (
                <View style={[styles.dotIconNotification]} />
              )}

              <Text
                style={[
                  profileTextColor ? profileTextColor : appStyles.white,
                  appStyles.textCenter,
                  Platform.OS === 'ios' ? {bottom: 2} : {},
                ]}
                Din79Font
                size={12}
                weight={700}
              >
                {user.firstName?.charAt(0) || 'T'}
                {user.lastName?.charAt(0) || 'M'}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const style = StyleSheet.create({
  iconContainer: {
    marginRight: moderateScale(-10),
  },
});

Header.propTypes = {
  isLogo: PropTypes.bool,
  isClose: PropTypes.bool,
  title: PropTypes.string,
  close: PropTypes.func,
  type: PropTypes.string,
};

Header.defaultProps = {
  isLogo: false,
  isClose: false,
  closeDark: false,
  title: '',
  showGoPro: false,
  profileBG: appStyles.blackBg,
  close: () => {},
  type: '',
};

const mapStateToProps = state => ({
  user: state.user,
  basket: state.basket,
});

export default connect(mapStateToProps, null)(Header);
