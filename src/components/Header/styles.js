import {StyleSheet, Platform} from 'react-native';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';

const isTablet = DeviceInfo.isTablet();

export default StyleSheet.create({
  upgradeButton: {
    borderTopLeftRadius: wp('50%'),
    borderTopRightRadius: wp('50%'),
    borderBottomLeftRadius: wp('50%'),
    borderBottomRightRadius: wp('50%'),
  },
  profileButton: {
    height: hp('4%'),
    width: hp('4.2%'),
    borderRadius: hp('100%'),
  },
  dotIconNotification: {
    height: hp('1.2%'),
    width: hp('1.2%'),
    borderRadius: hp('100%'),
    backgroundColor: 'red',
    position: 'absolute',
    top: hp('0%'),
    left: hp('0%'),
  },
  cartButton: {
    height: hp('3.5%'),
    width: hp('3.5%'),
  },
  cartHandle: {
    position: 'absolute',
    borderLeftWidth: isTablet ? wp('0.3%') : wp('0.4%'),
    borderTopWidth: isTablet ? wp('0.3%') : wp('0.4%'),
    borderRightWidth: isTablet ? wp('0.3%') : wp('0.4%'),
    width: hp('1.5%'),
    height: hp('0.8%'),
    top: hp('-0.8%'),
  },
});
