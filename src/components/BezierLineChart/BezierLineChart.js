import React from 'react';
import {View, Text} from 'react-native';
import {moderateScale} from 'react-native-size-matters';
import appStyles from '../../styles/global';
import {LineChart} from 'react-native-chart-kit';

const BezierLineChart = () => {
  // const {width, height} = Dimensions.get('window');
  // return (
  //   <View style={styles.container}>
  //     <Svg
  //       width={'100%'}
  //       height={height}
  //       style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}
  //     >
  //       {/* <Circle cx="150" cy="30" r="4" fill="black" />
  //       <Circle cx="110" cy="30" r="4" fill="black" /> */}
  //       <Circle cx="275" cy="120" r="4" fill="red" />
  //       <Path
  //         d={
  //           'M 50 20 M 0 100 L 20 100 A 45 50 1 0 0 60 70 A 85 100 1 0 1 130 10 A 70 85 1 0 1 200 70 A 45 50 1 0 0 240 100 L 260 100 L 0 100'
  //         }
  //         stroke={'black'}
  //         strokeWidth={2}
  //         fill="none"
  //       />
  //     </Svg>
  //   </View>
  // );
  return (
    <>
      <View style={{flex: 1, width: '100%', height: 250}}>
        <View>
          <LineChart
            data={{
              labels: [''],
              datasets: [
                {
                  data: [0, 10, 0],
                },
                {
                  data: [0, 5.4],
                  color: () => '#fff',
                },
              ],
            }}
            width={moderateScale(450)} // from react-native
            height={moderateScale(150)}
            withInnerLines={false}
            withOuterLines={false}
            withHorizontalLabels={false}
            withShadow={false}
            withDots={true}
            fromZero
            transparent
            chartConfig={{
              propsForLabels: {
                fontSize: '8',
              },
              color: () => `rgb(0, 0, 0)`,
              propsForDots: {
                r: '6',
                strokeWidth: '2',
                stroke: 'black',
              },
            }}
            bezier
            style={{
              marginVertical: 8,
              paddingRight: 0,
            }}
          />
        </View>
        <View
          style={{
            width: '100%',
            height: 3,
            backgroundColor: 'black',
            marginTop: moderateScale(-31),
          }}
        />
        <View style={[appStyles.row, appStyles.spaceBetween, {marginTop: 4}]}>
          <Text style={{fontSize: 8, fontWeight: '500'}}>Below Average</Text>
          <Text style={{fontSize: 8, fontWeight: '500'}}>Average</Text>
          <Text style={{fontSize: 8, fontWeight: '500'}}>Above Average</Text>
        </View>
      </View>
    </>
  );
};

// 'M 0 150 L 10 150   A 90 120 1 0 0 45 120 L 110 30'
// 'M 50 20 M 0 100 L 20 100 A 45 50 1 0 0 60 70 A 85 100 1 0 1 130 10 A 70 85 1 0 1 200 70 A 45 50 1 0 0 240 100 L 260 100 L 0 100'

export default BezierLineChart;
