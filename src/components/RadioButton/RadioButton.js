import React from 'react';
import {TouchableOpacity} from 'react-native';
import Octicons from 'react-native-vector-icons/Octicons';
import {GREEN_RADIO} from '../../config';

const RadioButton = ({checked, setChecked}) => {
  const onPressItem = () => {
    if (setChecked) setChecked();
  };
  return (
    <TouchableOpacity
      onPress={onPressItem}
      style={{
        width: 22,
        height: 22,
        borderRadius: 11,
        borderWidth: 1,
        borderColor: checked ? GREEN_RADIO : '#979797',
        backgroundColor: checked ? GREEN_RADIO : 'white',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {checked && <Octicons name="check" color={'white'} size={16} />}
    </TouchableOpacity>
  );
};

export default RadioButton;
