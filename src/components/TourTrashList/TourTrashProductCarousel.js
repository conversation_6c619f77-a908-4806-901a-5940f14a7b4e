import React, {useState, useEffect} from 'react';
import {TouchableOpacity, View, StyleSheet, Dimensions} from 'react-native';
import StorePromotion from '../StorePromotion';
import Carousel from 'react-native-snap-carousel';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';
import appStyles from 'styles/global';
import PlayButton from 'assets/imgs/icon-play-button.svg';
import FastImage from 'react-native-fast-image/src';

const WIDTH = Dimensions.get('window').width;

const TourTrashProductCarousel = ({data, gotoVideo, navigate}) => {
  const isTablet = DeviceInfo.isTablet();
  const [carouselOffset, setCarouselOffset] = useState([]);
  const [dataProduct, setDataProduct] = useState([]);

  useEffect(() => {
    getData();
  }, []);

  const getData = () => {
    let dataForm = [];
    data.images?.map(image => {
      dataForm.push({
        link: image?.imageUrl,
        ...data,
      });
    });
    if (data.videoId && data.videoId?.length > 0) {
      const video = {
        thumbnailUrl: `https://img.youtube.com/vi/${data?.videoId}/maxresdefault.jpg`,
        id: data.videoId,
        title: data.title,
        host: data.videoType,
      };
      dataForm.push(video);
    }
    setDataProduct(dataForm);
  };

  const renderItem = ({item}) => {
    if (item.thumbnailUrl) {
      return (
        <TouchableOpacity
          onPress={() => gotoVideo(item)}
          style={[appStyles.flex, appStyles.vCenter, appStyles.hCenter]}
        >
          <FastImage
            source={{uri: item.thumbnailUrl, cache: FastImage.cacheControl.web}}
            style={styles.thumbnail}
          />
          <PlayButton width={60} height={60} />
        </TouchableOpacity>
      );
    } else {
      return (
        <StorePromotion
          key={item?.id}
          item={item}
          navigate={navigate}
          origin={'tourtrash'}
        />
      );
    }
  };

  return (
    <View style={[appStyles.hCenter, {marginTop: 5}]}>
      <Carousel
        data={dataProduct}
        renderItem={renderItem}
        sliderWidth={wp('100%')}
        itemWidth={wp('100%')}
        height={WIDTH}
        enableMomentum={true}
        decelerationRate={0.9}
        inactiveSlideScale={1}
        inactiveSlideOpacity={1}
        onScroll={({nativeEvent}) => {
          const offset = nativeEvent.contentOffset.x;
          const carouselItemSize = nativeEvent.layoutMeasurement.width;
          setCarouselOffset((offset / carouselItemSize + 1) * 100 - 100);
        }}
      />
      <View
        style={[
          appStyles.row,
          {position: 'absolute', bottom: isTablet ? 40 : 10},
        ]}
      >
        {dataProduct.map((x, i) => {
          const paginationPosition = 100 * -i + carouselOffset;

          if (dataProduct?.length === 1) {
            return <View key={`${i}`} />;
          }
          return (
            <View
              key={`${i}`}
              style={[
                i !== dataProduct?.length - 1 ? appStyles.mRXs : {},
                {
                  position: 'relative',
                  overflow: 'hidden',
                },
              ]}
            >
              <View
                style={[
                  appStyles.indicator,
                  {opacity: 0.5, backgroundColor: '#FFFFFF'},
                ]}
              />
              <View
                style={{
                  position: 'absolute',
                  top: 0,
                  left: `${paginationPosition}%`,
                  backgroundColor: '#FFFFFF',
                  height: '100%',
                  width: '100%',
                }}
              />
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  thumbnail: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
});

export default TourTrashProductCarousel;
