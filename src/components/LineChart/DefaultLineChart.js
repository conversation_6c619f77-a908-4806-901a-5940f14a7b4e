import React, {useState} from 'react';
import {View, processColor, TouchableWithoutFeedback} from 'react-native';
import {LineChart} from 'react-native-charts-wrapper';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import Icon from 'react-native-vector-icons/Ionicons';
import DeviceInfo from 'react-native-device-info';

import Text from '../Text';
import ChartFilter from '../ChartFilter';

import appStyles from '../../styles/global';
import {LIGHT_GREY} from '../../config';

const TEXT_COLOR = processColor(LIGHT_GREY);
const GRID_COLOR = processColor('rgba(140, 140, 145, 0.3)');

const LineChartComponent = ({height, preview}) => {
  const isTablet = DeviceInfo.isTablet();
  const [filter, setFilter] = useState('3 Months');

  return (
    <View style={appStyles.flex}>
      {!preview ? (
        <>
          <ChartFilter
            type="line"
            filter={filter}
            setFilter={setFilter}
            options={['3 Months', '6 Months', '1 Year']}
          />
          <View style={[appStyles.row, appStyles.vCenter, appStyles.mBSm]}>
            <TouchableWithoutFeedback>
              <Icon
                name={'chevron-back'}
                size={isTablet ? wp('3%') : wp('5%')}
                color={'white'}
                style={appStyles.mRAuto}
              />
            </TouchableWithoutFeedback>
            <Text style={[appStyles.white, appStyles.text, appStyles.bold]}>
              linechart.jan2021
            </Text>
            <TouchableWithoutFeedback>
              <Icon
                name={'chevron-forward'}
                size={isTablet ? wp('3%') : wp('5%')}
                color={'white'}
                style={appStyles.mLAuto}
              />
            </TouchableWithoutFeedback>
          </View>
        </>
      ) : null}
      <LineChart
        style={[appStyles.flex, {height: height || hp('30%')}]}
        data={{
          dataSets: [
            {
              values: [
                {x: -1, y: 50},
                {x: -0.9, y: 54},
                {x: -0.8, y: 62},
                {x: -0.7, y: 57},
                {x: 0, y: 59},
                {x: 0.1, y: 54},
                {x: 0.2, y: 44},
                {x: 1, y: 50},
                {x: 1.1, y: 55},
                {x: 1.2, y: 40},
                {x: 1.3, y: 67},
                {x: 1.4, y: 70},
                {x: 2, y: 52},
                {x: 3, y: 48},
              ],
              label: '',
              config: {
                colors: [processColor('#fff')],
                drawCircles: false,
                drawValues: false,
                lineWidth: 3,
              },
            },
          ],
        }}
        xAxis={{
          valueFormatter: ['JAN', 'FEB', 'MAR'],
          position: 'BOTTOM',
          labelCount: 3,
          axisMinimum: -1,
          axisMaximum: 3,
          gridColor: GRID_COLOR,
          textColor: TEXT_COLOR,
          textSize: hp('1.2%'),
          gridDashedLine: {
            lineLength: 5,
            spaceLength: 3,
            phase: 1,
          },
          axisLineColor: processColor('transparent'),
        }}
        yAxis={{
          right: {enabled: false},
          left: {
            labelCount: 5,
            axisMinimum: 0,
            axisMaximum: 110,
            drawLabels: true,
            gridColor: GRID_COLOR,
            textColor: TEXT_COLOR,
            textSize: hp('1.2%'),
            gridDashedLine: {
              lineLength: 5,
              spaceLength: 3,
              phase: 1,
            },
            axisLineColor: processColor('transparent'),
          },
        }}
        chartDescription={{text: ''}}
        legend={{enabled: false}}
        marker={{enabled: false}}
        doubleTapToZoomEnabled={false}
        highlightPerTapEnabled={false}
        drawValueAboveBar={false}
        highlightPerDragEnabled={false}
        pinchZoom={false}
      />
    </View>
  );
};

export default LineChartComponent;
