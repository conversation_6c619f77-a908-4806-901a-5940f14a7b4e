import React from 'react';
import {View} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

import Text from '../Text';

import appStyles from '../../styles/global';

const ValueChart = ({chart, width, height, fontSize}) => {
  const chartData = chart?.chartData || chart?.data;

  const getValueNumber = () => {
    const number = chartData[0]?.number ? parseFloat(chartData[0]?.number) : 0;
    return number;
  };

  return (
    <View style={[appStyles.center]}>
      <View
        style={[
          {
            width: width || wp('65%'),
            height: height || wp('65%'),
            borderWidth: 15,
            borderColor: '#3DB472',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 1000,
          },
        ]}
      >
        <Text
          style={[
            fontSize ? appStyles[fontSize] : appStyles.xxl,
            appStyles.bold,
            {color: '#3DB472'},
          ]}
          DINbold
        >
          {getValueNumber()}
        </Text>
        <Text
          style={[
            appStyles.textCenter,
            appStyles.white,
            appStyles.xs,
            {width: wp('40%'), paddingHorizontal: wp('5%')},
          ]}
        >
          {chart?.numberLabel}
        </Text>
      </View>
    </View>
  );
};

export default ValueChart;
