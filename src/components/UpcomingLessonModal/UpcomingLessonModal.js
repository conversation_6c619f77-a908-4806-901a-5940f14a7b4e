import React from 'react';
import {Modal, View, Image} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

import Text from '../Text';
import Button from '../Button';

import appStyles from '../../styles/global';

const UpcomingLessonModal = ({visible, setVisible}) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={() => {}}
    >
      <View
        style={[
          appStyles.flex,
          appStyles.center,
          appStyles.pHSm,
          {backgroundColor: 'rgba(0,0,0,0.5)'},
        ]}
      >
        <View
          style={[
            appStyles.whiteBg,
            appStyles.borderRadius,
            appStyles.alignCenter,
            appStyles.pVMd,
          ]}
        >
          {/* <Image
            style={{width: wp('30%'), height: wp('30%'), alignSelf: 'center'}}
            source={require('../../../assets/imgs/upcoming-lesson.png')}
          /> */}
          <View style={[appStyles.pHMd]}>
            <Text
              style={[appStyles.mTSm, appStyles.bold, appStyles.textCenter]}
            >
              upcoming.lesson.modal.upcoming_lesson
            </Text>
            <Text
              style={[
                appStyles.mTSm,
                appStyles.textCenter,
                appStyles.lightGrey,
              ]}
            >
              upcoming.lesson.modal.mytaylormadecoach_is_a_step_by_step
            </Text>
            <Button
              style={[appStyles.mTMd]}
              text="GOT IT"
              textColor="white"
              backgroundColor={'black'}
              loadingMode="dark"
              onPress={() => setVisible(false)}
              centered
              DINbold
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default UpcomingLessonModal;
