import React from 'react';
import {processColor} from 'react-native';
import {Pie<PERSON>hart} from 'react-native-charts-wrapper';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {moderateScale} from 'react-native-size-matters';
import styles from '../../components/Text/styles';
import appStyles from '../../styles/global';

const PieChartComponent = ({values}) => {
  return (
    <PieChart
      // style={[appStyles.flex, {height: hp('28%')}]}
      style={{height: 400, width: '100%'}}
      chartBackgroundColor={processColor('white')}
      logEnabled={true}
      extraOffsets={{left: 39, top: 0, right: 28, bottom: 0}}
      data={{
        dataSets: [
          {
            values,
            label: 'Pie dataset',
            config: {
              colors: [
                processColor('#F2994A'),
                processColor('#FFCC00'),
                processColor('#2D9CDB'),
                processColor('#3E4D54'),
                processColor('#42926E'),
              ],
              axisLineWidth: false,
              valueTextSize: moderateScale(26),
              valueTextColor: processColor('black'),
              sliceSpace: 0,
              fontFamily: styles.DINbold.fontFamily,
              selectionShift: 19,
              xValuePosition: 'OUTSIDE_SLICE',
              yValuePosition: 'OUTSIDE_SLICE',
              valueFormatter: "#.#'%'",
              valueLineColor: processColor('transparent'),
              valueLinePart2Length: -0.4,
              valueLinePart1Length: 1.0,
            },
          },
        ],
      }}
      entryLabelColor={processColor('black')}
      entryLabelTextSize={moderateScale(13)}
      entryLabelFontFamily={styles.font.fontFamily}
      drawEntryLabels={true}
      rotationEnabled={false}
      usePercentValues={true}
      maxAngle={360}
      holeRadius={38}
      transparentCircleRadius={0}
      chartDescription={{text: ''}}
      legend={{enabled: false}}
      marker={{enabled: false}}
      touchEnabled={true}
    />
  );
};

export default PieChartComponent;
