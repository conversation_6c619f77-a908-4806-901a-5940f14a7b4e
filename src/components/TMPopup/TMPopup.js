import React from 'react';
import {View, StyleSheet, TouchableOpacity, Image} from 'react-native';
import Text from 'components/Text/Text';
import Modal from 'react-native-modal';
import appStyles from '../../styles/global';
import {Trans} from 'react-i18next';
import Icon from 'react-native-vector-icons/Feather';

const TMPopup = ({
  visible,
  icon,
  iconStyle,
  title,
  description,
  descriptionValues,
  descriptionComponents,
  button1,
  button2,
  button1Action,
  button2Action,
  showCloseButton,
  closeAction,
}) => {
  return (
    <Modal
      backdropOpacity={0.6}
      style={{alignItems: 'center'}}
      isVisible={visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
    >
      <View style={styles.container}>
        {!!icon && (
          <Image
            source={icon}
            style={[styles.icon, iconStyle]}
            resizeMode="contain"
          />
        )}
        <Text style={styles.textTitle}>{title}</Text>
        <View
          style={[
            appStyles.hCenter,
            appStyles.vCenter,
            {
              marginTop: 20,
            },
          ]}
        >
          <Trans
            defaults={description}
            values={descriptionValues}
            parent={Text}
            style={styles.description}
            components={descriptionComponents}
          />
        </View>
        {!!button1 && (
          <TouchableOpacity style={[styles.button1]} onPress={button1Action}>
            <Text style={styles.textButton1}>{button1}</Text>
          </TouchableOpacity>
        )}
        {!!button2 && (
          <TouchableOpacity style={styles.button2} onPress={button2Action}>
            <Text style={styles.textButton2}>{button2}</Text>
          </TouchableOpacity>
        )}
        {!!showCloseButton && (
          <TouchableOpacity style={styles.closeButton} onPress={closeAction}>
            <Icon name="x" size={25} />
          </TouchableOpacity>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    backgroundColor: 'white',
    alignItems: 'center',
    overflow: 'hidden',
    width: 300,
    paddingVertical: 70,
  },
  icon: {
    width: 47,
    height: 47,
    marginBottom: 20,
  },
  textTitle: {
    fontWeight: '700',
    fontSize: 17,
    textAlign: 'center',
    marginHorizontal: 30,
  },
  description: {
    color: '#8C8B8F',
    textAlign: 'center',
    marginHorizontal: 30,
    fontSize: 13,
  },
  button1: {
    marginTop: 20,
    backgroundColor: '#111111',
    paddingHorizontal: 25,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 22,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 4,
    shadowColor: 'rgba(0,0,0,0.2)',
  },
  textButton1: {
    color: 'white',
    fontSize: 13,
    fontWeight: '600',
    textAlign: 'center',
  },

  button2: {
    marginTop: 20,
    backgroundColor: 'white',
    paddingHorizontal: 25,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 22,
    borderWidth: 2,
    borderColor: '#111111',
  },
  textButton2: {
    color: '#111111',
    fontSize: 13,
    fontWeight: '600',
    textAlign: 'center',
  },
  closeButton: {
    position: 'absolute',
    width: 36,
    height: 36,
    right: 0,
    top: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default TMPopup;
