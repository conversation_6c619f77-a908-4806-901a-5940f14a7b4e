import React, {useState} from 'react';
import {ImageBackground} from 'react-native';
import FastImage from 'react-native-fast-image/src';

const CustomImageBackground = props => {
  const [isError, setError] = useState(false);
  const [useLocalImage, setUseLocalImage] = useState(
    !props.source.uri && !props.fallbackUri,
  );
  const shouldUseFallback = isError || !props.source.uri;
  return (
    <FastImage
      {...props}
      source={
        useLocalImage
          ? props.fallBackImg
          : {
              uri: shouldUseFallback ? props.fallbackUri : props.source.uri,
            }
      }
      onError={e => {
        console.log('load image background fail', e.nativeEvent);
        console.log('props.source.uri', props.source.uri);
        console.log('props.fallbackUri', props.fallbackUri);
        if (props.fallbackUri && props.source.uri) {
          //if the fallback image still loads error, use the local image
          //if props.fallbackUri equal to props.source.uri so no need to set uri to fallback, use directly the local image
          if (
            isError ||
            props.source.uri?.toLowerCase?.() ===
              props.fallbackUri?.toLowerCase?.()
          ) {
            setUseLocalImage(true);
          } else {
            setError(true);
          }
        } else {
          setUseLocalImage(true);
        }
      }}
    >
      {props.children}
    </FastImage>
  );
};

export default CustomImageBackground;
