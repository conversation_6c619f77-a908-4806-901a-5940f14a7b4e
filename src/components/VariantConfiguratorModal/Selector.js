import React from 'react';
import {View, ScrollView} from 'react-native';
import {moderateScale} from 'react-native-size-matters';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {SafeAreaView} from 'react-native-safe-area-context';

import Header from '../CustomShopSelector/Header';
import Button from '../Button';

import appStyles from '../../styles/global';
import {GREY} from '../../config';

const Selector = ({
  title,
  variants,
  option,
  setOption,
  validateBasket,
  loading,
  setVariantName,
}) => {
  return (
    <SafeAreaView edges={['right', 'bottom', 'left']}>
      <View
        style={[
          appStyles.pVSm,
          {
            height: hp('55%'),
            width: '100%',
            backgroundColor: 'white',
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
          },
        ]}
      >
        <Header title={title} />
        <ScrollView style={appStyles.flex}>
          <View
            style={[
              appStyles.pHSm,
              appStyles.mTMd,
              appStyles.flex,
              appStyles.row,
              {flexWrap: 'wrap'},
            ]}
          >
            {variants
              .filter(variant => variant.orderable)
              .map(variant => {
                return (
                  <Button
                    key={variant.product_id}
                    style={[
                      {
                        width: wp('44%'),
                        height: moderateScale(38),
                      },
                      appStyles.mRXs,
                      appStyles.mBSm,
                    ]}
                    text={variant.variation_values?.Akeneo_Size}
                    textColor={
                      option === variant.product_id ? 'white' : 'black'
                    }
                    backgroundColor={
                      option === variant.product_id ? 'black' : 'white'
                    }
                    borderColor={option === variant.product_id ? 'black' : GREY}
                    onPress={() => {
                      setOption(variant.product_id);
                      setVariantName(variant.variation_values?.Akeneo_Size);
                    }}
                    textSize="xs"
                  />
                );
              })}
          </View>
        </ScrollView>

        <View
          style={[
            appStyles.pHSm,
            appStyles.pBMd,
            appStyles.pTSm,
            {marginBottom: 40},
          ]}
        >
          <Button
            text="ADD TO CART"
            textColor={'white'}
            backgroundColor={option ? 'black' : GREY}
            borderColor={option ? 'black' : GREY}
            disabled={loading || !option}
            onPress={validateBasket}
            loading={loading}
            loadingMode="dark"
            centered
            DINbold
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Selector;
