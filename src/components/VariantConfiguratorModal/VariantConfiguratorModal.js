import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import Toast from 'react-native-toast-message';
import Icon from 'react-native-vector-icons/Feather';
import {moderateScale} from 'react-native-size-matters';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import * as Animatable from 'react-native-animatable';
import BottomSheet from 'reanimated-bottom-sheet';
import {connect, useSelector} from 'react-redux';

import Selector from './Selector';
import BasketMessageModal from '../BasketMessageModal';

import {
  addProductsToBasket,
  createBasket,
  deleteBasket,
  getProductDetails,
} from '../../requests/ecom';
import {updateCurrentBasket} from '../../reducers/basket';

import appStyles from '../../styles/global';
import {showToast} from '../../utils/toast';
import {t} from 'i18next';
import analytics from '@react-native-firebase/analytics';
import {NOT_APPLICABLE, SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';
import Header from '../CustomShopSelector/Header';
import Button from 'components/Button';
import {convertCustomShopText} from 'utils/convert';
import {GREY} from 'config';

const VariantConfiguratorModal = ({
  toggleSelector,
  setToggleSelector,
  variants,
  updateCurrentBasket,
  navigate,
  product,
  attributes,
  originProductId,
  quantity,
}) => {
  const bottomSheetRef = useRef(null);
  const toastRef = useRef(null);
  const basket = useSelector(state => state.basket);
  const [currentData, setCurrentData] = useState(null);
  const [selectedData, setSelectedData] = useState({});

  const [visible, setVisible] = useState(false);
  const [clearingBasket, setClearingBasket] = useState(false);
  const [loading, setLoading] = useState(false);
  const hasTTBItem = basket.product_items?.some(product => {
    const hasTTB = product?.c_tm_customconfigurator_nodes
      ? JSON.parse(product?.c_tm_customconfigurator_nodes)?.selectedNodes?.some(
          node => {
            return node.name === 'TTB';
          },
        )
      : false;
    return hasTTB;
  });

  useEffect(() => {
    if (attributes?.length > 0) {
      const displayData = filterCurrentData(attributes[0], {});
      setCurrentData(displayData);
    }
  }, [attributes]);

  const filterCurrentData = (initData, selectedOptions) => {
    const availableVariants = variants?.filter(variant => {
      let isMapping = true;
      const variation_values = variant?.variation_values;
      Object.keys(selectedOptions).forEach(key => {
        if (variation_values[key] !== selectedOptions[key]) {
          isMapping = false;
        }
      });
      return isMapping;
    });

    const selectableValues = initData?.values.map(option => {
      const isSelectable = availableVariants.some(
        variant => variant?.variation_values[initData.id] === option.value,
      );
      return {...option, selectable: isSelectable};
    });
    return {...initData, values: selectableValues};
  };

  const nextAttributeData = async value => {
    const newSelected = {...selectedData, [currentData?.id]: value};
    setSelectedData(newSelected);
    let indexOfCurrent = 0;
    for (let i = 0; i < attributes?.length; i++) {
      if (currentData?.id === attributes[i]?.id) {
        indexOfCurrent = i;
      }
    }

    if (indexOfCurrent < attributes?.length - 1) {
      const displayData = filterCurrentData(
        attributes[indexOfCurrent + 1],
        newSelected,
      );
      setCurrentData(displayData);
    } else {
      // setShowAttributeData(false);
      // if (!hasSizeVariants) {
      //   onFinishShowAttributes();
      //   setToggleSelector(false);
      // }

      const orderableVariants = variants.filter(
        variant => variant.orderable === true,
      );
      const productVariant = orderableVariants.find(variant => {
        let isSame = true;
        Object.keys(newSelected).forEach(key => {
          if (variant.variation_values[key] !== newSelected[key]) {
            isSame = false;
          }
        });
        if (isSame) {
          return true;
        }
      });
      const product_id = productVariant?.product_id || originProductId;
      if (hasTTBItem) {
        setVisible(true);
      } else {
        addToCart(product_id);
      }
    }
  };

  useEffect(() => {
    if (toggleSelector) {
      bottomSheetRef?.current?.snapTo(0);
    } else {
      bottomSheetRef?.current?.snapTo(1);
    }
  }, [toggleSelector]);

  const GA_addProductToCart = async currentBasket => {
    let productBasket =
      currentBasket?.product_items[currentBasket?.product_items?.length - 1];
    const productDetails = await getProductDetails(productBasket?.product_id);

    analytics().logEvent('add_to_cart', {
      app_screen_name: 'shop - drops - product', //e.g home, my orders
      screen_type: SCREEN_CLASS.PDP, //e.g checkout, pdp, plp, account
      page_name: 'shop - drops - product', //e.g home, my orders
      page_type: SCREEN_TYPES.MEMBERSHOP, //e.g basket, home, order
      page_category: SCREEN_TYPES.MEMBERSHOP,
      currency: 'USD',
      price: productBasket?.price, //e.g 123.22
      items: [
        {
          item_id: productBasket?.product_id,
          item_name: productBasket?.product_name,
          item_category: NOT_APPLICABLE,
          item_brand: 'TaylorMade Golf',
          item_list_name: 'Cart',
          item_location_id: 'shop - drops - product', //optional
          item_variant: productDetails?.c_tm_comfil_size || NOT_APPLICABLE,
          index: 0, //e.g 4
          quantity: quantity || 1, //e.g 2
        },
      ],
    });
  };

  const addToCart = async (productId, newBasket) => {
    setLoading(true);
    try {
      // Make request to add product to sfcc basket
      const currentBasket = await addProductsToBasket({
        basketId: newBasket ? newBasket.basket_id : basket.basket_id,
        resourceState: newBasket
          ? newBasket['_resource_state']
          : basket['_resource_state'],
        products: [
          {
            product_id: productId,
            quantity: quantity || 1,
          },
        ],
      });
      GA_addProductToCart(currentBasket);
      // Update basket in redux
      updateCurrentBasket(currentBasket);

      setLoading(false);
      setToggleSelector(false);

      // Navigate to basket
      navigate('ShopBasket');
    } catch (error) {
      const errorMessage =
        error.response?.data?.errorMessage ||
        error.response?.data?.message ||
        error.response?.data?.fault?.message
          ? error.response?.data?.errorMessage ||
            error.response?.data?.message ||
            error.response?.data?.fault?.message
          : t('variant.configurator_modal.default_message');
      setLoading(false);
      setToggleSelector(false);
      showToast({
        type: 'error',
        message: t('variant.configurator_modal.fore'),
        subText: errorMessage,
      });
    }
  };

  const clearBasket = async () => {
    setClearingBasket(true);

    try {
      // Delete basket
      await deleteBasket({
        basketId: basket.basket_id,
        resourceState: basket._resource_state,
      });
      // Recreate basket
      const currentBasket = await createBasket();
      // Update basket in redux
      updateCurrentBasket(currentBasket);
      // Hide modal and loading state
      setVisible(false);
      setClearingBasket(false);
      // Add item to the new basket
      addToCart(currentBasket);
    } catch (error) {
      const errorMessage =
        error.response?.data?.errorMessage ||
        error.response?.data?.message ||
        error.response?.data?.fault?.message
          ? error.response?.data?.errorMessage ||
            error.response?.data?.message ||
            error.response?.data?.fault?.message
          : t('An_error_occurred_clearing_cart');
      setVisible(false);
      setClearingBasket(false);
      showToast({
        type: 'error',
        message: t('Shop_Error'),
        subText: errorMessage,
      });
    }
  };

  return (
    <Modal
      transparent={true}
      visible={toggleSelector}
      onRequestClose={() => {}}
    >
      <BasketMessageModal
        visible={visible}
        setVisible={setVisible}
        clearBasket={clearBasket}
        clearingBasket={clearingBasket}
        setLoading={setLoading}
        description={
          hasTTBItem
            ? t('You_can_only_purchase_Drops')
            : t('You_can_only_Try_Then_Buy')
        }
      />
      <View
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(0,0,0,0.7)',
        }}
      >
        <Toast ref={toastRef} />
      </View>
      <BottomSheet
        ref={bottomSheetRef}
        snapPoints={['55%', 0]}
        renderContent={() => {
          return (
            <>
              <Animatable.View animation="fadeInUp" delay={100}>
                <TouchableOpacity
                  style={[appStyles.mLAuto, appStyles.pBXs]}
                  onPress={() => setToggleSelector(false)}
                >
                  <View
                    style={[
                      styles.closeIcon,
                      appStyles.vCenter,
                      appStyles.hCenter,
                      appStyles.mRXs,
                    ]}
                    onPress={() => setToggleSelector(false)}
                  >
                    <Icon name="x" size={moderateScale(18)} />
                  </View>
                </TouchableOpacity>
              </Animatable.View>
              <AttributeSelector
                title={currentData?.name}
                options={currentData?.values || []}
                nextAction={nextAttributeData}
                loading={loading}
                isLastAttribute={
                  attributes[attributes?.length - 1]?.id === currentData?.id
                }
              />
            </>
          );
        }}
        initialSnap={1}
        enabledContentTapInteraction={false}
        enabledContentGestureInteraction={false}
      />
    </Modal>
  );
};

const AttributeSelector = ({
  title,
  options,
  nextAction,
  loading,
  isLastAttribute,
}) => {
  const [value, setValue] = useState(null);

  const nextActionHandle = () => {
    if (!isLastAttribute) {
      setValue(null);
    }
    nextAction(value);
  };

  return (
    <SafeAreaView edges={['right', 'bottom', 'left']}>
      <View
        style={[
          appStyles.pVSm,
          {
            height: hp('55%'),
            width: '100%',
            backgroundColor: 'white',
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
          },
        ]}
      >
        <Header title={title} />
        <ScrollView style={appStyles.flex}>
          <View
            style={[
              appStyles.pHSm,
              appStyles.mTMd,
              appStyles.flex,
              appStyles.row,
              {flexWrap: 'wrap'},
            ]}
          >
            {options
              .filter(option => option.orderable)
              .map(option => {
                return (
                  <Button
                    key={option?.value}
                    style={[
                      {
                        width: '46%',
                        height: moderateScale(38),
                      },
                      appStyles.mRXs,
                      appStyles.mBSm,
                    ]}
                    text={convertCustomShopText(option?.name)}
                    textColor={
                      !option.selectable
                        ? 'white'
                        : value === option.value
                        ? 'white'
                        : 'black'
                    }
                    backgroundColor={
                      !option.selectable
                        ? GREY
                        : value === option.value
                        ? 'black'
                        : 'white'
                    }
                    borderColor={
                      !option.selectable
                        ? GREY
                        : value === option.value
                        ? 'black'
                        : GREY
                    }
                    disabled={!option.selectable}
                    onPress={() => setValue(option.value)}
                    textSize="xs"
                    centered
                  />
                );
              })}
          </View>
        </ScrollView>
        <View
          style={[
            appStyles.pHSm,
            appStyles.pBMd,
            appStyles.pTSm,
            {marginBottom: 40},
          ]}
        >
          <Button
            text={isLastAttribute ? 'shop.pdp_buy.cta' : 'common.next'}
            textColor={'white'}
            backgroundColor={value ? 'black' : GREY}
            borderColor={value ? 'black' : GREY}
            disabled={!value || loading}
            onPress={nextActionHandle}
            loading={loading}
            loadingMode="dark"
            centered
            DINbold
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  closeIcon: {
    backgroundColor: '#F5F6F7',
    width: moderateScale(24),
    height: moderateScale(24),
    borderRadius: moderateScale(12),
  },
});

const mapDispatchToProps = {updateCurrentBasket};

export default connect(null, mapDispatchToProps)(VariantConfiguratorModal);
