import {useCallback} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import InAppReview from 'react-native-in-app-review';
import moment from 'moment';

const LAUNCH_COUNT_KEY = 'app_launch_count';
/**
 * Hook to trigger in-app rating prompt with expiration control.
 * Shows the prompt only if conditions are met (e.g., available and cooldown expired).
 */
export const useInAppReview = () => {
  const triggerInAppReview = useCallback(async () => {
    try {
      // Check if In-App Review is available on the device
      if (!InAppReview.isAvailable()) {
        return;
      }

      // Get current date
      const today = moment();
      // Get stored expiration time from previous rating attempt
      const expireTime = await AsyncStorage.getItem('ratingExpiresIn');
      if (expireTime) {
        const expiresIn = moment(expireTime);

        // If not yet expired, skip showing rating prompt
        if (!today.isAfter(expiresIn)) {
          return;
        }
      }

      console.log('[InAppReview] Triggering review prompt...', {
        expireTime,
        today: today.toISOString(),
      });

      // Show the in-app review dialog
      const hasFlowFinishedSuccessfully =
        await InAppReview.RequestInAppReview();
      // Remove launch count after review
      AsyncStorage.removeItem(LAUNCH_COUNT_KEY);

      console.log('[InAppReview] Review flow result:', {
        hasFlowFinishedSuccessfully,
      });

      // Store next expiration time (1 month later) regardless of the outcome
      await AsyncStorage.setItem(
        'ratingExpiresIn',
        moment().add(1, 'M').toISOString(),
      );

      // You can add any custom behavior here if needed
      if (hasFlowFinishedSuccessfully) {
        // e.g., log analytics, reward user, etc.
      }
    } catch (error) {
      console.warn('[InAppReview] Error during review prompt:', error);
    }
  }, []);

  return {triggerInAppReview};
};
