import {useSelector} from 'react-redux';

const useTrialSubscriptionTTB = () => {
  const permissions = useSelector(state => state?.app?.permissions);
  const launchFeaturesSelector = useSelector(
    state => state.app?.launchFeatures,
  );
  const isTrialSubscriptionTTB =
    !!permissions.isTrialSubscription && !launchFeaturesSelector?.TTBFreeTrial;
  return isTrialSubscriptionTTB;
};

export default useTrialSubscriptionTTB;
