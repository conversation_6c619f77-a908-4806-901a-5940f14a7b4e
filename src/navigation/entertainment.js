import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import ListArticlesByPlayer from 'screens/Clubhouse/components/Entertainment&Drills/ListArticlesByPlayer';

const EntertainmentStack = createStackNavigator();

export default ({navigation}) => (
  <EntertainmentStack.Navigator>
    <EntertainmentStack.Screen
      name="ListArticlesByPlayer"
      component={ListArticlesByPlayer}
      options={{
        headerShown: false,
        gestureEnabled: false,
      }}
    />
  </EntertainmentStack.Navigator>
);
