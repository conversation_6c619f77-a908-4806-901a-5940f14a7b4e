import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {horizontalLeftToRightScreenAnimation} from '../utils/animations';
import HandicapUSGA from 'screens/Handicap/HandicapUSGA';

const HandicapStack = createStackNavigator();

export default ({navigation: {navigate}}) => (
  <HandicapStack.Navigator screenOptions={horizontalLeftToRightScreenAnimation}>
    <HandicapStack.Screen
      name="HandicapUSGA"
      component={HandicapUSGA}
      options={{
        headerShown: false,
      }}
    />
  </HandicapStack.Navigator>
);
