import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import FeedArticle from 'screens/Article/FeedArticle';
import FeedStories from 'screens/Article/FeedStories';
import AllArticle from 'screens/Article/AllArticle';
import BackButton from 'components/BackButton';
import {t} from 'i18next';
import VideoArticle from 'screens/Article/VideoArticle';

const FeedArticleStack = createStackNavigator();

export default ({navigation}) => (
  <FeedArticleStack.Navigator>
    <FeedArticleStack.Screen
      name="FeedArticle"
      component={FeedArticle}
      options={{
        headerShown: false,
      }}
    />
    <FeedArticleStack.Screen
      name="VideoArticle"
      component={VideoArticle}
      options={{
        headerShown: false,
      }}
    />
    <FeedArticleStack.Screen
      name="FeedStories"
      component={FeedStories}
      options={{
        headerShown: false,
      }}
    />
    <FeedArticleStack.Screen
      name="AllArticle"
      component={AllArticle}
      options={{
        headerShown: true,
        headerTitle: t('All_Stories'),
        headerLeft: () => (
          <BackButton color="black" onPress={() => navigation.goBack()} />
        ),
      }}
    />
  </FeedArticleStack.Navigator>
);
