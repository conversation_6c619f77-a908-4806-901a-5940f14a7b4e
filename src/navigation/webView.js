import React from 'react';
import {View} from 'react-native';
import {createStackNavigator} from '@react-navigation/stack';

import BackButton from '../components/BackButton';
import WebView from '../screens/WebView';

import appStyles from '../styles/global';
import {LINK_BLUE} from '../config';

const WebViewStack = createStackNavigator();

export default ({navigation}) => (
  <WebViewStack.Navigator>
    <WebViewStack.Screen
      name="WebView"
      component={WebView}
      options={{
        headerTitleStyle: appStyles.sm,
        headerLeft: () => (
          <BackButton
            color={LINK_BLUE}
            text="common.done"
            onPress={() => navigation.goBack()}
          />
        ),
        headerRight: () => <View />,
        headerTitleStyle: {
          alignSelf: 'center',
        },
      }}
    />
  </WebViewStack.Navigator>
);
