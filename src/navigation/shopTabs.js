import React, {useEffect} from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import {Platform, View} from 'react-native';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import {useIsFocused} from '@react-navigation/native';
import analytics from '@react-native-firebase/analytics';

import ShopDrops from 'screens/Shop/ShopDrops';
import ShopTTB from 'screens/Shop/ShopTTB';
import ShopTourTrash from 'screens/Shop/ShopTourTrash';

import Header from 'components/Header';

import {getPermissions} from 'requests/accounts';
import {setDeepLink, updatePermissions} from 'reducers/app';
import {getBasket, createBasket} from 'requests/ecom';
import {updateCurrentBasket} from 'reducers/basket';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import MemberBenefit from 'screens/MemberBenefit/MemberBenefit';

const ShopStack = createStackNavigator();
const ShopTab = createMaterialTopTabNavigator();

const ShopTabs = ({navigation}) => {
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);

  return (
    <>
      <ShopTab.Navigator
        swipeEnabled={false}
        tabBarOptions={{
          labelStyle: {
            textTransform: 'capitalize',
            fontSize: 13,
            fontWeight: '400',
            paddingHorizontal: wp('1.5%'),
          },
          tabStyle: {
            width: 'auto',
          },
          indicatorStyle: {
            backgroundColor: '#fff',
            marginBottom: -2,
          },
          activeTintColor: '#fff',
          inactiveTintColor: '#8C8B8F',
          pressOpacity: 1,
          style: {
            backgroundColor: '#111111',
            borderBottomWidth: 2,
            borderBottomColor: '#8C8B8F',
            elevation: 0,
          },
          allowFontScaling: false,
        }}
      >
        {showHideFeatures?.data?.DROPS && (
          <ShopStack.Screen
            name="ShopDrops"
            component={ShopDrops}
            options={{
              headerShown: false,
              tabBarLabel: t('shop.try.headline.member_shop.drops'),
            }}
          />
        )}
        {showHideFeatures?.data?.TTB && (
          <ShopStack.Screen
            name="ShopTTB"
            component={ShopTTB}
            options={{
              headerShown: false,
              tabBarLabel: t('common.try_then_buy'),
            }}
          />
        )}
        {showHideFeatures?.data?.TOURTRASH && (
          <ShopStack.Screen
            name="ShopTourTrash"
            component={ShopTourTrash}
            options={{
              headerShown: false,
              tabBarLabel: t(
                'shop.available_drops.headline.member_shop.tour_trash',
              ),
            }}
          />
        )}
        {showHideFeatures?.data?.PERKS && (
          <ShopStack.Screen
            name="MemberBenefit"
            component={MemberBenefit}
            options={{
              headerShown: false,
              tabBarLabel: t('shop.benefit.headline.member_benefit'),
            }}
          />
        )}
      </ShopTab.Navigator>
    </>
  );
};

const ShopNavigator = ({
  navigation: {navigate},
  setDeepLink,
  updatePermissions,
  updateCurrentBasket,
}) => {
  const deepLink = useSelector(state => state.app?.deepLink);
  const isFocused = useIsFocused();
  const basket = useSelector(state => state.basket);

  useEffect(() => {
    // Navigate to shop tab depending on deeplink
    if (deepLink?.length) {
      setTimeout(() => {
        switch (deepLink) {
          case 'tour-trash':
            navigate('ShopTabs', {screen: 'ShopTourTrash'});
            setDeepLink('');
            break;
          case 'ttb':
            navigate('ShopTTB');
            setDeepLink('');
            break;
          case 'shopDrops':
            navigate('ShopDrops');
            setDeepLink('');
            break;
          case 'perks':
            navigate('MemberBenefit');
            setDeepLink('');
            break;
          default:
            break;
        }
      }, 500);
    }
  }, [deepLink]);

  useEffect(() => {
    getCurrentPermissions();
    getCurrentBasket();
  }, [isFocused]);

  const getCurrentPermissions = async () => {
    try {
      // Make request to get user data
      const userPermissions = await getPermissions();
      updatePermissions({
        myTMSubscriptionLevel: userPermissions?.myTMSubscriptionLevel,
        myTMPermission: userPermissions?.myTMPermission,
        subscriptionService: userPermissions?.subscriptionService,
        isTrialSubscription: userPermissions?.isTrialSubscription,
      });
    } catch (error) {
      throw error;
    }
  };

  const getCurrentBasket = async () => {
    try {
      // Get current sfcc basket
      let currentBasket = await getBasket();
      // Create new basket if it doesn't exist
      if (!currentBasket?.total) {
        // Set logging event for cart abandoment if redux basket had items compared to sfcc basket
        if (basket?.product_items?.length) {
          await analytics().logEvent('shop_cart_abandonment');
        }
        currentBasket = await createBasket();
        // Update basket in redux
        updateCurrentBasket(currentBasket);
      } else {
        updateCurrentBasket(currentBasket?.baskets[0]);
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: t('shopTabs.fore_right'),
        subText: t('shopTabs.toast_subText'),
      });
    }
  };

  return (
    <ShopStack.Navigator>
      <ShopStack.Screen
        name="ShopTabs"
        component={ShopTabs}
        options={{
          header: () => (
            <SafeAreaView edges={['top']} style={{backgroundColor: '#111111'}}>
              <View style={[appStyles.pHSm, appStyles.pTMd, appStyles.pBSm]}>
                <Header
                  title={'shop.tour.headline.member_shop'}
                  titleColor={{color: '#FFFFFF'}}
                  navigate={navigate}
                  isShop
                  titleStyle={{
                    marginTop: Platform.OS === 'android' ? 0 : hp('1.1%'),
                  }}
                  profileBG={{backgroundColor: '#BDBDBD'}}
                  profileTextColor={{color: '#111111'}}
                />
              </View>
            </SafeAreaView>
          ),
        }}
      />
    </ShopStack.Navigator>
  );
};

const mapDispatchToProps = {
  setDeepLink,
  updatePermissions,
  updateCurrentBasket,
};

export default connect(null, mapDispatchToProps)(ShopNavigator);
