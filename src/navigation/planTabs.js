import React, {useEffect, useRef, useState} from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import {View, ActivityIndicator, AppState, Platform} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {moderateScale} from 'react-native-size-matters';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector, useDispatch} from 'react-redux';
import {useIsFocused, useFocusEffect} from '@react-navigation/native';

import PlanRoadMap from 'screens/CoachRoadMap/PlanRoadMap';
import PlanStarted from 'screens/MyCoach/PlanStarted';
import PlanGoals from 'screens/MyCoach/PlanGoals';

import Header from 'components/Header';

import {getPermissions} from 'requests/accounts';
import {setDeepLink, updatePermissions} from 'reducers/app';

import appStyles from 'styles/global';
import {getRoadMap} from 'utils/plans';
import {t} from 'i18next';
import {
  getPlayerInstructor,
  getPlayerProfile,
  getSwingCredentials,
  registerSwingPlayer,
} from 'requests/swing-index';
import {updatePlanCoach} from 'reducers/plans';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CoachQuickFix from 'screens/CoachQuickFix/CoachQuickFix';
import useAppState from 'hooks/useAppState';
import {isAlreadyHasData} from 'utils/nativeCoach';

const PlanStack = createStackNavigator();
const PlanTab = createMaterialTopTabNavigator();

const PlanTabs = () => {
  const [isLoading, setIsLoading] = useState(false);
  const profileImageUrl = useSelector(
    state => state?.plans?.planCoach?.profileImageUrl,
  );
  const roadMap = useSelector(state => state.plans.planSwingRoadMap);
  const siUserProfile = useSelector(state => state.plans.siUserProfile);
  const permissions = useSelector(state => state?.app?.permissions);
  const dispatch = useDispatch();
  const isFocused = useIsFocused();

  const refreshRoadMap = async () => {
    try {
      if (
        permissions?.myTMPermission?.canVirtualCoaching ||
        isAlreadyHasData(siUserProfile?.desiredHandicap)
      ) {
        if (siUserProfile?.desiredHandicap == null) {
          setIsLoading(true);
        }
        await getPlayerProfile(dispatch);
        await getRoadMap(dispatch);
        setIsLoading(false);
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isFocused) {
      refreshRoadMap();
    }
  }, [isFocused]);

  useAppState({
    onForeground: () => {
      getCoachProfile();
      refreshRoadMap();
    },
  });

  const getCoachProfile = async () => {
    if (
      permissions?.myTMPermission?.canVirtualCoaching ||
      isAlreadyHasData(siUserProfile?.desiredHandicap)
    ) {
      try {
        if (!profileImageUrl) {
          const profile = await getPlayerInstructor();
          dispatch(updatePlanCoach(profile));
          if (!profile) {
            await registerSwingPlayer();
            await getSwingCredentials();
            const profileData = await getPlayerInstructor();
            dispatch(updatePlanCoach(profileData));
          }
        }
      } catch (error) {}
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      getCoachProfile();
    }, [profileImageUrl]),
  );

  if (isLoading) {
    return (
      <View style={[appStyles.overlay, appStyles.center, {zIndex: 1}]}>
        <View
          style={[
            appStyles.pSm,
            appStyles.blackBg,
            appStyles.borderRadius,
            {bottom: '10%'},
          ]}
        >
          <ActivityIndicator color="white" />
        </View>
      </View>
    );
  }

  return (
    <>
      {/* {loadingCoachingPlan ? (
        <View style={[appStyles.overlay, appStyles.center, {zIndex: 1}]}>
          <View
            style={[
              appStyles.pSm,
              appStyles.blackBg,
              appStyles.borderRadius,
              {bottom: '10%'},
            ]}
          >
            <ActivityIndicator color="white" />
          </View>
        </View>
      ) : null} */}
      <PlanTab.Navigator
        swipeEnabled={false}
        tabBarOptions={{
          labelStyle: {
            textTransform: 'capitalize',
            fontSize: moderateScale(13),
            paddingHorizontal: wp('1.5%'),
          },
          tabStyle: {
            width: 'auto',
          },
          indicatorStyle: {
            backgroundColor: '#fff',
            marginBottom: -2,
          },
          activeTintColor: '#FFFFFF',
          inactiveTintColor: '#BDBDBD',
          pressOpacity: 1,
          style: {
            backgroundColor: '#3E4D54',
            borderBottomWidth: 2,
            borderBottomColor: '#BDBDBD',
            elevation: 0,
          },
          allowFontScaling: false,
        }}
      >
        {!isAlreadyHasData(siUserProfile?.desiredHandicap) ? (
          <PlanStack.Screen
            name="PlanStarted"
            component={PlanStarted}
            options={({}) => ({
              headerShown: false,
              tabBarLabel: t('Get_Started'),
            })}
          />
        ) : (
          <>
            <PlanStack.Screen
              name="PlanGoals"
              component={PlanGoals}
              options={{
                headerShown: false,
                tabBarLabel: t('native_coach_goals'),
              }}
            />
            {roadMap.roadmapStats ? (
              <PlanStack.Screen
                name="PlanRoadMap"
                component={PlanRoadMap}
                options={{
                  headerShown: false,
                  tabBarLabel: t('Roadmap'),
                }}
              />
            ) : null}

            <PlanStack.Screen
              name="CoachQuickFix"
              component={CoachQuickFix}
              options={{
                headerShown: false,
                tabBarLabel: t('coach_single_lesson'),
              }}
            />
          </>
        )}
      </PlanTab.Navigator>
    </>
  );
};

const PlanNavigator = ({
  navigation: {navigate},
  updatePermissions,
  setDeepLink,
}) => {
  const deepLink = useSelector(state => state.app?.deepLink);
  const isFocused = useIsFocused();
  const siUserProfile = useSelector(state => state.plans.siUserProfile);

  useEffect(() => {
    // Navigate to plan tab depending on deeplink
    if (deepLink?.length) {
      setTimeout(() => {
        switch (deepLink) {
          case 'swing-index':
            navigate('NativeCoachHorizontal', {screen: 'PlanSwingProfile'});
            setDeepLink('');
            break;
          case 'road-map':
            navigate('PlanRoadMap');
            setDeepLink('');
            break;
          case 'quick-fix':
            navigate('CoachQuickFix');
            setDeepLink('');
            break;
          case 'putting':
            navigate('NativeCoachHorizontal', {screen: 'PutterImprove'});
            setDeepLink('');
            break;
          default:
            break;
        }
      }, 500);
    }
  }, [deepLink]);

  useEffect(() => {
    getCurrentPermissions();
  }, [isFocused]);

  const getCurrentPermissions = async () => {
    try {
      // Make request to get user data
      const userPermissions = await getPermissions();
      updatePermissions({
        myTMSubscriptionLevel: userPermissions?.myTMSubscriptionLevel,
        myTMPermission: userPermissions?.myTMPermission,
        subscriptionService: userPermissions?.subscriptionService,
        isTrialSubscription: userPermissions?.isTrialSubscription,
      });
    } catch (error) {
      throw error;
    }
  };

  return (
    <PlanStack.Navigator>
      <PlanStack.Screen
        name="PlanTabs"
        component={PlanTabs}
        options={{
          header: () => {
            return (
              <>
                {/* {loadingCoachingPlan ? (
                    <View style={[appStyles.overlay, {zIndex: 1}]} />
                  ) : null} */}
                <SafeAreaView
                  edges={['top']}
                  style={{backgroundColor: '#3E4D54'}}
                >
                  <View
                    style={[
                      appStyles.pHSm,
                      appStyles.pTMd,
                      appStyles.pBSm,
                      {backgroundColor: '#3E4D54'},
                    ]}
                  >
                    <Header
                      title={'plan.coaching'}
                      titleColor={appStyles.white}
                      navigate={navigate}
                      haveLogo={false}
                      titleStyle={{
                        marginTop: Platform.OS === 'android' ? 0 : hp('1.1%'),
                        marginLeft: 0,
                      }}
                    />
                  </View>
                </SafeAreaView>
              </>
            );
          },
        }}
      />
    </PlanStack.Navigator>
  );
};

const mapDispatchToProps = {
  setDeepLink,
  updatePermissions,
};

export default connect(null, mapDispatchToProps)(PlanNavigator);
