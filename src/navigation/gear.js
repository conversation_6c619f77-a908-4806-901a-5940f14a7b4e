import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import GearDeepDives from 'screens/Clubhouse/components/Entertainment&Drills/GearDeepDives';

const GearStack = createStackNavigator();

export default ({navigation}) => (
  <GearStack.Navigator>
    <GearStack.Screen
      name="GearDeepDives"
      component={GearDeepDives}
      options={({route}) => ({
        headerShown: false,
        gestureEnabled: true,
      })}
    />
  </GearStack.Navigator>
);
