import React, {useEffect} from 'react';
import {
  TouchableOpacity,
  View,
  Linking,
  Platform,
  StyleSheet,
} from 'react-native';
import {
  CardStyleInterpolators,
  createStackNavigator,
} from '@react-navigation/stack';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';
import AsyncStorage from '@react-native-async-storage/async-storage';

import BackButton from '../components/BackButton';

import Icon from 'react-native-vector-icons/Ionicons';
import {horizontalScreenAnimation} from 'utils/animations';
import appStyles from 'styles/global';
import {useDispatch, useSelector} from 'react-redux';
import {ARCCOS} from 'utils/constant';
import {t} from 'i18next';
import {ENVIRONMENTS} from 'config/env';
import ApproachStats from 'screens/Stats/components/ApproachStats/ApproachStatsClassic';
import ScoreDetail from 'screens/Scores/ScoreDetail';
import ScoreAddEditDetails from 'screens/Scores/ScoreAddEditDetails';
import {setDeepLink} from 'reducers/app';
import StrokesGainedBaseline from 'screens/Stats/components/StrokesGainedBaseline';
import FilterOptions from 'screens/Stats/components/StatsHomeHeader/FilterOptions';
import Text from 'components/Text';
import DrivingStatsTabScreen from 'screens/Stats/components/DrivingStats/DrivingStatsTab';
import ApproachStatsTabScreen from 'screens/Stats/components/ApproachStats/ApproachStatsTab';
import ShortStatsTabScreen from 'screens/Stats/components/ShortsStats/ShortStatsTab';
import PuttingStats from 'screens/Stats/components/PuttingStats/PuttingStats';
import CourseMap from 'screens/PlayCourseMap/CourseMap';
import ContactFromDevice from 'screens/Mutilplayer/ContactFromDevice';
import RoundDetailMultiplayer from 'screens/Mutilplayer/RoundDetailMultiplayer';
import AddPlayer from 'screens/Mutilplayer/AddPlayer';
import RecentPlayer from 'screens/PlayTrackRound/components/RecentPlayer';
import TrackRound from 'screens/PlayTrackRound/TrackRound';
import FindACourse from 'screens/PlayFindACourse/FindACourse';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import MenuPlayRound from 'screens/PlayCourseMap/MenuPlayRound';
import {moderateScale} from 'react-native-size-matters';
import ScoreAddCourse from 'screens/Scores/ScoreAddCourse';
import GhinConfirmCourse from 'screens/Scores/components/GhinConfirmCourse';
import GhinConfirmTee from 'screens/Scores/components/GhinConfirmTee';
import TextComponent from 'components/Text/Text';
import RoundOverview from 'screens/Scores/RoundOverview';

const ScoresStats = createStackNavigator();

const isTablet = DeviceInfo.isTablet();

const navigateToOnCourse = async (playService, route) => {
  const env = await AsyncStorage.getItem('env');
  Linking.openURL(
    playService === ARCCOS
      ? `arccosgolf://applink/stats?roundId=${route.params?.roundInfo?.id}`
      : env === ENVIRONMENTS.PROD
      ? 'https://v28z.app.link'
      : 'https://v28z.test-app.link',
  );
};

const renderEditScore = (route, playService, navigate) => {
  return (
    <TouchableOpacity
      style={[appStyles.pRSm, {paddingRight: wp('5%')}]}
      onPress={() => {
        if (route.params?.roundInfo?.roundMode === 'Simple') {
          navigate('ScoreAddEditDetails', {
            scoreType: 'edit',
            roundInfo: route.params?.roundInfo,
          });
        } else {
          navigateToOnCourse(playService, route);
        }
      }}
    >
      <Text
        style={[
          appStyles.black,
          isTablet ? appStyles.xs : '',
          {fontWeight: '600'},
        ]}
      >
        play.complete_round.edit
      </Text>
    </TouchableOpacity>
  );
};

export default ({navigation: {navigate, goBack}}) => {
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );
  const deepLink = useSelector(state => state.app?.deepLink);
  const dispatch = useDispatch();
  const insets = useSafeAreaInsets();

  useEffect(() => {
    // Navigate to shop tab depending on deeplink
    if (deepLink?.length) {
      setTimeout(() => {
        switch (deepLink) {
          case 'stats':
            navigate('Stats');
            dispatch(setDeepLink(''));
            break;
          // eslint-disable-next-line no-fallthrough
          default:
            break;
        }
      }, 500);
    }
  }, [deepLink]);

  return (
    <ScoresStats.Navigator screenOptions={horizontalScreenAnimation}>
      <ScoresStats.Screen
        name="ScoreDetail"
        component={ScoreDetail}
        options={({route, navigation}) => ({
          headerShown: false,
          // headerTitleStyle: [appStyles.sm, appStyles.alignCenter],
          // headerTitle: t('score.scores_stats'),
          // headerStyle: {shadowColor: 'transparent'},
          headerLeft: () => (
            <BackButton color="white" onPress={() => navigation.goBack()} />
          ),
          headerRight: () => renderEditScore(route, playService, navigate),
        })}
      />
      <ScoresStats.Screen
        name="RoundOverview"
        component={RoundOverview}
        options={({route, navigation}) => ({
          headerShown: false,
        })}
      />
      <ScoresStats.Screen
        name="ScoreAddCourse"
        component={ScoreAddCourse}
        options={({route, navigation}) => ({
          headerTitleStyle: [
            {fontWeight: '400', fontSize: 17, marginBottom: 11},
          ],
          headerRightContainerStyle: {},
          headerTitleAlign: 'center',
          headerTitleContainerStyle: {alignSelf: 'flex-end'},
          headerTitle: '',
          headerStyle: {
            shadowColor: 'transparent',
            height: 60 + (Platform.OS === 'ios' ? insets.top : 0),
          },
          headerLeft: () => (
            <TextComponent
              Din79Font
              black
              size={22}
              style={{fontWeight: '800', marginLeft: 16}}
            >
              {t('play.find_a_course').toLocaleUpperCase()}
            </TextComponent>
          ),
          headerRight: () => (
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 40,
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 5,
              }}
            >
              <Icon
                name={'close-outline'}
                color={'#000'}
                size={isTablet ? wp('3%') : wp('7%')}
              />
            </TouchableOpacity>
          ),
          headerShown: false
        })}
      />
      <ScoresStats.Screen
        name="ScoreAddEditDetails"
        component={ScoreAddEditDetails}
        options={({route}) => ({
          headerShown: false,
        })}
      />
      <ScoresStats.Screen
        name="ApproachStats"
        component={ApproachStats}
        options={({route, navigation}) => ({
          headerTitleStyle: [appStyles.sm, appStyles.alignCenter],
          headerTitle: t('scores.stats.approach_stat'),
          headerStyle: {shadowColor: 'transparent'},
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
        })}
      />

      <ScoresStats.Screen
        name="StrokesGainedBaseline"
        component={StrokesGainedBaseline}
        options={({route, navigation}) => ({
          headerTitleStyle: [appStyles.sm, appStyles.alignCenter],
          headerTitle: t('scores.stats.title.strokes_gained_baseline'),
          headerStyle: {shadowColor: 'transparent'},
          headerTitleAlign: 'center',
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
        })}
      />
      <ScoresStats.Screen
        name="FilterOptions"
        component={FilterOptions}
        options={({route, navigation}) => ({
          headerTitleStyle: [appStyles.sm, appStyles.alignCenter],
          headerTitle: 'Filter Options',
          headerStyle: {shadowColor: 'transparent'},
          headerTitleAlign: 'center',
          cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS,
          headerLeft: () => (
            <BackButton
              closeIcon
              color="black"
              onPress={() => navigation.goBack()}
            />
          ),
        })}
      />
      <ScoresStats.Screen
        name="DrivingStats"
        component={DrivingStatsTabScreen}
        options={({route, navigation}) => ({
          headerTitle: () => (
            <Text style={{fontSize: 17, fontWeight: '400'}}>
              {t('stats.header_label.driving')}
            </Text>
          ),
          headerStyle: {shadowColor: 'transparent'},
          headerTitleAlign: 'center',
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
          headerLeftContainerStyle: {
            height: '100%',
            justifyContent: 'flex-end',
            paddingBottom: 6,
          },
          headerTitleContainerStyle: {
            height: '100%',
            justifyContent: 'flex-end',
            paddingBottom: Platform.OS === 'ios' ? 10 : 5,
          },
        })}
      />
      <ScoresStats.Screen
        name="ApproachStatsOverall"
        component={ApproachStatsTabScreen}
        options={({route, navigation}) => ({
          headerTitleStyle: [
            appStyles.sm,
            appStyles.alignCenter,
            {fontWeight: '400'},
          ],
          headerTitle: t('stats.header_label.approach'),
          headerStyle: {shadowColor: 'transparent'},
          headerTitleAlign: 'center',
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
        })}
      />
      <ScoresStats.Screen
        name="ShortStatsOverall"
        component={ShortStatsTabScreen}
        options={({route, navigation}) => ({
          headerTitleStyle: [
            appStyles.sm,
            appStyles.alignCenter,
            {fontWeight: '400'},
          ],
          headerTitle: t('stats.header_label.short'),
          headerStyle: {shadowColor: 'transparent'},
          headerTitleAlign: 'center',
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
        })}
      />
      <ScoresStats.Screen
        name="PuttingStats"
        component={PuttingStats}
        options={({route, navigation}) => ({
          headerTitleStyle: [
            appStyles.sm,
            appStyles.alignCenter,
            {fontWeight: '400'},
          ],
          headerTitle: t('stats.header_label.putting'),
          headerStyle: {shadowColor: 'transparent'},
          headerTitleAlign: 'center',
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
        })}
      />
      <ScoresStats.Screen
        name="CourseMap"
        component={CourseMap}
        options={({route, navigation}) => ({
          headerShown: false,
          gestureEnabled: false,
        })}
      />
      <ScoresStats.Screen
        name="MenuPlayRound"
        component={MenuPlayRound}
        options={({route, navigation}) => ({
          headerShown: false,
          gestureEnabled: false,
        })}
      />
      <ScoresStats.Screen
        name="RoundDetailMultiplayer"
        component={RoundDetailMultiplayer}
        options={({route, navigation}) => ({
          headerTitleStyle: [appStyles.sm, appStyles.alignCenter],
          headerTitle: 'Round Details',
          headerStyle: {shadowColor: 'transparent'},
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
        })}
      />
      <ScoresStats.Screen
        name="ContactFromDevice"
        component={ContactFromDevice}
        options={({route, navigation}) => ({
          headerTitleStyle: [appStyles.sm, appStyles.alignCenter],
          headerTitle: 'Contact from device',
          headerStyle: {shadowColor: 'transparent'},
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
        })}
      />
      <ScoresStats.Screen
        name="AddPlayer"
        component={AddPlayer}
        options={({route, navigation}) => ({
          headerTitleStyle: [appStyles.sm, appStyles.alignCenter],
          headerTitle: 'Add Player',
          headerStyle: {shadowColor: 'transparent'},
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
        })}
      />
      <ScoresStats.Screen
        name="RecentPlayer"
        component={RecentPlayer}
        options={({route, navigation}) => ({
          headerTitleStyle: [
            appStyles.alignCenter,
            {fontSize: 17, fontWeight: '400'},
          ],
          headerTitle: 'Recent Players',
          headerStyle: {shadowColor: 'transparent'},
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
          headerTitleAlign: 'center',
        })}
      />
      <ScoresStats.Screen
        name="TrackRound"
        component={TrackRound}
        options={({route, navigation}) => ({
          headerTitleStyle: [
            appStyles.alignCenter,
            {fontSize: 17, fontWeight: '400'},
          ],
          headerTitle: 'Track Round',
          headerStyle: {shadowColor: 'transparent'},
          headerTitleAlign: 'center',
          headerLeft: () => (
            <BackButton color="black" onPress={() => navigation.goBack()} />
          ),
        })}
      />
      <ScoresStats.Screen
        name="FindACourse"
        component={FindACourse}
        options={({route, navigation}) => ({
          headerTitleStyle: [
            {fontWeight: '400', fontSize: 17, marginBottom: 11},
          ],
          headerRightContainerStyle: {},
          headerTitleAlign: 'center',
          headerTitleContainerStyle: {alignSelf: 'flex-end'},
          headerTitle: '',
          headerStyle: {
            shadowColor: 'transparent',
            height: 60 + (Platform.OS === 'ios' ? insets.top : 0),
          },
          headerLeft: () => (
            <TextComponent
              Din79Font
              black
              size={22}
              style={{fontWeight: '800', marginLeft: 16}}
            >
              {t('play.find_a_course').toLocaleUpperCase()}
            </TextComponent>
          ),
          headerRight: () => (
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 40,
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 5,
              }}
            >
              <Icon
                name={'close-outline'}
                color={'#000'}
                size={isTablet ? wp('3%') : wp('7%')}
              />
            </TouchableOpacity>
          ),
        })}
      />
      <ScoresStats.Screen
        name="GhinConfirmCourse"
        component={GhinConfirmCourse}
        options={({route, navigation}) => ({
          headerTitleStyle: [
            appStyles.alignCenter,
            {fontSize: 17, fontWeight: '400'},
          ],
          headerTitle: t('ghin.score.confirm.headline'),
          headerStyle: {shadowColor: 'transparent'},
          headerRight: () => (
            <BackButton
              color="black"
              closeIcon
              onPress={() => navigation.goBack()}
              style={{marginRight: 10}}
            />
          ),
          headerLeft: () => <View />,
          headerTitleAlign: 'center',
        })}
      />
      <ScoresStats.Screen
        name="GhinConfirmTee"
        component={GhinConfirmTee}
        options={({route, navigation}) => ({
          headerTitleStyle: [
            appStyles.alignCenter,
            {fontSize: 17, fontWeight: '400'},
          ],
          headerTitle: t('ghin.score.confirm.headline'),
          headerStyle: {shadowColor: 'transparent'},
          headerRight: () => (
            <BackButton
              color="black"
              closeIcon
              onPress={() => navigation.goBack()}
              style={{marginRight: 10}}
            />
          ),
          headerLeft: () => <View />,
          headerTitleAlign: 'center',
        })}
      />
    </ScoresStats.Navigator>
  );
};
