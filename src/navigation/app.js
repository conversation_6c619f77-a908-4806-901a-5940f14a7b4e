import React, {useEffect, useRef, useState} from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createStackNavigator} from '@react-navigation/stack';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import {View, AppState, Platform} from 'react-native';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';
import analytics from '@react-native-firebase/analytics';
import {moderateScale} from 'react-native-size-matters';

import Header from 'components/Header';
import BackButton from 'components/BackButton';

import Home from 'screens/Home/Home';
import Play from 'screens/Play/Play';
import PlanTabs from './planTabs';

import DrillsForYou from 'screens/Drills/DrillsForYou';
import DrillsBrowse from 'screens/Drills/DrillsBrowse';
import DrillsSaved from 'screens/Drills/DrillsSaved';
import DrillsCollections from 'screens/Drills/DrillsCollections';
import PlanAnnotations from 'screens/MyCoach/PlanAnnotations';

import {
  clearFocusScreen,
  updateFocusTab,
  updatePermissions,
} from 'reducers/app';
import {updateCurrentBasket} from 'reducers/basket';
import {getPermissions} from 'requests/accounts';
import {getBasket, createBasket} from 'requests/ecom';

import HomeIcon from 'assets/imgs/icon-home.svg';
import PlayIcon from 'assets/imgs/icon-play.svg';
import ShopIcon from 'assets/imgs/icon-shop.svg';

import HomeIconActive from 'assets/imgs/icon-home-active.svg';
import PlayIconActive from 'assets/imgs/icon-play-active.svg';

import appStyles from 'styles/global';
import {sortPurchases, subscribePurchase} from 'utils/iap';
import {t} from 'i18next';
import {isOtherPayment} from 'utils/user';
import {
  checkMainCountry,
  GA_EVENT_NAME,
  NOT_APPLICABLE,
  PAGE_CATEGORY,
  SCREEN_TYPES,
  TAB_NAME,
} from 'utils/constant';
import {canSubscribeToIAP} from 'requests/payments';
import {refreshConfigureFeatures} from 'utils/configureFeatures';
import ShopMain from 'screens/Shop/ShopMain';
import Rewards from 'screens/Rewards/Rewards';
import ClubHouse from 'screens/Clubhouse/Clubhouse';
import BottomTabCustomize from 'components/BottomTabCustom/BottomTabCustomize';
import {GA_logEvent} from 'utils/googleAnalytics';

const Tab = createBottomTabNavigator();
const HomeStack = createStackNavigator();
const PlayStack = createStackNavigator();
const ShopStack = createStackNavigator();
const RewardsStack = createStackNavigator();

// Define default styles for each screen
const screenOptions = {
  headerTintColor: '#fff',
  headerTitleStyle: {
    fontSize: 25,
    alignSelf: 'center',
  },
  headerShown: false,
};

const TabBar = props => <BottomTabCustomize {...props} />;

const HomeScreen = () => (
  <HomeStack.Navigator screenOptions={screenOptions}>
    <HomeStack.Screen name="Home" component={Home} />
  </HomeStack.Navigator>
);

const PlayScreen = ({navigation}) => (
  <PlayStack.Navigator screenOptions={screenOptions}>
    <PlayStack.Screen name="Play" component={Play} />
  </PlayStack.Navigator>
);

const ShopTabScreen = () => (
  <ShopStack.Navigator screenOptions={screenOptions}>
    <ShopStack.Screen name="ShopTabScreen" component={ShopMain} />
  </ShopStack.Navigator>
);

const RewardsScreen = ({navigation}) => (
  <RewardsStack.Navigator screenOptions={screenOptions}>
    <RewardsStack.Screen name="Rewards" component={Rewards} />
  </RewardsStack.Navigator>
);

const App = () => {
  const dispatch = useDispatch();
  const appState = useRef(AppState.currentState);
  const basket = useSelector(state => state.basket);
  const user = useSelector(state => state.user);

  const [focusShop, setFocusShop] = useState(false);
  const [focusHome, setFocusHome] = useState(false);
  const [focusPlay, setFocusPlay] = useState(false);
  const [focusReward, setfocusReward] = useState(false);
  const [focusClubHouse, setFocusClubHouse] = useState(false);
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
  const userCountry = user?.userCountry;

  const isHideShopTab =
    !checkMainCountry(userCountry) ||
    (showHideFeatures?.data &&
      !showHideFeatures?.data?.DROPS &&
      !showHideFeatures?.data?.TTB &&
      !showHideFeatures?.data?.TOURTRASH &&
      !showHideFeatures?.data?.PERKS);

  const isHideRewards = !checkMainCountry(userCountry);

  useEffect(() => {
    dispatch(clearFocusScreen());
    return () => {
      // End iap connection
    };
  }, []);

  const getCurrentPermissions = async () => {
    try {
      // Make request to get user data
      const userPermissions = await getPermissions();
      dispatch(
        updatePermissions({
          myTMSubscriptionLevel: userPermissions?.myTMSubscriptionLevel,
          myTMPermission: userPermissions?.myTMPermission,
          subscriptionService: userPermissions?.subscriptionService,
          isTrialSubscription: userPermissions?.isTrialSubscription,
        }),
      );
    } catch (error) {
      throw error;
    }
  };

  const getCurrentBasket = async () => {
    try {
      // Get current sfcc basket
      let currentBasket = await getBasket();
      // Create new basket if it doesn't exist
      if (!currentBasket?.total) {
        // Set logging event for cart abandoment if redux basket had items compared to sfcc basket
        if (basket?.product_items?.length) {
          await analytics().logEvent('shop_cart_abandonment');
        }
        currentBasket = await createBasket();
        // Update basket in redux
        dispatch(updateCurrentBasket(currentBasket));
      } else {
        dispatch(updateCurrentBasket(currentBasket?.baskets[0]));
      }
    } catch (error) {
      throw error;
    }
  };

  useEffect(() => {
    const appStateHandler = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      appStateHandler?.remove();
    };
  }, []);

  const handleAppStateChange = nextAppState => {
    // Check auth0 refresh token expiration, permissions, basket, and iap if user is coming back into the app
    if (appState.current.match(/background/) && nextAppState === 'active') {
      getCurrentPermissions();
      getCurrentBasket();
    }

    appState.current = nextAppState;
  };

  const GA_bottomTabNavigationClick = async tabName => {
    let screenType = NOT_APPLICABLE;
    let pageCategory = NOT_APPLICABLE;
    let pageName = NOT_APPLICABLE;

    switch (tabName) {
      case 'home':
        screenType = SCREEN_TYPES.HOME;
        pageCategory = PAGE_CATEGORY.HOME;
        break;
      case 'clubhouse':
        screenType = SCREEN_TYPES.CLUBHOUSE;
        pageCategory = PAGE_CATEGORY.CLUBHOUSE;
        break;
      case 'play':
        screenType = SCREEN_TYPES.PLAY;
        pageCategory = PAGE_CATEGORY.PLAY;
        break;
      case 'shop':
        screenType = SCREEN_TYPES.SHOP;
        pageCategory = PAGE_CATEGORY.SHOP;
        break;
      case 'rewards':
        screenType = SCREEN_TYPES.REWARDS;
        pageCategory = PAGE_CATEGORY.REWARDS;
        break;
      default:
        break;
    }
    try {
      GA_logEvent(GA_EVENT_NAME.NAV_CLICK, {
        screen_type: screenType,
        page_name: tabName,
        page_type: screenType,
        page_category: pageCategory,
        nav_type: 'navigation tabs',
        nav_item_selected: tabName,
        nav_level: 'footer',
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  const getTabs = () => {
    const tabs = [
      {
        name: 'Home',
        component: HomeScreen,
        options: {
          tabBarLabel: t('app.tab_label.home'),
          tabBarIcon: ({color, focused}) => {
            if (focused) {
              return <HomeIconActive />;
            } else {
              return <HomeIcon color={color} />;
            }
          },
        },
        listeners: {
          focus: () => setFocusHome(true),
          blur: () => setFocusHome(false),
          tabPress: () => {
            refreshConfigureFeatures(dispatch, user, showHideFeatures);
            GA_bottomTabNavigationClick('home');
          },
        },
      },
    ];

    // Add tab ClubHouse and Play by order and condition
    if (checkMainCountry(userCountry)) {
      // // If is main country, the order is: Home -> ClubHouse -> Play
      tabs.push({
        name: 'ClubHouse',
        component: ClubHouse,
        options: {
          tabBarLabel: t('app.tab_label.clubHouse'),
          tabBarIcon: ({color, focused}) => {
            if (focused) {
              return <PlayIconActive />;
            } else {
              return <PlayIcon color={color} />;
            }
          },
        },
        listeners: {
          focus: () => setFocusClubHouse(true),
          blur: () => setFocusClubHouse(false),
          tabPress: () => {
            refreshConfigureFeatures(dispatch, user, showHideFeatures);
            GA_bottomTabNavigationClick('clubhouse');
          },
        },
      });

      tabs.push({
        name: 'Play',
        component: PlayScreen,
        options: {
          tabBarLabel: t('app.tab_label.play'),
          tabBarIcon: ({color, focused}) => {
            if (focused) {
              return <PlayIconActive />;
            } else {
              return <PlayIcon color={color} />;
            }
          },
        },
        listeners: {
          focus: () => setFocusPlay(true),
          blur: () => setFocusPlay(false),
          tabPress: () => {
            refreshConfigureFeatures(dispatch, user, showHideFeatures);
            GA_bottomTabNavigationClick('play');
          },
        },
      });
    } else {
      // If is not main country, the order is: Home -> Play -> ClubHouse
      tabs.push({
        name: 'Play',
        component: PlayScreen,
        options: {
          tabBarLabel: t('app.tab_label.play'),
          tabBarIcon: ({color, focused}) => {
            if (focused) {
              return <PlayIconActive />;
            } else {
              return <PlayIcon color={color} />;
            }
          },
        },
        listeners: {
          focus: () => setFocusPlay(true),
          blur: () => setFocusPlay(false),
          tabPress: () => {
            refreshConfigureFeatures(dispatch, user, showHideFeatures);
            GA_bottomTabNavigationClick('play');
          },
        },
      });

      tabs.push({
        name: 'ClubHouse',
        component: ClubHouse,
        options: {
          tabBarLabel: t('app.tab_label.clubHouse'),
          tabBarIcon: ({color, focused}) => {
            if (focused) {
              return <PlayIconActive />;
            } else {
              return <PlayIcon color={color} />;
            }
          },
        },
        listeners: {
          focus: () => setFocusClubHouse(true),
          blur: () => setFocusClubHouse(false),
          tabPress: () => {
            refreshConfigureFeatures(dispatch, user, showHideFeatures);
            GA_bottomTabNavigationClick('clubhouse');
          },
        },
      });
    }

    // add tab Shop if needed
    if (!isHideShopTab) {
      tabs.push({
        name: 'ShopTabScreen',
        component: ShopTabScreen,
        options: {
          tabBarLabel: t('app.tab_label.shop'),
          tabBarIcon: ({color, focused}) => {
            if (focused) {
              return <ShopIcon fill={color} />;
            } else {
              return <ShopIcon />;
            }
          },
        },
        listeners: {
          focus: () => setFocusShop(true),
          blur: () => setFocusShop(false),
          tabPress: () => {
            refreshConfigureFeatures(dispatch, user, showHideFeatures);
            GA_bottomTabNavigationClick('shop');
          },
        },
      });
    }

    // Add tab Rewards if needed
    if (!isHideRewards) {
      tabs.push({
        name: 'Rewards',
        component: RewardsScreen,
        options: {
          tabBarLabel: t('app.tab_label.rewards'),
          tabBarIcon: ({color, focused}) => {
            if (focused) {
              return <PlayIconActive />;
            } else {
              return <PlayIcon color={color} />;
            }
          },
        },
        listeners: {
          focus: () => setfocusReward(true),
          blur: () => setfocusReward(false),
          tabPress: () => {
            refreshConfigureFeatures(dispatch, user, showHideFeatures);
            GA_bottomTabNavigationClick('rewards');
          },
        },
      });
    }

    return tabs;
  };

  const tabsToRender = getTabs();

  return (
    <Tab.Navigator tabBar={TabBar} initialRouteName="Home">
      {tabsToRender.map(tab => (
        <Tab.Screen
          key={tab.name}
          name={tab.name}
          component={tab.component}
          options={tab.options}
          listeners={({route}) => tab.listeners}
        />
      ))}
    </Tab.Navigator>
  );
};

export default App;
