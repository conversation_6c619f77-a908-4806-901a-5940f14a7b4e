import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import DrillsByCategory from 'screens/Clubhouse/DrillsByCategory';
import CoachDetail from 'screens/Clubhouse/CoachDetail';
import CollectionDetail from 'screens/Clubhouse/components/Entertainment&Drills/CollectionDetail';

const DrillsStack = createStackNavigator();

export default ({navigation}) => (
  <DrillsStack.Navigator>
    <DrillsStack.Screen
      name="DrillsByCategory"
      component={DrillsByCategory}
      options={({route}) => ({
        headerShown: false,
        gestureEnabled: true,
      })}
    />
    <DrillsStack.Screen
      name="CoachDetail"
      component={CoachDetail}
      options={{
        headerShown: false,
        gestureEnabled: false,
      }}
    />
    <DrillsStack.Screen
      name="CollectionDetail"
      component={CollectionDetail}
      options={({route}) => ({
        headerShown: false,
        gestureEnabled: true,
      })}
    />
  </DrillsStack.Navigator>
);
