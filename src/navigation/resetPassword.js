import React from 'react';
import {View} from 'react-native';
import {createStackNavigator} from '@react-navigation/stack';

import Logo from '../components/Logo';
import BackButton from '../components/BackButton';
import ResetPassword from '../screens/Profile/ResetPassword';
import ShippingAddress from 'screens/Profile/ShippingAddress';

const ResetPasswordStack = createStackNavigator();

export default ({navigation}) => (
  <ResetPasswordStack.Navigator
    screenOptions={{
      cardStyle: {
        backgroundColor: 'black',
      },
      headerTitle: () => <Logo style={{alignSelf: 'center'}} />,
      headerStyle: {
        backgroundColor: 'black',
        shadowColor: 'transparent',
      },
      headerTitleStyle: {
        alignSelf: 'center',
      },
    }}
  >
    <ResetPasswordStack.Screen
      name="ShippingAddress"
      component={ShippingAddress}
      options={{
        headerLeft: () => <View />,
        headerRight: () => (
          <BackButton
            style={{marginRight: 20}}
            closeIcon={true}
            onPress={() => navigation.goBack()}
          />
        ),
      }}
    />
    <ResetPasswordStack.Screen
      name="ResetPassword"
      component={ResetPassword}
      options={{
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
        headerRight: () => <View />,
      }}
    />
  </ResetPasswordStack.Navigator>
);
