import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {View} from 'react-native';

import Logo from 'components/Logo';
import BackButton from 'components/BackButton';
import QuizWelcome from 'screens/QuizOnboarding/QuizWelcome';
import QuizName from 'screens/QuizOnboarding/QuizName';
import QuizGender from 'screens/QuizOnboarding/QuizGender';
import QuizBirthday from 'screens/QuizOnboarding/QuizBirthday';
import QuizHeight from 'screens/QuizOnboarding/QuizHeight';
import QuizHanded from 'screens/QuizOnboarding/QuizHanded';
import QuizYearsOfExperience from 'screens/QuizOnboarding/QuizYearsOfExperience';
import EditQuizYearsOfExperience from 'screens/QuizOnboarding/EditQuizYearsOfExperience';
import QuizRounds from 'screens/QuizOnboarding/QuizRounds';
import QuizHandicap from 'screens/QuizOnboarding/QuizHandicap';
import EditQuizHandicap from 'screens/QuizOnboarding/EditQuizHandicap';
import QuizScoreTarget from 'screens/QuizOnboarding/QuizScoreTarget';
import QuizDriveLength from 'screens/QuizOnboarding/QuizDriveLength';
import QuizStrength from 'screens/QuizOnboarding/QuizStrength';
import QuizWeakness from 'screens/QuizOnboarding/QuizWeakness';
import QuizShotShape from 'screens/QuizOnboarding/QuizShotShape';
import QuizBallStrike from 'screens/QuizOnboarding/QuizBallStrike';
import QuizBallMiss from 'screens/QuizOnboarding/QuizBallMiss';
import QuizAvoid from 'screens/QuizOnboarding/QuizAvoid';
import QuizFear from 'screens/QuizOnboarding/QuizFear';
import QuizGolfCourse from 'screens/QuizOnboarding/QuizGolfCourse';
import QuizPro from 'screens/QuizOnboarding/QuizPro';
import QuizMoreInsights from 'screens/QuizOnboarding/QuizMoreInsights';
import QuizTargetHandicapOrAvg from 'screens/QuizOnboarding/QuizTargetHandicapOrAvg';
import QuizCommonMisHit from 'screens/QuizOnboarding/QuizCommonMisHit';
import QuizPuttingMisHit from 'screens/QuizOnboarding/QuizPuttingMisHit';
import QuizStrokesGained from 'screens/QuizOnboarding/QuizStrokesGained';
import QuizRegion from 'screens/QuizOnboarding/QuizRegion';

const QuizStack = createStackNavigator();

export default ({navigation}) => {
  const {navigate, replace} = navigation;
  return (
    <QuizStack.Navigator
      screenOptions={{
        cardOverlayEnabled: true,
        cardOverlay: () => (
          <View
            style={{
              flex: 1,
              backgroundColor: 'rgba(0,0,0,0.7)',
            }}
          />
        ),
        cardStyle: {
          backgroundColor: 'black',
        },
        headerTitle: () => <Logo style={{alignSelf: 'center'}} />,
        headerStyle: {
          backgroundColor: 'black',
          shadowColor: 'transparent',
        },
        headerTitleStyle: {
          alignSelf: 'center',
        },
      }}
    >
      <QuizStack.Screen
        name="QuizWelcome"
        component={QuizWelcome}
        options={{
          headerShown: false,
        }}
      />
      <QuizStack.Screen
        name="QuizRegion"
        component={QuizRegion}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizWelcome')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizName"
        component={QuizName}
        options={({route, navigation}) => ({
          headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
          headerRight: () => <View />,
        })}
      />
      <QuizStack.Screen
        name="QuizGender"
        component={QuizGender}
        options={{
          headerLeft: () => <BackButton onPress={() => navigate('QuizName')} />,
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizBirthday"
        component={QuizBirthday}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizGender')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizHeight"
        component={QuizHeight}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizBirthday')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizHanded"
        component={QuizHanded}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizMoreInsights')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizMoreInsights"
        component={QuizMoreInsights}
        options={{
          headerLeft: () => <BackButton onPress={() => navigate('Home')} />,
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizYearsOfExperience"
        component={QuizYearsOfExperience}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizGolfCourse')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizRounds"
        component={QuizRounds}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizYearsOfExperience')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizHandicap"
        component={QuizHandicap}
        options={({route}) => ({
          headerLeft: () => (
            <BackButton color="white" onPress={() => navigation.goBack()} />
          ),
        })}
      />
      <QuizStack.Screen
        name="QuizScoreTarget"
        component={QuizScoreTarget}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizHandicap')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizDriveLength"
        component={QuizDriveLength}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizHanded')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizStrength"
        component={QuizStrength}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizDriveLength')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizWeakness"
        component={QuizWeakness}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizStrength')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizShotShape"
        component={QuizShotShape}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizWeakness')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizBallStrike"
        component={QuizBallStrike}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizShotShape')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizBallMiss"
        component={QuizBallMiss}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizBallStrike')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizAvoid"
        component={QuizAvoid}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizBallMiss')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizFear"
        component={QuizFear}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizWeakness')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="QuizGolfCourse"
        component={QuizGolfCourse}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizBirthday')} />
          ),
        }}
      />
      <QuizStack.Screen
        name="QuizPro"
        component={QuizPro}
        options={{
          headerLeft: () => (
            <BackButton onPress={() => navigate('QuizGolfCourse')} />
          ),
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen
        name="EditQuizYearsOfExperience"
        component={EditQuizYearsOfExperience}
        options={{
          headerRight: () => <View />,
        }}
      />
      <QuizStack.Screen name="EditQuizHandicap" component={EditQuizHandicap} />
      {/* use for Native Coach */}
      <QuizStack.Screen
        name="QuizTargetHandicapOrAvg"
        component={QuizTargetHandicapOrAvg}
        options={({route}) => ({
          headerLeft: () => (
            <BackButton
              color="white"
              onPress={() => navigation.goBack()}
              navigationText={route.params?.lessonTitle}
            />
          ),
        })}
      />
      <QuizStack.Screen
        name="QuizCommonMisHit"
        component={QuizCommonMisHit}
        options={({route}) => ({
          headerLeft: () => (
            <BackButton
              color="white"
              onPress={() => navigation.goBack()}
              navigationText={route.params?.lessonTitle}
            />
          ),
        })}
      />
      <QuizStack.Screen
        name="QuizPuttingMisHit"
        component={QuizPuttingMisHit}
        options={({route}) => ({
          headerLeft: () => (
            <BackButton
              color="white"
              onPress={() => navigation.goBack()}
              navigationText={route.params?.lessonTitle}
            />
          ),
        })}
      />
      <QuizStack.Screen
        name="QuizStrokesGained"
        component={QuizStrokesGained}
        options={({route}) => ({
          headerLeft: () => (
            <BackButton color="white" onPress={() => navigation.goBack()} />
          ),
        })}
      />
    </QuizStack.Navigator>
  );
};
