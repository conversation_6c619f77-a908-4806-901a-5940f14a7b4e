import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {t} from 'i18next';
import PlanSwingProfile from 'screens/MyCoach/PlanSwingProfile';
import PutterImprove from 'screens/PutterImprove/PutterImprove';
import BackButton from 'components/BackButton';
import appStyles from 'styles/global';
import LessonShortGameDetail from 'screens/PlanRelevantLessons/LessonShortGameDetail';
import SwingMaintenanceDetail from 'screens/CoachQuickFix/SwingMaintenanceDetail';

const NativeCoachHorizontalStack = createStackNavigator();

export default ({navigation: {navigate, goBack}}) => {
  return (
    <NativeCoachHorizontalStack.Navigator>
      <NativeCoachHorizontalStack.Screen
        name="PlanSwingProfile"
        component={PlanSwingProfile}
        options={{
          headerShown: true,
          headerTitle: t('swing_index_detail'),
          headerStyle: {
            backgroundColor: '#3A4E55',
            shadowColor: 'transparent',
          },
          headerTitleStyle: [
            appStyles.sm,
            appStyles.alignCenter,
            appStyles.white,
          ],
        }}
      />
      <NativeCoachHorizontalStack.Screen
        name="PutterImprove"
        component={PutterImprove}
        options={{
          headerShown: true,
          headerTitle: t('putter_improve_title'),
          headerStyle: {
            backgroundColor: '#3A4E55',
            shadowColor: 'transparent',
          },
          headerTitleStyle: [
            appStyles.sm,
            appStyles.alignCenter,
            appStyles.white,
          ],
        }}
      />
      <NativeCoachHorizontalStack.Screen
        name="LessonShortGameDetail"
        component={LessonShortGameDetail}
        options={{
          headerShown: true,
          headerTitle: null,
          headerStyle: {
            backgroundColor: '#3A4E55',
            shadowColor: 'transparent',
          },
          headerTitleStyle: [
            appStyles.sm,
            appStyles.alignCenter,
            appStyles.white,
          ],
        }}
      />
      <NativeCoachHorizontalStack.Screen
        name="SwingMaintenanceDetail"
        component={SwingMaintenanceDetail}
        options={{
          headerShown: true,
          headerTitle: null,
          headerStyle: {
            backgroundColor: '#3A4E55',
            shadowColor: 'transparent',
          },
          headerTitleStyle: [
            appStyles.sm,
            appStyles.alignCenter,
            appStyles.white,
          ],
        }}
      />
    </NativeCoachHorizontalStack.Navigator>
  );
};
