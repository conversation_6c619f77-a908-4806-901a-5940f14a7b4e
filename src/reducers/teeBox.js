import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  status: '',
  selectedCourseId: '',
  teeBoxPageRequests: {},
  proTipLoader: false,
  teeListLoader: false,
  courseGpsVectorLoader: false,
  scoreDetailsLoader: false,
  awsElevationLoader: false,
  courseElevationDataDetailsError: {},
  proTipMessageListError: {},
  gpsVectorDetailsError: {},
  scoreCardDetailsError: {},
  teeBoxDetailsFailure: {},
  elevationAwsRequest: {},
  teeBoxDetailsSuccess: {},
  scoreCardDetails: {},
  gpsVectorDetails: {},
  proTipMessageList: {},
  elevationAwsValue: {},
};

const TeeBoxSlice = createSlice({
  name: 'teeBox',
  initialState,
  reducers: {
    setSelectedCourseId: (state, action) => {
      state.selectedCourseId = action.payload;
      state.status = action.type;
    },
    getTeeBoxPageRequests: (state, {payload, type}) => {
      state.teeBoxPageRequests = payload;
      state.proTipLoader = true;
      state.teeListLoader = true;
      state.courseGpsVectorLoader = true;
      state.scoreDetailsLoader = true;
    },
    setTeeBoxDetails: (state, {payload, type}) => {
      state.teeBoxDetailsSuccess = payload;
      state.status = type;
      state.teeListLoader = false;
    },
    setScoreCardDetails: (state, {payload, type}) => {
      state.scoreCardDetails = payload;
      state.status = type;
      state.scoreDetailsLoader = false;
    },

    getGpsVectorDetails: (state, {payload, type}) => {
      state.gpsVectorDetails = payload;
      state.status = type;
      state.courseGpsVectorLoader = false;
    },
    getProTipMessageList: (state, {payload, type}) => {
      state.proTipMessageList = payload;
      state.status = type;
      state.proTipLoader = false;
    },
    setElevationAwsRequest: (state, {payload, type}) => {
      state.elevationAwsValue = payload;
      state.status = type;

      state.awsElevationLoader = false;
    },

    setElevationAwsRequestError: (state, {payload, type}) => {
      state.status = type;
      state.awsElevationLoader = false;
    },
    getCourseElevationDataDetailsError: (state, {payload, type}) => {
      state.courseElevationDataDetailsError = payload;
      state.status = type;
    },
    getProTipMessageListError: (state, {payload, type}) => {
      state.proTipMessageListError = payload;
      state.status = type;
      state.proTipLoader = false;
    },
    getGpsVectorDetailsError: (state, {payload, type}) => {
      state.gpsVectorDetailsError = payload;
      state.status = type;
      state.courseGpsVectorLoader = false;
    },
    setScoreCardDetailsError: (state, {payload, type}) => {
      state.scoreCardDetailsError = payload;
      state.status = type;
      state.scoreDetailsLoader = false;
    },
    setTeeBoxDetailsError: (state, {payload, type}) => {
      state.teeBoxDetailsFailure = payload;
      state.status = type;
      state.teeListLoader = false;
    },

    getElevationAwsRequest: (state, {payload, type}) => {
      state.elevationAwsRequest = payload;
      state.status = type;
      state.awsElevationLoader = true;
    },
  },
});

export const {
  setSelectedCourseId,
  getTeeBoxPageRequests,
  setElevationAwsRequestError,
  getCourseElevationDataDetailsError,
  getProTipMessageListError,
  getGpsVectorDetailsError,
  setScoreCardDetailsError,
  setTeeBoxDetailsError,
  getElevationAwsRequest,
  setTeeBoxDetails,
  setScoreCardDetails,
  getProTipMessageList,
  setElevationAwsRequest,
  getGpsVectorDetails,
} = TeeBoxSlice.actions;

export default TeeBoxSlice.reducer;
