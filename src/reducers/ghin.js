import {createSlice} from '@reduxjs/toolkit';

const ghinSlice = createSlice({
  name: 'ghin',
  initialState: {},
  reducers: {
    clearGHINHandicapIndex() {
      return {};
    },
    updateGHINHandicapIndex(state, action) {
      const payload = {...state, ...action.payload};
      return payload;
    },
  },
});

export const {clearGHINHandicapIndex, updateGHINHandicapIndex} =
  ghinSlice.actions;

export default ghinSlice.reducer;
