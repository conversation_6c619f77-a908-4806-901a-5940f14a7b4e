import {createSlice} from '@reduxjs/toolkit';
import {combineReducers} from 'redux';

const ordersSlice = createSlice({
  name: 'orders',
  initialState: [],
  reducers: {
    clearTTBOrders() {
      return [];
    },
    updateTTBOrders(state, action) {
      return action.payload;
    },
  },
});

export const {clearTTBOrders, updateTTBOrders} = ordersSlice.actions;

export default combineReducers({
  orders: ordersSlice.reducer,
});
