import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  country: null,
  features: [],
};
const appCacheVersionSlice = createSlice({
  name: 'appCacheVersions',
  initialState,
  reducers: {
    updateCacheVersionData(state, action) {
      return action.payload;
    },
    resetCacheVersions() {
      return initialState;
    },
  },
});

export const {updateCacheVersionData, resetCacheVersions} =
  appCacheVersionSlice.actions;

export default appCacheVersionSlice.reducer;
