import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
  Image,
} from 'react-native';
import Video from 'react-native-video';
import appStyles from '../../../styles/global';
import {widthPercentageToDP} from 'react-native-responsive-screen';
import Icon from 'react-native-vector-icons/FontAwesome5';
import {convertToMinutesAndSeconds} from 'utils/constant';
import HiddenVideoPlayer from 'components/HiddenVideoPlayer';
const VideoInformation = ({data, showDuration = false}) => {
  const ref = useRef(null);
  const [end, setEnd] = useState(false);
  const [loadingVideo, setLoadingVideo] = useState(true);
  const hiddenVideoRef = useRef(null);

  const handleTapVideo = () => {
    // if (Platform.OS === 'ios') {
    //   ref?.current?.presentFullscreenPlayer();
    // }
    hiddenVideoRef?.current?.play();
  };

  useEffect(() => {
    if (end) {
      ref?.current?.seek(0.01);
      setEnd(false);
    }
  }, [end]);

  const onEnd = () => {
    setEnd(true);
  };

  const onLoad = () => {
    ref?.current?.seek(0.01);
    setLoadingVideo(false);
  };

  return (
    <TouchableOpacity
      style={[appStyles.full, loadingVideo ? {backgroundColor: 'gray'} : {}]}
      onPress={handleTapVideo}
    >
      {loadingVideo ? (
        <View
          style={[
            appStyles.hCenter,
            appStyles.vCenter,
            appStyles.absoluteFill,
            {backgroundColor: 'gray'},
          ]}
        >
          <ActivityIndicator color="white" />
        </View>
      ) : null}
      <Video
        onLoad={onLoad}
        source={{uri: data?.uri}} // Can be a URL or a local file.
        ref={ref} // Store reference
        style={appStyles.full}
        fullscreen={true}
        resizeMode={'cover'}
        onEnd={onEnd}
      />
      <HiddenVideoPlayer ref={hiddenVideoRef} videoUri={data?.uri} />
      <Image
        source={{uri: data?.thumbnailUrl}}
        style={{width: '100%', height: '100%', position: 'absolute'}}
      />

      {!loadingVideo && (
        <View
          style={[appStyles.hCenter, appStyles.vCenter, appStyles.absoluteFill]}
        >
          <View style={[appStyles.hCenter, appStyles.vCenter, styles.boxIcon]}>
            <Icon name="play" size={widthPercentageToDP('4%')} color="white" />
          </View>
        </View>
      )}

      {!loadingVideo && showDuration && (
        <View style={styles.boxTime}>
          <Text style={styles.time}>
            {data?.duration
              ? convertToMinutesAndSeconds(data?.duration)
              : '00:00'}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  boxIcon: {
    width: widthPercentageToDP('8%'),
    height: widthPercentageToDP('8%'),
    borderColor: 'white',
    borderWidth: 3,
    borderRadius: widthPercentageToDP('8%'),
    paddingLeft: 2,
  },
  boxTime: {
    backgroundColor: '#ffffff80',
    borderRadius: 5,
    position: 'absolute',
    bottom: 10,
    right: 7,
  },
  time: {
    fontSize: 10,
    color: 'white',
    zIndex: 1,
    paddingVertical: 2,
    paddingHorizontal: 7,
  },
});

export default VideoInformation;
