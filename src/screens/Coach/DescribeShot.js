import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Text,
  Alert,
  Platform,
  Image,
  BackHandler,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import PlanCoachMessage from '../../components/PlanCoachMessage';
import appStyles from '../../styles/global';
import {widthPercentageToDP} from 'react-native-responsive-screen';
import Button from '../../components/Button';
import {GREY, LINK_BLUE} from '../../config';
import HeaderRightButton from '../../components/HeaderRightButton';
import checkLocationPermission from '../../utils/checkLocationPermission';
import Geolocation from 'react-native-geolocation-service';
import {
  optionTypeClubsOther,
  optionTypeClubScramble,
  optionShotTypes,
  optionBallRemember,
  optionDetailFairwayWood,
  optionBallNotRemember,
  optionDetailHybrid,
  optionDetailIron,
  optionDetailWedge,
  optionMisHitShotsWedge,
  optionMisHitShotsOther,
  COACH_SWING_SHOT_TYPE,
  COACH_VIDEO_TYPE,
  FULL_SWING_SHOT_TYPE,
  maintainmentShotTypesOptions,
} from '../../json/describeShot';
import {t} from 'i18next';
import {uploadVideo, uploadVideoMetadata} from 'requests/swing-index';
import {
  golfClubType,
  shotContact,
  shotLocation,
  shotMistake,
  shotShape,
  shotTrajectory,
} from './DescribeShotData';
import CustomSelector from '../../components/Selector/CustomSelector';
import DeviceInfo from 'react-native-device-info';
import {convertToMinutesAndSeconds, NOT_APPLICABLE, SCREEN_TYPES} from 'utils/constant';
import {useSelector} from 'react-redux';
import PlayButton from '../../../assets/imgs/icon-play-button.svg';
import CongratsIcon from '../../../assets/imgs/congrats_icon.png';
import {
  getFirstBaselineData,
  setFirstBaselineData,
  setReloadScreen,
} from 'utils/commonVariable';
import {showToast} from 'utils/toast';
import AndroidNumberPicker from '../../components/AndroidNumberPicker/AndroidNumberPicker';
import PlanStore from 'screens/PlanRelevantLessons/store/PlanStore';
import HiddenVideoPlayer from 'components/HiddenVideoPlayer';
import {useFocusEffect} from '@react-navigation/native';
import CoachPopup from 'components/CoachPopup';
import AsyncStorage from '@react-native-async-storage/async-storage';

const DescribeShot = ({navigation, route}) => {
  const hasNotch = DeviceInfo.hasNotch();
  const {
    swingSubElementId,
    swingShotTypeId,
    videoTypeId,
    rejectSwingName,
    swingName,
    lessonType,
  } = route?.params;
  const planContentBatch = useSelector(state => state?.plans?.planContentBatch);
  const optionCourseRanges = planContentBatch?.shotLocation?.map(a => a.name);

  const video = route?.params?.dataRecord;
  const typeVideo = route?.params?.typeVideo;
  const [typeClub, setTypeClub] = useState(null);
  const [exactClub, setExactClub] = useState(null);
  const [courseRange, setCourseRange] = useState(null);
  const [message, setMessage] = useState('');
  const [selectedBall, setSelectedBall] = useState({});
  const [shotType, setShotType] = useState(null);
  const [misHitShot, setMisHitShot] = useState(null);
  const [autoFocusMessage, setAutoFocusMessage] = useState(false);
  const [uploading, setUploading] = useState(false);

  const [isVisible, setIsVisible] = useState(false);

  const typeClubRef = useRef(null);
  const courseRangeRef = useRef(null);
  const exactClubRef = useRef(null);
  const messageRef = useRef(null);
  const ballRef = useRef(null);
  const videoRef = useRef(null);
  const hiddenVideoRef = useRef(null);
  const shotTypeRef = useRef(null);
  const misHitShotRef = useRef(null);
  const [ignoreOnChange, setIgnoreOnChange] = useState(true);
  const [showFullScreen, setShowFullScreen] = useState(false);
  const [endVideo, setEndVideo] = useState(false);

  const [latitude, setLatitude] = useState(null);
  const [longitude, setLongitude] = useState(null);
  const [visibleTypeClubSelector, setVisibleTypeClubSelector] = useState(false);
  const [visibleExactSelector, setVisibleExactSelector] = useState(false);
  const [visibleShotTypeSelector, setVisibleShotTypeSelector] = useState(false);
  const [visibleMisHitSelector, setVisibleMisHitSelector] = useState(false);
  const [visibleCourseSelector, setVisibleCourseSelector] = useState(false);
  const [visibleModalUploaded, setVisibleModalUploaded] = useState(false);
  const [showReuploadSuccessfully, setShowReuploadSuccessfully] =
    useState(false);
  const [swingShotType, setSwingShotType] = useState(''); // use for alert
  const [showUploadSuccess, setShowUploadSuccess] = useState(false);
  const originScreen = route?.params?.originScreen;

  const optionMisHitShots =
    typeClub === 'Wedge' ? optionMisHitShotsWedge : optionMisHitShotsOther;

  const optionExactClubs =
    typeClub === 'Fairway Wood'
      ? Platform.OS === 'ios'
        ? optionDetailFairwayWood
        : optionDetailFairwayWood?.map(item => ({value: item}))
      : typeClub === 'Hybrid'
      ? Platform.OS === 'ios'
        ? optionDetailHybrid
        : optionDetailHybrid?.map(item => ({value: item}))
      : typeClub === 'Iron'
      ? Platform.OS === 'ios'
        ? optionDetailIron
        : optionDetailIron?.map(item => ({value: item}))
      : typeClub === 'Wedge'
      ? Platform.OS === 'ios'
        ? optionDetailWedge
        : optionDetailWedge?.map(item => ({value: item}))
      : [];

  const optionTypeClubs =
    swingShotTypeId === COACH_SWING_SHOT_TYPE.SINGLE_SHOT ||
    swingShotTypeId === COACH_SWING_SHOT_TYPE.SINGLE_SHOT_RETURNED
      ? optionTypeClubScramble
      : optionTypeClubsOther;

  const isRequireContract = !(
    typeVideo === 'selectVideo' &&
    swingShotTypeId === COACH_SWING_SHOT_TYPE.BASELINE
  );
  const optionBall = isRequireContract
    ? optionBallNotRemember
    : optionBallRemember;

  const isShowExactClub = typeClub !== null && typeClub !== 'Driver';

  const arrBall = Object.values(selectedBall);
  const remember = arrBall?.find(x => x.type === 'other');
  const optionOther = arrBall?.filter(x => x.type !== 'other');
  const valueShotShap = optionOther?.find(x => x.type === 'shotShape');
  const valueTrajectory = optionOther?.find(x => x.type === 'trajectory');
  const valueContact = optionOther?.find(x => x.type === 'contact');
  const textBall =
    arrBall?.length === 0
      ? t('describe_shot.what_happenbed_to_the_ball')
      : remember
      ? remember?.name
      : optionOther?.length === 3
      ? `${valueShotShap.name}, ${valueTrajectory.name}, ${valueContact.name} contact`
      : t('describe_shot.what_happenbed_to_the_ball');

  const showMisHit =
    Object.values(selectedBall).find(x => x.name === 'Poor') ||
    swingShotTypeId === COACH_SWING_SHOT_TYPE.SWING_MAINT ||
    swingShotTypeId === COACH_SWING_SHOT_TYPE.SWING_MAINT_RETURNED;

  const showShotType =
    swingShotTypeId === COACH_SWING_SHOT_TYPE.SINGLE_SHOT ||
    swingShotTypeId === COACH_SWING_SHOT_TYPE.SINGLE_SHOT_RETURNED ||
    (swingShotTypeId === COACH_SWING_SHOT_TYPE.SWING_MAINT &&
      typeClub === 'Iron') ||
    (swingShotTypeId === COACH_SWING_SHOT_TYPE.SWING_MAINT &&
      typeClub === 'Wedge')
      ? true
      : false;
  let shotTypeOptions = optionShotTypes;
  if (COACH_SWING_SHOT_TYPE.SWING_MAINT) {
    shotTypeOptions = maintainmentShotTypesOptions;
  }
  useEffect(() => {
    handleContext();
  }, []);

  useFocusEffect(
    //prevent back behavior from this screen
    React.useCallback(() => {
      const onBackPress = () => {
        return true;
      };
      BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );

  const handleVideo = () => {
    hiddenVideoRef?.current?.play();
  };

  useEffect(() => {
    if (swingShotTypeId === COACH_SWING_SHOT_TYPE.SINGLE_SHOT_RETURNED) {
      planContentBatch.scrambleShotType.forEach(item => {
        if (item.id === PlanStore.scrambleShotTypeId) {
          setShotType(item.name);
        }
      });
    }
  }, [swingShotTypeId]);

  // get localtion
  useEffect(() => {
    (async () => {
      if (checkLocationPermission) {
        Geolocation.getCurrentPosition(
          position => {
            setLatitude(position?.coords?.latitude);
            setLongitude(position?.coords?.longitude);
          },
          error => {
            // See error code charts below.
            console.log(error.code, error.message);
          },
          {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
        );
      }
    })();
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightButton
          isClose
          isLargeIcon={true}
          onPress={() => {
            setIsVisible(true);
          }}
        />
      ),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (endVideo) {
      videoRef?.current?.seek(0.01);
      setEndVideo(false);
    }
  }, [endVideo]);

  const handleContext = () => {
    switch (swingShotTypeId) {
      case COACH_SWING_SHOT_TYPE.BASELINE:
        setSwingShotType(t('upload_baseline'));
        break;

      case COACH_SWING_SHOT_TYPE.RETURNED:
        setSwingShotType(t('returned_Swing_Shot'));
        break;
      case COACH_SWING_SHOT_TYPE.SINGLE_SHOT:
      case COACH_SWING_SHOT_TYPE.SINGLE_SHOT_RETURNED:
        setSwingShotType(t('Scramble'));
        break;
      case COACH_SWING_SHOT_TYPE.SWING_MAINT:
      case COACH_SWING_SHOT_TYPE.SWING_MAINT_RETURNED:
        setSwingShotType(t('Swing_Maintenance'));
        break;
      default:
        break;
    }
  };

  const GA_Continue_Video_Setup = async () => {
    try {
      await analytics().logEvent('continue_video_setup', {
        lesson_type: lessonType, //e.g single lesson, full lesson plan, improve putting
        app_screen_name: 'coach - video upload description', //e.g home, my orders
        screen_type: SCREEN_TYPES.COACH, //e.g checkout, pdp, plp, account
        page_name: 'coach - video upload description', //e.g home, my orders
        page_type: SCREEN_TYPES.COACH, //e.g basket, home, order
        page_category: SCREEN_TYPES.COACH,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  const handleActionModal = () => {
    GA_Cancel_Video_Setup();
    setIsVisible(false);
    navigation.navigate('PlanTabs', {
      screen: 'planStarted',
    });
  };

  const GA_Cancel_Video_Setup = async () => {
    try {
      await analytics().logEvent('cancel_video_setup', {
        lesson_type: lessonType, //e.g single lesson, full lesson plan, improve putting
        app_screen_name: 'coach - video upload description', //e.g home, my orders
        screen_type: SCREEN_TYPES.COACH, //e.g checkout, pdp, plp, account
        page_name: 'coach - video upload description', //e.g home, my orders
        page_type: SCREEN_TYPES.COACH, //e.g basket, home, order
        page_category: SCREEN_TYPES.COACH,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  const onPressCancel = () => {
    GA_Continue_Video_Setup();
    setIsVisible(false);
  };

  const deleteMessage = () => {
    Alert.alert(t('describe_shot.are_you_sure_you_to_delete_message'), '', [
      {
        text: t('describe_shot.are_you_sure_you_to_delete_message.cancel'),
        onPress: () => {},
        style: 'cancel',
      },
      {
        text: t('describe_shot.are_you_sure_you_to_delete_message.yes'),
        onPress: () => setMessage(''),
      },
    ]);
  };

  const handleBallCancel = () => {
    ballRef?.current?.snapTo(1);
  };
  const onPressTypeClub = () => {
    if (Platform.OS === 'android') {
      setVisibleTypeClubSelector(true);
    }
    typeClubRef?.current?.snapTo(0);
    ballRef?.current?.snapTo(1);
    exactClubRef?.current?.snapTo(1);
    courseRangeRef?.current?.snapTo(1);
    messageRef?.current?.snapTo(1);
    shotTypeRef?.current?.snapTo(1);
    misHitShotRef?.current?.snapTo(1);
  };

  const onPressExact = () => {
    if (Platform.OS === 'android') {
      setVisibleExactSelector(true);
    }
    typeClubRef?.current?.snapTo(1);
    ballRef?.current?.snapTo(1);
    exactClubRef?.current?.snapTo(0);
    courseRangeRef?.current?.snapTo(1);
    messageRef?.current?.snapTo(1);
    shotTypeRef?.current?.snapTo(1);
    misHitShotRef?.current?.snapTo(1);
  };

  const onPressShotType = () => {
    if (Platform.OS === 'android') {
      setVisibleShotTypeSelector(true);
    }
    typeClubRef?.current?.snapTo(1);
    ballRef?.current?.snapTo(1);
    exactClubRef?.current?.snapTo(1);
    courseRangeRef?.current?.snapTo(1);
    messageRef?.current?.snapTo(1);
    shotTypeRef?.current?.snapTo(0);
    misHitShotRef?.current?.snapTo(1);
  };

  const onPressMisHitShot = () => {
    if (Platform.OS === 'android') {
      setVisibleMisHitSelector(true);
    }
    typeClubRef?.current?.snapTo(1);
    ballRef?.current?.snapTo(1);
    exactClubRef?.current?.snapTo(1);
    courseRangeRef?.current?.snapTo(1);
    messageRef?.current?.snapTo(1);
    shotTypeRef?.current?.snapTo(1);
    misHitShotRef?.current?.snapTo(0);
  };

  const onPressBall = () => {
    ballRef?.current?.snapTo(0);
    exactClubRef?.current?.snapTo(1);
    courseRangeRef?.current?.snapTo(1);
    messageRef?.current?.snapTo(1);
    typeClubRef?.current?.snapTo(1);
    shotTypeRef?.current?.snapTo(1);
    misHitShotRef?.current?.snapTo(1);
  };

  const onPressCourse = () => {
    if (Platform.OS === 'android') {
      setVisibleCourseSelector(true);
    }
    ballRef?.current?.snapTo(1);
    exactClubRef?.current?.snapTo(1);
    courseRangeRef?.current?.snapTo(0);
    messageRef?.current?.snapTo(1);
    typeClubRef?.current?.snapTo(1);
    shotTypeRef?.current?.snapTo(1);
    misHitShotRef?.current?.snapTo(1);
  };

  const onPressMessage = () => {
    setAutoFocusMessage(true);
    ballRef?.current?.snapTo(1);
    exactClubRef?.current?.snapTo(1);
    courseRangeRef?.current?.snapTo(1);
    messageRef?.current?.snapTo(0);
    typeClubRef?.current?.snapTo(1);
    shotTypeRef?.current?.snapTo(1);
    misHitShotRef?.current?.snapTo(1);
  };

  const onChangeTypeClub = value => {
    if (
      (!ignoreOnChange ||
        (ignoreOnChange && showFullScreen) ||
        (!ignoreOnChange && !showFullScreen) ||
        (!ignoreOnChange && showFullScreen) ||
        (ignoreOnChange && showFullScreen)) &&
      value !== typeClub
    ) {
      setExactClub(null);
      setMisHitShot(null);
      setTypeClub(value);
    }
  };

  const onChangeShotType = value => {
    if (
      !ignoreOnChange ||
      (ignoreOnChange && showFullScreen) ||
      (!ignoreOnChange && !showFullScreen) ||
      (!ignoreOnChange && showFullScreen) ||
      (ignoreOnChange && showFullScreen)
    ) {
      setShotType(value);
    }
  };

  const onChangeMisHitShot = value => {
    if (
      !ignoreOnChange ||
      (ignoreOnChange && showFullScreen) ||
      (!ignoreOnChange && !showFullScreen) ||
      (!ignoreOnChange && showFullScreen) ||
      (ignoreOnChange && showFullScreen)
    ) {
      setMisHitShot(value);
    }
  };

  const onChangeExactClub = value => {
    if (
      !ignoreOnChange ||
      (ignoreOnChange && showFullScreen) ||
      (!ignoreOnChange && !showFullScreen) ||
      (!ignoreOnChange && showFullScreen) ||
      (ignoreOnChange && showFullScreen)
    ) {
      setExactClub(value);
    }
  };

  const onChangeBall = value => {
    if (
      !ignoreOnChange ||
      (ignoreOnChange && showFullScreen) ||
      (!ignoreOnChange && !showFullScreen) ||
      (!ignoreOnChange && showFullScreen) ||
      (ignoreOnChange && showFullScreen)
    ) {
      setMisHitShot(null);
      setSelectedBall(value);
    }
  };

  const onChangeCourseRange = value => {
    if (
      !ignoreOnChange ||
      (ignoreOnChange && showFullScreen) ||
      (!ignoreOnChange && !showFullScreen) ||
      (!ignoreOnChange && showFullScreen) ||
      (ignoreOnChange && showFullScreen)
    ) {
      setCourseRange(value);
    }
  };

  // selector will call onChange when init (library error), so we need to ignore first call by firstSecond value
  useEffect(() => {
    setTimeout(() => {
      setIgnoreOnChange(false);
    }, 1000);
  }, []);

  const isNotCompleteAllField = () => {
    const notEnoughBallField =
      !selectedBall?.shotShape?.name ||
      !selectedBall?.trajectory?.name ||
      !selectedBall?.contact?.name;

    switch (swingShotTypeId) {
      case COACH_SWING_SHOT_TYPE.BASELINE:
      case COACH_SWING_SHOT_TYPE.RETURNED:
        if (
          !typeClub ||
          (isShowExactClub && !exactClub) ||
          (isRequireContract && notEnoughBallField) ||
          (!isRequireContract &&
            notEnoughBallField &&
            !selectedBall?.other?.name) ||
          (selectedBall?.contact?.name === 'Poor' && !misHitShot) ||
          !courseRange
        ) {
          return true;
        }
        return false;

      case COACH_SWING_SHOT_TYPE.SINGLE_SHOT:
        if (
          !typeClub ||
          !exactClub ||
          !shotType ||
          notEnoughBallField ||
          !misHitShot ||
          !courseRange
        ) {
          return true;
        }
        return false;
      case COACH_SWING_SHOT_TYPE.SINGLE_SHOT_RETURNED:
        if (
          !typeClub ||
          !exactClub ||
          !shotType ||
          notEnoughBallField ||
          (selectedBall?.contact?.name === 'Poor' && !misHitShot) ||
          !courseRange
        ) {
          return true;
        }
        return false;
      case COACH_SWING_SHOT_TYPE.SWING_MAINT:
      case COACH_SWING_SHOT_TYPE.SWING_MAINT_RETURNED:
        if (
          !typeClub ||
          (isShowExactClub && !exactClub) ||
          notEnoughBallField ||
          !misHitShot ||
          !courseRange
        ) {
          return true;
        }
        return false;
      default:
        return false;
    }
  };

  const GA_Complete_Video_Information = async () => {
    try {
      await analytics().logEvent('complete_video_information', {
        club_used: typeClub || NOT_APPLICABLE, //e.g driver ect
        shot_shape: valueShotShap.name || NOT_APPLICABLE, //e.g left, straight etc
        trajectory: valueTrajectory.name || NOT_APPLICABLE, //e.g high, mid etc
        strike: valueContact.name, //e.g solid, poor
        lesson_type: lessonType || NOT_APPLICABLE, //e.g single lesson, full lesson plan improve putting
        app_screen_name: 'coach - video upload description', //e.g home, my orders
        screen_type: SCREEN_TYPES.COACH, //e.g checkout, pdp, plp, account
        page_name: 'coach - video upload description', //e.g home, my orders
        page_type: SCREEN_TYPES.COACH, //e.g basket, home, order
        page_category: SCREEN_TYPES.COACH,
      });
      const dataCompletedVideo = {
        club_used: typeClub || NOT_APPLICABLE, //e.g driver ect
        shot_shape: valueShotShap.name || NOT_APPLICABLE, //e.g left, straight etc
        trajectory: valueTrajectory.name || NOT_APPLICABLE, //e.g high, mid etc
        strike: valueContact.name, //e.g solid, poor
        lesson_type: lessonType || NOT_APPLICABLE, //e
      };
      if (originScreen === 'Single Lesson Uploads') {
        await AsyncStorage.setItem(
          'dataCompletedVideo',
          JSON.stringify(dataCompletedVideo),
        );
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const handleDone = async () => {
    if (isNotCompleteAllField()) {
      Alert.alert('', t('describe_shot.please_complete'), [
        {
          text: t('select_video.alert.okay'),
          onPress: () => {},
          style: 'cancel',
        },
      ]);
      return;
    }
    try {
      let golfClubId = null;
      let shotShapeId = null;
      let shotTrajectoryId = null;
      let shotContactId = null;
      let shotLocationId = null;
      let shotMistakeId = null;
      let scrambleShotTypeId = null;

      let golfClubs = null;

      golfClubType.forEach(item => {
        if (item.name === typeClub) {
          golfClubs = item?.golfClubs;
        }
      });

      if (golfClubs && golfClubs.length > 0) {
        golfClubs.forEach(item => {
          if (item.name === exactClub) {
            golfClubId = item.id;
          }
        });
        if (golfClubs.length === 1) {
          golfClubId = golfClubs[0].id;
        }
      }

      shotShape.forEach(item => {
        if (item.name === selectedBall?.shotShape?.name) {
          shotShapeId = item.id;
        }
      });
      shotTrajectory.forEach(item => {
        if (item.name === selectedBall?.trajectory?.name) {
          shotTrajectoryId = item.id;
        }
      });
      shotContact.forEach(item => {
        if (item.name === selectedBall?.contact?.name) {
          shotContactId = item.id;
        }
      });
      shotLocation.forEach(item => {
        if (item.name === courseRange) {
          shotLocationId = item.id;
        }
      });
      shotMistake.forEach(item => {
        if (item.name === misHitShot) {
          shotMistakeId = item.id;
        }
      });
      let swingShotTypeIdUpdateByShotType = swingShotTypeId;
      if (shotType) {
        if (
          swingShotTypeId === COACH_SWING_SHOT_TYPE.SWING_MAINT &&
          shotType !== FULL_SWING_SHOT_TYPE
        ) {
          swingShotTypeIdUpdateByShotType = COACH_SWING_SHOT_TYPE.SINGLE_SHOT;
        }
        planContentBatch.scrambleShotType.forEach(item => {
          if (item.name === shotType) {
            scrambleShotTypeId = item.id;
          }
        });
      }

      const attributes = {
        golfClubId,
        shotShapeId,
        shotTrajectoryId,
        shotContactId,
        shotLocationId,
        message: message && message.length > 0 ? message : null,
        swingSubElementId: swingSubElementId || null,
        swingShotTypeId: swingShotTypeIdUpdateByShotType,
        videoTypeId: videoTypeId,
        shotMistakeId: shotMistakeId,
        locationLat: latitude,
        locationLong: longitude,
      };
      if (
        swingShotTypeIdUpdateByShotType === COACH_SWING_SHOT_TYPE.SINGLE_SHOT
      ) {
        attributes.scrambleShotTypeId = scrambleShotTypeId || null;
      }
      if (
        swingShotTypeIdUpdateByShotType ===
        COACH_SWING_SHOT_TYPE.SINGLE_SHOT_RETURNED
      ) {
        attributes.scrambleShotTypeId = scrambleShotTypeId || null;
        attributes.parentVideoId = PlanStore.shortGameParentVideoId;
      }
      GA_Complete_Video_Information();
      if (swingShotTypeIdUpdateByShotType === COACH_SWING_SHOT_TYPE.BASELINE) {
        handleUploadBaseline(attributes, video);
        return;
      }
      if (
        swingShotTypeIdUpdateByShotType ===
        COACH_SWING_SHOT_TYPE.SWING_MAINT_RETURNED
      ) {
        delete attributes.locationLat;
        delete attributes.locationLong;
        attributes.parentVideoId = PlanStore.swingMaintParentVideoId;
      }
      if (
        swingShotTypeIdUpdateByShotType === COACH_SWING_SHOT_TYPE.SWING_MAINT
      ) {
        delete attributes.locationLat;
        delete attributes.locationLong;
        delete attributes.swingSubElementId;
      }

      handleUploadOtherSwing(attributes);
    } catch (error) {
      showToast({
        type: 'error',
        message: t('Error'),
        subText: t('uploaded_error'),
      });
      setUploading(false);
    }
  };

  const GA_Upload_New_Swing = async () => {
    try {
      await analytics().logEvent('upload_new_swing', {
        lesson_type: 'full lesson plan', //e.g single lesson, full lesson plan improve putting
        app_screen_name: 'coach - video upload description', //e.g home, my orders
        screen_type: SCREEN_TYPES.COACH, //e.g checkout, pdp, plp, account
        page_name: 'coach - video upload description', //e.g home, my orders
        page_type: SCREEN_TYPES.COACH, //e.g basket, home, order
        page_category: SCREEN_TYPES.COACH,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  const handleUploadBaseline = async (attributes, recordVideo) => {
    if (videoTypeId === COACH_VIDEO_TYPE.faceOn) {
      setFirstBaselineData({
        attributes,
        recordVideo,
      });
      GA_Upload_New_Swing();
      navigation.navigate('NativeCoach', {
        screen: 'SelectVideoMenu',
        params: {
          swingShotTypeId: COACH_SWING_SHOT_TYPE.BASELINE,
          videoTypeId: COACH_VIDEO_TYPE.downTheLine,
          lessonType: 'full lesson plan',
        },
      });
    } else if (videoTypeId === COACH_VIDEO_TYPE.downTheLine) {
      try {
        const firstBaselineData = getFirstBaselineData();
        const attributes1 = firstBaselineData.attributes;
        const video1 = firstBaselineData.recordVideo;
        // upload 2 videos
        setUploading(true);
        const metadata1 = await uploadVideoMetadata(attributes1);
        const metadata2 = await uploadVideoMetadata(attributes);
        await uploadVideo(metadata1.playerSwingShotVideoId, video1);
        await uploadVideo(metadata2.playerSwingShotVideoId, recordVideo);
        setUploading(false);

        setVisibleModalUploaded(true);
        // navigation.setOptions({
        //   headerShown: false,
        // });
      } catch (error) {
        showToast({
          type: 'error',
          message: t('Error'),
          subText: t('uploaded_error'),
        });
        navigation.setOptions({
          headerShown: true,
        });
        setUploading(false);
      }
    }
  };

  const handleUploadOtherSwing = async attributes => {
    try {
      setUploading(true);
      const metadata = await uploadVideoMetadata(attributes);
      await uploadVideo(metadata.playerSwingShotVideoId, video);
      setUploading(false);
      setReloadScreen(true);
      if (rejectSwingName) {
        setShowReuploadSuccessfully(true);
      } else {
        setShowUploadSuccess(true);
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: t('Error'),
        subText: t('uploaded_error'),
      });
      setUploading(false);
    }
  };

  const onLoad = () => {
    videoRef?.current?.seek(0.1);
  };

  const handleCancel = () => {
    ballRef?.current?.snapTo(1);
    exactClubRef?.current?.snapTo(1);
    courseRangeRef?.current?.snapTo(1);
    messageRef?.current?.snapTo(1);
    typeClubRef?.current?.snapTo(1);
    shotTypeRef?.current?.snapTo(1);
    misHitShotRef?.current?.snapTo(1);
  };

  const onEnd = () => {
    setEndVideo(true);
  };

  const closeModalUploaded = () => {
    setVisibleModalUploaded(false);
    navigation.setOptions({
      headerShown: true,
    });
    navigation.navigate('PlanTabs', {
      screen: 'planStarted',
    });
  };

  const closeModalReUploaded = () => {
    setShowReuploadSuccessfully(false);
    navigation.setOptions({
      headerShown: true,
    });
    navigation.navigate('PlanTabs');
  };

  const closeModalUploadedSuccess = () => {
    setShowUploadSuccess(false);
    navigation.setOptions({
      headerShown: true,
    });
    navigation.navigate('PlanTabs');
  };

  const renderVideoView = () => (
    <View style={[appStyles.column, {height: 150}]}>
      <View style={[appStyles.fullWidth, styles.backgroundColorContainer]} />
      <View style={[appStyles.fullWidth, styles.heightBox]} />
      <View style={[appStyles.hCenter, appStyles.full, {position: 'absolute'}]}>
        <TouchableOpacity
          onPress={handleVideo}
          style={[appStyles.hCenter, appStyles.vCenter, styles.boxImage]}
        >
          {/* <Video
            onLoad={onLoad}
            source={{uri: video?.uri}} // Can be a URL or a local file.
            ref={videoRef} // Store reference
            style={[styles.backgroundVideo]}
            fullscreen={true}
            resizeMode={'cover'}
            onEnd={onEnd}
          /> */}
          <HiddenVideoPlayer ref={hiddenVideoRef} videoUri={video?.uri} />
          {video?.thumbnailUrl !== '' && (
            <View style={[appStyles.full, {position: 'absolute'}]}>
              <Image
                source={{uri: video?.thumbnailUrl}}
                style={[appStyles.full, {borderRadius: 8}]}
              />
            </View>
          )}
          <View
            style={[
              appStyles.absoluteFill,
              appStyles.hCenter,
              appStyles.vCenter,
            ]}
          >
            {
              <PlayButton
                width={widthPercentageToDP('10%')}
                height={widthPercentageToDP('10%')}
              />
            }
            <View style={styles.boxTime}>
              <Text style={styles.time}>
                {video?.duration
                  ? convertToMinutesAndSeconds(
                      typeVideo === 'recordVideo' && video?.duration >= 59
                        ? 60
                        : video?.duration,
                    )
                  : '00:00'}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <>
      <ScrollView style={appStyles.flex}>
        <PlanCoachMessage type="describeShot" swingIndex={1} />
        <View style={[appStyles.hCenter, styles.boxText]} />
        {renderVideoView()}

        <View style={appStyles.mHSm}>
          <Button
            textColor={typeClub ? 'black' : GREY}
            borderColor={GREY}
            backgroundColor={'white'}
            text={typeClub || t('describe_shot.type_of_club_you_used')}
            style={appStyles.mTSm}
            onPress={onPressTypeClub}
            rightIcon={'chevron-down'}
            textStyle={{fontSize: 13}}
            iconSize={13}
          />

          {isShowExactClub && (
            <Button
              textColor={exactClub ? 'black' : GREY}
              backgroundColor={'white'}
              borderColor={GREY}
              text={exactClub || t('describe_shot.exact_club_you_used')}
              style={appStyles.mTSm}
              onPress={onPressExact}
              rightIcon={'chevron-down'}
              textStyle={{fontSize: 13}}
              iconSize={13}
            />
          )}

          {showShotType && (
            <Button
              backgroundColor={
                swingShotTypeId !== COACH_SWING_SHOT_TYPE.SINGLE_SHOT_RETURNED
                  ? 'white'
                  : '#d7d6da'
              }
              textColor={shotType ? 'black' : GREY}
              borderColor={GREY}
              text={shotType || t('describe_shot.shot_type')}
              style={appStyles.mTSm}
              onPress={onPressShotType}
              rightIcon={
                swingShotTypeId !== COACH_SWING_SHOT_TYPE.SINGLE_SHOT_RETURNED
                  ? 'chevron-down'
                  : null
              }
              textStyle={{fontSize: 13}}
              disabled={
                swingShotTypeId === COACH_SWING_SHOT_TYPE.SINGLE_SHOT_RETURNED
              }
              iconSize={13}
            />
          )}

          <Button
            backgroundColor={'white'}
            textColor={remember || optionOther?.length === 3 ? 'black' : GREY}
            borderColor={GREY}
            text={textBall}
            style={appStyles.mTSm}
            onPress={onPressBall}
            rightIcon={'chevron-down'}
            textStyle={{fontSize: 13}}
            iconSize={13}
          />

          {(swingShotTypeId === COACH_SWING_SHOT_TYPE.SINGLE_SHOT ||
            (typeClub && showMisHit)) && (
            <Button
              textColor={misHitShot ? 'black' : GREY}
              backgroundColor={'white'}
              borderColor={GREY}
              text={
                misHitShot ||
                t('describe_shot.what_was_your_mis_hit_on_this_shot')
              }
              style={appStyles.mTSm}
              onPress={onPressMisHitShot}
              rightIcon={'chevron-down'}
              textStyle={{fontSize: 13}}
              iconSize={13}
            />
          )}

          <Button
            textColor={courseRange ? 'black' : GREY}
            backgroundColor={'white'}
            borderColor={GREY}
            text={courseRange || t('describe_shot.course_or_practice_range')}
            onPress={onPressCourse}
            style={appStyles.mTSm}
            textStyle={{fontSize: 13}}
            rightIcon={'chevron-down'}
            iconSize={13}
          />
          <Button
            textColor={message.length > 0 ? 'black' : GREY}
            backgroundColor={'white'}
            borderColor={GREY}
            text={message || t('describe_shot.option_message')}
            style={appStyles.mTSm}
            onPress={onPressMessage}
            rightIcon={null}
            textStyle={{fontSize: 13}}
            iconSize={13}
            numberOfLines={2}
          />
          <Button
            textStyle={{fontSize: 18}}
            textColor={'white'}
            text={
              swingShotTypeId === COACH_SWING_SHOT_TYPE.SINGLE_SHOT ||
              (swingShotTypeId === COACH_SWING_SHOT_TYPE.BASELINE &&
                videoTypeId === COACH_VIDEO_TYPE.downTheLine)
                ? t('describe_shot.upload_swing_shot')
                : swingShotTypeId === COACH_SWING_SHOT_TYPE.BASELINE &&
                  videoTypeId === COACH_VIDEO_TYPE.faceOn
                ? 'Done'
                : t('describe_shot.done')
            }
            centered
            DINbold
            borderColor={'black'}
            backgroundColor={'black'}
            style={appStyles.mVMd}
            onPress={handleDone}
            loading={uploading}
            disabled={uploading}
            loadingMode="dark"
          />
        </View>
      </ScrollView>

      <CoachPopup
        visible={isVisible}
        title="coach_modal.are_you_sure"
        description={t('describe_shot.are_you_sure_description', {
          swing_shot_type: swingShotType,
        })}
        button1="coach_modal.i_want_to_leave"
        button1Action={handleActionModal}
        button2="coach_modal.keep_me_here"
        button2Action={onPressCancel}
      />

      {Platform.OS === 'ios' ? (
        <CustomSelector
          ref={typeClubRef}
          type="text"
          value={typeClub}
          onChange={onChangeTypeClub}
          options={optionTypeClubs}
          styleDone={{color: LINK_BLUE, marginVertical: 10}}
          textCancel={t('describe_shot.cancel')}
          styleCancel={{
            color: LINK_BLUE,
            fontSize: 16,
            paddingVertical: 10,
          }}
          handleCancel={handleCancel}
          borderRadius={0}
        />
      ) : (
        <AndroidNumberPicker
          options={optionTypeClubs?.map(item => ({value: item}))}
          value={typeClub}
          setValue={onChangeTypeClub}
          visible={visibleTypeClubSelector}
          onClose={() => setVisibleTypeClubSelector(false)}
          title={t('describe_shot.type_of_club_you_used')}
        />
      )}

      {Platform.OS === 'ios' ? (
        <CustomSelector
          ref={exactClubRef}
          type="text"
          value={exactClub}
          onChange={onChangeExactClub}
          options={optionExactClubs}
          styleDone={{color: LINK_BLUE, marginVertical: 10}}
          snapPoints={'30%'}
          textCancel={t('describe_shot.cancel')}
          styleCancel={{
            color: LINK_BLUE,
            fontSize: 16,
            paddingVertical: 10,
          }}
          handleCancel={handleCancel}
          borderRadius={0}
        />
      ) : (
        <AndroidNumberPicker
          options={optionExactClubs}
          value={exactClub}
          setValue={onChangeExactClub}
          visible={visibleExactSelector}
          onClose={() => setVisibleExactSelector(false)}
          title={t('describe_shot.exact_club_you_used')}
        />
      )}

      {Platform.OS === 'ios' ? (
        <CustomSelector
          ref={shotTypeRef}
          type="text"
          value={shotType}
          onChange={onChangeShotType}
          options={shotTypeOptions}
          styleDone={{color: LINK_BLUE, marginVertical: 10}}
          textCancel={t('describe_shot.cancel')}
          borderRadius={0}
          styleCancel={{
            color: LINK_BLUE,
            fontSize: 16,
            paddingVertical: 10,
          }}
          handleCancel={handleCancel}
        />
      ) : (
        <AndroidNumberPicker
          options={shotTypeOptions?.map(item => ({value: item}))}
          value={shotType}
          setValue={onChangeShotType}
          visible={visibleShotTypeSelector}
          onClose={() => setVisibleShotTypeSelector(false)}
          title={t('describe_shot.shot_type')}
        />
      )}

      {Platform.OS === 'ios' ? (
        <CustomSelector
          ref={misHitShotRef}
          type="text"
          value={misHitShot}
          onChange={onChangeMisHitShot}
          options={optionMisHitShots}
          borderRadius={0}
          textCancel={t('describe_shot.cancel')}
          styleCancel={{
            color: LINK_BLUE,
            fontSize: 16,
            paddingVertical: 10,
          }}
          handleCancel={handleCancel}
          styleDone={{color: LINK_BLUE, marginVertical: 10}}
        />
      ) : (
        <AndroidNumberPicker
          options={optionMisHitShots?.map(item => ({value: item}))}
          value={misHitShot}
          setValue={onChangeMisHitShot}
          visible={visibleMisHitSelector}
          onClose={() => setVisibleMisHitSelector(false)}
          title={t('describe_shot.what_was_your_mis_hit_on_this_shot')}
        />
      )}

      <CustomSelector
        ref={ballRef}
        type="box"
        borderRadius={0}
        default={selectedBall}
        value={selectedBall}
        onChange={onChangeBall}
        options={optionBall}
        textCancel={t('describe_shot.cancel')}
        styleCancel={{
          color: LINK_BLUE,
          fontSize: 16,
          paddingVertical: 10,
        }}
        styleDone={{color: LINK_BLUE, fontSize: 16, marginVertical: 10}}
        handleCancel={handleBallCancel}
      />
      {Platform.OS === 'ios' ? (
        <CustomSelector
          ref={courseRangeRef}
          type="text"
          value={courseRange}
          onChange={onChangeCourseRange}
          options={optionCourseRanges}
          styleDone={{color: LINK_BLUE, marginVertical: 10}}
          snapPoints={'30%'}
          handleCancel={handleCancel}
          textCancel={t('describe_shot.cancel')}
          styleCancel={{
            color: LINK_BLUE,
            fontSize: 16,
            paddingVertical: 10,
          }}
          borderRadius={0}
        />
      ) : (
        <AndroidNumberPicker
          options={optionCourseRanges?.map(item => ({value: item}))}
          value={courseRange}
          setValue={onChangeCourseRange}
          visible={visibleCourseSelector}
          onClose={() => setVisibleCourseSelector(false)}
          title={t('describe_shot.course_or_practice_range')}
        />
      )}

      <CustomSelector
        ref={messageRef}
        type="message"
        textCancel={t('describe_shot.delete')}
        borderRadius={0}
        snapPoints={Platform.OS === 'ios' ? (hasNotch ? '56%' : '50%') : '40%'}
        value={message}
        onChange={setMessage}
        styleCancel={{
          color: message.length > 0 ? 'black' : 'gray',
          fontSize: 16,
          paddingVertical: 10,
        }}
        styleDone={{color: LINK_BLUE, fontSize: 16, marginVertical: 10}}
        title={`${250 - message.length}`}
        styleTitle={{fontSize: 11}}
        autoCorrect={false}
        multiline={true}
        maxLength={250}
        handleCancel={deleteMessage}
        disabledCancel={message.length === 0 ? true : false}
        styleHeader={{backgroundColor: '#0000001a'}}
        autoFocus={autoFocusMessage}
      />
      <CoachPopup
        visible={visibleModalUploaded}
        title="coach.modal.on_their_way"
        description="coach.modal.within_24_hours"
        button1="describe_shot.plan_coach_message.okay"
        button1Action={closeModalUploaded}
      />

      <CoachPopup
        visible={showReuploadSuccessfully}
        title={t('lesson_title_shot_upload_success', {rejectSwingName})}
        description={t('lesson_describe_shot_upload_success', {
          rejectSwingName,
          swingElementName: swingName,
        })}
        button1="describe_shot.plan_coach_message.okay"
        button1Action={closeModalReUploaded}
      />
      <CoachPopup
        visible={showUploadSuccess}
        icon={CongratsIcon}
        title="coach.modal_uploaded.title.congratulations"
        description={t('coach.modal_uploaded.description.congratulations', {
          name: swingShotType,
        })}
        button1="describe_shot.plan_coach_message.okay"
        button1Action={closeModalUploadedSuccess}
      />
    </>
  );
};

const styles = StyleSheet.create({
  boxText: {
    backgroundColor: '#3E4D54',
    paddingTop: 20,
    paddingBottom: 8,
  },
  backgroundColorContainer: {
    backgroundColor: '#3E4D54',
    height: '50%',
  },
  heightBox: {
    height: '50%',
  },
  boxImage: {
    width: 75,
    height: '100%',
  },
  boxTime: {
    backgroundColor: '#ffffff80',
    borderRadius: 5,
    position: 'absolute',
    bottom: 10,
    right: 7,
  },
  time: {
    fontSize: 10,
    color: 'white',
    zIndex: 1,
    paddingVertical: 2,
    paddingHorizontal: 7,
  },
});

export default DescribeShot;
