import React, {useState} from 'react';
import {View, Image, ScrollView, RefreshControl} from 'react-native';
import Text from 'components/Text/Text';
import {heightPercentageToDP} from 'react-native-responsive-screen';
import analytics from '@react-native-firebase/analytics';
import appStyles from 'styles/global';
import CoachingStarted from 'assets/imgs/CoachingStarted.png';
import CoachRoadmap from 'assets/imgs/CoachRoadmap.png';
import CoachTeeShot from 'assets/imgs/CoachTeeShot.png';
import Button from 'components/Button';
import {getPlayerProfile} from 'requests/swing-index';
import {useDispatch, useSelector} from 'react-redux';
import Icon from 'react-native-vector-icons/Foundation';
import {isOtherPayment, openOtherPayment} from 'utils/user';
import {t} from 'i18next';
import {SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';

const PlanStarted = ({navigation}) => {
  const [loading, setLoading] = useState(false);
  const permissions = useSelector(state => state?.app?.permissions);
  const dispatch = useDispatch();

  const getData = async () => {
    if (loading) {
      return;
    }
    try {
      setLoading(true);
      await getPlayerProfile(dispatch);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const reloadData = async () => {
    getData();
  };

  const GA_SetYourGoals = async () => {
    try {
      await analytics().logEvent('coaching_onboarding_start', {
        app_screen_name: 'coach - first use', //e.g home, my orders
        screen_type: SCREEN_TYPES.COACH, //e.g checkout, pdp, plp, account
        page_name: 'Get Started', //e.g home, my orders
        page_type: SCREEN_CLASS.COACH, //e.g basket, home, order
        page_category: SCREEN_CLASS.COACH,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  return (
    <ScrollView
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={reloadData} />
      }
    >
      <View
        style={{
          height: heightPercentageToDP('60'),
        }}
      >
        <Image
          source={CoachingStarted}
          style={[appStyles.full, {height: '72%'}]}
          resizeMode={'cover'}
        />
        <View
          style={{
            flexDirection: 'row',
            position: 'absolute',
            top: '32%',
            alignSelf: 'center',
          }}
        >
          <Image
            source={CoachTeeShot}
            style={[
              appStyles.full,
              {
                height: heightPercentageToDP('24'),
                width: 120,
                alignSelf: 'center',
              },
            ]}
            resizeMode={'contain'}
          />
          <Image
            source={CoachRoadmap}
            style={[
              {
                height: heightPercentageToDP('38'),
                width: 146,
                marginLeft: -30,
              },
            ]}
            resizeMode={'contain'}
          />
        </View>
      </View>
      <View style={[appStyles.hCenter, appStyles.mBMd, {marginHorizontal: 30}]}>
        <Text
          style={[
            {
              color: '#000000',
              fontSize: 26,
              textAlign: 'center',
            },
          ]}
          DINbold
        >
          coach.plan_started.coaching_staff
        </Text>
        <Text
          style={[
            {color: '#000000', fontSize: 17, top: 15, textAlign: 'center'},
          ]}
        >
          coach.plan_started.pga_teaching_professional
        </Text>
      </View>
      <View style={[appStyles.row, {justifyContent: 'center', marginTop: 10}]}>
        <Button
          style={[
            appStyles.mBMd,
            {height: 40, marginRight: 10, paddingHorizontal: 25},
          ]}
          text={
            permissions?.myTMSubscriptionLevel > 0
              ? 'plan.set_your_goals'
              : 'plan.upgrade_for_access_low'
          }
          backgroundColor="black"
          borderColor="black"
          onPress={() => {
            if (permissions?.myTMSubscriptionLevel > 0) {
              GA_SetYourGoals();
              navigation.navigate('Quiz', {
                screen: 'QuizTargetHandicapOrAvg',
                params: {planStarted: true},
              });
            } else {
              if (isOtherPayment(permissions)) {
                openOtherPayment();
                return;
              }
            }
          }}
          loading={loading}
          disabled={loading}
          loadingMode={'dark'}
          textColor="white"
          textStyle={{fontSize: 13, fontWeight: 'bold'}}
          centered
        />
        <Button
          style={[appStyles.mBMd, {height: 40, paddingHorizontal: 25}]}
          text={'Learn More'}
          borderColor="#000000"
          onPress={() =>
            navigation.navigate('Video', {
              video: {
                id: '-YRRi7VZ9oE',
                host: 'youtube',
                annotation: true,
                videoUrl: 'https://youtu.be/-YRRi7VZ9oE',
                title: t('coach_quick_hit_video'),
                // contentId: '430079',
              },
            })
          }
          loading={loading}
          disabled={loading}
          loadingMode={'dark'}
          textColor="#000000"
          textStyle={{fontSize: 13, fontWeight: 'bold'}}
          centered
          textSize={13}
          rightIconOther={() => (
            <Icon
              name="play-circle"
              size={18}
              color="#000000"
              style={{marginLeft: 8}}
            />
          )}
        />
      </View>
    </ScrollView>
  );
};

export default PlanStarted;
