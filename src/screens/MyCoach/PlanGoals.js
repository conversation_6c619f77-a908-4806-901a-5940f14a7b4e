import React, {useState} from 'react';
import {Al<PERSON>, Image, ScrollView, View} from 'react-native';
import Text from 'components/Text/Text';
import appStyles from 'styles/global';
import PlanCoachMessage from 'components/PlanCoachMessage';
import GoalsComponent from 'components/Goals';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {
  getPutterLockRoom,
  getShortGameLookRoomData,
  getSwingMaintenanceLockRoom,
  getWorkFlowEvents,
} from 'requests/swing-index';
import {useFocusEffect} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {t} from 'i18next';
import {COACH_SWING_SHOT_TYPE, COACH_VIDEO_TYPE} from 'json/describeShot';
import {getRoadMap} from 'utils/plans';
import {isOtherPayment, openOtherPayment} from 'utils/user';
import {moderateScale} from 'react-native-size-matters';
import analytics from '@react-native-firebase/analytics';
import {SCREEN_TYPES} from 'utils/constant';

const coachExplainerIcon = require('assets/imgs/coach-explainer-icon.png');
const playExplainerIcon = require('assets/imgs/play-coach-explain-icon.png');

const PlanGoals = ({navigation}) => {
  const roadMap = useSelector(state => state.plans.planSwingRoadMap);
  const permissions = useSelector(state => state?.app?.permissions);

  const isFreePermission =
    !permissions?.myTMPermission?.canVirtualCoaching &&
    !permissions?.myTMSubscriptionLevel;
  const [hasPuttingData, setHasPuttingData] = useState(false);
  const [loadingPutting, setLoadingPutting] = useState(false);
  const [isWaitingForRoadmap, setIsWaitingForRoadmap] = useState(false);
  const [loading, setLoading] = useState(false);
  const [quickFixTitle, setQuickFixTitle] = useState(
    'coach_get_a_single_lesson',
  );
  const dispatch = useDispatch();

  const planWorkFlowStatus = useSelector(
    state => state.plans.planWorkFlowStatus,
  );

  useFocusEffect(
    React.useCallback(() => {
      if (!roadMap?.roadmapStats) {
        getData();
      }
    }, [roadMap]),
  );

  useFocusEffect(
    React.useCallback(() => {
      if (!roadMap?.roadmapStats) {
        getRoadMap(dispatch);
      }
    }, []),
  );

  useFocusEffect(
    React.useCallback(() => {
      if (!hasPuttingData) {
        getPutterData();
      }
    }, [hasPuttingData]),
  );

  const getSwingData = async () => {
    try {
      const [maintenance, shortGame] = await Promise.all([
        getSwingMaintenanceLockRoom(),
        getShortGameLookRoomData(),
      ]);
      let quickData = maintenance.concat(shortGame);
      quickData = quickData.sort((a, b) =>
        a.uploadedDate > b.uploadedDate ? -1 : 1,
      ); // get latest data\
      let canUpload = true;

      if (quickData?.length > 0) {
        const item = quickData[0];
        if (
          item.processStatusId !== 4 ||
          (item.processStatusId === 4 &&
            item.childVideoStatusId !== null &&
            item.childVideoStatusId !== 4)
        ) {
          canUpload = false;
        }
        if (item.processStatusId === 4 && !item.childVideoStatusId) {
          canUpload = false;
        }
      }

      if (canUpload) {
        setQuickFixTitle('coach_get_a_single_lesson');
      } else {
        setQuickFixTitle('coach_review_coach_feedback');
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getQuickFixTitle = () => {
    return quickFixTitle;
  };

  useFocusEffect(
    React.useCallback(() => {
      getSwingData();
    }, []),
  );

  const getData = async () => {
    if (loading) {
      return;
    }
    try {
      setLoading(true);
      const events = await getWorkFlowEvents();
      setLoading(false);

      const workflowObj = planWorkFlowStatus?.find(
        e => e.code === 'BASELINE_SUBMITTED',
      );

      if (events[0]?.workflowStatusId === workflowObj.id) {
        setIsWaitingForRoadmap(true);
      } else {
        setIsWaitingForRoadmap(false);
      }
    } catch (error) {
      setLoading(false);
    }
  };

  const getPutterData = async () => {
    try {
      setLoadingPutting(true);
      const lockRoom = await getPutterLockRoom();
      if (lockRoom && lockRoom?.length > 0) {
        setHasPuttingData(true);
      } else {
        setHasPuttingData(false);
      }
      setLoadingPutting(false);
    } catch (error) {
      setLoadingPutting(false);
      console.log('error', error);
    }
  };

  const getRoadmapTitle = () => {
    if (roadMap.roadmapStats) {
      return 'See_Latest_Drill';
    } else if (isWaitingForRoadmap) {
      return 'coach_get_a_full_lesson_plan';
    } else {
      return 'coach_get_a_full_lesson_plan';
    }
  };

  const GA_Upload_New_Swing = async () => {
    try {
      await analytics().logEvent('upload_new_swing', {
        lesson_type: 'full lesson plan', //e.g single lesson, full lesson plan improve putting
        app_screen_name: 'coach - goals', //e.g home, my orders
        screen_type: SCREEN_TYPES.COACH, //e.g checkout, pdp, plp, account
        page_name: 'coach - goals', //e.g home, my orders
        page_type: SCREEN_TYPES.COACH, //e.g basket, home, order
        page_category: SCREEN_TYPES.COACH,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  const roadmapButtonPress = () => {
    if (roadMap.roadmapStats) {
      navigation.navigate('PlanRoadMap', {origin: 'PlanGoals'});
    } else if (isWaitingForRoadmap) {
      showAlertReceiptAnalysisWithinDay();
    } else {
      GA_Upload_New_Swing();
      navigation.navigate('NativeCoach', {
        screen: 'SelectVideoMenu',
        params: {
          swingShotTypeId: COACH_SWING_SHOT_TYPE.BASELINE,
          videoTypeId: COACH_VIDEO_TYPE.faceOn,
          lessonType: 'full lesson plan',
        },
      });
    }
  };

  const showAlertReceiptAnalysisWithinDay = () => {
    Alert.alert(
      t('video_in_process'),
      t('coach.plan_started.plan_within_24_hours'),
      [
        {
          text: t('select_video.alert.okay'),
          onPress: null,
          style: 'cancel',
        },
      ],
    );
  };
  const onPressUpgrade = () => {
    if (isOtherPayment(permissions)) {
      openOtherPayment();
      return;
    }
  };

  const GA_Re_Enter_Handicap_Goal = async () => {
    try {
      await analytics().logEvent('re_enter_handicap_goal', {
        app_screen_name: 'target handicap/average score', //e.g home, my orders
        screen_type: SCREEN_TYPES.ONBOARDING, //e.g checkout, pdp, plp, account
        page_name: 'target handicap/average score', //e.g home, my orders
        page_type: SCREEN_TYPES.ONBOARDING, //e.g basket, home, order
        page_category: SCREEN_TYPES.ONBOARDING,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  return (
    <>
      <ScrollView style={{backgroundColor: '#E5E5E5'}}>
        <PlanCoachMessage type={'goals'} />
        <GoalsComponent
          imageUrl={require('assets/imgs/completelesson.png')}
          title={'coach_single_lesson_title'}
          description={'coach_single_lesson_description'}
          functionClick={() =>
            isFreePermission
              ? onPressUpgrade()
              : navigation.navigate('CoachQuickFix')
          }
          buttonTitle={getQuickFixTitle()}
          isFreePermission={isFreePermission}
        />
        <GoalsComponent
          imageUrl={require('assets/imgs/singlelesson.png')}
          title={'coach_complete_lesson_plan_title'}
          description={'coach_complete_lesson_plan_description'}
          functionClick={() =>
            isFreePermission ? onPressUpgrade() : roadmapButtonPress()
          }
          // buttonLoading={loading}
          buttonTitle={getRoadmapTitle()}
          isFreePermission={isFreePermission}
        />
        {/* <GoalsComponent
        imageUrl={require('assets/imgs/focus_on_your_short_game.png')}
        title={'coach_short_game_review_title'}
        description={'coach_short_game_review_description'}
        functionClick={() =>
          isFreePermission ? onPressUpgrade() : navigation.navigate('CoachQuickFix')
        }
        buttonTitle={'Submit_A_Swing'}
        isFreePermission={isFreePermission}
      /> */}
        <GoalsComponent
          imageUrl={require('assets/imgs/improve_putting.png')}
          title={'coach_putting_title'}
          description={'coach_putting_description'}
          functionClick={() =>
            isFreePermission
              ? onPressUpgrade()
              : navigation.navigate('NativeCoachHorizontal', {
                  screen: 'PutterImprove',
                })
          }
          buttonTitle={
            hasPuttingData ? 'View_Your_Progress' : 'Begin_the_Challenge'
          }
          buttonLoading={loadingPutting}
          isFreePermission={isFreePermission}
        />
        <TouchableOpacity
          onPress={() => {
            GA_Re_Enter_Handicap_Goal();
            navigation.navigate('Quiz', {
              screen: 'QuizTargetHandicapOrAvg',
            });
          }}
          style={[appStyles.mTLg, appStyles.mBMd, appStyles.hCenter]}
        >
          <Text
            style={[appStyles.underlined, {fontSize: 13, color: '#8C8B8F'}]}
          >
            coach.underline_text
          </Text>
        </TouchableOpacity>
      </ScrollView>
      <View style={{position: 'absolute', bottom: 10, right: 0}}>
        <TouchableOpacity
          onPress={() =>
            navigation.navigate('Video', {
              video: {
                id: '-YRRi7VZ9oE',
                host: 'youtube',
                annotation: true,
                videoUrl: 'https://youtu.be/-YRRi7VZ9oE',
                title: t('coach_quick_hit_video'),
                // contentId: '430079',
              },
            })
          }
          activeOpacity={0.7}
        >
          <Image
            style={[
              {
                height: 60,
                width: 60,
              },
            ]}
            source={coachExplainerIcon}
          />
          <View style={{position: 'absolute', bottom: 13, left: 8}}>
            <Image
              style={[
                {
                  height: 16,
                  width: 16,
                },
              ]}
              source={playExplainerIcon}
            />
          </View>
        </TouchableOpacity>
      </View>
    </>
  );
};
export default PlanGoals;
