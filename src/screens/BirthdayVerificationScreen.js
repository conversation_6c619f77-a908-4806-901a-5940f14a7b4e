import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Image,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Text from 'components/Text';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Images from 'assets/imgs/Images';
import Button from 'components/Button';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import {getUser, login, updateUser} from 'requests/accounts';
import {useDispatch, useSelector} from 'react-redux';
import {addCurrentUser} from 'reducers/user';
import {setNewUser} from 'utils/commonVariable';
import moment from 'moment';
import analytics from '@react-native-firebase/analytics';
import Selector from 'components/Selector';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {updatePermissions} from '../reducers/app';
import {getAuth0Client} from '../utils/auth0';
import {refreshConfigureFeatures} from '../utils/configureFeatures';
import {
  API_TOKEN_EXPIRATION_TIME,
  AUTH0_TOKEN_EXPIRATION_TIME,
  GA_EVENT_NAME,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_CLASS,
  SCREEN_TYPES,
} from 'utils/constant';
import {isCanadaMarket, setCountry, setLanguage} from 'utils/commonVariable';
import {getWHSGolferProfile} from 'requests/whs';
import {updateWHSHandicapIndex} from 'reducers/whs';
import {getGolferProfile} from 'requests/ghin';
import {updateGHINHandicapIndex} from 'reducers/ghin';
import TaylorMadeBlack from 'assets/imgs/onBoarding/TaylorMadeBlack.svg';
import LinearGradient from 'react-native-linear-gradient';
import {GA_logEvent, GA_logScreenViewV2} from '../utils/googleAnalytics';
import appStyles from 'styles/global';

const BIRTHDAY_RANGE = [
  {
    max: 24,
    min: 18,
    range: '18-24',
  },
  {
    max: 34,
    min: 25,
    range: '25-34',
  },
  {
    max: 44,
    min: 35,
    range: '35-44',
  },
  {
    max: 54,
    min: 45,
    range: '45-54',
  },
  {
    max: 64,
    min: 55,
    range: '55-64',
  },
];

const BirthdayVerificationScreen = ({navigation, route}) => {
  // Current date for age calculation
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();

  // State for selected date
  const [month, setMonth] = useState('');
  const [day, setDay] = useState('');
  const [year, setYear] = useState('');
  const [isValid, setIsValid] = useState(false);
  const [loadingSave, setLoadingSave] = useState(false);
  const [birthDay, setBirthDay] = useState(null);
  const [hasSelectedDay, setHasSelectedDay] = useState(false);
  const [hasSelectedMonth, setHasSelectedMonth] = useState(false);
  const [hasSelectedYear, setHasSelectedYear] = useState(false);
  const [availableDays, setAvailableDays] = useState([]);
  const [loadingFull, setLoadingFull] = useState(false);
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);

  const checkIfInputDate =
    hasSelectedDay &&
    hasSelectedMonth &&
    hasSelectedYear &&
    month &&
    day &&
    year;
  const sheetRef_day = useRef(null);
  const sheetRef_month = useRef(null);
  const sheetRef_year = useRef(null);
  const dispatch = useDispatch();
  // Generate years (go back 100 years from current)
  const years = Array.from({length: 100}, (_, i) =>
    (currentYear - i).toString(),
  );

  // Months
  const months = [
    'JAN',
    'FEB',
    'MAR',
    'APR',
    'MAY',
    'JUN',
    'JUL',
    'AUG',
    'SEPT',
    'OCT',
    'NOV',
    'DEC',
  ];

  const DEFAULT_DATE = new Date().getDate()?.toString()?.padStart?.(2, '0');
  const DEFAULT_MONTH = months[new Date().getMonth()];
  // 18 years from now
  const DEFAULT_YEAR = (new Date().getFullYear() - 18)?.toString();

  // Function to determine if a year is a leap year
  const isLeapYear = yearVal => {
    const year = parseInt(yearVal);
    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
  };

  // Function to get the number of days in a month
  const getDaysInMonth = (monthName, yearVal) => {
    const monthIndex = months.indexOf(monthName);

    // If month is not selected yet, return a default array of 31 days
    if (monthIndex === -1) {
      return Array.from({length: 31}, (_, i) =>
        (i + 1).toString().padStart(2, '0'),
      );
    }

    // Check for months with 30 days
    if ([3, 5, 8, 10].includes(monthIndex)) {
      // APR, JUN, SEPT, NOV
      return Array.from({length: 30}, (_, i) =>
        (i + 1).toString().padStart(2, '0'),
      );
    }
    // Check for February
    else if (monthIndex === 1) {
      // FEB
      const isLeap = yearVal ? isLeapYear(yearVal) : false;
      const daysInFeb = isLeap ? 29 : 28;
      return Array.from({length: daysInFeb}, (_, i) =>
        (i + 1).toString().padStart(2, '0'),
      );
    }
    // All other months have 31 days
    else {
      return Array.from({length: 31}, (_, i) =>
        (i + 1).toString().padStart(2, '0'),
      );
    }
  };

  // Update available days when month or year changes
  useEffect(() => {
    const days = getDaysInMonth(month, year);
    setAvailableDays(days);

    // If the currently selected day is not valid for the new month, adjust it
    if (day && parseInt(day) > days.length) {
      setDay(days[days.length - 1]);
    }
  }, [month, year]);

  // Validate age whenever date changes
  useEffect(() => {
    validateAge();
  }, [month, day, year]);

  // Age validation function
  const validateAge = () => {
    if (!month || !day || !year) return;

    const birthYear = parseInt(year);
    const birthMonth = months.indexOf(month);
    const birthDay = parseInt(day);

    const birthDate = new Date(birthYear, birthMonth, birthDay);
    setBirthDay(birthDate);
    const ageDate = new Date(currentDate - birthDate);
    const age = Math.abs(ageDate.getUTCFullYear() - 1970);

    setIsValid(age >= 18);
  };

  const openSelector = selector => {
    switch (selector) {
      case 'month':
        sheetRef_month.current?.snapTo(0);
        sheetRef_day.current?.snapTo(1);
        sheetRef_year.current?.snapTo(1);
        if (!hasSelectedMonth) {
          setHasSelectedMonth(true);
        }
        break;
      case 'day':
        sheetRef_day.current?.snapTo(0);
        sheetRef_month.current?.snapTo(1);
        sheetRef_year.current?.snapTo(1);
        if (!hasSelectedDay) {
          setHasSelectedDay(true);
        }
        break;
      case 'year':
        sheetRef_year.current?.snapTo(0);
        sheetRef_day.current?.snapTo(1);
        sheetRef_month.current?.snapTo(1);
        if (!hasSelectedYear) {
          setHasSelectedYear(true);
        }
        break;
      default:
        break;
    }
  };

  const getRangeBirthday = userAge => {
    let userAgeConverted = Math.trunc(userAge);
    let rangeBirthday = '';
    if (userAgeConverted >= 65) {
      rangeBirthday = '65+';
    } else {
      BIRTHDAY_RANGE.forEach(element => {
        if (
          element.min <= userAgeConverted &&
          userAgeConverted <= element.max
        ) {
          rangeBirthday = element.range;
          return;
        }
      });
    }
    return rangeBirthday;
  };

  const GA_logOnboardingBirthday = userAge => {
    try {
      let range = getRangeBirthday(userAge);
      console.log('range', range);
      analytics().logEvent('user_onboarding_birthday', {
        user_age: range, // e.g. male, female or prefer not to say
        screen_name: `onboarding - dob`,
        screen_type: SCREEN_TYPES.ONBOARDING,
        page_name: `onboarding - dob`,
        page_type: SCREEN_TYPES.ONBOARDING,
        page_category: SCREEN_CLASS.SIGNUP,
      });
      analytics().setUserProperty('user_age', range);
    } catch (error) {
      console.log(error);
    }
  };

  const clearWebAuthSession = async () => {
    const auth0 = await getAuth0Client();
    setTimeout(() => {
      auth0.webAuth.clearSession();
    }, 1000);
  };

  const openAuth0 = async () => {
    const auth0 = await getAuth0Client();
    setLoadingSave(true);
    // Open browser and navigate to Auth0 Universal Login page
    auth0.webAuth
      .authorize({scope: 'openid profile email offline_access'})
      .then(credentials => userLogin(credentials))
      .catch(error => {
        setLoadingSave(false);
      });
  };

  const GA_signInFromOnBoarding = async (status, errorMessage) => {
    try {
      await GA_logScreenViewV2(
        PAGE_NAME.LOGIN,
        PAGE_CATEGORY.LOGIN,
        SCREEN_TYPES.LOGIN,
        SCREEN_TYPES.LOGIN,
      );
      if (status === 'success') {
        await GA_logEvent(GA_EVENT_NAME.LOGIN, {
          authentication_status: status,
          signup_method: 'email',
          page_type: SCREEN_TYPES.LOGIN,
          page_category: PAGE_CATEGORY.LOGIN,
          page_name: PAGE_NAME.LOGIN,
          screen_type: SCREEN_TYPES.LOGIN,
        });
      } else {
        await GA_logEvent(GA_EVENT_NAME.LOGIN, {
          authentication_status: status,
          signup_method: 'email',
          error_message: errorMessage,
          page_type: SCREEN_TYPES.LOGIN,
          page_category: PAGE_CATEGORY.LOGIN,
          page_name: PAGE_NAME.LOGIN,
          screen_type: SCREEN_TYPES.LOGIN,
        });
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const userLogin = async credentials => {
    try {
      setLoadingFull(true);
      // Make request to login endpoint to get user data
      const user = await login(credentials.accessToken);
      // Save current user's email for jwt renewal
      await AsyncStorage.setItem('userEmail', user.email);
      // Update permissions in redux
      dispatch(
        updatePermissions({
          myTMSubscriptionLevel: user?.myTMSubscriptionLevel,
          myTMPermission: user?.myTMPermission,
          subscriptionService: user?.subscriptionService,
          isTrialSubscription: user?.isTrialSubscription,
        }),
      );
      setLanguage(user?.language || 'en');
      // Set auth0 tokens to AsyncStorage
      await AsyncStorage.setItem('auth0AccessToken', credentials.accessToken);
      await AsyncStorage.setItem('auth0RefreshToken', credentials.refreshToken);
      await AsyncStorage.setItem(
        'auth0ExpiresIn',
        moment().add(AUTH0_TOKEN_EXPIRATION_TIME, 'm').toISOString(),
      );
      await AsyncStorage.setItem(
        'refreshApp',
        moment().add(29, 'days').toISOString(),
      );
      // Set TM tokens to AsyncStorage
      await AsyncStorage.setItem('idToken', user.idToken);
      await AsyncStorage.setItem('refreshToken', user.refreshToken);
      await AsyncStorage.setItem(
        'expiresIn',
        moment().add(API_TOKEN_EXPIRATION_TIME, 'm').toISOString(),
      );
      setCountry(user?.userCountry);
      dispatch(addCurrentUser({...user}));
      getUser();
      await refreshConfigureFeatures(dispatch, user, showHideFeatures);
      if (isCanadaMarket()) {
        try {
          // Add user to redux
          const golferProfile = await getWHSGolferProfile();

          if (golferProfile) {
            //Show handicap Entered
            dispatch(updateWHSHandicapIndex(golferProfile));
            dispatch(
              addCurrentUser({
                golfCanadaCardId: golferProfile?.golfCanadaCardId,
              }),
            );
          }
        } catch (error) {
          if (error?.response?.data?.golfCanadaCardId) {
            dispatch(
              updateWHSHandicapIndex({
                golfCanadaCardId: error?.response?.data.golfCanadaCardId,
              }),
            );
          }
        }
      } else {
        try {
          // Add user to redux
          const golferProfile = await getGolferProfile();

          if (golferProfile) {
            //Show handicap Entered
            dispatch(updateGHINHandicapIndex(golferProfile));
            dispatch(addCurrentUser({ghinId: golferProfile.ghin}));
          }
        } catch (error) {
          if (error?.response?.data?.ghin_id) {
            dispatch(
              updateGHINHandicapIndex({
                ghin: error?.response?.data.ghin_id,
                email: error?.response?.data?.ghinEmail,
              }),
            );
          }
          //Show handicap Pendding
        }
      }
      GA_signInFromOnBoarding('success', 'N/A');
      navigation.replace('Introduce');
      // Stop loading state and navigate to next screen
      setLoadingSave(false);
    } catch (error) {
      setLoadingSave(false);
      if (error?.response?.data?.internalErrorCode === 'UNAUTHORIZED') {
        showToast({
          type: 'error',
          message: error?.response?.data?.errorMessage,
        });
        GA_signInFromOnBoarding('failure', error?.response?.data?.errorMessage);
        clearWebAuthSession();
      } else {
        showToast({
          type: 'error',
          message: t('login.confirmation.backup_on_the_first_tee'),
          subText: t('login.confirmation.your_group_will_be_teeing_off_soon'),
        });
        GA_signInFromOnBoarding(
          'failure',
          t('login.confirmation.backup_on_the_first_tee'),
        );
      }
    }
  };

  const onConfirmPress = async () => {
    try {
      const userAge = moment().diff(moment(birthDay), 'years', true);
      setLoadingSave(true);
      let updateUserResponse = await updateUser({
        dob: birthDay,
      });
      GA_logOnboardingBirthday(userAge);
      setNewUser(true);
      openAuth0();
      setLoadingSave(false);
    } catch (error) {
      setLoadingSave(false);
      const errorMessage =
        error.response?.data?.errorMessage || error.response?.data?.message
          ? error.response?.data?.errorMessage ||
            error.response?.data?.message ||
            error.message
          : t('An_error_occurred_signing_up');
      showToast({
        type: 'error',
        message: errorMessage,
      });
    }
  };

  // Custom selector button
  const SelectorButton = ({value, onPress, style}) => (
    <TouchableOpacity style={[styles.selectorButton, style]} onPress={onPress}>
      <Text style={styles.selectorValue} Din79Font>
        {value}
      </Text>
      <Ionicons name="chevron-down" size={20} color="#000" />
    </TouchableOpacity>
  );

  // Render selector modal
  const renderSelectorModal = () => {
    return (
      <>
        <Selector
          ref={sheetRef_day}
          type="text"
          value={day || DEFAULT_DATE}
          onChange={item => {
            setDay(item);
          }}
          options={
            availableDays.length > 0
              ? availableDays
              : getDaysInMonth(month, year)
          }
          onCloseEnd={() => {
            if (!day || day === '') {
              // Ensure default day is valid for the selected month
              const days = getDaysInMonth(
                month || DEFAULT_MONTH,
                year || DEFAULT_YEAR,
              );
              const validDefault =
                parseInt(DEFAULT_DATE) <= days.length
                  ? DEFAULT_DATE
                  : days[days.length - 1];
              setDay(validDefault);
            }
          }}
        />
        <Selector
          ref={sheetRef_month}
          type="text"
          value={month || DEFAULT_MONTH}
          onChange={item => {
            setMonth(item);
          }}
          options={months}
          onCloseEnd={() => {
            if (!month || month === '') {
              setMonth(DEFAULT_MONTH);
            }
          }}
        />
        <Selector
          ref={sheetRef_year}
          type="text"
          value={year || DEFAULT_YEAR}
          onChange={item => {
            setYear(item);
          }}
          options={years}
          onCloseEnd={() => {
            if (!year || year === '') {
              setYear(DEFAULT_YEAR);
            }
          }}
        />
      </>
    );
  };
  const insets = useSafeAreaInsets();
  const isDisableConfirmButton =
    loadingSave ||
    !isValid ||
    !hasSelectedDay ||
    !hasSelectedMonth ||
    !hasSelectedYear;
  return loadingFull ? (
    <SafeAreaView
      style={[
        appStyles.flex,
        appStyles.hCenter,
        {paddingTop: hp(33), backgroundColor: '#fff'},
      ]}
    >
      <LinearGradient
        start={{x: 1, y: 0.1}}
        end={{x: 1, y: 1}}
        colors={['#fff', 'rgba(0, 0, 0, 1)']}
        style={[
          {
            width: wp('100%'),
            top: hp(33),
            bottom: 0,
            left: 0,
            opacity: 0.25,
            position: 'absolute',
          },
        ]}
      />
      <View style={[{marginBottom: 40}]}>
        <TaylorMadeBlack />
      </View>
      <View>
        <ActivityIndicator
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
          size={'large'}
          color={'black'}
        />
      </View>
    </SafeAreaView>
  ) : (
    <>
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          {/* Logo */}
          <View
            style={{
              alignItems: 'center',
              paddingBottom: 80,
              marginTop: Platform.OS === 'ios' ? 0 : 12 + insets.top,
            }}
          >
            <Image style={styles.logo} source={Images.logo_Black} />
          </View>
          {/* Title */}
          <Text style={styles.title}>What is your birthday?</Text>

          {/* Date selectors */}
          <View style={styles.selectorRow}>
            <SelectorButton
              value={hasSelectedMonth ? month : 'MONTH'}
              onPress={() => openSelector('month')}
              style={styles.monthSelector}
            />

            <SelectorButton
              value={hasSelectedDay ? day : 'DAY'}
              onPress={() => openSelector('day')}
              style={styles.daySelector}
            />

            <SelectorButton
              value={hasSelectedYear ? year : 'YEAR'}
              onPress={() => openSelector('year')}
              style={styles.yearSelector}
            />
          </View>

          {/* Age requirement message */}
          {!isValid && checkIfInputDate && (
            <Text style={styles.requirementText}>
              You must be at least 18 years old to use the TaylorMade app
            </Text>
          )}

          {/* Confirm button */}
          <Button
            text={'quiz.country.confirm'}
            textColor={isDisableConfirmButton ? 'white' : '#000'}
            textStyle={{fontWeight: '700', fontSize: 12, letterSpacing: 1.62}}
            backgroundColor={
              isDisableConfirmButton ? 'rgb(225, 225, 225)' : '#fff'
            }
            borderColor={'rgb(225, 225, 225)'}
            onPress={onConfirmPress}
            loading={loadingSave}
            style={[
              styles.nextButton,
              loadingSave && {shadowColor: 'rgb(200, 200, 200)'},
              loadingSave && Platform.OS === 'android' && styles.borderAndroids,
            ]}
            disabled={isDisableConfirmButton}
            centered
            Din79Font
          />
        </View>
      </SafeAreaView>
      {/* Selector Modal */}
      {renderSelectorModal()}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  logo: {
    with: 50,
    height: 50,
    marginTop: 12,
  },
  title: {
    fontSize: 16,
    marginBottom: 36,
    color: 'black',
  },
  selectorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 20,
  },
  selectorButton: {
    backgroundColor: '#fff',
    borderRadius: 30,
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    flex: 1,
  },
  monthSelector: {
    marginHorizontal: 4,
    marginBottom: 16,
    height: 40,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 4,
    shadowRadius: 9,
    elevation: 4,
    shadowColor: '#d8d7d7',
  },
  daySelector: {
    marginHorizontal: 4,
    marginBottom: 16,
    height: 40,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 4,
    shadowRadius: 9,
    elevation: 4,
    shadowColor: '#d8d7d7',
  },
  yearSelector: {
    marginHorizontal: 4,
    marginBottom: 16,
    height: 40,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 4,
    shadowRadius: 9,
    elevation: 4,
    shadowColor: '#d8d7d7',
  },
  selectorValue: {
    fontSize: 12,
    fontWeight: 'bold',
    letterSpacing: 1.62,
    color: 'black',
  },
  requirementText: {
    textAlign: 'center',
    marginHorizontal: 16,
    fontSize: 12,
    fontWeight: 'bold',
    letterSpacing: 1.3,
    color: 'black',
  },
  nextButton: {
    width: wp(100) - 32,
    height: 40,
    marginTop: 24,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 7,
    shadowColor: '#d8d7d7',
    marginBottom: 8,
    elevation: 6,
    position: 'absolute',
    bottom: 0,
  },
  borderAndroids: {
    borderWidth: 1,
    borderColor: 'white',
  },
});

export default BirthdayVerificationScreen;
