import React, {useEffect, useState} from 'react';
import {
  ScrollView,
  RefreshControl,
  View,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {useIsFocused} from '@react-navigation/native';
import {useSelector, useDispatch} from 'react-redux';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import CustomClubIntro from 'components/TryThenBuy/CustomClubIntro';
import TrialStatus from 'components/TryThenBuy/TrialStatus';
import HowItWorks from 'components/TryThenBuy/HowItWorks';
import PreviouslyTried from 'components/TryThenBuy/PreviouslyTried';
import ClubConfiguratorModal from 'components/ClubConfiguratorModal';
import {useFocusEffect} from '@react-navigation/native';

import {getTTBOrders} from 'requests/ttb';
import {updateTTBOrders} from 'reducers/ttb';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import {refreshConfigureFeatures} from 'utils/configureFeatures';
import {ENVIRONMENTS} from 'config/env';
import AsyncStorage from '@react-native-async-storage/async-storage';
import PlayExplainerIcon from 'assets/imgs/ttb_explain_icon.png';
import DeviceInfo from 'react-native-device-info';

const ShopTTB = ({navigation}) => {
  const permissions = useSelector(state => state?.app?.permissions);
  const orders = useSelector(state => state?.ttb?.orders);
  const user = useSelector(state => state.user);
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
  const [env, setEnv] = useState(ENVIRONMENTS.DEV);
  const [refreshing, setRefreshing] = useState(false);
  const tabBarheight = useBottomTabBarHeight();
  const isFocused = useIsFocused();
  const dispatch = useDispatch();
  const [toggleSelector, setToggleSelector] = useState(false);
  const [showToastError, setShowToastError] = useState(false);
  const hasPermission = permissions?.myTMPermission?.canTryThenBuy || false;
  const bundleId = DeviceInfo.getBundleId();
  const isStagingApp =
    (Platform.OS === 'ios' &&
      bundleId === 'com.taylormadegolf.mytaylormadeplus.ios') ||
    (Platform.OS === 'android' &&
      bundleId === 'com.taylormadegolf.mytaylormadeplus.android');
  const isAllowSelectionOtherClub =
    env === ENVIRONMENTS.DEV ||
    env === ENVIRONMENTS.STAGING ||
    (env === ENVIRONMENTS.PROD && isStagingApp);
  const trialOrders = orders.filter(order => {
    return (
      order.status !== 'PRODUCT_RETURNED' &&
      order.status !== 'BOUGHT' &&
      order.status !== 'PRODUCT_RECEIVED_TO_TM' &&
      order.status !== 'PRODUCT_RETURNING_TO_TM' &&
      order.status !== 'CHARGED' &&
      order.status !== 'CANCELLED' &&
      order.status !== 'CANCELED'
    );
  });

  useEffect(() => {
    getEnvironment();
  }, []);

  const getEnvironment = async () => {
    const environment = await AsyncStorage.getItem('env');
    setEnv(environment);
  };

  useFocusEffect(
    React.useCallback(() => {
      onRefresh();
    }, []),
  );

  useEffect(() => {
    if (isFocused) {
      getOrders();
    }
  }, [isFocused]);

  useEffect(() => {
    if (showToastError) {
      showToast({
        type: 'error',
        message: t('club_config_session_invalid'),
      });
      setTimeout(() => {
        setToggleSelector(true);
      }, 1000);
      setShowToastError(false);
    }
  }, [showToastError]);

  const getOrders = async () => {
    try {
      // Make request to retrieve TTB orders
      const ttbOrders = await getTTBOrders();
      dispatch(updateTTBOrders(ttbOrders));
    } catch (error) {
      showToast({
        type: 'error',
        message: t('An_error_occurred_retrieving_your_orders'),
      });
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await getOrders();
    refreshConfigureFeatures(dispatch, user, showHideFeatures);
    setRefreshing(false);
  };
  return (
    <View style={{backgroundColor: '#111111'}}>
      <ClubConfiguratorModal
        toggleSelector={toggleSelector}
        setToggleSelector={setToggleSelector}
        setShowToastError={setShowToastError}
        model={isAllowSelectionOtherClub ? '' : 'TM_DRIVER_MODEL'}
        navigate={navigation.navigate}
        isTTB
        isAllowSelectionOtherClub={isAllowSelectionOtherClub}
      />
      <FocusAwareStatusBar barStyle={'light-content'} />
      <ScrollView
        contentContainerStyle={{paddingBottom: tabBarheight}}
        refreshControl={
          <RefreshControl refreshing={false} onRefresh={onRefresh} />
        }
      >
        {!trialOrders.length ? (
          <CustomClubIntro
            navigate={navigation.navigate}
            setToggleSelector={setToggleSelector}
            hasPermission={hasPermission}
          />
        ) : (
          <TrialStatus navigate={navigation.navigate} order={trialOrders[0]} />
        )}
        <HowItWorks active={trialOrders?.length ? true : false} />
        <PreviouslyTried
          navigate={navigation.navigate}
          trialOrders={trialOrders}
          orders={orders}
          hasPermission={hasPermission}
        />
      </ScrollView>
      <View style={{position: 'absolute', bottom: tabBarheight + 7, right: 0}}>
        <TouchableOpacity
          onPress={() =>
            navigation.navigate('Video', {
              video: {
                id: 'AQi5AuqrFoY',
                host: 'youtube',
                annotation: true,
                videoUrl: 'https://youtu.be/AQi5AuqrFoY',
                title: t('ttb.explainer_video.title'),
                // contentId: '430079',
              },
            })
          }
          activeOpacity={0.7}
        >
          <Image source={PlayExplainerIcon} style={{width: 59, height: 57}} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ShopTTB;
