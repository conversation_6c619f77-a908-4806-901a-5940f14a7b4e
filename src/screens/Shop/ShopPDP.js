import React, {useState, useEffect, useMemo} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  ScrollView,
  View,
  TouchableOpacity,
  StyleSheet,
  Image,
  Platform,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {connect, useSelector} from 'react-redux';
import {moderateScale} from 'react-native-size-matters';
import Text from 'components/Text';
import Button from 'components/Button';
import HtmlParser from 'components/HtmlView';
import BasketMessageModal from 'components/BasketMessageModal';
import ClubConfiguratorModal from 'components/ClubConfiguratorModal';
import VariantConfiguratorModal from 'components/VariantConfiguratorModal';
import {
  getProductDetails,
  addProductsToBasket,
  createBasket,
  deleteBasket,
  getProductOverwrite,
  getProductsInCategory,
} from 'requests/ecom';
import {updateCurrentBasket} from 'reducers/basket';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {GREY} from 'config';
import {t} from 'i18next';
import Icon from 'react-native-vector-icons/Feather';
import analytics from '@react-native-firebase/analytics';
import {NOT_APPLICABLE, SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';
import {isOtherPayment, openOtherPayment} from 'utils/user';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import QuantitySection from 'components/QuantitySection';
import {checkHiddenAttribute} from 'utils/shop';
import {getCurrencySymbol} from 'utils/commonVariable';

const hasNotch = DeviceInfo.hasNotch();

const ShopPDP = ({navigation, route, updateCurrentBasket, basket}) => {
  const permissions = useSelector(state => state?.app?.permissions);
  const productId = route.params?.productId;
  const origin = route.params?.origin;
  const maxQtyFromParams = route.params?.maxQty;
  const [maxQty, setMaxQty] = useState(maxQtyFromParams || 1);
  const participationLevel = route.params?.participationLevel || '1';
  const checkPermissionLevelCanBuy = () => {
    if (
      participationLevel?.includes(
        permissions?.myTMSubscriptionLevel?.toString(),
      ) ||
      permissions?.myTMPermission?.canExclProductDrops ||
      permissions?.myTMSubscriptionLevel === 2 //For Legend
    ) {
      return true;
    }
    return false;
  };
  const isMember = checkPermissionLevelCanBuy();
  const [loading, setLoading] = useState(false);
  const [product, setProduct] = useState(null);
  const [visible, setVisible] = useState(false);
  const [clearingBasket, setClearingBasket] = useState(false);
  const [toggleSelector, setToggleSelector] = useState(false);
  const [soldOut, setSoldOut] = useState(route.params?.soldOut);
  const [isOverwrite, setOverwrite] = useState(route.params?.isOverwrite);
  const [orderable, setOrderable] = useState(route.params?.orderable);
  const [showToastError, setShowToastError] = useState(false);
  const [attributeData, setAttributeData] = useState([]);
  const [quantity, setQuantity] = useState(1);
  const addedToCart = basket.product_items?.some(item => {
    if (
      item.product_id ===
      (product?.c_oc_model_name ? product?.c_oc_model_name : productId)
    ) {
      return true;
    }
    return product?.variants?.some(
      variant => variant?.product_id === item.product_id,
    );
  });
  const hasTTBItem = basket.product_items?.some(product => {
    const hasTTB = product?.c_tm_customconfigurator_nodes
      ? JSON.parse(product?.c_tm_customconfigurator_nodes)?.selectedNodes?.some(
          node => {
            return node.name === 'TTB';
          },
        )
      : false;
    return hasTTB;
  });

  const GA_measureProductClicks = productDetail => {
    let screenName = '';
    let screenClass = '';
    let screenType = '';
    switch (origin) {
      case 'feed':
        screenName = 'home - feed';
        screenClass = SCREEN_CLASS.HOME;
        screenType = SCREEN_TYPES.HOME;
        break;
      case 'drops':
        screenName = 'shop - drops';
        screenClass = SCREEN_CLASS.PLP;
        screenType = SCREEN_TYPES.MEMBERSHOP;
        break;
      case 'tourtrash':
        screenName = 'shop - tour trash';
        screenClass = SCREEN_CLASS.SHOP;
        screenType = SCREEN_TYPES.TOURTRASH;
        break;
      case 'home-justin':
        screenName = 'home - just in';
        screenClass = SCREEN_CLASS.HOME;
        screenType = SCREEN_TYPES.HOME;
        break;
      case 'home-vertical':
        screenName = 'home - vertical';
        screenClass = SCREEN_CLASS.HOME;
        screenType = SCREEN_TYPES.HOME;
        break;
      default:
        break;
    }
    let itemListForGA = [];
    itemListForGA.push({
      item_id: productDetail?.id,
      item_name: productDetail?.name,
      item_category: NOT_APPLICABLE,
      item_brand: 'TaylorMade Golf',
      item_list_name: 'Drops',
      item_location_id: screenName || NOT_APPLICABLE, //optional
      item_variant: NOT_APPLICABLE,
      index: 0, //e.g 4
      price: productDetail?.price, //e.g 123.22
      quantity: quantity, //e.g 2
    });
    analytics().logEvent('select_item', {
      app_screen_name: screenName, //e.g home, my orders
      screen_type: screenClass, //e.g checkout, pdp, plp, account
      page_name: screenName, //e.g home, my orders
      page_type: screenType, //e.g basket, home, order
      page_category: screenType,
      currency: 'USD',
      items: itemListForGA,
    });
  };

  const GA_measureProductDetails = productDetail => {
    let itemListForGA = [];
    itemListForGA.push({
      item_id: productDetail?.id,
      item_name: productDetail?.name,
      item_category: NOT_APPLICABLE,
      item_brand: 'TaylorMade Golf',
      item_list_name: 'Drops',
      item_location_id: 'shop - drops - product', //optional
      item_variant: NOT_APPLICABLE,
      index: 0, //e.g 4
      price: productDetail?.price, //e.g 123.22
      quantity: quantity, //e.g 2
    });
    analytics().logEvent('view_item', {
      app_screen_name: 'shop - drops - product', //e.g home, my orders
      screen_type: SCREEN_CLASS.PDP, //e.g checkout, pdp, plp, account
      page_name: 'shop - drops - product', //e.g home, my orders
      page_type: SCREEN_TYPES.MEMBERSHOP, //e.g basket, home, order
      page_category: SCREEN_TYPES.MEMBERSHOP,
      currency: 'USD',
      items: itemListForGA,
    });
  };

  const GA_addProductToCart = () => {
    analytics().logEvent('add_to_cart', {
      app_screen_name: 'shop - drops - product', //e.g home, my orders
      screen_type: SCREEN_CLASS.PDP, //e.g checkout, pdp, plp, account
      page_name: 'shop - drops - product', //e.g home, my orders
      page_type: SCREEN_TYPES.MEMBERSHOP, //e.g basket, home, order
      page_category: SCREEN_TYPES.MEMBERSHOP,
      currency: 'USD',
      price: product?.price, //e.g 123.22
      items: [
        {
          item_id: product?.id,
          item_name: product?.name,
          item_category: NOT_APPLICABLE,
          item_brand: 'TaylorMade Golf',
          item_list_name: 'Cart',
          item_location_id: 'shop - drops - product', //optional
          item_variant: NOT_APPLICABLE,
          index: 0, //e.g 4
          quantity: quantity, //e.g 2
        },
      ],
    });
  };

  useEffect(() => {
    if (showToastError) {
      showToast({
        type: 'error',
        message: t('club_config_session_invalid'),
      });
      setTimeout(() => {
        setToggleSelector(true);
      }, 1000);
      setShowToastError(false);
    }
  }, [showToastError]);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        // Make request to get product details from sfcc
        const productDetails = await getProductDetails(productId);
        await checkProductWithEcom(productDetails);
        GA_measureProductClicks(productDetails);
        GA_measureProductDetails(productDetails);
        setProduct(productDetails);
        getSelectorData(productDetails);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('variant.configurator_modal.fore'),
          subText: t('shopPDP.we_will_retreive_your_product'),
        });
      }
    })();
  }, []);

  const checkProductWithEcom = async ecomDetail => {
    const productOverwrite = await getProductOverwrite();
    if (productOverwrite) {
      let result;
      result = productOverwrite.find(_item => _item.product_id === productId);
      if (!result) {
        const productsInCategory = await getProductsInCategory();
        if (productsInCategory) {
          const resultCategory = productsInCategory.find(
            _item => _item.represented_product.id === productId,
          );
          if (resultCategory) {
            result = productOverwrite.find(
              _item => _item.product_id === resultCategory.product_id,
            );
            if (resultCategory?.orderable === false) {
              setOrderable(false);
            }
          }
        }
      }
      if (result && result?.maxQty && !maxQtyFromParams) {
        setMaxQty(result?.maxQty);
      }
      if (result && result?.sold_out) {
        setOverwrite(true);
        setSoldOut(true);
      } else {
        if (orderable == null) {
          setOrderable(ecomDetail?.inventory?.orderable);
        }
      }
    } else {
      if (orderable == null) {
        setOrderable(ecomDetail?.inventory?.orderable);
      }
    }
  };
  const getSelectorData = productDetails => {
    if (productDetails?.variation_attributes?.length > 0) {
      const data = [];
      productDetails?.variation_attributes?.forEach?.(attribute => {
        const availableOptions = attribute?.values?.filter?.(
          item => item.orderable,
        );
        let isHiddenAttribute = checkHiddenAttribute(productDetails, attribute);
        if (isHiddenAttribute) {
          return;
        }
        // && attribute?.id !== 'Akeneo_Size'
        if (availableOptions?.length > 0) {
          data.push(attribute);
        }
      });
      setAttributeData(data);
    }
  };

  const validateBasket = () => {
    if (
      (product?.c_oc_configurable && product?.c_oc_model_name) ||
      attributeData?.length > 0
    ) {
      setToggleSelector(true);
    } else if (hasTTBItem) {
      setVisible(true);
    } else {
      addToCart();
    }
  };

  const addToCart = async newBasket => {
    setLoading(true);
    try {
      // Make request to add product to sfcc basket
      const currentBasket = await addProductsToBasket({
        basketId: newBasket ? newBasket.basket_id : basket.basket_id,
        resourceState: newBasket
          ? newBasket._resource_state
          : basket._resource_state,
        products: [
          {
            product_id: productId,
            quantity: quantity,
          },
        ],
      });
      GA_addProductToCart();
      // Update basket in redux
      updateCurrentBasket(currentBasket);
      setLoading(false);

      // Navigate to basket
      navigation.navigate('ShopBasket');
    } catch (error) {
      const errorMessage =
        error.response?.data?.errorMessage ||
        error.response?.data?.message ||
        error.response?.data?.fault?.message
          ? error.response?.data?.errorMessage ||
            error.response?.data?.message ||
            error.response?.data?.fault?.message
          : t('variant.configurator_modal.default_message');
      setLoading(false);
      showToast({
        type: 'error',
        message: t('variant.configurator_modal.fore'),
        subText: errorMessage,
      });
    }
  };

  const clearBasket = async () => {
    setClearingBasket(true);

    try {
      // Delete basket
      await deleteBasket({
        basketId: basket.basket_id,
        resourceState: basket._resource_state,
      });
      // Recreate basket
      const currentBasket = await createBasket();
      // Update basket in redux
      updateCurrentBasket(currentBasket);
      // Hide modal and loading state
      setVisible(false);
      setClearingBasket(false);
      // Add item to the new basket
      addToCart(currentBasket);
    } catch (error) {
      setVisible(false);
      setClearingBasket(false);
      showToast({
        type: 'error',
        message: t('An_error_occurred_clearing_basket'),
      });
    }
  };

  const buttonProps = useMemo(() => {
    //if product has been added to cart
    if (addedToCart) {
      return {
        disabled: true,
        text: t('shop.pdp_buy.cta'),
        color: GREY,
      };
    }
    //if data from BE
    if (isOverwrite === true) {
      if (soldOut === true) {
        return {
          disabled: true,
          text: t('shop.drops.sold_out_uppercase'),
          color: GREY,
        };
      } else {
        if (orderable === false) {
          return {
            disabled: true,
            text: t('shop.drops.sold_out_uppercase'),
            color: GREY,
          };
        }
      }
    } //data from ECOM
    else {
      if (orderable === false) {
        return {
          disabled: true,
          text: t('shop.drops.sold_out_uppercase'),
          color: GREY,
        };
      }
    }
    return {
      disabled: false,
      text: isMember ? t('shop.pdp_buy.cta') : t('plan.upgrade_for_access'),
      color: '#111111',
    };
  }, [addedToCart, isMember, orderable, soldOut, isOverwrite]);

  const renderSelector = () => {
    if (
      product?.c_oc_configurable &&
      product?.c_oc_model_name &&
      toggleSelector
    ) {
      return (
        <ClubConfiguratorModal
          setShowToastError={setShowToastError}
          toggleSelector={toggleSelector}
          setToggleSelector={setToggleSelector}
          model={product?.c_oc_model_name}
          clubConfig={{
            name: product?.c_oc_head_model_name,
            model: product?.c_oc_head_model_name,
          }}
          origin="shop"
          navigate={navigation.navigate}
          productId={product?.id}
          quantity={quantity}
        />
      );
    }

    if (attributeData?.length > 0 && toggleSelector) {
      return (
        <VariantConfiguratorModal
          attributes={attributeData}
          toggleSelector={toggleSelector}
          setToggleSelector={setToggleSelector}
          variants={product?.variants || []}
          navigate={navigation.navigate}
          product={product}
          originProductId={productId}
          quantity={quantity}
        />
      );
    }
    return null;
  };

  const renderTextLimit = () => {
    return (
      <Text
        style={[
          appStyles.black,
          appStyles.textCenter,
          {fontSize: moderateScale(8)},
        ]}
        params={{value: maxQty}}
      >
        shop.pdp_buy.limit
      </Text>
    );
  };

  return (
    <>
      {renderSelector()}
      <SafeAreaView
        style={[appStyles.flex]}
        edges={['right', 'bottom', 'left']}
      >
        <BasketMessageModal
          visible={visible}
          setVisible={setVisible}
          clearBasket={clearBasket}
          clearingBasket={clearingBasket}
          setLoading={setLoading}
          description="You can only purchase Drops if you don't have a Try Then Buy item in your cart."
        />
        <ScrollView style={[appStyles.flex, appStyles.pBSm]}>
          <Image
            source={{uri: product?.image_groups?.[0]?.images?.[0]?.link}}
            style={{
              width: wp(100),
              height: wp(100),
            }}
          />
          <View style={appStyles.pHXSm}>
            <Text black style={[appStyles.mTMd, {fontSize: 26}]} DINbold>
              {product?.name?.toUpperCase?.()}
            </Text>
            {soldOut === true || orderable === false ? (
              <Text
                style={[
                  appStyles.grey,
                  appStyles.mBMd,
                  appStyles.xs,
                  {
                    textDecorationLine: 'line-through',
                    textDecorationStyle: 'solid',
                    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
                  },
                ]}
              >
                {product?.price !== undefined && product?.price !== null
                  ? `${getCurrencySymbol(product?.currency)}${product?.price}`
                  : ''}
              </Text>
            ) : product?.price !== undefined && product?.price !== null ? (
              <Text
                style={[
                  appStyles.grey,
                  appStyles.mBMd,
                  appStyles.xs,
                  {fontWeight: Platform.OS === 'ios' ? '600' : 'bold'},
                ]}
              >
                {`${getCurrencySymbol(product?.currency)}${product?.price}`}
              </Text>
            ) : null}
            <View style={[appStyles.mBMd]}>
              <HtmlParser
                html={product?.long_description?.replace(/(\r\n|\n|\r)/gm, '')}
              />
            </View>
          </View>
          {product?.image_groups?.[0]?.images?.length > 0 &&
            product?.image_groups?.[0]?.images?.map((item, index) => {
              if (index === 0) {
                return;
              }
              return (
                <Image
                  style={{
                    width: wp(100),
                    height: wp(100),
                    marginBottom: 10,
                  }}
                  source={{uri: item?.link}}
                />
              );
            })}
        </ScrollView>
        <View
          style={[
            appStyles.pHSm,
            appStyles.pTSm,
            appStyles.pBXs,
            appStyles.row,
            appStyles.fullWidth,
            styles.bottomSection,
          ]}
        >
          {maxQty > 1 && (
            <QuantitySection
              maxQty={maxQty}
              quantity={quantity}
              setQuantity={setQuantity}
              renderTextLimit={renderTextLimit}
            />
          )}
          <Button
            style={{flex: 1}}
            text={buttonProps?.text}
            textColor="white"
            backgroundColor={buttonProps?.color}
            borderColor={buttonProps?.color}
            disabled={loading || buttonProps?.disabled}
            loading={loading}
            loadingMode="dark"
            onPress={
              isMember
                ? validateBasket
                : () => {
                    if (isOtherPayment(permissions)) {
                      openOtherPayment();
                      return;
                    }
                  }
            }
            centered
            DINbold
          />
        </View>
        {maxQty === 1 && renderTextLimit()}
        <TouchableOpacity
          style={styles.iconContainer}
          onPress={() => navigation.goBack()}
        >
          <View
            style={[styles.closeIcon, appStyles.vCenter, appStyles.hCenter]}
          >
            <Icon name="x" size={moderateScale(20)} />
          </View>
        </TouchableOpacity>
      </SafeAreaView>
    </>
  );
};

const mapStateToProps = state => ({
  basket: state.basket,
});

const styles = StyleSheet.create({
  closeIcon: {
    backgroundColor: 'rgba(255, 255, 255, 1)',
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(18),
  },
  iconContainer: {
    width: moderateScale(40),
    height: moderateScale(40),
    top: hasNotch ? 45 : 25,
    right: 15,
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomSection: {
    paddingHorizontal: moderateScale(25),
    justifyContent: 'space-between',
  },
});

const mapDispatchToProps = {updateCurrentBasket};

export default connect(mapStateToProps, mapDispatchToProps)(ShopPDP);
