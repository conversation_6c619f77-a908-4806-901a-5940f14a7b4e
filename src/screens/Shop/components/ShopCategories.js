import React, {useEffect, useImperativeHandle, useRef, useState} from 'react';
import {View, StyleSheet, TouchableOpacity, Platform} from 'react-native';
import Text from 'components/Text';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import appStyles from 'styles/global';
import {FlatList} from 'react-native-gesture-handler';
import {getShopCategories} from 'requests/shop';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {getAuth0AccessToken} from 'utils/user';
import {openEcomWebview, prepareLink} from 'utils/shop';
import {t} from 'i18next';
import {CACHE_KEY, GA_EVENT_NAME, WEBVIEW_PAGE_TYPE} from 'utils/constant';
import {GA_logEvent} from 'utils/googleAnalytics';
import {updateShopCategoriesData} from 'reducers/dataCache';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

const ShopCategories = ({isScrollFlatlist, onLayout, isVisibleToUser}, ref) => {
  const [shopCategoriesData, setShopCategoriesData] = useState([]);
  const navigation = useNavigation();
  const {navigate} = navigation;
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const isScreenFocused = useIsFocused();
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const shopCategoriesCache = useSelector(
    state => state.dataCache?.shopCategories,
  );
  const currentCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.SHOP_CATEGORIES,
  )?.version;
  const dispatch = useDispatch();
  const {getEcomProductDetail} = useEcomProductDetail();
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (appCacheVersions) {
      refreshDataIfNeeded();
    }
  }, [appCacheVersions]);

  useEffect(() => {
    if (shopCategoriesCache) {
      getShopCategoriesData(shopCategoriesCache);
      isFirstRender.current = false;
    }
  }, [shopCategoriesCache]);

  const refreshDataIfNeeded = async () => {
    try {
      if (
        currentCacheVersion === shopCategoriesCache?.version &&
        appCacheVersions?.country === shopCategoriesCache?.country &&
        currentCacheVersion != null
      ) {
        if (isScreenFocused && !isFirstRender.current) {
          //if the data is not changed from the Admin Portal, so re-assign old value from cache to trigger the Google Analytics events
          dispatch(
            updateShopCategoriesData({
              country: userCountry,
              version: currentCacheVersion,
              data: shopCategoriesCache?.data,
            }),
          );
        }
      } else {
        const shopCategories = await getShopCategories(userCountry);
        dispatch(
          updateShopCategoriesData({
            country: userCountry,
            version: currentCacheVersion,
            data: shopCategories?.shopCategories || [],
          }),
        );
      }
    } catch (error) {}
  };

  useEffect(() => {
    try {
      if (isVisibleToUser) {
        //GA4 logging event
      }
    } catch (error) {}
  }, [isVisibleToUser]);

  const onItemPress = async (item, index) => {
    const accessToken = await getAuth0AccessToken(dispatch);
    if (
      item?.ctaLink &&
      item?.ctaLink?.length > 0 &&
      (item?.ctaLink?.includes('http') || item?.ctaLink?.includes('www.'))
    ) {
      const linkUrl = await prepareLink(
        item?.ctaLink,
        accessToken,
        WEBVIEW_PAGE_TYPE.GRID_PAGE,
      );
      openEcomWebview(
        navigate,
        {
          title: item?.title,
          uri: linkUrl,
          canGoBack: true,
          originUri: item?.ctaLink,
          clickLocation: 'shop-categories',
          isLogSelectItem: false,
        },
        getEcomProductDetail,
      );
      logEventPromotion({
        products: [item],
        eventName: GA_EVENT_NAME.SELECT_PROMOTION,
        clickIndex: index,
        tileTitle: item?.title,
      });
      return;
    }
  };

  const getShopCategoriesData = shopCategories => {
    try {
      if (shopCategories?.data) {
        let dataCategories = shopCategories?.data;
        setShopCategoriesData(dataCategories);
        if (dataCategories?.length > 0) {
          logEventPromotion({
            products: dataCategories,
            eventName: GA_EVENT_NAME.VIEW_PROMOTION,
          });
        }
      } else {
        setShopCategoriesData([]);
      }
    } catch (error) {}
  };

  const logEventPromotion = ({
    products,
    eventName,
    clickIndex = 0,
    tileTitle,
  }) => {
    if (products && products.length) {
      const items = [];
      products.map(val => {
        const item = {
          promotion_id: val?.id,
          promotion_name: val?.title,
          creative_name: 'Categories Shop',
          creative_slot: 'Shop > Categories',
          location_id: 'Shop > Categories',
        };
        if (eventName === GA_EVENT_NAME.SELECT_PROMOTION) {
          item.index = clickIndex + 1 + '';
        }
        items.push(item);
      });
      GA_logEvent(eventName, {
        currency: 'USD',
        tile_title: tileTitle,
        items,
      });
    }
  };

  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        delayPressIn={100}
        delayPressOut={100}
        onPress={() => onItemPress(item, index)}
        style={[styles.itemWrapper, appStyles.viewShadowLightBig]}
      >
        <Text
          size={12}
          white
          Din79Font
          style={{
            fontWeight: '700',
            letterSpacing: 1.62,
            textTransform: 'uppercase',
          }}
        >
          {item.title}
        </Text>
      </TouchableOpacity>
    );
  };

  return shopCategoriesData.length > 0 ? (
    <View
      style={styles.viewContainer}
      onLayout={e => {
        onLayout?.(e.nativeEvent.layout.y, e.nativeEvent.layout.height);
      }}
    >
      <View style={{marginHorizontal: 16}}>
        <Text size={16} black style={{marginBottom: 6}}>
          {t('shop.shop_by_category')}
        </Text>
      </View>
      <FlatList
        data={shopCategoriesData}
        horizontal
        onTouchMove={() => {
          if (Platform.OS === 'android') {
            isScrollFlatlist.current = true;
          }
        }}
        onTouchCancel={() => {
          if (Platform.OS === 'android') {
            isScrollFlatlist.current = false;
          }
        }}
        keyExtractor={(item, index) => item?.id}
        renderItem={renderItem}
        style={{width: wp(100), paddingTop: 8}}
        contentContainerStyle={{paddingRight: 8}}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  viewContainer: {
    marginTop: 16,
    marginBottom: 35,
  },
  itemWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    borderRadius: 24,
    backgroundColor: 'black',
    paddingHorizontal: 16,
    paddingVertical: 14,
  },
});

export default React.forwardRef(ShopCategories);
