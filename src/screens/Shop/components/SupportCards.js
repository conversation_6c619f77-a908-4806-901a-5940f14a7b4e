import React, {useEffect, useImperativeHandle, useRef, useState} from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Text from 'components/Text';
import CustomImage from 'components/CustomImage/CustomImage';
import appStyles from 'styles/global';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {getSupportCards} from 'requests/shop';
import {useDispatch, useSelector} from 'react-redux';
import Carousel from 'react-native-reanimated-carousel';
import {getAuth0AccessToken} from 'utils/user';
import {getProductIdFromUrl, openEcomWebview, prepareLink} from 'utils/shop';
import {
  GA_logEvent,
  GA_logViewItemList,
  GA_selectPromotionWithEcomData,
  getProductIds,
} from 'utils/googleAnalytics';
import {CACHE_KEY, GA_EVENT_NAME, WEBVIEW_PAGE_TYPE} from 'utils/constant';
import {isEmpty} from 'lodash';
import {setDeepLink} from 'reducers/app';
import {updateMainstaysData} from 'reducers/dataCache';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

const SupportCards = ({}, ref) => {
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const [data, setData] = useState([]);
  const [styleContent, setStyleContent] = useState('flex-start');
  const navigation = useNavigation();
  const {navigate} = navigation;
  const dispatch = useDispatch();
  const isScreenFocused = useIsFocused();
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const mainstaysDataCache = useSelector(state => state.dataCache?.mainstays);
  const currentCacheVersion = appCacheVersions?.features.find(
    item => item.key === CACHE_KEY.SHOP_MAINSTAYS,
  )?.version;
  const {getEcomProductDetail, getMultipleEcomProductDetails} =
    useEcomProductDetail();
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (appCacheVersions) {
      refreshDataIfNeeded();
    }
  }, [appCacheVersions]);

  useEffect(() => {
    if (mainstaysDataCache) {
      getDataSupportCard(mainstaysDataCache);
      isFirstRender.current = false;
    }
  }, [mainstaysDataCache]);

  useEffect(() => {
    if (data && data.length) {
      let listProductId = getProductIds(data);
      if (listProductId.length > 0) {
        GA_logViewItemList(
          listProductId,
          'shop-mainstays',
          undefined,
          undefined,
          getMultipleEcomProductDetails,
        );
      }
      logEventPromotion({
        products: data,
        eventName: GA_EVENT_NAME.VIEW_PROMOTION,
      });
    }
  }, [data]);

  const getDataSupportCard = results => {
    if (results?.data?.length > 0) {
      setData(results?.data);
    } else {
      setData([]);
    }
  };

  const refreshDataIfNeeded = async () => {
    try {
      if (
        currentCacheVersion === mainstaysDataCache?.version &&
        appCacheVersions?.country === mainstaysDataCache?.country &&
        currentCacheVersion != null
      ) {
        if (isScreenFocused && !isFirstRender.current) {
          //if the data is not changed from the Admin Portal, so re-assign old value from cache to trigger the Google Analytics events
          dispatch(
            updateMainstaysData({
              country: userCountry,
              version: currentCacheVersion,
              data: mainstaysDataCache?.data,
            }),
          );
        }
      } else {
        try {
          const params = {
            page: 1,
            take: 31,
            country: userCountry,
          };
          const results = await getSupportCards(params);
          dispatch(
            updateMainstaysData({
              country: userCountry,
              version: currentCacheVersion,
              data: results?.widgets || [],
            }),
          );
        } catch (error) {}
      }
    } catch (error) {
      console.log('error fresh mainstays data', error.message);
    }
  };

  const onItemPress = async (item, index) => {
    let options = null;
    try {
      options = JSON.parse(item?.options);
    } catch (error) {}
    if (options?.category === 'DEEP_LINK') {
      const paramsDeelinks = options?.ctaDeepLink.split('/');
      if (paramsDeelinks?.length > 0) {
        const lastItem = paramsDeelinks[paramsDeelinks.length - 1];
        if (lastItem && lastItem?.length > 0) {
          dispatch(setDeepLink(lastItem));
        }
      }
    } else if (
      item?.ctaLink &&
      item?.ctaLink?.length > 0 &&
      (item?.ctaLink?.includes('http') || item?.ctaLink?.includes('www.'))
    ) {
      const accessToken = await getAuth0AccessToken(dispatch);
      const linkUrl = await prepareLink(
        item?.ctaLink,
        accessToken,
        options?.ctaLinkType,
      );
      logGASelectPromotion(item, index);
      openEcomWebview(
        navigate,
        {
          title: item?.title,
          uri: linkUrl,
          canGoBack: true,
          originUri: item?.ctaLink,
          imageUrl: item?.imageLink,
          clickLocation: 'shop-mainstays',
          origin: 'MainstaysWeb',
          isLogSelectItem: false,
        },
        getEcomProductDetail,
      );
    }
  };

  const logGASelectPromotion = async (productItem, index) => {
    if (productItem) {
      const listProductId = [];
      const options =
        typeof productItem?.options === 'object'
          ? productItem?.options
          : JSON.parse(productItem?.options);
      if (options?.ctaLinkType === WEBVIEW_PAGE_TYPE.PDP) {
        try {
          let productId = getProductIdFromUrl(productItem?.ctaLink);
          let productDetails = await getEcomProductDetail(productId);
          if (productDetails) {
            listProductId.push(productId);
          }
        } catch (error) {}
      }
      const tileTitle = productItem?.title;
      if (!isEmpty(listProductId)) {
        const paramGA = {
          promotion_id: productItem?.id,
          promotion_name: productItem?.title,
          creative_name: `Shop Mainstays Banner`,
          creative_slot: 'Shop > Mainstays',
          location_id: 'Shop > Mainstays',
        };
        const rsLogGA = await GA_selectPromotionWithEcomData({
          listProductId,
          paramGA,
          clickIndex: index,
          tileTitle,
          getMultipleEcomProductDetails,
        });
        if (!rsLogGA) {
          logEventPromotion({
            products: [productItem],
            eventName: GA_EVENT_NAME.SELECT_PROMOTION,
            clickIndex: index,
            tileTitle,
          });
        }
      } else {
        logEventPromotion({
          products: [productItem],
          eventName: GA_EVENT_NAME.SELECT_PROMOTION,
          clickIndex: index,
          tileTitle,
        });
      }
    }
  };

  const logEventPromotion = ({
    products,
    eventName,
    clickIndex = 0,
    tileTitle,
  }) => {
    if (products && products.length) {
      const items = [];
      products.map(val => {
        const item = {
          promotion_id: val?.id,
          promotion_name: val?.title,
          creative_name: `Shop Mainstays Banner`,
          creative_slot: 'Shop > Mainstays',
          location_id: 'Shop > Mainstays',
        };
        if (eventName === GA_EVENT_NAME.SELECT_PROMOTION) {
          item.index = clickIndex + 1 + '';
        }
        items.push(item);
      });
      GA_logEvent(eventName, {
        currency: 'USD',
        tile_title: tileTitle,
        items,
      });
    }
  };

  const renderItem = ({item, index}) => {
    let configOptions = {};
    try {
      configOptions = JSON.parse(item?.options);
    } catch (error) {
      configOptions = {};
    }
    return (
      <TouchableOpacity
        key={item?.id}
        index={index}
        delayPressIn={100}
        delayPressOut={100}
        activeOpacity={0.8}
        onPress={() => onItemPress(item, index)}
        style={[
          styles.itemWrapper,
          appStyles.viewShadowLightBig,
          {
            backgroundColor: configOptions?.background || 'white',
          },
          styleContent === 'flex-start'
            ? {marginLeft: 8, marginRight: 0}
            : {marginRight: 8, marginLeft: 0},
        ]}
      >
        <CustomImage style={styles.itemImage} source={{uri: item?.imageLink}} />
        <View
          style={{
            paddingHorizontal: 8,
            flex: 1,
            justifyContent: 'center',
          }}
        >
          <Text
            style={[
              styles.copyText,
              {color: configOptions?.titleColor || 'black'},
            ]}
            size={16}
            numberOfLines={3}
          >
            {item?.title}
          </Text>
          <Text
            size={12}
            style={{
              lineHeight: 16.5,
              color: configOptions?.subColor || '#00000080',
            }}
            numberOfLines={3}
          >
            {item?.description}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return data && data.length > 0 ? (
    <View style={styles.viewContainer}>
      <Carousel
        loop={false}
        data={data}
        renderItem={renderItem}
        width={wp(82) + 16}
        height={131 + 16}
        style={{
          width: wp(100),
          justifyContent: styleContent,
        }}
        scrollAnimationDuration={300}
        panGestureHandlerProps={{
          activeOffsetX: [-10, 10],
        }}
        onSnapToItem={index => {
          if (index === 0) {
            setStyleContent('flex-start');
          } else if (index === data.length - 1) {
            setStyleContent('flex-end');
          }
        }}
      />
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  viewContainer: {
    marginBottom: 10,
  },
  itemWrapper: {
    width: wp(82) + 8,
    height: 140,
    marginLeft: 8,
    flexDirection: 'row',
    borderRadius: 16,
    marginTop: 2,
    backgroundColor: 'white',
    padding: 4,
  },
  itemImage: {
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    height: 131,
    width: 131,
    marginRight: 8,
  },
  copyText: {
    fontWeight: '700',
    marginBottom: 12,
  },
});

export default React.forwardRef(SupportCards);
