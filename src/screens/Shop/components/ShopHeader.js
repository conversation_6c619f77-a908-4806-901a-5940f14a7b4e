import React from 'react';
import {View, StyleSheet} from 'react-native';
import {useSelector} from 'react-redux';
import Header from 'components/Header';
import appStyles from 'styles/global';
import {useNavigation} from '@react-navigation/native';
import Text from 'components/Text';
import LogoMyTMBlackNoPlus from 'assets/imgs/logo-mytm-black-no-plus.svg';
import LogoMyTMNoPlus from 'assets/imgs/logo-no-plus.svg';
import { checkMainCountry } from 'utils/constant';

const ShopHeader = ({colorPTS, isDarkBackground, headerStyle, pointHide}) => {
  const loyalty = useSelector(state => state?.loyalty);
  const user = useSelector(state => state?.user);
  const {tier, points} = loyalty || {};

  const shouldHidePoint = !checkMainCountry(user?.userCountry) || pointHide;

  const navigation = useNavigation();
  const {navigate} = navigation;
  //{points?.availablePoints || 0} | $
  //{points?.creditsToCurrencyValue || 0}
  return (
    <View
      style={[
        styles.headerContainer,
        {
          width: '100%',
        },
        headerStyle,
      ]}
    >
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <View style={[appStyles.row, {alignItems: 'center'}]}>
          {isDarkBackground ? (
            <LogoMyTMNoPlus style={{marginRight: 8, marginLeft: 16}} />
          ) : (
            <LogoMyTMBlackNoPlus style={{marginRight: 8, marginLeft: 16}} />
          )}
          {!shouldHidePoint && (
            <View style={appStyles.row}>
              <Text
                Din79Font
                size={16}
                weight={800}
                style={[styles.textPts, colorPTS && {color: colorPTS}]}
              >
                {`${points?.availablePoints || 0}PTS`}
              </Text>
              <Text
                Din79Font
                size={16}
                weight={800}
                style={{
                  letterSpacing: 1.2,
                  color: isDarkBackground ? '#fff' : '#000',
                }}
              >
                {`$${points?.creditsToCurrencyValue || 0}`}
              </Text>
            </View>
          )}
        </View>

        <Header
          navigate={navigate}
          profileBG={
            isDarkBackground
              ? {backgroundColor: 'rgba(241, 241, 241, 1)'}
              : undefined
          }
          titleColor={appStyles.white}
          profileTextColor={isDarkBackground ? {color: '#000'} : undefined}
          headerStyle={{
            alignSelf: 'flex-end',
            marginRight: 16,
          }}
        />
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  headerContainer: {
    zIndex: 4,
    marginBottom: 10,
  },
  textPts: {
    marginRight: 8,
    color: '#0093e5',
    letterSpacing: 1.2,
  },
});
export default ShopHeader;
