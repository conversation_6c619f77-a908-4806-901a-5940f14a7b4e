import React, {useImperativeHandle, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  FlatList,
} from 'react-native';
import Text from 'components/Text';
import appStyles from 'styles/global';
import moment from 'moment';
import Animated, {
  Extrapolation,
  useAnimatedStyle,
  useSharedValue,
  interpolate,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import {getTrackingOrders} from 'requests/shop';
import {useDispatch, useSelector} from 'react-redux';
import logoDelivered from 'assets/imgs/shop/ic_delivered.png';
import {convertProductIdToImageURL, loadTrackingOrdersData} from 'utils/shop';
import {t} from 'i18next';
import {useNavigation} from '@react-navigation/native';
import {DELIVERY_STATUS} from 'utils/constant';
let timeOut = null;
const TrackingOrders = ({onLayoutFunction}, ref) => {
  const offset = useSharedValue(0);
  const [expanded, setExpanded] = useState(false);
  const user = useSelector(state => state.user);
  const ordersData = useSelector(state => state.trackingOrders?.orders);
  const refFlatList = useRef();
  const navigation = useNavigation();
  const dispatch = useDispatch();
  useImperativeHandle(ref, () => ({
    refreshData: () => {
      reloadData();
    },
  }));

  const validDeliveryStatus = [
    DELIVERY_STATUS.NOT_FOUND,
    DELIVERY_STATUS.EXPIRED,
    DELIVERY_STATUS.IN_TRANSIT,
    DELIVERY_STATUS.PICK_UP,
    DELIVERY_STATUS.DELIVERED,
  ];

  const pendingDeliStatus = [
    DELIVERY_STATUS.NOT_FOUND,
    DELIVERY_STATUS.EXPIRED,
  ];

  const shippingStatus = [DELIVERY_STATUS.IN_TRANSIT, DELIVERY_STATUS.PICK_UP];

  const reloadData = async () => {
    await loadTrackingOrdersData(dispatch, user);
  };

  const scrollToIndex = index => {
    if (timeOut) {
      clearTimeout(timeOut);
    }
    timeOut = setTimeout(() => {
      refFlatList?.current?.scrollToIndex({
        animated: false,
        index: index,
      });
    }, 100);
  };

  const animatedScaleStyles = useAnimatedStyle(() => {
    const height = interpolate(offset.value, [0, 1], [32, 88], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      height,
    };
  });

  const imageScaleStyles = useAnimatedStyle(() => {
    const size = interpolate(offset.value, [0, 1], [24, 80], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      height: size,
      width: size,
    };
  });

  const renderItem = ({item, index}) => {
    const image =
      item?.image?.link || convertProductIdToImageURL(item?.productId) || '';
    return (
      <Animated.View style={[animatedScaleStyles]}>
        <TouchableOpacity
          onPress={() => {
            if (!expanded) {
              setExpanded(!expanded);
              offset.value = withTiming(
                !expanded ? 1 : 0,
                {duration: 300},
                () => {
                  runOnJS(scrollToIndex)(index);
                },
              );
            } else {
              if (item?.packageStatus) {
                navigation?.navigate('WebView', {
                  screen: 'WebView',
                  params: {
                    uri: `https://www.fedex.com/fedextrack/?trknbr=${item.shipmentId}`,
                    canGoBack: true,
                  },
                });
              }
            }
          }}
          activeOpacity={0.9}
        >
          <Animated.View
            style={[
              styles.itemWrapper,
              appStyles.viewShadowLight,
              animatedScaleStyles,
            ]}
          >
            <Animated.Image
              style={[
                styles.itemImage,
                imageScaleStyles,
                {
                  borderTopRightRadius: expanded ? 0 : 12,
                  borderBottomRightRadius: expanded ? 0 : 12,
                },
              ]}
              source={{uri: image}}
            />
            {!expanded && (
              <Text
                Din79Font
                style={[styles.copyText]}
                black
                size={16}
                numberOfLines={1}
              >
                {item?.productName}
              </Text>
            )}
            <View
              style={{paddingHorizontal: 8, flex: 1, justifyContent: 'center'}}
            >
              {expanded && (
                <>
                  <Text
                    Din79Font
                    style={styles.copyText}
                    black
                    size={16}
                    numberOfLines={1}
                  >
                    {item?.productName}
                  </Text>
                  <Text
                    black
                    size={16}
                    style={{lineHeight: 18, marginVertical: 8}}
                  >
                    {t('shop.orders.ordered')}:{' '}
                    {`${moment(item?.orderedDate, 'YYYY-MM-DD').format(
                      'MM.DD.YY',
                    )}`}
                  </Text>
                </>
              )}
              {pendingDeliStatus.includes(
                item?.packageStatus?.toUpperCase?.(),
              ) || !item?.packageStatus ? (
                <Text
                  black
                  size={16}
                  style={{lineHeight: 18}}
                  numberOfLines={1}
                  ellipsizeMode={'clip'}
                >
                  {t('shop.orders.pending')}
                </Text>
              ) : shippingStatus?.includes(
                  item?.packageStatus?.toUpperCase?.(),
                ) ? (
                <Text
                  black
                  size={16}
                  style={{lineHeight: 18}}
                  numberOfLines={1}
                  ellipsizeMode={'clip'}
                >
                  {t('shop.orders.est_arrival')}:{' '}
                  {`${moment(item?.scheduledArrivalDate, 'DD-MMM-YYYY').format(
                    'MM.DD.YY',
                  )}`}
                </Text>
              ) : (
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <Image
                    source={logoDelivered}
                    style={{width: 16, height: 16}}
                  />
                  <Text
                    size={16}
                    style={{lineHeight: 18, color: '#03A800', marginLeft: 4}}
                    numberOfLines={1}
                  >
                    {t('shop.orders.delivered')}
                  </Text>
                </View>
              )}
            </View>
          </Animated.View>
        </TouchableOpacity>
      </Animated.View>
    );
  };
  return ordersData && ordersData.length > 0 ? (
    <Animated.View
      style={[styles.viewContainer, animatedScaleStyles]}
      onLayout={onLayoutFunction}
    >
      <FlatList
        horizontal
        data={ordersData}
        keyExtractor={(item, index) => `${item.productId}${item.orderNo}`}
        renderItem={renderItem}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{paddingRight: 8}}
        ref={refFlatList}
        alwaysBounceHorizontal={false}
      />
    </Animated.View>
  ) : null;
};

const styles = StyleSheet.create({
  viewContainer: {
    marginBottom: 8,
  },
  itemWrapper: {
    marginLeft: 8,
    flexDirection: 'row',
    borderRadius: 16,
    backgroundColor: 'rgb(230,230,230)',
    padding: 4,
    overflow: 'hidden',
    alignItems: 'center',
  },
  itemImage: {
    height: 24,
    width: 24,
    marginRight: 10,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
  },
  copyText: {
    fontWeight: '700',
    letterSpacing: 16 * 0.03,
  },
});

export default React.forwardRef(TrackingOrders);
