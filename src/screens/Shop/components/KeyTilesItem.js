import Text from 'components/Text';
import React, {useRef, useState} from 'react';
import {View, StyleSheet} from 'react-native';
import Button from 'components/Button';
import appStyles from 'styles/global';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Animated, {
  Extrapolation,
  useAnimatedStyle,
  interpolate,
} from 'react-native-reanimated';
import {
  useFocusEffect,
  useIsFocused,
  useNavigation,
} from '@react-navigation/native';
import Video from 'react-native-video';
import {getAuth0AccessToken} from 'utils/user';
import {useDispatch} from 'react-redux';
import {getProductIdFromUrl, openEcomWebview, prepareLink} from 'utils/shop';
import useAppState from 'hooks/useAppState';
import {GA_EVENT_NAME, WEBVIEW_PAGE_TYPE} from 'utils/constant';
import {isEmpty} from 'lodash';
import {GA_selectPromotionWithEcomData} from 'utils/googleAnalytics';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

const widthItem = wp(96);
const widthPixel = wp(2);
const maxValue = 400;
const heightBelowView = wp(34);
const spaceBetweenItem = 16;
const maxValueContainer = 400;
const AnimatedVideo = Animated.createAnimatedComponent(Video);
const TILE_RATIO = 1.38;

const TextContent = ({item, styleCustom}) => {
  const [titleHeight, setTitleHeight] = useState(0);
  return (
    <View style={appStyles.flex}>
      <Text
        Din79Font
        weight={800}
        size={22}
        numberOfLines={2}
        style={[
          appStyles.textCenter,
          {
            marginTop: 10,
            marginBottom: 8,
            color: styleCustom?.titleColor || '#000',
            lineHeight: 25,
            letterSpacing: 1.1,
          },
        ]}
        onLayout={event => {
          let {height} = event.nativeEvent.layout;
          setTitleHeight(height);
        }}
      >
        {item?.title}
      </Text>
      <Text
        size={16}
        numberOfLines={titleHeight > 26 ? 4 : 5}
        style={[
          appStyles.textCenter,
          {
            color: styleCustom?.subColor || '#000',
          },
        ]}
      >
        {item?.description?.trim?.()}
      </Text>
    </View>
  );
};

const KeyTilesItem = ({
  offset,
  offsetTransItem,
  item,
  index,
  selectedIndex,
  size,
  backToTop,
  logEventPromotion,
}) => {
  const dataVideo = useRef(null);
  const [pause, setPause] = useState(true);
  const dispatch = useDispatch();
  const refIsFocus = React.useRef(false);

  const indexWhenChange = selectedIndex < 0 ? index - 0 : index - selectedIndex;
  const styleCustom = item?.options ? JSON.parse(item?.options) : null;
  const width =
    index <= selectedIndex
      ? widthItem
      : widthItem - (size - index) * widthPixel;
  const navigation = useNavigation();
  const focused = useIsFocused();
  const {getEcomProductDetail, getMultipleEcomProductDetails} =
    useEcomProductDetail();

  useAppState({
    onForeground: () => {
      if (refIsFocus.current) {
        playVideo();
      }
    },
    onBackground: () => {
      pauseVideo();
    },
  });

  useFocusEffect(
    React.useCallback(() => {
      refIsFocus.current = true;
      playVideo();
      return () => {
        refIsFocus.current = false;
        pauseVideo();
      };
    }, []),
  );

  const pauseVideo = () => {
    setPause(true);
  };
  const playVideo = () => {
    setPause(false);
  };

  const animateActiveStyles = useAnimatedStyle(() => {
    //Push item
    if (offsetTransItem.value <= 0) {
      //View active
      if (index === selectedIndex) {
        const translateX = interpolate(
          offsetTransItem.value,
          [0, -maxValue],
          [0, -widthItem - 100],
          {
            extrapolateRight: Extrapolation.CLAMP,
          },
        );
        const translateY = interpolate(
          offsetTransItem.value,
          [0, -maxValue],
          [0, -100],
          {
            extrapolateRight: Extrapolation.CLAMP,
          },
        );
        const rotate = interpolate(
          offsetTransItem.value,
          [0, -maxValue],
          [0, -30],
          {
            extrapolateRight: Extrapolation.CLAMP,
          },
        );
        return {
          top: 0,
          transform: [{translateX}, {translateY}, {rotate: rotate + 'deg'}],
        };
      }
      //Below view active
      if (index > selectedIndex) {
        const topWait = interpolate(
          offset.value,
          [0, maxValueContainer],
          [
            indexWhenChange * spaceBetweenItem,
            widthItem + heightBelowView + indexWhenChange * spaceBetweenItem,
          ],
          {
            extrapolateRight: Extrapolation.CLAMP,
          },
        );
        const topActive = interpolate(
          offsetTransItem.value,
          [0, -maxValue],
          [
            topWait,
            selectedIndex + 1 === index ? 0 : topWait - spaceBetweenItem,
          ],
          {
            extrapolateRight: Extrapolation.CLAMP,
          },
        );
        return {
          top: offsetTransItem.value !== 0 ? topActive : topWait,
        };
      }
      //Out of view
      return {
        transform: [{translateX: -widthItem - 100}],
        opacity: 0,
      };
    } else {
      //Pull item
      //Out of View with index = selectedIndex - 1
      if (index === selectedIndex - 1) {
        const translateX = interpolate(
          offsetTransItem.value,
          [0, maxValue],
          [-widthItem, 0],
          {
            extrapolateRight: Extrapolation.CLAMP,
          },
        );
        const translateY = interpolate(
          offsetTransItem.value,
          [0, maxValue],
          [-100, 0],
          {
            extrapolateRight: Extrapolation.CLAMP,
          },
        );
        const rotate = interpolate(
          offsetTransItem.value,
          [0, maxValue],
          [-30, 0],
          {
            extrapolateRight: Extrapolation.CLAMP,
          },
        );

        return {
          transform: [{translateX}, {translateY}, {rotate: rotate + 'deg'}],
          position: offsetTransItem.value !== 0 ? 'absolute' : 'relative',
          opacity: 1,
        };
      }
      //View active and below view active
      if (index >= selectedIndex) {
        const topWait =
          widthItem + heightBelowView + indexWhenChange * spaceBetweenItem;

        const topActive = interpolate(
          offsetTransItem.value,
          [0, maxValue],
          [
            index === selectedIndex ? 0 : topWait,
            index === selectedIndex
              ? topWait + indexWhenChange * spaceBetweenItem + spaceBetweenItem
              : topWait + spaceBetweenItem,
          ],
          {
            extrapolateRight: Extrapolation.CLAMP,
          },
        );
        return {
          opacity: 1,
          top: offsetTransItem.value !== 0 ? topActive : topWait,
        };
      }
    }
    //Out of view
    return {
      transform: [{translateX: -widthItem - 100}],
      opacity: 0,
    };
  });

  const animateStyles = useAnimatedStyle(() => {
    if (offset.value > 0 && offset.value < maxValueContainer && index === 0) {
      const widthChange = interpolate(
        offset.value,
        [0, maxValueContainer],
        [widthItem - (size - index) * widthPixel, widthItem],
        {
          extrapolateRight: Extrapolation.CLAMP,
        },
      );
      return {width: widthChange};
    }
    //Push item
    if (offsetTransItem.value <= 0) {
      const widthChange = interpolate(
        offsetTransItem.value,
        [0, -maxValue],
        [width, selectedIndex + 1 === index ? widthItem : width],
        {
          extrapolateRight: Extrapolation.CLAMP,
        },
      );
      return {width: widthChange};
    } else if (offsetTransItem.value > 0) {
      //Pull item
      const widthChange = interpolate(
        offsetTransItem.value,
        [0, maxValue],
        [
          width,
          selectedIndex - 1 === index
            ? widthItem
            : widthItem - (size - index) * widthPixel,
        ],
        {
          extrapolateRight: Extrapolation.CLAMP,
        },
      );

      return {width: widthChange};
    }
    return {};
  });

  const animateImageStyles = useAnimatedStyle(() => {
    //Push item
    if (offset.value > 0 && offset.value < maxValueContainer && index === 0) {
      const widthChange = interpolate(
        offset.value,
        [0, maxValueContainer],
        [widthItem - (size - index) * widthPixel, widthItem],
        {
          extrapolateRight: Extrapolation.CLAMP,
        },
      );
      return {width: widthChange, height: widthChange / TILE_RATIO};
    }
    if (offsetTransItem.value <= 0) {
      const widthChange = interpolate(
        offsetTransItem.value,
        [0, -maxValue],
        [width, selectedIndex + 1 === index ? widthItem : width],
        {
          extrapolateRight: Extrapolation.CLAMP,
        },
      );
      return {width: widthChange, height: widthChange / TILE_RATIO};
    } else if (offsetTransItem.value > 0 && selectedIndex === index) {
      //Pull item
      const widthChange = interpolate(
        offsetTransItem.value,
        [0, maxValue],
        [
          width,
          selectedIndex - 1 === index
            ? widthItem
            : widthItem - (size - index) * widthPixel,
        ],
        {
          extrapolateRight: Extrapolation.CLAMP,
        },
      );
      return {width: widthChange, height: widthChange / TILE_RATIO};
    }
    return {};
  });

  const onPressCTA = async () => {
    const accessToken = await getAuth0AccessToken(dispatch);
    let options = null;
    try {
      options = JSON.parse(item?.options);
    } catch (error) {}
    const linkUrl = await prepareLink(
      item?.ctaLink,
      accessToken,
      options?.ctaLinkType,
    );
    logGASelectPromotion(item);
    openEcomWebview(
      navigation.navigate,
      {
        title: item?.title,
        uri: linkUrl,
        canGoBack: true,
        originUri: item?.ctaLink,
        imageUrl: item?.imageLink,
        clickLocation: 'shop-marquee-tiles',
        origin: 'ShopTileKeyWeb',
        isLogSelectItem: false,
      },
      getEcomProductDetail,
    );
  };

  const logGASelectPromotion = async productItem => {
    if (productItem) {
      const listProductId = [];
      const options =
        typeof productItem?.options === 'object'
          ? productItem?.options
          : JSON.parse(productItem?.options);
      if (options?.ctaLinkType === WEBVIEW_PAGE_TYPE.PDP) {
        try {
          let productId = getProductIdFromUrl(productItem?.ctaLink);
          let productDetails = await getEcomProductDetail(productId);
          if (productDetails) {
            listProductId.push(productId);
          }
        } catch (error) {}
      }
      const tileTitle = productItem?.title;
      if (!isEmpty(listProductId)) {
        const paramGA = {
          promotion_id: productItem?.id,
          promotion_name: productItem?.title,
          creative_name: `${productItem?.ctaText} CTA`,
          creative_slot: 'Shop > Marquee',
          location_id: 'Shop > Marquee',
        };
        const rsLogGA = await GA_selectPromotionWithEcomData({
          listProductId,
          paramGA,
          clickIndex: index + 1,
          tileTitle,
          getMultipleEcomProductDetails,
        });
        if (!rsLogGA) {
          logEventPromotion({
            products: [productItem],
            eventName: GA_EVENT_NAME.SELECT_PROMOTION,
            clickIndex: index + 1,
            tileTitle,
          });
        }
      } else {
        logEventPromotion({
          products: [productItem],
          eventName: GA_EVENT_NAME.SELECT_PROMOTION,
          clickIndex: index + 1,
          tileTitle,
        });
      }
    }
  };

  const onLoad = () => {
    if (focused) {
      playVideo();
    }
    dataVideo?.current?.seek(0.01);
  };

  const onEnd = () => {
    dataVideo?.current?.seek(0.01);
  };

  return (
    <Animated.View
      key={item.title}
      style={[
        {
          position: 'absolute',
          zIndex: index + 1,
          top: indexWhenChange * spaceBetweenItem,
          alignSelf: 'center',
          height: widthItem * TILE_RATIO,
        },
        selectedIndex === -1 && {opacity: 1, transform: [{translateX: 0}]},
        animateActiveStyles,
      ]}
    >
      <Animated.View
        style={[
          styles.viewContainer,
          {backgroundColor: styleCustom?.background || '#fff'},
          animateStyles,
        ]}
      >
        <Animated.View style={[styles.viewImage, animateImageStyles]}>
          {item?.videoLink ? (
            <AnimatedVideo
              source={{uri: item?.videoLink?.replace(/ /g, '%20')}} // Can be a URL or a local file.
              ref={dataVideo} // Store reference
              style={[styles.viewImage, animateImageStyles]}
              paused={pause}
              onLoad={onLoad}
              onEnd={onEnd}
              resizeMode={'cover'}
              ignoreSilentSwitch={'ignore'}
              repeat
            />
          ) : (
            <Animated.Image
              source={{
                uri: item?.imageLink,
              }}
              style={[styles.viewImage, animateImageStyles]}
            />
          )}
        </Animated.View>

        <View
          style={[
            styles.viewContent,
            appStyles.hCenter,
            {backgroundColor: styleCustom?.background || '#fff'},
          ]}
        >
          <TextContent item={item} styleCustom={styleCustom} />
          <Button
            text={item?.ctaText}
            Din79Font
            textColor={styleCustom?.ctaTextColor || '#fff'}
            borderColor={
              styleCustom?.ctaBackgroundColor || 'rgba(0, 34, 66, 1)'
            }
            backgroundColor={
              styleCustom?.ctaBackgroundColor || 'rgba(0, 34, 66, 1)'
            }
            textStyle={[
              appStyles.textCenter,
              {fontWeight: '700', letterSpacing: 1.2, fontSize: 12},
            ]}
            style={{height: 40, minWidth: 180, marginBottom: 24, marginTop: 16}}
            onPress={onPressCTA}
          />
        </View>
      </Animated.View>
    </Animated.View>
  );
};
const styles = StyleSheet.create({
  viewContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 24,
    marginBottom: 24,
    alignSelf: 'center',
    ...appStyles.viewShadow,
  },
  viewImage: {
    width: widthItem,
    height: widthItem / 1.33,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  viewContent: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    borderBottomEndRadius: 24,
    borderBottomStartRadius: 24,
  },
});
export default KeyTilesItem;
