/* eslint-disable react-hooks/exhaustive-deps */
import React, {useState, useEffect} from 'react';
import {
  View,
  Image,
  ScrollView,
  ActivityIndicator,
  Platform,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import appStyles from 'styles/global';
import {GRAY_LIGHT} from 'config';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {connect} from 'react-redux';
import {CLUB_NAME, TYPE_OF_ROW} from 'utils/constant';
import clubSilhouettes from 'json/club-silhouettes';
import Text from 'components/Text';
import {getStatsClub, getStatsClubPutter} from 'requests/accounts';
import CompareClubs from 'components/Compare/CompareClubs';
import GraphPutterPoint from 'components/Graph/GraphPutterPoint';
import FastImage from 'react-native-fast-image/src';
import {SafeAreaView} from 'react-native-safe-area-context';
import {moderateScale} from 'react-native-size-matters';
import IconFeather from 'react-native-vector-icons/Feather';
import Entypo from 'react-native-vector-icons/Entypo';
import LogoTM from 'assets/imgs/WITBTMLogo.png';

const BagCompareDetail = ({navigation, route, user}) => {
  const [dataFirst, setDataFirst] = useState(null);
  const [dataSecond, setDataSecond] = useState(null);
  const [dataPutterFirst, setDataPutterFirst] = useState(null);
  const [dataPutterSecond, setDataPutterSecond] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const clubFirst = route?.params?.itemFirst?.clubFamily?.name;
  const clubSecond = route?.params?.itemSecond?.clubFamily?.name;
  const units = user?.golferProfile?.measurementUnits || 'yards';
  const [inactiveTextWidth, setInactiveTextWidth] = useState(0);
  useEffect(() => {
    if (route?.params) {
      getData();
    }
  }, []);
  const getData = async () => {
    try {
      setIsLoading(true);
      if (clubFirst === CLUB_NAME.PUTTER) await getDataClubsPutter();
      else await getDataClubs();
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };
  const isCompareClub = () => {
    return (
      isGreenOrFairway(clubFirst) ||
      isGreenOrFairway(clubSecond) ||
      (clubFirst === CLUB_NAME.FAIRWAY && clubSecond === CLUB_NAME.FAIRWAY)
    );
  };
  const isGreenOrFairway = name => {
    return name === CLUB_NAME.DRIVER || name === CLUB_NAME.HYBRID;
  };

  const getDataClubs = async () => {
    const paramsFirst = `club=${
      isCompareClub() ? 'fairway' : 'green'
    }&units=${units}`;
    const paramsSecond = `club=${
      isCompareClub() ? 'fairway' : 'green'
    }&units=${units}`;

    const getDataFirst = getStatsClub(route?.params?.itemFirst.id, paramsFirst);
    const getDataSecond = getStatsClub(
      route?.params?.itemSecond.id,
      paramsSecond,
    );
    const arr = await Promise.all([getDataFirst, getDataSecond]);
    setDataFirst(arr[0]);
    setDataSecond(arr[1]);
  };

  const getDataClubsPutter = async () => {
    const getDataFirst = getStatsClubPutter(
      route?.params?.itemFirst.id,
      `units=${units}`,
    );
    const getDataSecond = getStatsClubPutter(
      route?.params?.itemSecond.id,
      `units=${units}`,
    );
    const arr = await Promise.all([getDataFirst, getDataSecond]);
    setDataPutterFirst(arr[0]);
    setDataPutterSecond(arr[1]);
  };

  const getClubImage = (club, clubCategoryId) => {
    const silhouette = clubSilhouettes.find(
      item => item.categoryId === club?.clubFamily?.id,
    )?.image;
    return club?.manufacturer?.name === 'TaylorMade' && club?.imageLarge
      ? {uri: club.imageLarge, cache: FastImage.cacheControl.web}
      : silhouette;
  };

  const renderClubItem = club => {
    return (
      <View
        style={[{width: '50%', height: hp('41%')}, appStyles.hCenter]}
        key={club?.id}
      >
        <View
          style={[
            appStyles.row,
            appStyles.hCenter,
            appStyles.vCenter,
            {width: '100%', paddingTop: Platform.OS === 'ios' ? '5%' : 0},
            !club.inBag
              ? {
                  left: inactiveTextWidth / 2,
                }
              : {},
          ]}
        >
          <Text
            style={[
              !club.inBag ? appStyles.grey : appStyles.black,
              {fontSize: 40},
            ]}
            numberOfLines={1}
            DINbold
          >
            {club.clubFamily.name === CLUB_NAME.IRONS
              ? club?.clubTypeInitial.replace('I', 'i')
              : club?.clubTypeInitial}
          </Text>
          {!club.inBag && (
            <Text
              onLayout={event => {
                let {width} = event?.nativeEvent?.layout;
                setInactiveTextWidth(width);
              }}
              style={{color: '#8C8B8F', fontSize: 13, alignSelf: 'center'}}
            >
              {' '}
              Inactive
            </Text>
          )}
        </View>
        <FastImage
          style={[
            {
              height: hp('24%'),
              width: wp('49%'),
              marginTop: 10,
              marginBottom: 20,
            },
          ]}
          resizeMode={'contain'}
          source={getClubImage(club)}
        />
        {club?.manufacturer?.name === 'TaylorMade' ? (
          <Image
            style={{
              width: moderateScale(87),
              height: moderateScale(17),
              resizeMode: 'contain',
            }}
            source={LogoTM}
          />
        ) : (
          <Text style={[appStyles.mHSm]} numberOfLines={2}>
            {club?.manufacturer && club?.manufacturer?.name}
          </Text>
        )}

        <Text
          style={[
            appStyles.grey,
            appStyles.mHSm,
            {fontSize: 13, marginTop: Platform.OS === 'ios' ? '2%' : 0},
          ]}
          numberOfLines={2}
        >
          {club?.modelName && club?.modelName?.name}
        </Text>
      </View>
    );
  };
  const renderArrow = () => {
    return (
      <View
        style={[appStyles.hCenter, {position: 'absolute', left: 0, right: 0}]}
      >
        <View
          style={{
            height: hp('46%') + 20,
            width: 1,
            backgroundColor: GRAY_LIGHT,
          }}
        />
      </View>
    );
  };
  return (
    <SafeAreaView
      style={[appStyles.flex, appStyles.whiteBg]}
      edges={['top', 'right', 'left']}
    >
      <View
        style={[
          appStyles.row,
          appStyles.hCenter,
          appStyles.spaceBetween,
          {paddingHorizontal: 20},
        ]}
      >
        <TouchableOpacity
          style={[styles.closeIcon, appStyles.vCenter, appStyles.hCenter]}
          onPress={() => navigation.goBack()}
        >
          <Entypo name="chevron-thin-left" size={22} color={'#111111'} />
        </TouchableOpacity>
        <Text style={{paddingVertical: 11, fontSize: 17}} black>
          my_bag.compare.club_comparison
        </Text>
        <TouchableOpacity
          style={[styles.closeIcon, appStyles.vCenter, appStyles.hCenter]}
          onPress={() => navigation.goBack()}
        >
          <IconFeather name="x" size={26} color={'#111111'} />
        </TouchableOpacity>
      </View>
      <ScrollView style={[appStyles.flex]}>
        <View style={[appStyles.flex]}>
          <View
            style={[
              appStyles.row,
              appStyles.whiteBg,
              {
                borderWidth: 1,
                borderColor: GRAY_LIGHT,
                width: '100%',
                paddingTop: 20,
              },
            ]}
          >
            {renderClubItem(route.params.itemFirst)}
            {renderClubItem(route.params.itemSecond)}
            {renderArrow()}
          </View>
          {clubFirst === CLUB_NAME.PUTTER ? (
            <GraphPutterPoint
              measurementUnits={user?.golferProfile?.measurementUnits}
              itemNameFirst={route.params.itemFirst?.modelName?.name}
              itemNameSecond={route.params.itemSecond?.modelName?.name}
              dataPutterFirst={dataPutterFirst}
              dataPutterSecond={dataPutterSecond}
            />
          ) : (
            <View>
              <CompareClubs
                key={TYPE_OF_ROW[0].name}
                titleLeft={dataFirst?.distance?.close}
                titleRight={dataSecond?.distance?.close}
                typeRow={TYPE_OF_ROW[0]}
                user={user}
              />
              <CompareClubs
                key={TYPE_OF_ROW[1].name}
                titleLeft={dataFirst?.shot?.value}
                titleRight={dataSecond?.shot?.value}
                typeRow={TYPE_OF_ROW[1]}
                user={user}
              />
              <CompareClubs
                key={TYPE_OF_ROW[2].name}
                titleLeft={
                  dataFirst?.shot?.value > 0
                    ? dataFirst?.shotsGained?.value
                    : null
                }
                titleRight={
                  dataSecond?.shot?.value > 0
                    ? dataSecond?.shotsGained?.value
                    : null
                }
                typeRow={TYPE_OF_ROW[2]}
                user={user}
              />
              <CompareClubs
                key={
                  isCompareClub() ? TYPE_OF_ROW[3].name : TYPE_OF_ROW[4].name
                }
                titleLeft={
                  dataFirst?.shot?.value > 0
                    ? isCompareClub()
                      ? dataFirst?.fairwayHit?.value
                      : dataFirst?.green?.value
                    : null
                }
                titleRight={
                  dataSecond?.shot?.value > 0
                    ? isCompareClub()
                      ? dataSecond?.fairwayHit?.value
                      : dataSecond?.green?.value
                    : null
                }
                typeRow={isCompareClub() ? TYPE_OF_ROW[3] : TYPE_OF_ROW[4]}
                user={user}
              />
            </View>
          )}
          {isLoading && (
            <ActivityIndicator
              style={appStyles.absoluteFill}
              color="black"
              size="large"
            />
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  iconContainer: {
    width: moderateScale(30),
    height: moderateScale(30),
    justifyContent: 'center',
    alignItems: 'center',
    right: 10,
    position: 'absolute',
  },
  closeIcon: {
    backgroundColor: 'white',
    width: moderateScale(26),
    height: moderateScale(26),
    borderRadius: moderateScale(12),
  },
});
const mapState = state => ({
  user: state.user,
});
export default connect(mapState)(BagCompareDetail);
