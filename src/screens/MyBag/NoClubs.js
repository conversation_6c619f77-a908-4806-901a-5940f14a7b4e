import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Image,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import {t} from 'i18next';

import appStyles from 'styles/global';
import Text from 'components/Text';
import {GREEN_STATS} from 'config';
import IconPlus from 'assets/imgs/ic_plus.png';
import IconTrackYourRound from 'assets/imgs/ic_trackYourRound_NoClubs.svg';
import IconGetAccurate from 'assets/imgs/ic_getAccurate_NoClubs.svg';
import IconSeeStats from 'assets/imgs/ic_seeStats_NoClubs.svg';
import {moderateScale} from 'react-native-size-matters';
import HeaderMyBag from './component/HeaderMyBag';

const NoClubs = ({navigation, route}) => {
  const addNewClub = () => {
    navigation.push('MyBagAdd');
  };
  const renderDescription = (icon, textDes) => {
    return (
      <View
        key={textDes}
        style={[appStyles.row, {alignItems: 'center', marginVertical: 15}]}
      >
        <View style={{paddingRight: 25, paddingLeft: 10}}>{icon}</View>
        <Text
          size={moderateScale(24)}
          style={{color: '#8C8C90', fontWeight: 'bold'}}
        >
          {textDes}
        </Text>
      </View>
    );
  };
  return (
    <View style={[appStyles.flex, {backgroundColor: '#ffffff'}]}>
      <SafeAreaView style={[{backgroundColor: '#ffffff'}]} />
      <HeaderMyBag
        title={t('profile.supporting_copy.settings.my_bag')}
        onPressBack={() => route?.params.backToRoot()}
      />
      <View style={{flex: 1, backgroundColor: '#F5F5F5'}}>
        <ScrollView style={[appStyles.flex]} alwaysBounceVertical={false}>
          <Text style={styles.textTitle} size={moderateScale(17)}>
            my_bag.no_club.cant_play_golf_without_clubs
          </Text>
          <TouchableOpacity
            style={[
              appStyles.viewShadow,
              appStyles.row,
              appStyles.spaceBetween,
              styles.touchAddClub,
            ]}
            onPress={addNewClub}
          >
            <View style={styles.viewAdd}>
              <Image
                style={{
                  width: 16,
                  height: 16,
                  tintColor: '#ffffff',
                }}
                source={IconPlus}
              />
            </View>
            <Text size={moderateScale(13)} style={{color: '#8C8B8F'}}>
              {t('my_bag.no_club.add_clubs_to_your_bag')} 􀄫
            </Text>
          </TouchableOpacity>
          <View style={styles.viewDes}>
            {renderDescription(
              <IconTrackYourRound />,
              t('my_bag.no_club.track_your_rounds'),
            )}
            {renderDescription(
              <IconGetAccurate />,
              t('my_bag.no_club.get_accurate_distances'),
            )}
            {renderDescription(
              <IconSeeStats />,
              t('my_bag.no_club.see_stats_per_club'),
            )}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  textTitle: {
    paddingHorizontal: 25,
    marginTop: 28,
    marginBottom: 20,
    color: '#8C8C90',
  },
  touchAddClub: {
    marginHorizontal: 10,
    backgroundColor: 'white',
    padding: 15,
    alignItems: 'center',
    borderRadius: 8,
  },
  viewAdd: {
    backgroundColor: GREEN_STATS,
    borderRadius: 6,
    width: moderateScale(56),
    height: moderateScale(56),
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewDes: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 25,
    paddingVertical: 40,
    marginTop: 70,
  },
});

export default NoClubs;
