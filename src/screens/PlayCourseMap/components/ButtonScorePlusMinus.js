import React from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';

const ButtonScorePlusMinus = ({
  onPress = () => {},
  isMinus,
  buttonStyle = {},
  buttonSize = 24,
  colorIcon = '#808080',
}) => {
  return (
    <TouchableOpacity
      style={[styles.touchItem, buttonStyle]}
      onPress={onPress}
      activeOpacity={1}
    >
      <Icon
        name={isMinus ? 'minus' : 'plus'}
        size={buttonSize}
        color={colorIcon}
      />
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  touchItem: {
    width: 40,
    height: 40,
    borderRadius: 64,
    borderWidth: 1.5,
    borderColor: '#808080',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
});
export default ButtonScorePlusMinus;
