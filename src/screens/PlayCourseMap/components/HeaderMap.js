import React, {useEffect, useRef} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/Entypo';

import appStyles from 'styles/global';
import Text from 'components/Text';
import {BlurView} from '@react-native-community/blur';
import {getModePlayed} from 'utils/commonVariable';
import {filterListMarker, MODE_ROUND} from '../DataSubmitDefault';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {convertDistanceFromYards} from 'utils/convert';
import {useSelector} from 'react-redux';

const opacity = Platform.OS === 'android' ? '70' : '30';
const HeaderMap = ({
  holes,
  selectHole,
  setSelectHole,
  scoreCardDefault,
  teeSelected,
  roundData,
  listMarkers,
  addShot,
  showEditShot,
  defaultHole,
  onPressOption = () => {},
  onCompleteHole = () => {},
  setSelectOtherHole = () => {},
  onAcceptChange = () => {},
}) => {
  const insets = useSafeAreaInsets();
  const topInset = Platform.OS === 'ios' ? insets?.top : 20;
  const refList = useRef();
  const tempHole = useRef(null);
  const user = useSelector(state => state.user);
  const userDistanceUnit =
    user?.measurementUnits?.toLowerCase?.() === 'meters' ? 'meters' : 'yards';
  useEffect(() => {
    if (refList) {
      setTimeout(
        () =>
          refList.current.scrollToOffset({
            offset: 40 * selectHole + 4.5 * (selectHole - 1),
          }),
        100,
      );
    }
    tempHole.current = roundData.holes[selectHole - 1];
  }, [selectHole]);

  const renderHeaderLabelText = text => {
    return (
      <Text
        size={16}
        style={{
          marginRight: 6,
          lineHeight: 22,
          color: 'rgba(0, 0, 0, 0.5)',
        }}
      >
        {text}
      </Text>
    );
  };

  const renderHeaderValueText = (text, isBold, hasMarginRight) => {
    return (
      <Text
        size={16}
        black
        style={{
          marginRight: hasMarginRight ? 10 : 0,
          fontWeight: isBold ? '700' : 'normal',
          marginTop: Platform.OS === 'android' ? -2 : 2,
        }}
      >
        {text}
      </Text>
    );
  };

  const renderHeaderSelectHole = item => {
    const holeDistanceObj = convertDistanceFromYards({
      distanceInYards: teeSelected?.ydsHole?.[selectHole - 1],
      userUnit: userDistanceUnit,
    });
    return (
      <View style={appStyles.row}>
        {renderHeaderLabelText('Hole')}
        {renderHeaderValueText(item.holeNumber, true, true)}
        {renderHeaderLabelText('Par')}
        {renderHeaderValueText(
          scoreCardDefault?.parHole?.[selectHole - 1],
          true,
          true,
        )}
        {teeSelected?.ydsHole?.[selectHole - 1]
          ? renderHeaderValueText(holeDistanceObj.value, true, false)
          : null}
        {teeSelected?.ydsHole?.[selectHole - 1]
          ? renderHeaderLabelText(holeDistanceObj.unit)
          : null}
      </View>
    );
  };

  const renderScore = (title, score) => {
    return (
      <View style={[appStyles.hCenter, {marginLeft: 10}]}>
        <Text size={13} style={{color: '#ffffff'}}>
          {title}
        </Text>
        <Text size={24} style={{color: '#ffffff', fontWeight: 'bold'}}>
          {score}
        </Text>
      </View>
    );
  };
  const renderItem = ({item, index}) => {
    if (!roundData?.holes[index]) {
      return null;
    }
    const addedScore = roundData.holes[index].completeHole;
    return (
      <TouchableOpacity
        activeOpacity={1}
        style={[
          item.holeNumber === selectHole
            ? {
                ...styles.touchButtonHoleActive,
                backgroundColor: '#ffffff',
              }
            : {
                ...styles.touchButtonHole,
                width: 40,
                backgroundColor: '#ffffff' + opacity,
              },
        ]}
        onPress={() => {
          const listMarkerFilter = filterListMarker(listMarkers);
          if (item.holeNumber !== selectHole) {
            if (getModePlayed() === MODE_ROUND.BASIC) {
              setSelectOtherHole(item.holeNumber);
              return;
            }
            if (
              getModePlayed() === MODE_ROUND.ADVANCED &&
              ((listMarkerFilter && listMarkerFilter[selectHole]?.length > 0) ||
                ((defaultHole[selectHole - 1]?.strokes?.length > 0 ||
                  (tempHole.current &&
                    tempHole.current?.strokes?.length > 0)) &&
                  listMarkerFilter[selectHole]?.length === 0))
            ) {
              if (showEditShot) {
                onAcceptChange(null, selectHole);
              } else {
                onCompleteHole(roundData, false, selectHole);
              }
            }
            setSelectHole(item.holeNumber);
          }
        }}
      >
        {item.holeNumber !== selectHole && (
          <>
            {Platform.OS === 'android' ? (
              <View
                style={[
                  styles.viewBlur,
                  {
                    width: 40,
                    backgroundColor: '#f2f2f2',
                    opacity: 0.2,
                  },
                ]}
              />
            ) : (
              <BlurView
                style={[
                  styles.viewBlur,
                  {
                    width: 40,
                  },
                ]}
                blurType="light"
                blurAmount={10}
                reducedTransparencyFallbackColor="white"
              />
            )}
          </>
        )}
        {item.holeNumber === selectHole ? (
          renderHeaderSelectHole(item)
        ) : (
          <Text
            size={13}
            style={{
              color: '#000000',
              fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
            }}
          >
            {item.holeNumber}
          </Text>
        )}
      </TouchableOpacity>
    );
  };
  return (
    <View
      style={{position: 'absolute', left: 0, right: 0, marginTop: topInset}}
    >
      <FlatList
        data={holes}
        ref={refList}
        keyExtractor={item => item.holeNumber}
        extraData={holes}
        renderItem={renderItem}
        horizontal
        style={{flexGrow: 0}}
        contentContainerStyle={{
          alignItems: 'center',
          paddingLeft: wp(35),
          paddingRight: wp(35),
        }}
        showsHorizontalScrollIndicator={false}
        initialNumToRender={20}
      />
    </View>
  );
};
const styles = StyleSheet.create({
  touchButtonHole: {
    backgroundColor: '#ffffff' + opacity,
    width: 40,
    height: 40,
    borderRadius: 20,
    marginHorizontal: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  touchButtonHoleActive: {
    backgroundColor: '#ffffff',
    borderRadius: 22,
    margin: 2,
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewBlur: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
  },
});
export default HeaderMap;
