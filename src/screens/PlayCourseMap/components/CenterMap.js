import React, {
  useState,
  useEffect,
  useRef,
  useImperativeHandle,
  useMemo,
} from 'react';
import {View, Platform, StyleSheet} from 'react-native';

import appStyles from 'styles/global';
import Text from 'components/Text';
import {BlurView} from '@react-native-community/blur';
import {getDistance} from 'geolib';
import {convertDistanceFromMeters} from 'utils/convert';
import {MODE_ROUND, checkCurrentUserInsiteHole} from '../DataSubmitDefault';
import {getModePlayed} from 'utils/commonVariable';
import * as Animatable from 'react-native-animatable';

import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import {useSelector} from 'react-redux';

const widthBlur = 121;
const heightItem = 105;
const opacity = Platform.OS === 'android' ? '70' : '30';

const CenterMap = ({
  dataCourseDefault,
  currentLocation,
  coordTouch,
  selectHole,
  roundData,
  pointDistance,
  frontBackLocation,
  setDataDistance,
}) => {
  const user = useSelector(state => state.user);
  const userDistanceUnit =
    user?.measurementUnits?.toLowerCase?.() === 'meters' ? 'meters' : 'yards';

  const distanceToFrontOrBack = (isBack = false) => {
    const coord =
      currentLocation?.latitude &&
      checkCurrentUserInsiteHole(currentLocation, dataCourseDefault)
        ? currentLocation
        : dataCourseDefault?.teeCenter?.latitude &&
          checkCurrentUserInsiteHole(
            dataCourseDefault?.teeCenter,
            dataCourseDefault,
          )
        ? dataCourseDefault?.teeCenter
        : null;
    let locationArr = isBack
      ? roundData?.holes[selectHole - 1]?.back_location
      : roundData?.holes[selectHole - 1]?.front_location;
    if (!locationArr) {
      locationArr = isBack
        ? frontBackLocation?.backLocation
        : frontBackLocation?.frontLocation;
    }

    if (coord && locationArr) {
      const front = {latitude: locationArr[0], longitude: locationArr[1]};
      const distanceUser = getDistance(coord, front);
      return distanceUser;
    }
    return null;
  };

  const distanceToPin = () => {
    const coord =
      currentLocation?.latitude &&
      checkCurrentUserInsiteHole(currentLocation, dataCourseDefault)
        ? currentLocation
        : coordTouch?.latitude &&
          checkCurrentUserInsiteHole(coordTouch, dataCourseDefault)
        ? coordTouch
        : null;
    if (coord && dataCourseDefault?.pin) {
      const distanceUser = getDistance(coord, dataCourseDefault.pin);
      return distanceUser;
    }
    if (dataCourseDefault?.distance) {
      return dataCourseDefault.distance;
    }
    return 'maxValue';
  };

  const distanceToBack = useMemo(
    () =>
      convertDistanceFromMeters({
        distanceInMeters: distanceToFrontOrBack(true),
        userUnit: userDistanceUnit,
        canConvertToFeet: true,
      }),
    [
      currentLocation,
      dataCourseDefault,
      roundData,
      selectHole,
      frontBackLocation,
      userDistanceUnit,
    ],
  );

  const distanceUserToPin = useMemo(
    () =>
      convertDistanceFromMeters({
        distanceInMeters: distanceToPin(),
        userUnit: userDistanceUnit,
        canConvertToFeet: true,
      }),
    [currentLocation, coordTouch, dataCourseDefault, userDistanceUnit],
  );

  const distanceToFront = useMemo(
    () =>
      convertDistanceFromMeters({
        distanceInMeters: distanceToFrontOrBack(),
        userUnit: userDistanceUnit,
        canConvertToFeet: true,
      }),
    [
      currentLocation,
      dataCourseDefault,
      roundData,
      selectHole,
      frontBackLocation,
      userDistanceUnit,
    ],
  );

  const distanceFromTeeToPin = useMemo(
    () =>
      convertDistanceFromMeters({
        distanceInMeters: dataCourseDefault?.distance,
        userUnit: userDistanceUnit,
        canConvertToFeet: true,
      }),
    [dataCourseDefault, userDistanceUnit],
  );

  useEffect(() => {
    setDataDistance?.({
      distanceToBack: distanceToBack.value,
      distanceUserToPin: distanceUserToPin.value,
      distanceToFront: distanceToFront.value,
      distanceFromTeeToPin: distanceFromTeeToPin.value,
      distanceUnitAbbr: distanceUserToPin.unit,
    });
  }, [
    distanceToBack,
    distanceUserToPin,
    distanceToFront,
    distanceFromTeeToPin,
  ]);
  return (
    <Animatable.View
      animation={pointDistance ? 'slideOutLeft' : 'slideInLeft'}
      duration={300}
    >
      <View style={[appStyles.row, styles.viewMap]}>
        {getModePlayed() !== MODE_ROUND.MULTIPLAYER && (
          <View style={styles.viewRoundScoreBlur}>
            {Platform.OS === 'android' ? (
              <View style={[styles.viewBlur]} />
            ) : (
              <BlurView
                style={styles.blurViewMapCenter}
                blurType="light"
                blurAmount={10}
                reducedTransparencyFallbackColor="white"
              />
            )}
            <View style={[appStyles.row]}>
              <Text
                Din79Font
                size={16}
                black
                style={{fontWeight: '800', marginBottom: -6, marginLeft: -15}}
              >
                {distanceToBack.value}
              </Text>
            </View>
            <View style={[appStyles.row, appStyles.hCenter]}>
              <Text Din79Font size={34} black style={{fontWeight: '700'}}>
                {distanceUserToPin.value}
              </Text>
              <Text Din79Font size={16} black style={styles.textCenter}>
                {distanceUserToPin?.unit?.toUpperCase?.()}
              </Text>
            </View>
            <View style={[appStyles.row]}>
              <Text
                Din79Font
                size={16}
                black
                style={{fontWeight: '800', marginLeft: -15, marginTop: -3}}
              >
                {distanceToFront.value}
              </Text>
            </View>
          </View>
        )}
      </View>
    </Animatable.View>
  );
};
const styles = StyleSheet.create({
  viewRoundScoreBlur: {
    width: widthBlur,
    height: heightItem,
    alignItems: 'center',
    justifyContent: 'center',
    borderTopRightRadius: 24,
    borderBottomRightRadius: 24,
    overflow: 'hidden',
  },
  textCenter: {
    marginBottom: Platform.OS === 'android' ? 0 : 2,
    marginLeft: 1,
    marginTop: 17,
    fontWeight: '800',
    letterSpacing: 2,
    lineHeight: 23,
  },
  blurViewMapCenter: {
    position: 'absolute',
    width: widthBlur,
    height: heightItem,
    overflow: 'hidden',
  },
  viewMap: {
    position: 'absolute',
    top: hp(42),
    borderBottomRightRadius: 24,
    borderTopRightRadius: 24,
  },
  viewBlur: {
    position: 'absolute',
    width: widthBlur,
    height: heightItem,
    backgroundColor: '#ffffff' + opacity,
  },
});
export default React.forwardRef(CenterMap);
