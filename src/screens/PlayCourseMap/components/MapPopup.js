import React from 'react';
import {View, StyleSheet, TouchableOpacity, Platform} from 'react-native';
import Text from 'components/Text/Text';
import appStyles from 'styles/global';
import {moderateScale} from 'react-native-size-matters';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Button from 'components/Button';

const MapPopup = ({visible, button1Action, button2Action}) => {
  if (!visible) {
    return null;
  }
  return (
    <View
      style={{
        padding: 20,
        ...StyleSheet.absoluteFill,
        backgroundColor: '#00000060',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1,
      }}
    >
      <View style={styles.viewPopup}>
        <Text style={styles.titleMessage}>{'play.finish_round'}</Text>
        <Text
          size={13}
          style={{color: '#8C8B8F', marginTop: moderateScale(20)}}
        >
          {'play.reached_the_end'}
        </Text>
        <Button
          style={[styles.btnCancelRound]}
          textColor="white"
          centered
          textStyle={{...appStyles.bold, fontSize: 13}}
          text={'play.end_round'}
          onPress={button2Action}
        />
        <TouchableOpacity style={styles.buttonBack} onPress={button1Action}>
          <Text style={styles.textBack}>{'play.continue_playing'}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  titleMessage: {
    fontWeigth: 'bold',
    fontSize: 17,
    ...appStyles.bold,
    textAlign: 'center',
    paddingHorizontal: 25,
  },
  viewPopup: {
    backgroundColor: '#ffffff',
    borderRadius: moderateScale(8),
    width: wp(80),
    height: moderateScale(245),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 30,
  },
  btnCancelRound: {
    minWidth: moderateScale(119),
    height: moderateScale(36),
    backgroundColor: '#111111',
    marginTop: moderateScale(20),
    marginBottom: moderateScale(16),
    ...appStyles.viewShadow,
    elevation: 2,
  },
  textBack: {
    color: '#8C8B8F',
    fontSize: 13,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    lineHeight: 13,
  },
  buttonBack: {
    borderBottomWidth: 1,
    borderBottomColor: '#8C8B8F',
  },
});

export default MapPopup;
