import React, {useState, useEffect, useRef, useImperativeHandle} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  FlatList,
  Animated,
} from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';

import appStyles from 'styles/global';
import Text from 'components/Text';
import Modal from 'react-native-modal';
import {t} from 'i18next';
import {MODE_ROUND} from '../DataSubmitDefault';
import {
  useSafeAreaInsets,
  initialWindowMetrics,
} from 'react-native-safe-area-context';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import fontFamily from 'components/Text/styles';
import ScoreInputByModeSection from './ScoreInputSection';
import {BlurView} from '@react-native-community/blur';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {convertDistanceFromYards} from 'utils/convert';
import {useSelector} from 'react-redux';

const opacity = '70';
const listHolePaddingHorizontal = 7;
const holeSize = 40;
const holeMarginRight = 4;
const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

const HoleByHoleScoreInput = (
  {
    isScoreInputVisible,
    roundData,
    setScoreInputVisible = () => {},
    setRoundData = () => {},
    resetRoundData = () => {},
    isEditing,
    selectedHoleFromScoreCard,
  },
  ref,
) => {
  const [mode, setMode] = useState(MODE_ROUND.BASIC);
  const [selectedHoleIndex, setSelectedHoleIndex] = useState(1);
  const [scoreInput, setScoreInput] = useState({});
  const [canShowEndRoundButton, setCanShowEndRoundButton] = useState(false);
  const [canShowContinueButton, setCanShowContinueButton] = useState(false);
  const insets = useSafeAreaInsets();
  const topInset =
    Platform.OS === 'ios' ? insets?.top : initialWindowMetrics?.insets?.top;
  const refList = useRef();
  const [holeSelectWidth, setHoleSelectWidth] = useState(0);
  const user = useSelector(state => state.user);
  const userDistanceUnit =
    user?.measurementUnits?.toLowerCase?.() === 'meters' ? 'meters' : 'yards';

  useImperativeHandle(ref, () => ({
    resetGhinMode: () => {
      setMode(MODE_ROUND.BASIC);
    },
  }));

  useEffect(() => {
    if (isScoreInputVisible) {
      if (selectedHoleFromScoreCard) {
        setSelectedHoleIndex(
          roundData?.holes?.findIndex?.(
            item => item.number === selectedHoleFromScoreCard,
          ) + 1,
        );
      } else {
        setSelectedHoleIndex(1);
      }
    }
  }, [isScoreInputVisible]);

  useEffect(() => {
    if (refList && isScoreInputVisible) {
      //check if user is inputting back-9 holes, then the hole 10 will be the first hole
      const holeIndex = selectedHoleIndex - 1;
      setTimeout(
        () =>
          refList?.current?.scrollToOffset?.({
            offset:
              holeSize * holeIndex +
              holeMarginRight * holeIndex -
              (wp(50) - listHolePaddingHorizontal) +
              holeSelectWidth / 2,
          }),
        100,
      );
    }
  }, [selectedHoleIndex, holeSelectWidth, isScoreInputVisible]);

  useEffect(() => {
    if (roundData?.holes) {
      // Count the number of completed holes
      const completedHoles = roundData.holes.filter(
        holeItem => holeItem.completeHole && holeItem.score > 0,
      ).length;
      if (roundData.holes.length === 18) {
        // Check if all front 9 holes (holes 1–9) are completed
        const frontNineCompleted =
          roundData.holes.slice(0, 9).filter(holeItem => holeItem.completeHole)
            .length === 9;

        // Check if all back 9 holes (holes 10–18) are completed
        const backNineCompleted =
          roundData.holes.slice(9, 18).filter(holeItem => holeItem.completeHole)
            .length === 9;

        // Case 1: Above 9 holes completed but neither front nor back 9 is fully completed
        const case1Invalid =
          completedHoles >= 9 && !frontNineCompleted && !backNineCompleted;

        // Case 2: Less than 9 holes completed
        const case2Invalid = completedHoles <= 8;
        // Set canEndRound = false if conditions are not met
        setCanShowEndRoundButton(!(case1Invalid || case2Invalid));
      } else if (roundData.holes.length === 9) {
        setCanShowEndRoundButton(completedHoles === 9);
      } else {
        setCanShowEndRoundButton(false);
      }

      //Check if user is almost done with the round
      if (
        (completedHoles === roundData.holes.length - 1 &&
          !roundData.holes[selectedHoleIndex - 1]?.completeHole) ||
        completedHoles === roundData.holes.length
      ) {
        setCanShowContinueButton(true);
      } else {
        setCanShowContinueButton(false);
      }
      if (selectedHoleFromScoreCard) {
        setCanShowContinueButton(true);
      }
    }
  }, [roundData?.holes, selectedHoleIndex, selectedHoleFromScoreCard]);

  const onCloseModal = () => {
    if (isEditing) {
      setScoreInputVisible(false);
      return;
    }
    resetRoundData();
    setScoreInputVisible(false);
  };
  const onPressCompleteHole = () => {
    saveScoreAndNextHole();
  };

  const onPressContinue = () => {
    saveScoreAndNextHole();
    setScoreInputVisible(false);
  };
  const onPressEndRound = () => {
    setScoreInputVisible(false);
  };

  const saveScoreAndNextHole = holeNavigate => {
    try {
      const holes = [...roundData?.holes];
      const hole = {...holes[selectedHoleIndex - 1]};
      let dataSubmit = {...roundData};
      if (mode === MODE_ROUND.BASIC) {
        hole.score = scoreInput.score;
        dataSubmit = {...dataSubmit, round_mode: mode};
      } else {
        hole.score = scoreInput.score;
        hole.putts_number = scoreInput.put;
        hole.fw_stats = hole.par > 3 ? scoreInput.fwStats : '';
        hole.gr_stats = scoreInput.grStats;
        hole.bunker_hit = scoreInput.bunkerHit;
        hole.penalty_hit = scoreInput.penaltyHit;
      }
      hole.completeHole = true;
      holes[selectedHoleIndex - 1] = hole;
      let roundScore =
        holes?.reduce((total, holeDt) => total + (holeDt.score || 0), 0) || 0;
      dataSubmit = {
        ...roundData,
        total_score: roundScore,
        round_mode: mode,
        holes,
      };
      setRoundData(dataSubmit);
      if (holes?.length) {
        //navigate to specific hole
        if (holeNavigate) {
          setSelectedHoleIndex(holeNavigate);
        } else if (holes?.length > selectedHoleIndex) {
          setSelectedHoleIndex(selectedHoleIndex + 1);
        } else {
          const backHoleIndex = holes.findIndex(item => !item.score);
          if (backHoleIndex > -1) {
            setSelectedHoleIndex(backHoleIndex + 1);
          }
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const renderHeaderLabelText = text => {
    return (
      <Text
        size={16}
        style={{
          marginRight: 6,
          color: 'rgba(0, 0, 0, 0.5)',
        }}
      >
        {text}
      </Text>
    );
  };

  const renderHeaderValueText = (text, isBold, hasMarginRight) => {
    return (
      <Text
        size={16}
        black
        style={{
          marginRight: hasMarginRight ? 10 : 0,
          fontWeight: isBold ? '700' : 'normal',
          marginTop: Platform.OS === 'android' ? -1 : 0,
        }}
      >
        {text}
      </Text>
    );
  };

  const renderItemHole = ({item, index}) => {
    if (!roundData?.holes[index]) {
      return null;
    }
    return (
      <TouchableOpacity
        activeOpacity={1}
        style={[
          index === selectedHoleIndex - 1
            ? {
                ...styles.touchButtonHoleActive,
                backgroundColor: '#ffffff',
              }
            : {
                ...styles.touchButtonHole,
                width: holeSize,
                backgroundColor: '#ffffff' + opacity,
              },
        ]}
        onPress={() => {
          if (index !== selectedHoleIndex - 1) {
            setSelectedHoleIndex(index + 1);
          }
        }}
        onLayout={e => {
          if (index === selectedHoleIndex - 1) {
            setHoleSelectWidth(e.nativeEvent.layout.width);
          }
        }}
        // disabled={isEditing}
      >
        {index !== selectedHoleIndex - 1 && (
          <>
            {Platform.OS === 'android' ? (
              <View
                style={[
                  styles.viewBlur,
                  {
                    width: holeSize,
                    backgroundColor: '#f2f2f2',
                    opacity: 0.2,
                  },
                ]}
              />
            ) : (
              <BlurView
                style={[
                  styles.viewBlur,
                  {
                    width: holeSize,
                  },
                ]}
                blurType="light"
                blurAmount={10}
                reducedTransparencyFallbackColor="white"
              />
            )}
          </>
        )}
        {index === selectedHoleIndex - 1 ? (
          renderHeaderSelectHole(item)
        ) : (
          <>
            <Text
              size={16}
              style={{
                color: '#000000',
                fontWeight: Platform.OS === 'ios' ? '700' : 'bold',
              }}
              MonoFont
            >
              {item.holeNumber}
            </Text>
            {item.score && (
              <Text
                MonoFont
                black
                size={12}
                style={{position: 'absolute', bottom: 0}}
              >
                •
              </Text>
            )}
          </>
        )}
      </TouchableOpacity>
    );
  };

  const renderListHoleNumber = () => {
    return (
      <View
        style={[
          appStyles.row,
          appStyles.vCenter,
          appStyles.hCenter,
          {
            marginTop: 20,
            marginBottom: 10,
          },
        ]}
      >
        <FlatList
          data={roundData?.holes}
          ref={refList}
          keyExtractor={item => item.holeNumber}
          extraData={roundData?.holes}
          renderItem={renderItemHole}
          horizontal
          style={{flexGrow: 0}}
          contentContainerStyle={{
            alignItems: 'center',
            paddingHorizontal: listHolePaddingHorizontal,
          }}
          showsHorizontalScrollIndicator={false}
          initialNumToRender={20}
        />
      </View>
    );
  };
  const renderHeaderSelectHole = item => {
    const holeDistanceObj = convertDistanceFromYards({
      distanceInYards: item.distance,
      userUnit: userDistanceUnit,
    });
    return (
      <View style={[appStyles.row, {alignItems: 'center'}]}>
        {renderHeaderLabelText('Hole')}
        {renderHeaderValueText(item.holeNumber, true, true)}
        {renderHeaderLabelText('Par')}
        {renderHeaderValueText(item.par, true, true)}
        {renderHeaderLabelText('HCP')}
        {renderHeaderValueText(
          item.stroke_index,
          true,
          item.distance ? true : false,
        )}
        {item.distance
          ? renderHeaderValueText(holeDistanceObj.value, true, false)
          : null}
        {item.distance ? renderHeaderLabelText(holeDistanceObj.unit) : null}
      </View>
    );
  };
  const bottomButtonHeight = insets.bottom > 0 ? 84 : 68;
  const renderBottomButtons = () => {
    return (
      <View style={[styles.buttonContainer, {height: bottomButtonHeight}]}>
        {canShowContinueButton ? (
          <AnimatedTouchableOpacity
            style={[
              styles.buttonCompleteHole,
              {
                backgroundColor: 'black',
              },
            ]}
            onPress={onPressContinue}
          >
            <View style={[appStyles.center]}>
              <Animated.Text
                style={[
                  {
                    fontWeight: 'bold',
                    letterSpacing: 1.5,
                    color: '#fff',
                    fontSize: 12,
                    textTransform: 'uppercase',
                    ...fontFamily.dinNext79,
                  },
                ]}
              >
                {t('play.continue')}
              </Animated.Text>
            </View>
          </AnimatedTouchableOpacity>
        ) : (
          <>
            {canShowEndRoundButton && (
              <AnimatedTouchableOpacity
                style={[
                  styles.buttonCompleteHole,
                  {
                    backgroundColor: '#000',
                    marginRight: 8,
                  },
                ]}
                onPress={onPressEndRound}
              >
                <View style={[appStyles.center]}>
                  <Animated.Text
                    style={[
                      {
                        fontWeight: 'bold',
                        letterSpacing: 1.5,
                        color: '#fff',
                        fontSize: 12,
                        textTransform: 'uppercase',
                        ...fontFamily.dinNext79,
                      },
                    ]}
                  >
                    {t('play.complete_round')}
                  </Animated.Text>
                </View>
              </AnimatedTouchableOpacity>
            )}
            <AnimatedTouchableOpacity
              style={[
                styles.buttonCompleteHole,
                {
                  backgroundColor: '#03a800',
                },
              ]}
              onPress={onPressCompleteHole}
            >
              <View style={[appStyles.center]}>
                <Animated.Text
                  style={[
                    {
                      fontWeight: 'bold',
                      letterSpacing: 1.5,
                      color: '#fff',
                      fontSize: 12,
                      textTransform: 'uppercase',
                      ...fontFamily.dinNext79,
                    },
                  ]}
                >
                  {t('play.complete_hole')}
                </Animated.Text>
              </View>
            </AnimatedTouchableOpacity>
          </>
        )}
      </View>
    );
  };
  return (
    <Modal
      isVisible={isScoreInputVisible}
      style={[appStyles.flex, {backgroundColor: '#EBEBEB', margin: 0}]}
      animationInTiming={300}
      animationOutTiming={300}
      animationIn="slideInRight"
      animationOut="slideOutRight"
    >
      <View style={[appStyles.flex, {paddingBottom: bottomButtonHeight}]}>
        <View
          style={{
            flexDirection: 'row',
            marginTop: topInset + (Platform.OS === 'android' ? 0 : 5),
            marginHorizontal: 16,
          }}
        >
          <TouchableOpacity style={[styles.closeButton]} onPress={onCloseModal}>
            <MaterialIcons name={'arrow-back'} color={'black'} size={22} />
          </TouchableOpacity>
          <Text
            Din79Font
            style={{
              fontWeight: '800',
              fontSize: 22,
              letterSpacing: 1.1,
              textTransform: 'uppercase',
              paddingTop: 2,
              paddingRight: 16,
            }}
            black
            numberOfLines={2}
          >
            {roundData?.courseName}
          </Text>
        </View>
        {renderListHoleNumber()}
        <View style={appStyles.flex}>
          <ScoreInputByModeSection
            selectHole={selectedHoleIndex}
            roundData={roundData}
            setScoreInputData={setScoreInput}
            mode={mode}
            setMode={setMode}
            isInputForUSGA
          />
        </View>
        {renderBottomButtons()}
      </View>
    </Modal>
  );
};
const styles = StyleSheet.create({
  closeButton: {
    backgroundColor: '#fff',
    width: 25,
    height: 25,
    borderRadius: 25,
    marginRight: 10,
    marginTop: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonCompleteHole: {
    backgroundColor: '#03a800',
    borderRadius: 46,
    height: holeSize,
    flex: 1,
  },
  touchButtonHole: {
    backgroundColor: '#ffffff' + opacity,
    width: holeSize,
    height: holeSize,
    borderRadius: 20,
    marginRight: holeMarginRight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  touchButtonHoleActive: {
    backgroundColor: '#ffffff',
    borderRadius: 22,
    height: holeSize,
    marginRight: holeMarginRight,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    paddingTop: 8,
    position: 'absolute',
    width: '100%',
    alignSelf: 'center',
    bottom: 0,
    backgroundColor: '#EBEBEB',
  },
});
export default React.forwardRef(HoleByHoleScoreInput);
