import React from 'react';
import {
  View,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Text from 'components/Text';
import {moderateScale} from 'react-native-size-matters';
import {t} from 'i18next';
import appStyles from 'styles/global';
import Button from 'components/Button';

const CancelRoundModal = ({visible, backdropPress, onCancelRound}) => {
  const windowHeight = Dimensions.get('window').height;

  return (
    <Modal
      style={[appStyles.hCenter, appStyles.vCenter]}
      isVisible={visible}
      onBackdropPress={backdropPress}
      animationIn="fadeIn"
      animationOut="fadeOut"
      deviceHeight={windowHeight}
      backdropOpacity={0.5}
      deviceWidth={wp('100%')}
    >
      <View style={styles.viewPopup}>
        <Text style={styles.titleMessage}>
          {t('play.cancel_round.confirm_message')}
        </Text>
        <Button
          style={[styles.btnCancelRound]}
          textColor="white"
          centered
          textStyle={{...appStyles.bold, fontSize: 13}}
          text={t('play.cancel_round')}
          onPress={() => {
            backdropPress();
            onCancelRound();
          }}
        />
        <TouchableOpacity
          style={styles.buttonBack}
          onPress={() => backdropPress()}
        >
          <Text style={styles.textBack}>{t('play.cancel_round.back')}</Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};
const styles = StyleSheet.create({
  titleMessage: {
    fontWeigth: '700',
    fontSize: 17,
    ...appStyles.bold,
    textAlign: 'center',
    paddingHorizontal: 25,
  },
  viewPopup: {
    backgroundColor: '#ffffff',
    borderRadius: moderateScale(8),
    width: wp(80),
    height: moderateScale(245),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 30,
  },
  btnCancelRound: {
    width: moderateScale(138),
    height: moderateScale(36),
    backgroundColor: '#111111',
    marginTop: moderateScale(20),
    marginBottom: moderateScale(16),
  },
  textBack: {
    color: '#8C8B8F',
    fontSize: 13,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    lineHeight: 13,
  },
  buttonBack: {
    borderBottomWidth: 1,
    borderBottomColor: '#8C8B8F',
  },
});
export default CancelRoundModal;
