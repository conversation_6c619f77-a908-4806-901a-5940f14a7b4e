import React, {useState, useEffect} from 'react';
import {View, StyleSheet, TouchableOpacity, Platform} from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';

import appStyles from 'styles/global';
import Text from 'components/Text';
import Modal from 'react-native-modal';
import {t} from 'i18next';
import {getModePlayed, setModePlayed} from 'utils/commonVariable';
import {
  formatRoundScore,
  getTotalScore,
  MODE_ROUND,
  TYPE_FAIRWAY,
  TYPE_GREEN,
} from '../DataSubmitDefault';
import {
  useSafeAreaInsets,
  initialWindowMetrics,
} from 'react-native-safe-area-context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  Extrapolation,
  runOnJS,
} from 'react-native-reanimated';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import fontFamily from 'components/Text/styles';
import ScoreInputByModeSection from './ScoreInputSection';
import {convertDistanceFromYards} from 'utils/convert';
import {useSelector} from 'react-redux';

const widthComplete = wp(96);
const widthRoundScore = wp(100);

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

const ScoreInputModal = ({
  isScoreInputVisible,
  selectHole,
  teeSelected,
  roundData,
  setScoreInputVisible = () => {},
  setRoundData = () => {},
  onCompleteHole = () => {},
}) => {
  const [mode, setMode] = useState(getModePlayed());
  const [openRoundScore, setOpenRoundScore] = useState(false);
  const [scoreInput, setScoreInput] = useState({
    score:
      roundData?.holes?.[selectHole - 1]?.score > 0
        ? roundData?.holes?.[selectHole - 1]?.score
        : roundData?.holes?.[selectHole - 1]?.par,
    put: 2,
    fwStats: TYPE_FAIRWAY.HIT,
    grStats: TYPE_GREEN.HIT,
    bunkerHit: false,
    penaltyHit: false,
    roundScore: formatRoundScore(roundData?.holes),
  });

  const user = useSelector(state => state.user);
  const userDistanceUnit =
    user?.measurementUnits?.toLowerCase?.() === 'meters' ? 'meters' : 'yards';

  const insets = useSafeAreaInsets();
  const topInset =
    Platform.OS === 'ios' ? insets?.top : initialWindowMetrics?.insets?.top;

  const offsetButton = useSharedValue(0);

  const animatedButtonCompleteStyles = useAnimatedStyle(() => {
    const height = interpolate(offsetButton.value, [0, 1], [40, 150], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const bottom = interpolate(offsetButton.value, [0, 1], [34, 0], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const borderRadius = interpolate(offsetButton.value, [0, 1], [46, 0], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const width = interpolate(
      offsetButton.value,
      [0, 1],
      [widthComplete, widthRoundScore],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    return {
      height,
      bottom,
      borderBottomEndRadius: borderRadius,
      borderBottomStartRadius: borderRadius,
      width,
    };
  }, [offsetButton]);

  const animatedTexttStyles = useAnimatedStyle(() => {
    const fontSize = interpolate(offsetButton.value, [0, 1], [12, 54], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      fontSize,
    };
  });

  const onCloseModal = () => {
    const hole = roundData?.holes?.[selectHole - 1];
    const isUpdate = hole.score > 0;
    const score =
      roundData?.holes?.[selectHole - 1]?.score > 0
        ? roundData?.holes?.[selectHole - 1]?.score
        : roundData?.holes?.[selectHole - 1]?.par;
    setScoreInput({
      score: score,
      put: isUpdate ? hole.putts_number : 2,
      fwStats: isUpdate ? hole.fw_stats : TYPE_FAIRWAY.HIT,
      grStats: isUpdate ? hole.gr_stats : TYPE_GREEN.HIT,
      bunkerHit: isUpdate ? hole.bunker_hit : false,
      penaltyHit: isUpdate ? hole.penalty_hit : false,
      roundScore: formatRoundScore(roundData?.holes),
    });
    setScoreInputVisible(false);
  };
  const onPress = () => {
    setOpenRoundScore(true);
    offsetButton.value = withTiming(
      1,
      {
        duration: 300,
      },
      () => {
        runOnJS(saveScoreAndDismissModal)();
      },
    );
  };

  const saveScoreAndDismissModal = () => {
    try {
      const holes = [...roundData.holes];
      const hole = {...holes[selectHole - 1]};
      let dataSubmit = {...roundData};
      if (mode === MODE_ROUND.BASIC) {
        hole.score = scoreInput.score;
        dataSubmit = {...dataSubmit, round_mode: mode};
      } else {
        hole.score = scoreInput.score;
        hole.putts_number = scoreInput.put;
        hole.fw_stats =
          roundData?.holes?.[selectHole - 1]?.par > 3 ? scoreInput.fwStats : '';
        hole.gr_stats = scoreInput.grStats;
        hole.bunker_hit = scoreInput.bunkerHit;
        hole.penalty_hit = scoreInput.penaltyHit;
      }
      hole.completeHole = true;
      holes[selectHole - 1] = hole;
      dataSubmit = {
        ...roundData,
        round_mode: mode,
        holes,
        total_score: getTotalScore(holes),
      };
      setRoundData(dataSubmit, selectHole);
      setScoreInputVisible();
      onCompleteHole(dataSubmit);
      //set Mode Played only when submitting hole
      setModePlayed(mode);
    } catch (error) {
      console.log(error.message);
    }
  };

  const renderHeaderLabelText = text => {
    return (
      <Text
        size={16}
        style={{
          marginRight: 6,
          lineHeight: 22,
          color: 'rgba(0, 0, 0, 0.5)',
        }}
      >
        {text}
      </Text>
    );
  };
  const renderHeaderValueText = (text, isBold, hasMarginRight) => {
    return (
      <Text
        size={16}
        black
        style={{
          marginRight: hasMarginRight ? 14 : 0,
          fontWeight: isBold ? '700' : 'normal',
          marginTop: Platform.OS === 'android' ? -2 : 2,
        }}
      >
        {text}
      </Text>
    );
  };

  const renderButton = () => {
    return (
      <AnimatedTouchableOpacity
        style={[
          styles.viewRoundScore,
          {
            position: openRoundScore ? 'absolute' : 'relative',
            backgroundColor:
              !openRoundScore && roundData.holes[selectHole - 1].score > 0
                ? '#000'
                : '#03a800',
          },
          animatedButtonCompleteStyles,
        ]}
        disabled={openRoundScore}
        onPress={onPress}
      >
        <View style={[appStyles.center]}>
          {openRoundScore && (
            <Text
              white
              size={22}
              Din79Font
              style={{
                fontWeight: '800',
              }}
            >
              play.round_score
            </Text>
          )}
          <Animated.Text
            style={[
              {
                fontWeight: 'bold',
                letterSpacing: 1.5,
                color: '#fff',
                fontSize: 12,
                ...fontFamily.dinNext79,
              },
              animatedTexttStyles,
            ]}
          >
            {openRoundScore
              ? scoreInput.roundScore
              : roundData.holes[selectHole - 1].score > 0
              ? t('play.save_hole_score')
              : t('play.complete_hole')}
          </Animated.Text>
        </View>
      </AnimatedTouchableOpacity>
    );
  };
  const holeDistanceObj = convertDistanceFromYards({
    distanceInYards: teeSelected?.ydsHole?.[selectHole - 1],
    userUnit: userDistanceUnit,
  });
  return (
    <Modal
      isVisible={isScoreInputVisible}
      style={[appStyles.flex, {backgroundColor: '#EBEBEB', margin: 0}]}
      animationInTiming={300}
      animationOutTiming={300}
      onModalHide={() => {
        setOpenRoundScore(false);
        offsetButton.value = 0;
      }}
    >
      <View style={appStyles.flex}>
        <View
          style={[
            appStyles.row,
            appStyles.vCenter,
            appStyles.hCenter,
            {
              marginTop: topInset + (Platform.OS === 'android' ? 0 : 5),
              marginHorizontal: 10,
              marginBottom: 10,
            },
          ]}
        >
          <View
            style={[
              appStyles.row,
              appStyles.flex,
              {
                justifyContent: 'space-between',
                marginLeft: 8,
              },
            ]}
          >
            <View
              style={[
                appStyles.row,
                {
                  backgroundColor: '#fff',
                  paddingHorizontal: 16,
                  paddingVertical: 10,
                  borderRadius: 22,
                },
              ]}
            >
              {renderHeaderLabelText('Hole')}
              {renderHeaderValueText(selectHole, true, true)}
              {renderHeaderLabelText('Par')}
              {renderHeaderValueText(
                roundData?.holes?.[selectHole - 1]?.par,
                true,
                true,
              )}
              {renderHeaderLabelText('HCP')}
              {renderHeaderValueText(
                roundData?.holes?.[selectHole - 1]?.stroke_index,
                true,
                true,
              )}
              {teeSelected?.ydsHole?.[selectHole - 1]
                ? renderHeaderValueText(holeDistanceObj.value, true, false)
                : null}
              {teeSelected?.ydsHole?.[selectHole - 1]
                ? renderHeaderLabelText(holeDistanceObj.unit)
                : null}
            </View>
            <View>
              <TouchableOpacity
                style={[styles.touchButtonHole]}
                onPress={onCloseModal}
              >
                <Icon
                  name="close"
                  size={20}
                  color={'#111111'}
                  style={{fontWeight: 'bold'}}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <View style={appStyles.flex}>
          <ScoreInputByModeSection
            selectHole={selectHole}
            roundData={roundData}
            setScoreInputData={setScoreInput}
            mode={mode}
            setMode={setMode}
          />
        </View>
        {renderButton()}
      </View>
    </Modal>
  );
};
const styles = StyleSheet.create({
  touchButtonHole: {
    backgroundColor: '#fff',
    width: 25,
    height: 25,
    borderRadius: 25,
    marginRight: 10,
    marginTop: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewRoundScore: {
    backgroundColor: '#03a800',
    borderRadius: 46,
    position: 'absolute',
    width: '100%',
    alignSelf: 'center',
    bottom: 34,
    marginTop: 40,
  },
});
export default ScoreInputModal;
