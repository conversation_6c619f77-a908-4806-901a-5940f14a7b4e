import React from 'react';

import {Polyline} from 'react-native-maps';
import {checkCurrentUserInsiteHole, checkIsPutter} from '../DataSubmitDefault';

const LineShot = ({
  data,
  pointDistance,
  isFinder,
  currentLocation,
  listMarkers,
  selectHole,
  selectPin,
  selectMarker,
  isFocusGreen,
  pin,
}) => {
  //polyline distance
  if (pin && pointDistance && isFinder) {
    let coordinate = checkCurrentUserInsiteHole(currentLocation, data)
      ? currentLocation
      : listMarkers &&
        listMarkers[selectHole][listMarkers[selectHole]?.length - 1]?.coordinate
      ? listMarkers[selectHole][listMarkers[selectHole]?.length - 1]?.coordinate
      : data.teeCenter;
    const coordinates = [pin, pointDistance, coordinate];
    return (
      <Polyline
        identifier={'distancePolyline'} //Define in ios and android
        key={'distancePolyline'}
        coordinates={coordinates}
        strokeWidth={2}
        strokeColor={'white'}
        zIndex={2}
      />
    );
  }
  if (listMarkers && listMarkers[selectHole] && pin) {
    let markers = listMarkers[selectHole];
    //check show marker putts club
    if (!isFocusGreen) {
      markers = markers.filter(
        _val => !checkIsPutter(_val.club?.clubFamily?.name),
      );
    }
    let listCoords = [];
    let temp = [];
    // format data if marker is penalty => [[marker1, marker2],[marker3, marker4]]
    for (let i = 0; i < markers?.length; i++) {
      if (markers[i].club.id === 'penalty') {
        temp = [...temp, markers[i]?.coordinate];
        listCoords = [...listCoords, temp];
        temp = [];
      } else {
        temp = [...temp, markers[i]?.coordinate];
      }
      if (i === markers?.length - 1 && markers[i].club.id !== 'penalty') {
        temp = [...temp, pin];
        listCoords = [...listCoords, temp];
      }
    }
    if (listCoords?.length > 0) {
      return listCoords.map((_coords, index) => {
        //get index when select marker to draw at native
        const indexOfLine = selectMarker
          ? _coords.findIndex(
              _val =>
                listMarkers[selectHole][selectMarker.index]?.coordinate
                  ?.latitude === _val.latitude &&
                listMarkers[selectHole][selectMarker.index]?.coordinate
                  ?.longitude === _val.longitude,
            )
          : -1;
        return (
          <Polyline
            identifier={
              selectPin &&
              markers[markers?.length - 1]?.club?.id !== 'penalty' &&
              index === listCoords?.length - 1
                ? _coords?.length - 1 + ''
                : indexOfLine > -1
                ? `${indexOfLine}`
                : '-1'
            }
            key={'PolylineMarker_' + index}
            nativeID={'PolylineMarker_' + index}
            coordinates={_coords}
            strokeWidth={2}
            strokeColor={'white'}
            zIndex={2}
          />
        );
      });
    }
    return null;
  }
  return null;
};

export default LineShot;
