import React, {useEffect, useState, useRef} from 'react';
import {Image, View, StyleSheet, Platform} from 'react-native';
import Carousel from 'react-native-snap-carousel';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {SafeAreaView} from 'react-native-safe-area-context';
import DeviceInfo from 'react-native-device-info';
import ProgressBar from 'react-native-progress/Bar';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Header from 'components/Header';

const isTablet = DeviceInfo.isTablet();

import appStyles from 'styles/global';

import feedStory from '../../json/homeFeedStory.json';
import FastImage from 'react-native-fast-image/src';

const FeedStories = ({navigation, route}) => {
  const [stories, setStories] = useState([]);
  const [storyCount, setStoryCount] = useState();
  const [tileIndex, setTileIndex] = useState(0);
  let [count, setCount] = useState(0);

  useEffect(() => {
    // TODO: Get stories from API (waiting for endpoint)
    setStories(feedStory.stories);
    setStoryCount(feedStory.stories.length);
  }, []);

  const renderItem = ({item}) => {
    return (
      <View>
        <FastImage
          style={[appStyles.full]}
          source={{
            uri: item.image,
            cache: FastImage.cacheControl.web,
          }}
        />
      </View>
    );
  };

  const setIndexes = index => {
    setTileIndex(index);
    setCount(0);
  };

  function useInterval(callback, delay) {
    const savedCallback = useRef();

    useEffect(() => {
      savedCallback.current = callback;
    }, [callback]);

    useEffect(() => {
      function tick() {
        savedCallback.current();
      }
      if (delay !== null) {
        let id = setInterval(tick, delay);
        return () => clearInterval(id);
      }
    }, [delay]);
  }

  useInterval(() => {
    if (count < 1.01) {
      setCount(count + 0.0225);
    }
  }, 100);

  return (
    <SafeAreaView
      style={[appStyles.center, appStyles.blackBg]}
      edges={['right', 'top', 'left']}
    >
      <FocusAwareStatusBar barStyle={'light-content'} />

      <View
        style={[
          styles.controlContainer,
          isTablet ? styles.tabletTop : styles.mobileTop,
        ]}
      >
        <View style={[appStyles.topLeft, appStyles.fullWidth]}>
          <View style={[appStyles.row, appStyles.alignCenter, appStyles.pHSm]}>
            {stories.map((x, i) => {
              return (
                <View key={i} style={[styles.tileIndicator]}>
                  <ProgressBar
                    progress={tileIndex === i ? count : 0}
                    animated={false}
                    width={null}
                    unfilledColor={
                      tileIndex > i
                        ? 'rgba(255, 255, 255, 1)'
                        : 'rgba(255, 255, 255, 0.5)'
                    }
                    color={'rgba(255, 255, 255, 2)'}
                    borderWidth={0}
                    height={hp('0.3%')}
                    borderRadius={0}
                    animationType={'timing'}
                  />
                </View>
              );
            })}
          </View>
        </View>
      </View>
      <View
        style={[
          styles.closeButtonContainer,
          isTablet ? styles.tabletCloseTop : styles.mobileCloseTop,
        ]}
      >
        <Header isClose close={() => navigation.goBack()} />
      </View>

      <Carousel
        data={stories}
        renderItem={renderItem}
        sliderWidth={wp('100%')}
        itemWidth={wp('100%')}
        layout={'stack'}
        autoplay={true}
        lockScrollWhileSnapping={true}
        autoplayInterval={5000}
        autoplayDelay={0}
        onSnapToItem={index => setIndexes(index)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  controlContainer: {
    position: 'absolute',
    width: '100%',
    height: '1%',
    zIndex: 2,
  },
  tabletTop: {
    ...Platform.select({
      ios: {
        top: hp('3.5%'),
      },
      android: {
        top: hp('4%'),
      },
    }),
  },
  mobileTop: {
    ...Platform.select({
      ios: {
        top: hp('7%'),
      },
      android: {
        top: hp('2%'),
      },
    }),
  },
  closeButtonContainer: {
    position: 'absolute',
    right: hp('2%'),
    top: hp('3%'),
    zIndex: 2,
  },
  tabletCloseTop: {
    ...Platform.select({
      ios: {
        top: hp('5%'),
      },
      android: {
        top: hp('3%'),
      },
    }),
  },
  mobileCloseTop: {
    ...Platform.select({
      ios: {
        top: hp('8.5%'),
      },
      android: {
        top: hp('3%'),
      },
    }),
  },
  tileIndicator: {
    flex: 1,
    height: hp('0.3%'),
    marginLeft: 2,
    marginRight: 2,
  },
});

export default FeedStories;
