import React from 'react';
import {View, Text, StyleSheet} from 'react-native';

const HashtagList = ({hashtags, light, style}) => {
  return (
    <View style={[styles.hashtagContainer, style]}>
      {hashtags.map((tag, index) => (
        <View style={styles.hashtagView}>
          <Text key={index} style={[styles.hashtag, light && {color: 'white'}]}>
            #{tag}
          </Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  hashtagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap', // This will make the hashtags wrap to the next line if they exceed the width
    marginVertical: 16,
    marginHorizontal: 8,
  },
  hashtagView: {
    borderWidth: 1.5,
    borderColor: '#a9a9a9',
    margin: 4,
    borderRadius: 24,
  },
  hashtag: {
    alignSelf: 'flex-start', // This will make each hashtag start a new line if it doesn't fit in the current line
    paddingHorizontal: 16,
    paddingVertical: 8, // Add some margin between hashtags
    fontSize: 12,
    fontWeight: '400',
    color: 'black',
  },
});

export default HashtagList;
