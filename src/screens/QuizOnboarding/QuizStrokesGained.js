import React, {useState, useEffect, useRef} from 'react';
import {Platform, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import Selector from 'components/Selector';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';

import appStyles from 'styles/global';
import {GREY} from 'config';
import {VALUE_STROKES_GAINED_PROFILE} from 'utils/constant';
import {showToast} from 'utils/toast';

let ignoreOnChange = true;

const QuizStrokesGained = ({navigation, route, updateQuiz}) => {
  const fromGameProfile = route.params?.origin === 'Setting';
  const valueStrokesOnAndroid = VALUE_STROKES_GAINED_PROFILE.find(
    item => item.value === route.params?.strokesGainedBaseline,
  )?.valueAndroid;
  const [strokesGained, setStrokesGained] = useState(
    Platform.OS === 'ios'
      ? route.params?.strokesGainedBaseline
      : valueStrokesOnAndroid,
  );
  const [loading, setLoading] = useState(false);
  const sheetRef = useRef(null);

  const text =
    Platform.OS === 'ios'
      ? VALUE_STROKES_GAINED_PROFILE.find(item => item.value === strokesGained)
          ?.name || 'Select'
      : VALUE_STROKES_GAINED_PROFILE.find(
          item => item.valueAndroid === strokesGained,
        )?.name || 'Select';

  // selector will call onChange when init (library error), so we need to ignore first call by firstSecond value
  useEffect(() => {
    setTimeout(() => {
      ignoreOnChange = false;
    }, 1000);
    return () => {
      ignoreOnChange = true;
    };
  }, []);

  const onSelectorChange = value => {
    if (!ignoreOnChange) {
      setStrokesGained(value);
    }
  };

  const saveBaseline = async () => {
    try {
      const convertValue =
        Platform.OS === 'ios'
          ? strokesGained
          : VALUE_STROKES_GAINED_PROFILE.find(
              item => item.valueAndroid === strokesGained,
            )?.value;
      route?.params?.setStrokesGainedBaseline(convertValue);
      navigation.goBack();
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: 'Baseline updated FAIL!',
      });
    }
  };

  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                quiz.strokes_gained_baseline.tell_us_your_strokes_gained
              </Text>
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={100}>
              <Button
                textColor={strokesGained ? 'white' : GREY}
                borderColor={GREY}
                onPress={() => sheetRef.current?.snapTo(0)}
                text={text}
                rightIcon="chevron-down"
                disabled={loading}
              />
            </Animatable.View>
          </View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              text={fromGameProfile ? 'common.update' : 'common.next'}
              backgroundColor={strokesGained ? 'white' : GREY}
              disabled={!strokesGained || loading}
              onPress={saveBaseline}
              loading={loading}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="stroke"
        value={
          Platform.OS === 'ios' ? strokesGained : parseFloat(strokesGained)
        }
        options={VALUE_STROKES_GAINED_PROFILE}
        onChange={onSelectorChange}
        onCloseEnd={() =>
          setStrokesGained(
            strokesGained
              ? strokesGained
              : Platform.OS === 'ios'
              ? VALUE_STROKES_GAINED_PROFILE[0].value
              : VALUE_STROKES_GAINED_PROFILE[0].valueAndroid,
          )
        }
        origin={'StrokesUpdate'}
      />
    </>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizStrokesGained);
