import React, {useState, useEffect} from 'react';
import {View, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import BackButton from 'components/BackButton';

const DrawImg = require('assets/imgs/Draw.png');
const FadeImg = require('assets/imgs/Fade.png');
const HookImg = require('assets/imgs/Hook.png');
const SliceImg = require('assets/imgs/Slice.png');
const StraightImg = require('assets/imgs/Straight.png');
const DefaultImg = require('assets/imgs/Default.png');

const activeImage = {
  DrawImg,
  FadeImg,
  HookImg,
  SliceImg,
  StraightImg,
  DefaultImg,
};

const QuizShotShape = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const fromGameProfile = route.params?.origin === 'Setting';
  const shotShapeProfile = route.params?.shotShape;
  const [shotShape, setShotShape] = useState(
    fromGameProfile
      ? shotShapeProfile
      : user.golferProfile?.shotShape || quiz.shotShape || 'Straight',
  );
  const shotShapeImgProfile =
    shotShapeProfile === 'Draw'
      ? activeImage.FadeImg
      : shotShapeProfile === 'Straight'
      ? activeImage.StraightImg
      : shotShapeProfile === 'Fade'
      ? activeImage.DrawImg
      : shotShapeProfile === 'Hook'
      ? activeImage.SliceImg
      : shotShapeProfile === 'Slice'
      ? activeImage.HookImg
      : activeImage.DefaultImg;
  const [shotShapeImg, setShotShapeImg] = useState(
    fromGameProfile
      ? shotShapeImgProfile
      : user.golferProfile?.shotShape
      ? activeImage[`${user.golferProfile?.shotShape || quiz.shotShape}Img`]
      : StraightImg,
  );
  const [loading, setLoading] = useState(false);
  const validated = !isEmpty(shotShape || '');

  useEffect(() => {
    // Render exit button if onboarding is being edited
    if (user.onboardingComplete && !fromGameProfile) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() =>
              route.params?.origin === 'home'
                ? navigation.navigate('App')
                : navigation.navigate('Profile')
            }
          />
        ),
      });
    }
    if (fromGameProfile) {
      navigation.setOptions({
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
      });
    }
  }, []);

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return shotShape?.includes(type) ? 'white' : GREY;
      case 'border':
        return shotShape?.includes(type) ? GREEN : GREY;
      case 'background':
        return shotShape?.includes(type) ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const updateUserShopShape = async () => {
    if (fromGameProfile) {
      route.params?.setShotShape(shotShape);
      navigation.goBack();
    } else {
      // Update quiz in redux
      updateQuiz({shotShape});
      // Update user in backend if this is an edit
      navigation.navigate('QuizBallStrike', {
        origin:
          route.params?.origin === 'home'
            ? 'home'
            : route.params?.origin
            ? 'profile'
            : null,
        isEdit,
      });
    }
  };

  const setShotShapeInfo = value => {
    setShotShape(value);
    if (user.golferProfile?.handed === 'right' || quiz.handed === 'right') {
      setShotShapeImg(activeImage[`${value}Img`]);
    } else {
      switch (value) {
        case 'Draw':
          setShotShapeImg(activeImage.FadeImg);
          break;
        case 'Straight':
          setShotShapeImg(activeImage.StraightImg);
          break;
        case 'Fade':
          setShotShapeImg(activeImage.DrawImg);
          break;
        case 'Hook':
          setShotShapeImg(activeImage.SliceImg);
          break;
        case 'Slice':
          setShotShapeImg(activeImage.HookImg);
      }
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View
        style={[
          appStyles.flex,
          appStyles.mTSm,
          appStyles.pHSm,
          appStyles.spaceBetween,
        ]}
      >
        <Animatable.View animation="fadeInUp">
          <Text
            style={[
              appStyles.sm,
              appStyles.white,
              appStyles.textCenter,
              appStyles.mBMd,
            ]}
          >
            quiz.shot_shape.headline
          </Text>
        </Animatable.View>
        <Animatable.View animation="fadeIn" delay={100}>
          <Image
            style={[
              appStyles.alignCenter,
              appStyles.mBXxs,
              appStyles.responsiveImageBallShape,
            ]}
            source={shotShapeImg}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={200}>
          <View style={(appStyles.flex, appStyles.wrap)}>
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.shot_shape.supporting_copy.draw"
              textColor={getButtonActiveColor('Draw', 'text')}
              borderColor={getButtonActiveColor('Draw', 'border')}
              backgroundColor={getButtonActiveColor('Draw', 'background')}
              onPress={() => setShotShapeInfo('Draw')}
              disabled={loading}
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.shot_shape.supporting_copy.straight"
              textColor={getButtonActiveColor('Straight', 'text')}
              borderColor={getButtonActiveColor('Straight', 'border')}
              backgroundColor={getButtonActiveColor('Straight', 'background')}
              onPress={() => setShotShapeInfo('Straight')}
              disabled={loading}
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.shot_shape.supporting_copy.fade"
              textColor={getButtonActiveColor('Fade', 'text')}
              borderColor={getButtonActiveColor('Fade', 'border')}
              backgroundColor={getButtonActiveColor('Fade', 'background')}
              onPress={() => setShotShapeInfo('Fade')}
              disabled={loading}
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.shot_shape.supporting_copy.hook"
              textColor={getButtonActiveColor('Hook', 'text')}
              borderColor={getButtonActiveColor('Hook', 'border')}
              backgroundColor={getButtonActiveColor('Hook', 'background')}
              onPress={() => setShotShapeInfo('Hook')}
              disabled={loading}
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.shot_shape.supporting_copy.slice"
              textColor={getButtonActiveColor('Slice', 'text')}
              borderColor={getButtonActiveColor('Slice', 'border')}
              backgroundColor={getButtonActiveColor('Slice', 'background')}
              onPress={() => setShotShapeInfo('Slice')}
              disabled={loading}
            />
          </View>
        </Animatable.View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text={fromGameProfile ? 'common.update' : 'common.next'}
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            onPress={updateUserShopShape}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizShotShape);
