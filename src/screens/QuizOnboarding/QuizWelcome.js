import React, {useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector, useDispatch} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Logo from 'components/Logo';
import Text from 'components/Text';
import Button from 'components/Button';
import AsyncStorage from '@react-native-async-storage/async-storage';

import appStyles from 'styles/global';
import analytics from '@react-native-firebase/analytics';
import {QUIZ_WELCOME, SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';
import {refreshConfigureFeatures} from 'utils/configureFeatures';

const QuizWelcome = ({navigation: {navigate}, user}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
  const dispatch = useDispatch();
  useEffect(() => {
    refreshConfigureFeatures(dispatch, user, showHideFeatures);
  }, []);

  const checkQuizOnboardingStatus = () => {
    AsyncStorage.setItem(QUIZ_WELCOME, 'true');

    const status = user.onboardingCompleteSteps;
    analytics().logEvent('user_onboarding_start', {
      screen_name: `onboarding - you're in`,
      screen_type: SCREEN_TYPES.ONBOARDING,
      page_name: `onboarding - you're in`,
      page_type: SCREEN_TYPES.ONBOARDING,
      page_category: SCREEN_CLASS.SIGNUP,
    });
    // Check status of onboarding items and navigate to a specific screen
    if (!quiz.country && showHideFeatures?.data?.COUNTRY_SELECTION) {
      navigate('QuizRegion');
    } else if (
      !status.firstName &&
      !status.lastName &&
      !quiz.firstName &&
      !quiz.lastName
    ) {
      navigate('QuizName');
    } else if (!status.gender && !quiz.gender) {
      navigate('QuizGender');
    } else if (!status.dob && !quiz.dob) {
      navigate('QuizBirthday');
    }
    // else if (
    //   !status.handicapPreference &&
    //   !status.averageScoreRange &&
    //   !quiz.handicap
    // ) {
    //   navigate('QuizHandicap');
    // }
    else if (!status.homeCourse && !quiz.homeCourse) {
      navigate('QuizGolfCourse', {
        fromOnboarding: true,
      });
    } else if (!status.timePlayingGolf && !quiz.yearsOfExperience) {
      navigate('QuizYearsOfExperience');
    } else {
      navigate('QuizName');
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.pHMd]}>
        <View style={[appStyles.flex, appStyles.vCenter]}>
          <Animatable.View animation="fadeInUp">
            <Logo style={{bottom: hp('7%')}} intro />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Text style={[appStyles.white, appStyles.xxl]} DINbold>
              quiz.welcome.headline_welcome
            </Text>
            <Text
              style={[appStyles.white, appStyles.mBSm, appStyles.xxl]}
              DINbold
            >
              quiz.welcome.headline_you_are_in
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Text style={[appStyles.white, appStyles.smm]}>
              quiz.welcome.get_to_know_you_better
            </Text>
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="quiz.welcome.cta"
            textColor="black"
            backgroundColor="white"
            borderColor="white"
            onPress={checkQuizOnboardingStatus}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  user: state.user,
});

export default connect(mapStateToProps, null)(QuizWelcome);
