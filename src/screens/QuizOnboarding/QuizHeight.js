import React, {useState, useRef, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Selector from 'components/Selector';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {GREY} from 'config';
import {showToast} from 'utils/toast';
import {convertHeight} from 'utils/convert';
import {t} from 'i18next';

const QuizHeight = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const [height, setHeight] = useState(
    user.golferProfile?.height || quiz.height || null,
  );
  const [loading, setLoading] = useState(false);
  const sheetRef = useRef(null);

  useEffect(() => {
    // Render exit button if onboarding is being edited
    if (user.onboardingComplete) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() => navigation.goBack()}
          />
        ),
      });
    }

    if (route.params?.origin === 'settings') {
      navigation.setOptions({
        headerLeft: () => null,
      });
    }
  }, []);

  const updateUserHeight = async () => {
    // Update quiz in redux
    updateQuiz({height});
    // Update user in backend if this is an edit
    if (isEdit) {
      setLoading(true);

      try {
        // Make request to update user's height
        const updatedUser = await updateUser({
          height,
        });
        // Update user in redux
        addCurrentUser(updatedUser);
        // Stop loading state and navigate to next screen
        setLoading(false);
      } catch (error) {
        setLoading(false);
        return showToast({
          type: 'error',
          message: t('An_error_occurred_updating_your_height'),
        });
      }
    }

    if (route.params?.origin === 'settings') {
      navigation.navigate('Settings');
    } else {
      navigation.navigate('QuizHanded');
    }
  };

  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                quiz.ht.headline
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={100}>
              <Button
                textColor={height ? 'white' : GREY}
                borderColor={GREY}
                onPress={() => sheetRef.current?.snapTo(0)}
                text={convertHeight('centimetersToText', height)}
                rightIcon="chevron-down"
                disabled={loading}
              />
            </Animatable.View>
          </View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              text={
                route.params?.origin === 'settings'
                  ? t('common.update')
                  : t('common.next')
              }
              backgroundColor="white"
              disabled={loading}
              onPress={updateUserHeight}
              loading={loading}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="height"
        value={height}
        onChange={setHeight}
      />
    </>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizHeight);
