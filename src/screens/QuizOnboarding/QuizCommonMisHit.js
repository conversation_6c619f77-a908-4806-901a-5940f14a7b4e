import React, {useState} from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import analytics from '@react-native-firebase/analytics';

import appStyles from 'styles/global';
import Button from 'components/Button';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import {GREY, GREEN} from 'config';
import {SCREEN_TYPES} from 'utils/constant';

const QuizCommonMisHit = ({navigation, route}) => {
  const {params} = route;
  const planContentBatch = useSelector(state => state?.plans?.planContentBatch);
  const {shotMistake} = planContentBatch;
  const [commonMisHitValue, setCommonMisHitValue] = useState(null);

  const handleButtonCommonHit = () => {
    return shotMistake?.map(item => (
      <Button
        style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
        textStyle={[styles.text]}
        text={item.name}
        borderColor={getButtonActiveColor(item.id, 'border')}
        backgroundColor={getButtonActiveColor(item.id, 'background')}
        onPress={() => setCommonMisHitValue(item.id)}
        centered
      />
    ));
  };

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'border':
        return commonMisHitValue === type ? GREEN : GREY;
      case 'background':
        return commonMisHitValue === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const GA_OnBoarding_MissHit = async () => {
    try {
      let commonMisHitName = shotMistake.find(
        item => item.id === commonMisHitValue,
      ).name;
      params.commonMisHitName = commonMisHitName;
      await analytics().logEvent('coaching_onboarding_mis_hit', {
        mis_hit: commonMisHitName,
        app_screen_name: 'mis-hit', //e.g home, my orders
        screen_type: SCREEN_TYPES.ONBOARDING, //e.g checkout, pdp, plp, account
        page_name: 'mis-hit', //e.g home, my orders
        page_type: SCREEN_TYPES.ONBOARDING, //e.g basket, home, order
        page_category: SCREEN_TYPES.ONBOARDING,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  const goToPuttingMisHitQuiz = () => {
    params.typicalShotMistakeId = commonMisHitValue;
    GA_OnBoarding_MissHit();
    navigation.navigate('Quiz', {
      screen: 'QuizPuttingMisHit',
      params: params,
    });
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View
        style={[
          appStyles.flex,
          appStyles.mTSm,
          appStyles.pHSm,
          appStyles.spaceBetween,
        ]}
      >
        <Animatable.View animation="fadeInUp">
          <Text
            style={[
              appStyles.sm,
              appStyles.white,
              appStyles.textCenter,
              appStyles.mBMd,
            ]}
          >
            quiz.ball_miss.headline
          </Text>
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={200}>
          <ScrollView style={{height: hp(60)}}>
            {handleButtonCommonHit()}
          </ScrollView>
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text={'common.next'}
            backgroundColor={!commonMisHitValue ? GREY : 'white'}
            onPress={goToPuttingMisHitQuiz}
            disabled={!commonMisHitValue}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  text: {
    textAlign: 'left',
    color: 'white',
  },
});

export default QuizCommonMisHit;
