import React, {useState, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import BackButton from 'components/BackButton';

const QuizStrength = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const fromGameProfile = route.params?.origin === 'Setting';
  const strongestAreaProfile = route.params?.strongestArea
    ? route.params?.strongestArea?.split(', ')
    : [];
  const [strengths, setStrengths] = useState(
    fromGameProfile
      ? strongestAreaProfile
      : user.golferProfile?.strongestArea?.split(', ') ||
          quiz.strengths?.split(', ') ||
          [],
  );
  const [loading, setLoading] = useState(false);
  const validated = strengths?.length;

  // useEffect(() => {
  //   // Render exit button if onboarding is being edited
  //   if (user.onboardingComplete && isEdit) {
  //     navigation.setOptions({
  //       headerRight: () => (
  //         <HeaderRightButton text="Exit" onPress={() => navigation.goBack()} />
  //       ),
  //     });
  //   }
  // }, []);
  useEffect(() => {
    if (route.params?.origin === 'Setting') {
      navigation.setOptions({
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
      });
    }
  }, []);

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return strengths?.includes(type) ? 'white' : GREY;
      case 'border':
        return strengths?.includes(type) ? GREEN : GREY;
      case 'background':
        return strengths?.includes(type) ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const updateUserStrengths = async () => {
    if (fromGameProfile) {
      route.params?.setStrongestArea(strengths.join(', '));
      navigation.goBack();
    } else {
      // Update quiz in redux
      updateQuiz({strengths: strengths.join(', ')});
      // Update user in backend if this is an edit
      if (isEdit) {
        setLoading(true);

        try {
          // Make request to update user's strengths
          const updatedUser = await updateUser({
            strongestArea: strengths.join(', '),
          });
          // Update user in redux
          addCurrentUser(updatedUser);
          // Stop loading state and navigate to next screen
          setLoading(false);
        } catch (error) {
          setLoading(false);
          return showToast({
            type: 'error',
            message: t('An_error_occurred_updating_your_strengths'),
          });
        }
      }
    }

    navigation.navigate('QuizWeakness');
  };

  const updateStrengths = strength => {
    // Check to see if incoming strength exists
    const hasStrength = strengths?.includes(strength);
    if (hasStrength) {
      // Remove strength for deselect
      setStrengths(values => values.filter(value => value !== strength));
    } else {
      // Add strength for select
      setStrengths(values => [...values, strength]);
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text style={[appStyles.sm, appStyles.white, appStyles.textCenter]}>
              {`${t('quiz.strength.what_is_the')} `}
              <Text style={[appStyles.bold]}>quiz.strength.strongest</Text>
              {` ${t('quiz.strength.part_of_your_game')}`}
            </Text>
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              quiz.strength.select_all_that_apply
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.strength.supporting_copy.driving"
              textColor={getButtonActiveColor('Driving', 'text')}
              borderColor={getButtonActiveColor('Driving', 'border')}
              backgroundColor={getButtonActiveColor('Driving', 'background')}
              onPress={() => updateStrengths('Driving')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.strength.supporting_copy.approach"
              textColor={getButtonActiveColor('Approach', 'text')}
              borderColor={getButtonActiveColor('Approach', 'border')}
              backgroundColor={getButtonActiveColor('Approach', 'background')}
              onPress={() => updateStrengths('Approach')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={300}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.strength.supporting_copy.around_the_green"
              textColor={getButtonActiveColor('Around the Green', 'text')}
              borderColor={getButtonActiveColor('Around the Green', 'border')}
              backgroundColor={getButtonActiveColor(
                'Around the Green',
                'background',
              )}
              onPress={() => updateStrengths('Around the Green')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.strength.supporting_copy.putting"
              textColor={getButtonActiveColor('Putting', 'text')}
              borderColor={getButtonActiveColor('Putting', 'border')}
              backgroundColor={getButtonActiveColor('Putting', 'background')}
              onPress={() => updateStrengths('Putting')}
              disabled={loading}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={500}>
          <Button
            text={fromGameProfile ? 'common.update' : 'common.next'}
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            onPress={updateUserStrengths}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizStrength);
