import React, {useState, useRef, useEffect} from 'react';
import {View, Platform} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';
import Slider from 'components/Slider';
import appStyles from 'styles/global';
import {GREY} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import Selector from 'components/Selector';
import BackButton from 'components/BackButton';
import analytics from '@react-native-firebase/analytics';
import {SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';

const QuizYearsOfExperience = ({addCurrentUser, navigation, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const options = [
    '0 - 1 Years',
    '2 - 3 Years',
    '4 - 5 Years',
    '6 - 7 Years',
    '8 - 9 Years',
    '10+ Years',
  ];
  const [yearsOfExperience, setYearsOfExperience] = useState(
    user.golferProfile?.timePlayingGolf?.value ||
      quiz.yearsOfExperience ||
      options[0],
  );
  const [loading, setLoading] = useState(false);
  const validated = !isEmpty(yearsOfExperience || '');
  const sheetRef = useRef(null);
  const [rounds, setRounds] = useState(
    user.golferProfile?.roundsPerMonth || quiz.roundsPerMonth || 1,
  );

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <BackButton
          color="#fff"
          onPress={() => navigation.navigate('QuizGolfCourse')}
          disabled={loading}
        />
      ),
    });
  }, [loading]);

  const GA_logOnboardingExperience = (years, roundsPerMonth) => {
    try {
      analytics().logEvent('user_onboarding_experience', {
        years_playing: years, // e.g. 0-1 years
        rounds_per_month: (roundsPerMonth || 0) + '', // e.g. 0-1, 2-3, 4-5, 6-7, 8-9, 10+
        screen_name: `onboarding - golf frequency`,
        screen_type: SCREEN_TYPES.ONBOARDING,
        page_name: `onboarding - golf frequency`,
        page_type: SCREEN_TYPES.ONBOARDING,
        page_category: SCREEN_CLASS.SIGNUP,
      });
      analytics().setUserProperty(
        'rounds_per_month',
        (roundsPerMonth || 0) + '',
      );
      analytics().setUserProperty('years_playing', years);
    } catch (error) {
      console.log(error);
    }
  };

  const GA_logCompleteOnboarding = () => {
    try {
      analytics().logEvent('user_onboarding_completed', {
        screen_name: `onboarding - golf frequency`,
        screen_type: SCREEN_TYPES.ONBOARDING,
        page_name: `onboarding - golf frequency`,
        page_type: SCREEN_TYPES.ONBOARDING,
        page_category: SCREEN_CLASS.SIGNUP,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const completeOnboarding = async () => {
    setLoading(true);
    try {
      // Update quiz in redux
      updateQuiz({yearsOfExperience, roundsPerMonth: rounds});
      GA_logOnboardingExperience(yearsOfExperience, rounds);
      // Make request to update user
      const updatedUser = await updateUser({
        firstName: quiz.firstName,
        lastName: quiz.lastName,
        gender: quiz.gender,
        dob: quiz.dob,
        handicapPreference: quiz.handicap ? 0 : 1,
        userInputHandicap: quiz.handicap,
        averageScoreRange: quiz.averageScore || '86 - 90',
        homeCourse: quiz.homeCourse,
        timePlayingGolf: yearsOfExperience,
        strokesGainedBaseline: quiz.strokesGainedBaseline,
        rpm: rounds,
        onboardingCompleteSteps: {
          firstName: true,
          lastName: true,
          gender: true,
          dob: true,
          handicapPreference: quiz.handicap ? true : false,
          userInputHandicap: quiz.handicap ? true : false,
          averageScoreRange: quiz.averageScore ? true : false,
          homeCourse: true,
          timePlayingGolf: true,
          rpmComplete: true,
        },
        onboardingComplete: true,
        signUpByDevice: Platform.OS,
      });
      GA_logCompleteOnboarding();
      // Stop loading state and navigate to next screen
      setLoading(false);
      // Update user in redux
      addCurrentUser(updatedUser);
    } catch (error) {
      setLoading(false);
      return showToast({
        type: 'error',
        message: t('An_error_occurred_with_your_onboarding'),
      });
    }
    if (user.myTMSubscriptionLevel) {
      navigation.replace('App', {screen: 'Home'});
    } else {
    }
  };

  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTMd, appStyles.pHSm]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                quiz.freq_years.headline
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={100}>
              <Button
                textColor={'white'}
                borderColor={GREY}
                onPress={() => sheetRef.current?.snapTo(0)}
                text={yearsOfExperience}
                rightIcon="chevron-down"
                disabled={loading}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={200}>
              <Text
                style={[
                  appStyles.white,
                  appStyles.textCenter,
                  appStyles.mVMd,
                  appStyles.mTXl,
                ]}
              >
                quiz.freq_rounds.headline
              </Text>
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={300}>
              <Slider
                min={1}
                max={31}
                minimumTrackTintColor="#ffffff"
                maximumTrackTintColor="#ffffff80"
                value={rounds}
                onValueChange={value => setRounds(Math.round(value))}
                disabled={loading}
              />
            </Animatable.View>
          </View>

          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              text={
                user.myTMSubscriptionLevel
                  ? 'go_to_home'
                  : 'quiz.cta.choose_a_plan'
              }
              backgroundColor={validated ? 'white' : GREY}
              disabled={!validated || loading}
              onPress={completeOnboarding}
              loading={loading}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="text"
        value={yearsOfExperience}
        onChange={setYearsOfExperience}
        options={options}
      />
    </>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizYearsOfExperience);
