import React, {useState, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Slider from 'components/Slider';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';

import appStyles from 'styles/global';
import BackButton from 'components/BackButton';

const QuizRounds = ({navigation, route}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const fromGameProfile = route.params?.origin === 'Setting';
  const [rounds, setRounds] = useState(
    fromGameProfile
      ? route.params?.roundsPerMonth
      : user.golferProfile?.roundsPerMonth || quiz.roundsPerMonth || 1,
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Render exit button if onboarding is being edited
    navigation.setOptions({
      headerRight: () =>
        user.onboardingComplete && !fromGameProfile ? (
          <HeaderRightButton
            text="common.exit"
            onPress={() =>
              route.params?.origin === 'home'
                ? navigation.navigate('App')
                : navigation.navigate('Profile')
            }
          />
        ) : (
          <View />
        ),
      headerLeft: () => (
        <BackButton color="white" onPress={() => navigation.goBack()} />
      ),
    });
  }, []);

  const updateUserRoundsPerMonth = async () => {
    route.params?.setRoundsPerMonth(rounds);
    navigation.goBack();
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              quiz.freq_rounds.headline
            </Text>
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={100}>
            <Slider
              min={1}
              max={31}
              value={rounds}
              onValueChange={value => setRounds(Math.round(value))}
              disabled={loading}
              minimumTrackTintColor="#ffffff"
              maximumTrackTintColor="#ffffff80"
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={200}>
          <Button
            text={fromGameProfile ? 'common.update' : 'common.next'}
            backgroundColor="white"
            disabled={loading}
            onPress={updateUserRoundsPerMonth}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizRounds);
