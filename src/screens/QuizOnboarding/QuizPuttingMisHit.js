import React, {useState, useRef, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import analytics from '@react-native-firebase/analytics';

import appStyles from 'styles/global';
import Button from 'components/Button';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Selector from 'components/Selector';
import {GREY} from 'config';
import {
  NOT_APPLICABLE,
  PUTTER_SHOT_MISTAKE,
  SCREEN_TYPES,
} from 'utils/constant';
import {
  convertGenderToCharacter,
  transformTargetScoreToPlayerGoalName,
  transformTargetScoreToPlayerGolfScoreName,
  transformHandicapToAverageScore,
} from 'utils/plans';
import {
  averageImprovement,
  handicapImprovement,
  setInitializedData,
} from 'requests/swing-index';
import {removeSpecialCharacters} from 'utils/validate';
import {showToast} from 'utils/toast';
import {t} from 'i18next';

const QuizPuttingMisHit = ({navigation, route}) => {
  const {
    desiredScore,
    desiredHandicap,
    typicalShotMistakeId,
    desiredScoreLabel,
    commonMisHitName,
    desiredHandicapLabel,
    currentHandicap,
    currentScore,
  } = route?.params;
  // console.log('desiredScoreLabel', desiredScoreLabel);
  // console.log('desiredHandicapLabel', desiredHandicapLabel);
  const sheetRef = useRef(null);
  const [putterMistakeValue, setPutterMistakeValue] = useState('None');
  const user = useSelector(state => state.user);
  const planContentBatch = useSelector(state => state?.plans?.planContentBatch);
  const {playerGolfScore, playerGoal} = planContentBatch;

  const [playerGoalId, setPlayerGoalId] = useState(null);
  const [playerGolfScoreId, setPlayerGolfScoreId] = useState(null);
  const [loading, setLoading] = useState(false);
  const gender = convertGenderToCharacter(user.gender);
  const defaultMistakeValue = 11;

  const timePlayer = user?.golferProfile?.timePlayingGolf?.value;

  const handleTextPutterMistake = () => {
    let text = 'Select';
    if (!isNaN(putterMistakeValue)) {
      PUTTER_SHOT_MISTAKE.forEach(item => {
        if (item.value === putterMistakeValue) {
          text = item.name;
        }
      });
    }
    return text;
  };

  const goToUploadSwing = async () => {
    const initializedData = {
      typicalShotMistakeId: typicalShotMistakeId,
      typicalPutterMistakeId: putterMistakeValue,
      playerGoalId: playerGoalId,
      firstName: removeSpecialCharacters(user?.firstName),
      genderCode: gender,
      playerGolfScoreId: playerGolfScoreId,
      lastName: removeSpecialCharacters(user?.lastName),
    };
    try {
      setLoading(true);
      GA_Coaching_Onboarding_Complete(putterMistakeValue);
      await setInitializedData(initializedData);
      if (
        currentHandicap !== null &&
        desiredHandicap !== null &&
        currentHandicap > desiredHandicap
      ) {
        await handicapImprovement(currentHandicap, desiredHandicap);
      } else if (
        currentScore !== null &&
        desiredScore !== null &&
        currentScore > desiredScore
      ) {
        await averageImprovement(currentScore, desiredScore);
      } else {
        await handicapImprovement(20, 15);
      }
      setLoading(false);
      navigation.navigate('PlanTabs');
    } catch (error) {
      setLoading(false);
      if (typeof error === 'string' || error instanceof String) {
        showToast({
          type: 'error',
          subText: error,
        });
      } else {
        showToast({
          type: 'error',
          subText: t('native_coach.message.retrieve_data_fail'),
        });
      }
      throw error;
    }
  };

  const getPlayerGoalAndGolfScoreId = () => {
    let typicalName = '';
    let playerGoalName = '';
    if (currentHandicap === null && currentScore !== null) {
      typicalName = transformTargetScoreToPlayerGolfScoreName(
        currentScore,
        timePlayer,
      );
      setPlayerGolfScoreId(
        playerGolfScore?.find(element => element.name === typicalName)?.id || 5,
      );
      playerGoalName = transformTargetScoreToPlayerGoalName(desiredScore);
      setPlayerGoalId(
        playerGoal?.find(element => element.name === playerGoalName)?.id || 3,
      );
    } else {
      let scoreConverted = transformHandicapToAverageScore(currentHandicap);
      typicalName = transformTargetScoreToPlayerGolfScoreName(
        scoreConverted,
        timePlayer,
      );
      setPlayerGolfScoreId(
        playerGolfScore?.find(element => element.name === typicalName)?.id || 5,
      );
      playerGoalName = transformTargetScoreToPlayerGoalName(desiredScore);
      setPlayerGoalId(
        playerGoal?.find(element => element.name === playerGoalName)?.id || 3,
      );
    }
  };

  const GA_Coaching_Onboarding_Complete = async putterMistakeVal => {
    try {
      let putterMistakeName =
        PUTTER_SHOT_MISTAKE.find(item => item.value === putterMistakeVal)
          ?.name || NOT_APPLICABLE;
      await analytics().logEvent('coaching_onboarding_complete', {
        putting_help: putterMistakeName,
        mis_hit: commonMisHitName || NOT_APPLICABLE,
        target_handicap: desiredHandicapLabel || NOT_APPLICABLE,
        target_score: desiredScoreLabel || NOT_APPLICABLE,
        app_screen_name: 'coach - goals', //e.g home, my orders
        screen_type: SCREEN_TYPES.COACH, //e.g checkout, pdp, plp, account
        page_name: 'coach - goals', //e.g home, my orders
        page_type: SCREEN_TYPES.COACH, //e.g basket, home, order
        page_category: SCREEN_TYPES.COACH,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  const GA_Putting_Miss_Hit = async putterMistakeVal => {
    try {
      let putterMistakeName =
        PUTTER_SHOT_MISTAKE.find(item => item.value === putterMistakeVal)
          ?.name || NOT_APPLICABLE;
      await analytics().logEvent('coaching_onboarding_putting', {
        putting_help: putterMistakeName,
        app_screen_name: 'putting mis-hit', //e.g home, my orders
        screen_type: SCREEN_TYPES.ONBOARDING, //e.g checkout, pdp, plp, account
        page_name: 'putting mis-hit', //e.g home, my orders
        page_type: SCREEN_TYPES.ONBOARDING, //e.g basket, home, order
        page_category: SCREEN_TYPES.ONBOARDING,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    getPlayerGoalAndGolfScoreId();
  }, []);

  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                putting.mis_hit.title
              </Text>
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={100}>
              <Button
                textColor={putterMistakeValue ? 'white' : GREY}
                borderColor={GREY}
                onPress={() => sheetRef.current?.snapTo(0)}
                text={handleTextPutterMistake()}
                rightIcon="chevron-down"
              />
            </Animatable.View>
          </View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              text={'finish_coach_onboarding'}
              backgroundColor={isNaN(putterMistakeValue) ? GREY : 'white'}
              onPress={goToUploadSwing}
              disabled={isNaN(putterMistakeValue)}
              loading={loading}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="putterMistake"
        options={PUTTER_SHOT_MISTAKE}
        value={putterMistakeValue}
        onChange={value => setPutterMistakeValue(value)}
        onCloseEnd={() => {
          setPutterMistakeValue(
            !isNaN(putterMistakeValue)
              ? putterMistakeValue
              : defaultMistakeValue,
          );
          GA_Putting_Miss_Hit(putterMistakeValue);
        }}
      />
    </>
  );
};

export default QuizPuttingMisHit;
