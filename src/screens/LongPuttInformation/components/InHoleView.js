import React from 'react';
import {View, StyleSheet, TouchableOpacity, Platform} from 'react-native';
import Text from 'components/Text';
const InHoleView = ({numberInHole, inHoleAction, style}) => {
  const renderHoles = () => {
    const holes = [];
    for (let i = 0; i < 5; i++) {
      holes.push(
        <View
          key={i}
          style={[styles.hole, i < numberInHole ? styles.blackHole : {}]}
        />,
      );
    }

    return <View style={styles.holes}>{holes}</View>;
  };

  return (
    <TouchableOpacity style={[styles.container, style]} onPress={inHoleAction}>
      <Text style={styles.textInHole} DINbold>
        IN_HOLE
      </Text>
      {renderHoles()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    flexDirection: 'row',
    marginTop: 10,
    paddingVertical: 15,
    paddingHorizontal: 25,
    backgroundColor: '#F2F2F2',
    borderRadius: 25,
  },
  textInHole: {
    fontSize: 18,
    color: 'black',
    marginTop: Platform.OS === 'ios' ? 3 : 0,
  },
  holes: {
    justifyContent: 'center',
    flexDirection: 'row',
    marginLeft: 10,
  },

  hole: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'white',
    marginLeft: 5,
  },
  blackHole: {
    backgroundColor: 'black',
  },
});

export default InHoleView;
