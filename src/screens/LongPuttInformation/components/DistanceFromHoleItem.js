import React from 'react';
import appStyles from '../../../styles/global';
import {View, StyleSheet} from 'react-native';
import Text from '../../../components/Text';

const DistanceFromHoleItem = ({text, value, style, key}) => {
  return (
    <View
      key={key}
      style={[
        [
          appStyles.flex,
          appStyles.row,
          appStyles.fullWidth,
          style ? style : {},
        ],
      ]}
    >
      <View style={[appStyles.boxText, {width: '60%'}]}>
        <Text style={[appStyles.bold, styles.text]}>{text}</Text>
      </View>
      <View style={[appStyles.boxText, {width: '40%'}]}>
        <Text style={[appStyles.bold, styles.text]}>{value}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  boxText: {
    paddingVertical: 7,
  },
  text: {
    fontSize: 12,
    paddingVertical: 7,
  },
});

export default DistanceFromHoleItem;
