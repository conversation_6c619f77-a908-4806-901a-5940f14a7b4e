import React from 'react';
import {View, StyleSheet} from 'react-native';
import Text from 'components/Text';
import CircleChart from 'components/CircleChart/CircleChart';
import CircleChartSmall from 'components/CircleChart/CircleChartSmall';
import { GRAY_BACKGROUND } from 'config';
const DrivingClassicStats = ({overallStatsData}) => {
  return (
    <View style={{marginVertical: 24, alignItems: 'center'}}>
      <Text Din79Font size={16} style={styles.statsTitle} black>
        scoreStats.title.driving
      </Text>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: 16,
        }}
      >
        <Text size={12} style={{color: 'rgba(0, 0, 0, 0.5)', marginRight: 10}}>
          scoreStats.title.fairways_hit
        </Text>
        <CircleChart
          value={Math.round(
            overallStatsData?.shortGame?.driving?.classicDriving || 0,
          )}
          color={'rgba(3, 168, 0, 1)'}
          textBelow={`${
            overallStatsData?.shortGame?.driving?.totalFairwayHit || 0
          }/${
            overallStatsData?.shortGame?.driving
              ?.totalFairwayHitOpportunities || 0
          }`}
          backgroundColor={'rgba(235, 235, 235, 1)'}
        />
        <View style={{justifyContent: 'space-between', marginLeft: 8}}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              flex: 1,
            }}
          >
            <CircleChartSmall
              value={Math.round(overallStatsData?.fwMissedLeftPercent || 0)}
              color={'rgba(255, 0, 0, 1)'}
              backgroundColor={'rgba(235, 235, 235, 1)'}
            />
            <Text
              style={{
                marginHorizontal: 10,
                color: 'rgba(0, 0, 0, 0.5)',
              }}
              size={12}
            >
              scoreStats.title.miss_left
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              flex: 1,
            }}
          >
            <CircleChartSmall
              value={Math.round(overallStatsData?.fwMissedRightPercent || 0)}
              color={'rgba(255, 0, 0, 1)'}
              backgroundColor={'rgba(235, 235, 235, 1)'}
            />
            <Text
              style={{
                marginHorizontal: 10,
                color: 'rgba(0, 0, 0, 0.5)',
              }}
              size={12}
            >
              scoreStats.title.miss_right
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  statsTitle: {fontWeight: '800', letterSpacing: 2.08},
});

export default DrivingClassicStats;
