import React from 'react';
import {View} from 'react-native';
import Text from 'components/Text';
import Circle<PERSON>hart from 'components/CircleChart/CircleChart';
import { GRAY_BACKGROUND } from 'config';
import CircleChartSmall from 'components/CircleChart/CircleChartSmall';
const ShortGameClassicStats = ({overallStatsData, isHorizontal = false}) => {
  return (
    <View style={{alignItems: 'center', flex: 1, marginVertical: 24}}>
      <Text
        Din79Font
        style={{fontWeight: '800', marginBottom: 13, letterSpacing: 2.08}}
        size={16}
        black
      >
        scoreStats.title.short_game
      </Text>
      <View
        style={{
          flexDirection: isHorizontal ? 'row' : 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 60
          }}
        >
          <CircleChart
            value={Math.round(overallStatsData?.shortGame?.scramble || 0)}
            color={'rgba(0,65,105,255)'}
            textBelow={`${
              overallStatsData?.shortGame?.totalHoleMisGirPutt || 0
            }/${overallStatsData?.shortGame?.totalHoleMisGir || 0}`}
            backgroundColor={'rgba(235, 235, 235, 1)'}
          />
          <Text
            style={{
              color: 'rgba(0, 0, 0, 0.5)',
              marginTop: isHorizontal ? 0 : 8,
              marginRight: isHorizontal ? 8 : 0,
            }}
            size={12}
          >
            scoreStats.title.scramble
          </Text>
        </View>
        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            marginLeft: isHorizontal ? 15 : 0,
            marginTop: isHorizontal ? 0 : 16,
          }}
        >
          <CircleChart
            value={Math.round(overallStatsData?.classicStatsSandSaves || 0)}
            color={'rgba(0,65,105,255)'}
            backgroundColor={'rgba(235, 235, 235, 1)'}
            textBelow={`${overallStatsData?.sand || 0}/${overallStatsData?.bunkerHit || 0}`}
          />
          <Text
            style={{
              color: 'rgba(0, 0, 0, 0.5)',
              marginTop: isHorizontal ? 0 : 8,
              marginLeft: isHorizontal ? 10 : 0,
            }}
            size={12}
          >
            scoreStats.title.sand_saves
          </Text>
        </View>
      </View>
    </View>
  );
};

export default ShortGameClassicStats;
