import React from 'react';
import {View} from 'react-native';
import Text from 'components/Text';

const ParAverage = ({overallStatsData}) => {
  const renderAvgBox = (title, value) => {
    return (
      <View
        style={{
          paddingHorizontal: 16,
          marginHorizontal: 5,
          alignItems: 'center',
        }}
      >
        <Text style={{color: 'rgba(0, 0, 0, 0.5)'}} size={12}>
          {title}
        </Text>
        <Text Din79Font size={34} style={{fontWeight: '700'}} black>
          {value != null ? parseFloat(value).toFixed(2) : '--'}
        </Text>
      </View>
    );
  };
  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'center',
        marginVertical: 24,
      }}
    >
      {renderAvgBox('Par 3 Avg', overallStatsData?.averageScoreForParThree)}
      {renderAvgBox('Par 4 Avg', overallStatsData?.averageScoreForParFour)}
      {renderAvgBox('Par 5 Avg', overallStatsData?.averageScoreForParFive)}
    </View>
  );
};

export default ParAverage;
