import React, {useState, useEffect, useRef, memo} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Image,
  Alert,
  ImageBackground,
} from 'react-native';
import appStyles from 'styles/global';
import Text from 'components/Text';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/AntDesign';
import {
  initialWindowMetrics,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import ChoosePhotoIcon from 'assets/imgs/playScore/share-round-pick-photo.png';
import DefaultBgPhoto from 'assets/imgs/playScore/share-round-default-photo.jpg';
import DoubleLineBox from 'assets/imgs/playScore/scorecard-doubleline-box.png';
import DoubleLineCircle from 'assets/imgs/playScore/scorecard-doubleline-circle.png';
import SingleLineCircle from 'assets/imgs/playScore/scorecard-singleline-circle.png';
import SingleLineBox from 'assets/imgs/playScore/scorecard-singleline-box.png';
import TMLogo from 'assets/imgs/playScore/share-round-tm-logo.png';
import moment from 'moment';
import * as ImagePicker from 'react-native-image-picker';
import {calculateOutInTotal, UNDEFINED_VALUE} from 'utils/scorecard';
import CircleChart from 'components/CircleChart/CircleChart';
import ProgressCircleCustom from 'components/ProgressCircleCustom';
import {ERROR_RED} from 'config';
import LinearGradient from 'react-native-linear-gradient';
import {check, request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import ViewShot from 'react-native-view-shot';
import Share from 'react-native-share';
import ZoomableImage from 'components/ZoomableImage/ZoomableImage';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {isSmallScreenIphone} from 'utils/constant';
let CROP_PADDING = 32;
if (isSmallScreenIphone()) {
  CROP_PADDING = 60;
}
let CROP_BOX_WIDTH = wp(100) - CROP_PADDING * 2;
let CROP_BOX_HEIGHT = ((wp(100) - CROP_PADDING * 2) * 16) / 9;
let PATTERN_STATS_WIDTH = 390;
let FOOTER_HEIGHT = 59;
const COURSE3PAR9HOLE = 27;
const COURSE3PAR18HOLE = 54;
const ShareRoundPreview = ({
  isShareRoundVisible,
  setShareRoundVisible = () => {},
  dataShare,
}) => {
  const dataShareCopy = {...dataShare};
  const [selectedPhoto, setSelectedPhoto] = useState(DefaultBgPhoto);
  const roundDetail = dataShareCopy?.roundDetail;
  const hideFairWay =
    roundDetail?.par == COURSE3PAR9HOLE || roundDetail?.par == COURSE3PAR18HOLE
      ? true
      : false;
  const overallStatsData = dataShareCopy?.overallStatsData;
  const isBasicMode = dataShareCopy?.isBasicMode;
  const onCloseModal = () => {
    setSelectedPhoto(DefaultBgPhoto);
    setShareRoundVisible(false);
  };
  const insets = useSafeAreaInsets();
  const topInset =
    Platform.OS === 'ios' ? insets?.top : initialWindowMetrics?.insets?.top;
  const viewShotRef = useRef(null);

  const requestPhotoLibraryPermission = async () => {
    let permission;

    if (Platform.OS === 'ios') {
      permission = PERMISSIONS.IOS.PHOTO_LIBRARY;
    } else if (Platform.OS === 'android') {
      const version = Platform.Version;
      if (version >= 33) {
        // Android 13 and above
        permission = PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
      } else {
        permission = PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
      }
    }

    try {
      const result = await check(permission);
      switch (result) {
        case RESULTS.UNAVAILABLE:
          Alert.alert(
            'Permission not available',
            'This feature is not available on this device.',
          );
          break;
        case RESULTS.DENIED:
          const requestResult = await request(permission);
          handlePermissionResult(requestResult);
          break;
        case RESULTS.GRANTED:
          onOpenChoosePhoto();
          break;
        case RESULTS.BLOCKED:
          Alert.alert(
            'Permission Blocked',
            'Please enable photo library access from settings.',
          );
          break;
      }
    } catch (error) {
      console.error('Error checking permission:', error);
    }
  };

  const handlePermissionResult = result => {
    if (result === RESULTS.GRANTED) {
      onOpenChoosePhoto();
    } else {
      Alert.alert(
        'Permission Denied',
        'You need to grant photo library access to select a picture.',
      );
    }
  };
  const onOpenChoosePhoto = () => {
    ImagePicker.launchImageLibrary(
      {
        mediaType: 'photo', // Set 'photo' to select only photos, 'video' for videos, or 'mixed' for both
        quality: 1, // Set the quality of the image (0 - 1)
      },
      response => {
        if (response.didCancel) {
          // console.log('User cancelled image picker');
        } else if (response.errorCode) {
          // console.log('Image Picker Error: ', response.errorMessage);
        } else {
          const source = {uri: response.assets[0].uri};
          setSelectedPhoto(source);
        }
      },
    );
  };
  const shareImage = async capturedImageUri => {
    if (capturedImageUri) {
      const shareOptions = {
        title: 'Share Image',
        url: capturedImageUri,
        failOnCancel: false,
      };

      try {
        let result = await Share.open(shareOptions);
        // console.log('Share result:', result);
        if (Platform.OS === 'android') {
          onCloseModal();
        } else {
          if (result.success) {
            onCloseModal();
          }
        }
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // console.log('No image selected to share');
    }
  };
  const onShareMyRound = () => {
    if (viewShotRef.current) {
      viewShotRef.current
        .capture()
        .then(uri => {
          // console.log('Image saved to', uri);
          shareImage(uri);
        })
        .catch(error => {
          console.error('Error capturing view:', error);
        });
    }
  };
  const renderScore = (holeScore, isPenalty) => {
    return (
      <View style={appStyles.hCenter}>
        <View
          style={[
            {
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
              marginTop: Platform.OS === 'android' ? -2 : 0,
            },
          ]}
        >
          {isPenalty && (
            <Text
              Din79Font
              size={16}
              style={[styles.penaltyIcon, styles.scoreTextStyle]}
            >
              !
            </Text>
          )}
          <Text Din79Font size={16} black style={[styles.scoreTextStyle]}>
            {holeScore}
          </Text>
        </View>
      </View>
    );
  };
  const ScoreView = ({holeData}) => {
    let par = holeData?.par || 0;
    let holeScore = holeData?.score;
    const scoreDif = holeScore - par;
    let isPenalty = holeData?.penalty_hit;
    if (holeScore === 0) {
      return (
        <View
          style={[
            {
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
            },
          ]}
        >
          <Text Din79Font size={16} black style={styles.scoreTextStyle}>
            {'--'}
          </Text>
        </View>
      );
    }
    if (scoreDif === 1) {
      if (Platform.OS === 'ios') {
        return (
          <ImageBackground
            source={SingleLineBox}
            style={{
              width: 26,
              height: 26,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {renderScore(holeScore, isPenalty)}
          </ImageBackground>
        );
      } else {
        return (
          <View style={[styles.oneLineShape]}>
            {renderScore(holeScore, isPenalty)}
          </View>
        );
      }
    }
    if (scoreDif >= 2) {
      if (Platform.OS === 'ios') {
        return (
          <ImageBackground
            source={DoubleLineBox}
            style={{
              width: 30,
              height: 30,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {renderScore(holeScore, isPenalty)}
          </ImageBackground>
        );
      } else {
        return (
          <View style={[styles.outsideLine]}>
            <View style={[styles.insideLine]}>
              {renderScore(holeScore, isPenalty)}
            </View>
          </View>
        );
      }
    }
    if (scoreDif === -1) {
      if (Platform.OS === 'ios') {
        return (
          <ImageBackground
            source={SingleLineCircle}
            style={{
              width: 26,
              height: 26,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {renderScore(holeScore, isPenalty)}
          </ImageBackground>
        );
      } else {
        return (
          <View style={[styles.oneLineShape, {borderRadius: 13}]}>
            <View>{renderScore(holeScore, isPenalty)}</View>
          </View>
        );
      }
    }
    if (scoreDif <= -2) {
      if (Platform.OS === 'ios') {
        return (
          <ImageBackground
            source={DoubleLineCircle}
            style={{
              width: 30,
              height: 30,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {renderScore(holeScore, isPenalty)}
          </ImageBackground>
        );
      } else {
        return (
          <View style={[styles.outsideLine, {borderRadius: 15}]}>
            <View style={[styles.insideLine, {borderRadius: 12}]}>
              {renderScore(holeScore, isPenalty)}
            </View>
          </View>
        );
      }
    }
    return <View>{renderScore(holeScore, isPenalty)}</View>;
  };
  const Cell = ({content, title}) => {
    return (
      <View
        style={[
          styles.scoreCardCellStyle,
          {
            minHeight: 30,
            minWidth: 30,
          },
        ]}
      >
        {content}
      </View>
    );
  };
  const ScoreCard = () => {
    let dataScoreCard = dataShareCopy?.scoreCard;
    let scoreCardData = calculateOutInTotal(
      dataScoreCard?.scoreDetail?.holes,
      dataScoreCard?.teeSelected,
      dataScoreCard?.scoreCardDefault,
      false,
      false,
      dataScoreCard?.scoreDetail,
    );
    let validFrontScore = scoreCardData?.find(
      item => item.number <= 9 && item.score,
    );
    let validBackScore = scoreCardData?.find(
      item => item.number > 9 && item.score,
    );
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          marginTop: 20,
          borderRadius: 16,
          overflow: 'hidden',
          padding: 8,
          backgroundColor: 'white',
        }}
      >
        {validFrontScore && (
          <View
            style={{
              flex: 1,
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            {scoreCardData?.map((item, index) => {
              if (item.number <= 9) {
                return <Cell content={<ScoreView holeData={item} />} />;
              }
            })}
            <View
              style={{
                height: '90%',
                width: 1,
                backgroundColor: 'rgba(128,128,128,0.2)',
              }}
            />
            <View style={{marginLeft: 4, alignItems: 'center'}}>
              <Text MonoFont size={8} black style={styles.scoreTextStyle}>
                {'FRONT'}
              </Text>
              <Text
                Din79Font
                size={20}
                style={{fontWeight: '700', marginTop: -4}}
                black
              >
                {scoreCardData?.find(item => item.number === 'OUT')?.score ||
                  scoreCardData?.find(item => item.number === 'TOTAL')?.score}
              </Text>
            </View>
          </View>
        )}
        {validBackScore && (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: validFrontScore ? 5 : 0,
            }}
          >
            {scoreCardData?.map((item, index) => {
              if (item.number > 9) {
                return <Cell content={<ScoreView holeData={item} />} />;
              }
            })}
            <View
              style={{
                height: '90%',
                width: 1,
                backgroundColor: 'rgba(128,128,128,0.2)',
              }}
            />
            <View style={{marginLeft: 4, alignItems: 'center'}}>
              <Text MonoFont size={8} black style={styles.scoreTextStyle}>
                {'BACK'}
              </Text>
              <Text
                Din79Font
                size={20}
                style={{fontWeight: '700', marginTop: -4}}
                black
              >
                {scoreCardData?.find(item => item.number === 'IN')?.score ||
                  scoreCardData?.find(item => item.number === 'TOTAL')?.score}
              </Text>
            </View>
          </View>
        )}
      </View>
    );
  };
  const RoundStatsView = () => {
    const renderCircleChartWithDot = () => {
      const totalPutts = Math.round(overallStatsData?.putts?.totalPutts || 0);
      const courseHolesLength = roundDetail?.holesLength;
      const MAX_PUTT_VALUE = courseHolesLength === 9 ? 24 : 48;
      const STANDARD_VALUE = courseHolesLength === 9 ? 18 : 36;
      return (
        <ProgressCircleCustom
          value={totalPutts}
          maxValue={MAX_PUTT_VALUE}
          size={78}
          valueDot={STANDARD_VALUE}
          colorProgress={
            totalPutts > STANDARD_VALUE ? ERROR_RED : 'rgba(3, 168, 0, 1)'
          }
          endText={''}
          duration={1}
          border={5}
          Din79Font
          textStyle={{
            fontWeight: '700',
            fontSize: 24,
          }}
          dotSFPro
          backgroundColor={'rgba(235, 235, 235, 1)'}
          textBelow={'PUTTS TOTAL'}
          textBelowSize={6}
          textBelowStyle={{fontWeight: '700'}}
          showDotText={false}
          isTextBelowDin79={true}
          borderOpacity={'0.7'}
        />
      );
    };
    let scale = +(CROP_BOX_WIDTH / PATTERN_STATS_WIDTH);
    let ratio = 16 / 9;
    const {teeName, teeDistance, distanceUnit} = roundDetail || {};
    const teeInfo = teeName
      ? `${teeName} TEE${teeDistance ? ` - ${teeDistance} ` : ''}`
      : '';

    return (
      <View
        style={{alignItems: 'flex-start', justifyContent: 'flex-start'}}
        pointerEvents={'none'}
      >
        <LinearGradient
          colors={['rgba(0, 0, 0, 1)', 'rgba(0, 0, 0, 0)']}
          style={{
            position: 'absolute',
            width: '100%',
            height: 196,
            opacity: 0.5,
          }}
        />
        <View
          style={[
            styles.shareContainer,
            {
              alignSelf: 'flex-start',
              transform: [
                {scale: scale},
                {
                  translateX:
                    -((PATTERN_STATS_WIDTH / 2) * (1 - scale)) / scale,
                },
                {
                  translateY:
                    -(((PATTERN_STATS_WIDTH * ratio) / 2) * (1 - scale)) /
                    scale,
                },
              ],
            },
          ]}
          pointerEvents={'none'}
        >
          <View style={styles.headerContainer}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <View style={styles.courseInfoView}>
                <Text Din79Font style={styles.courseInfoText} white>
                  {moment(roundDetail?.datePlayed).format('MM.DD.YYYY')}
                </Text>
                <Text
                  Din79Font
                  style={styles.courseInfoText}
                  white
                  numberOfLines={2}
                >
                  {roundDetail?.courseName}
                </Text>
                <Text
                  Din79Font
                  style={styles.courseInfoText}
                  white
                  numberOfLines={2}
                >
                  {teeInfo}
                  {teeDistance ? (
                    <Text Din79Font style={styles.courseInfoTextSmall}>
                      {distanceUnit?.toUpperCase?.()}
                    </Text>
                  ) : null}
                </Text>
              </View>
              <View style={styles.roundScoreInfoView}>
                <Text white Din79Font style={[styles.textAvg]}>
                  {roundDetail?.totalScore}
                </Text>
                <View style={styles.scoreToParContainer}>
                  <Text black Din79Font style={[styles.scoreToPar]}>
                    {roundDetail?.scoreToPar > 0 ? '+' : ''}
                    {roundDetail?.scoreToPar === 0
                      ? 'E'
                      : roundDetail?.scoreToPar}
                  </Text>
                </View>
              </View>
            </View>
            <ScoreCard />
          </View>
          <View style={styles.footerViewContainer}>
            {!isBasicMode && (
              <View
                style={
                  !hideFairWay
                    ? styles.statsViewContainer
                    : styles.statsViewContainerCenter
                }
              >
                {!hideFairWay && (
                  <View>
                    <CircleChart
                      value={Math.round(
                        overallStatsData?.shortGame?.driving?.classicDriving ||
                          0,
                      )}
                      color={'rgba(3, 168, 0, 1)'}
                      textBelow={`FAIRWAYS HIT`}
                      backgroundColor={'rgba(235, 235, 235, 1)'}
                      chartSize={78}
                      valueTextSize={24}
                      percentTextSize={12}
                      textBelowSize={6}
                      notApplicableSize={74}
                      textBelowStyle={{fontWeight: '700'}}
                      borderWidth={5}
                      isTextBelowDin79={true}
                      animationDuration={1}
                      borderOpacity={'0.7'}
                      isShareRound={true}
                    />
                  </View>
                )}
                <View>
                  <CircleChart
                    value={Math.round(
                      overallStatsData?.classicStatsGreensInRegulation || 0,
                    )}
                    color={'rgba(3, 168, 0, 1)'}
                    textBelow={`GREEN IN REGULATION`}
                    backgroundColor={'rgba(235, 235, 235, 1)'}
                    chartSize={78}
                    valueTextSize={24}
                    percentTextSize={12}
                    textBelowSize={6}
                    notApplicableSize={74}
                    textBelowStyle={{fontWeight: '700'}}
                    borderWidth={5}
                    isTextBelowDin79={true}
                    animationDuration={1}
                    borderOpacity={'0.7'}
                    isShareRound={true}
                  />
                </View>
                <View>
                  <CircleChart
                    value={Math.round(
                      overallStatsData?.shortGame?.scramble || 0,
                    )}
                    color={'rgba(0,65,105,255)'}
                    textBelow={`SCRAMBLING`}
                    backgroundColor={'rgba(235, 235, 235, 1)'}
                    chartSize={78}
                    valueTextSize={24}
                    percentTextSize={12}
                    textBelowSize={6}
                    notApplicableSize={74}
                    textBelowStyle={{fontWeight: '700'}}
                    borderWidth={5}
                    isTextBelowDin79={true}
                    animationDuration={1}
                    borderOpacity={'0.7'}
                    style={hideFairWay ? styles.viewMarginCenter : {}}
                    isShareRound={true}
                  />
                </View>
                <View>{renderCircleChartWithDot()}</View>
              </View>
            )}
            <View style={styles.powerByContainer}>
              <LinearGradient
                colors={['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0.5)']}
                style={{
                  position: 'absolute',
                  width: '100%',
                  height: FOOTER_HEIGHT,
                }}
              />
              <Text Din79Font size={12} white style={styles.footerText}>
                play.share_round.power_by_the
              </Text>
              <Image
                source={TMLogo}
                style={{
                  width: 100,
                  height: 19,
                  marginHorizontal: 8,
                }}
              />
              <Text Din79Font size={12} white style={styles.footerText}>
                play.share_round.app
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  const ShareContentView = () => {
    return (
      <GestureHandlerRootView>
        <View
          style={{
            width: '100%',
            height: '100%',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'absolute',
            backgroundColor: 'black',
            zIndex: 0,
          }}
          pointerEvents={'none'}
        />
        <View
          style={{
            width: '100%',
            height: '100%',
            alignItems: 'center',
            position: 'absolute',
            zIndex: 2,
            flexDirection: 'row',
          }}
          pointerEvents={'none'}
        >
          <View
            style={{
              height: '100%',
              width: CROP_PADDING,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
            }}
          />
          <View
            style={{
              width: '100%',
              height: '100%',
              flex: 1,
              borderWidth: 2,
              borderColor: 'white',
            }}
          />
          <View
            style={{
              height: '100%',
              width: CROP_PADDING,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              alignSelf: 'flex-end',
            }}
          />
        </View>
        <View
          style={{
            width: '100%',
            height: '100%',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'absolute',
            zIndex: 5,
          }}
          pointerEvents={'box-none'}
        >
          <TouchableOpacity
            onPress={requestPhotoLibraryPermission}
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 85,
              backgroundColor: 'rgba(1,3,18,0.5)',
              padding: 8,
            }}
          >
            <Image
              source={ChoosePhotoIcon}
              style={{
                width: 24,
                height: 24,
              }}
            />
            <Text
              white
              Din79Font
              size={12}
              style={[
                styles.footerText,
                {
                  marginLeft: 10,
                },
              ]}
            >
              play.share_round.change_photo
            </Text>
          </TouchableOpacity>
          <View pointerEvents={'none'}>
            <Text white size={8} style={{fontWeight: '400', marginTop: 8}}>
              play.share_round.pinch_to_adjust_photo
            </Text>
          </View>
        </View>
        <ViewShot
          ref={viewShotRef}
          options={{format: 'jpg', height: 2560, width: 1440}}
          style={{
            marginHorizontal: CROP_PADDING,
            backgroundColor: 'black',
          }}
          pointerEvents={'none'}
        >
          <View style={{width: CROP_BOX_WIDTH, height: CROP_BOX_HEIGHT}}>
            <ZoomableImage
              source={selectedPhoto}
              style={{
                width: '100%',
                height: '100%',
                position: 'absolute',
                top: 0,
              }}
              imageStyle={{
                boxWidth: CROP_BOX_WIDTH,
                boxHeight: CROP_BOX_HEIGHT,
              }}
            />
            <RoundStatsView />
          </View>
        </ViewShot>
      </GestureHandlerRootView>
    );
  };
  const ShareButton = () => {
    return (
      <View style={styles.shareButtonContainer}>
        <TouchableOpacity
          style={styles.buttonShareRound}
          onPress={onShareMyRound}
        >
          <Text Din79Font black style={styles.footerText} size={12}>
            play.share_round.share_my_round
          </Text>
        </TouchableOpacity>
      </View>
    );
  };
  const PreviewHeader = () => {
    return (
      <View
        style={[
          appStyles.vCenter,
          appStyles.hCenter,
          {
            zIndex: 10,
          },
        ]}
      >
        <View
          style={[
            appStyles.row,
            {
              width: '100%',
              justifyContent: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              paddingTop: Platform.OS === 'ios' ? topInset : 0,
            },
          ]}
        >
          <View
            style={[
              appStyles.row,
              {
                paddingHorizontal: 16,
                paddingVertical: 10,
              },
            ]}
          >
            <Text Din79Font size={16} style={styles.textPreview} white>
              play.share_round.preview
            </Text>
          </View>
          <View
            style={[
              styles.buttonClose,
              {
                top: (Platform.OS === 'ios' ? topInset : 0) + 8,
              },
            ]}
          >
            <TouchableOpacity onPress={onCloseModal}>
              <Icon
                name="close"
                size={20}
                color={'white'}
                style={{fontWeight: 'bold'}}
              />
            </TouchableOpacity>
          </View>
        </View>
        <View
          style={{
            height: 28,
            width: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
          }}
        />
      </View>
    );
  };
  return (
    <Modal
      isVisible={isShareRoundVisible}
      style={[appStyles.flex, {margin: 0}]}
      animationInTiming={300}
      animationOutTiming={300}
      onModalHide={() => {}}
    >
      <View style={[appStyles.flex, {backgroundColor: 'black'}]}>
        <PreviewHeader />
        <ShareContentView />
        <View
          style={{
            flex: 1,
            width: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
          }}
        />
        <ShareButton />
      </View>
    </Modal>
  );
};
const styles = StyleSheet.create({
  buttonClose: {
    width: 25,
    height: 25,
    borderRadius: 25,
    right: 16,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  shareContainer: {
    height: (PATTERN_STATS_WIDTH * 16) / 9,
    width: PATTERN_STATS_WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
  },
  courseInfoText: {
    fontSize: 12,
    fontWeight: '800',
    letterSpacing: 0.96,
    textTransform: 'uppercase',
  },
  footerText: {
    fontWeight: '700',
    letterSpacing: 1.62,
    textTransform: 'uppercase',
  },
  footerViewContainer: {
    position: 'absolute',
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsViewContainer: {
    width: PATTERN_STATS_WIDTH,
    paddingHorizontal: 14,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statsViewContainerCenter: {
    width: PATTERN_STATS_WIDTH,
    paddingHorizontal: 14,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewMarginCenter: {
    marginLeft: 12,
    marginRight: 12,
  },
  powerByContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: PATTERN_STATS_WIDTH,
    height: FOOTER_HEIGHT,
  },
  headerContainer: {
    position: 'absolute',
    top: 60,
    paddingHorizontal: 16,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  courseInfoView: {
    flex: 0.8,
  },
  roundScoreInfoView: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    flex: 0.2,
  },
  textAvg: {
    fontSize: 36,
    fontWeight: '700',
    marginBottom: -10,
    marginRight: 3,
  },
  scoreToPar: {
    fontSize: 12,
    fontWeight: '800',
    letterSpacing: 0.96,
    textAlign: 'center',
    marginTop: Platform.OS === 'ios' ? -2 : -1,
  },
  scoreToParContainer: {
    backgroundColor: 'white',
    borderRadius: 5,
    paddingVertical: 1,
    paddingHorizontal: 5,
    minWidth: 25,
    height: 16,
    marginBottom: -2,
  },
  courseInfoTextSmall: {
    fontSize: 8,
    fontWeight: '800',
    letterSpacing: 0.64,
    textTransform: 'uppercase',
  },
  shareButtonContainer: {
    width: '100%',
    borderTopWidth: 1,
    borderTopColor: 'rgba(128, 128, 128, 1)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 0,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 32,
    backgroundColor: 'rgba(26, 26, 26, 1)',
  },
  buttonShareRound: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 24,
  },
  textPreview: {
    letterSpacing: 1.28,
    textTransform: 'uppercase',
    fontWeight: '800',
  },
  scoreCardCellStyle: {
    ...appStyles.hCenter,
    ...appStyles.vCenter,
    alignContent: 'center',
    marginRight: 4,
  },
  insideLine: {
    width: 24,
    height: 24,
    borderColor: 'black',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: 0,
    borderStyle: 'solid',
  },
  outsideLine: {
    height: 30,
    width: 30,
    borderColor: 'black',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 0,
    borderStyle: 'solid',
  },
  oneLineShape: {
    width: 26,
    height: 26,
    borderColor: '#000',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  penaltyIcon: {
    color: '#FF0000',
    marginLeft: 3,
  },
  scoreTextStyle: {
    fontWeight: '400',
    borderColor: '#000',
  },
});
export default memo(ShareRoundPreview);
