import React, {useState, useEffect, useCallback, useRef} from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {
  View,
  TouchableOpacity,
  Platform,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {connect, useDispatch, useSelector} from 'react-redux';
import Geolocation from 'react-native-geolocation-service';
import Header from 'components/Header';
import {
  isConnectedNetwork,
  showToastErrorInternet,
} from 'utils/queueAndNetwork';
import LogoMyTM from 'assets/imgs/logo-no-plus.svg';
import USGALogo from 'assets/imgs/logo-USGA-long-horizontal.svg';

import Text from 'components/Text';
import appStyles from 'styles/global';
import {
  ARCCOS,
  MYTMOC,
  SCREEN_CLASS,
  SCREEN_TYPES,
  IMAGES,
  GA_EVENT_NAME,
  PAGE_CATEGORY,
  PAGE_NAME,
  NOT_APPLICABLE,
} from '../../utils/constant';
import {t} from 'i18next';
import {get} from 'lodash';
import {
  getModeFakeGpsStorage,
  getCourseOutlineStorage,
} from 'utils/asyncStorage';
import {updateServicePreference} from 'requests/content';
import {addCurrentUser} from 'reducers/user';
import {showErrorToast, showToast} from 'utils/toast';
import useAppState from 'hooks/useAppState';
import {updatePlayLocation} from 'reducers/play';
import moment from 'moment';
import {useIsFocused} from '@react-navigation/native';
import {getDistance} from 'geolib';
import {
  request,
  PERMISSIONS,
  RESULTS,
  openSettings,
  check,
} from 'react-native-permissions';
import {getNearbyCoursesList} from 'utils/play';
import {checkCanPostRoundToUSGA} from 'utils/user';
import {BlurView} from '@react-native-community/blur';
import {
  getCourseDetails,
  getCourseElevationDetails,
  getCourseScoreCard,
  getCourseTeeDetails,
} from 'requests/play-stats';
import {
  addRound,
  getCourseDetailsGps,
  getCourseGPSVector,
} from 'requests/add-round';
import {
  getGpsVectorDetails,
  setElevationAwsRequest,
  setElevationAwsRequestError,
  setScoreCardDetails,
} from 'reducers/teeBox';
import {getAWSApi} from 'requests/apiRequest3D';
const locationPermissionTitle =
  Platform.OS === 'ios'
    ? PERMISSIONS.IOS.LOCATION_ALWAYS
    : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;
import {deleteCoursePlaying, getCoursePlaying} from 'utils/realmHelper';
import {parseDataGps} from 'screens/PlayCourseMap/iGolfFormat';
import {
  createRoundDefault,
  MODE_ROUND,
  parseListDataDefault,
} from 'screens/PlayCourseMap/DataSubmitDefault';
import {checkConnectedWHS} from 'utils/user';
import ConfirmModal from 'components/Modal/ConfirmModal';
import {getDataCourseCache} from 'utils/home';
import {
  getUserLocation,
  setInprogressHoleSubmitArray,
  setModePlayed,
  setUserLocation,
} from 'utils/commonVariable';
import ScoreStats from './components/ScoreStats';
import CoursePlaySection from './components/CoursePlaySection';
import ButtonConnectUSGA from './components/ButtonConnectUSGA';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  Extrapolation,
  runOnJS,
} from 'react-native-reanimated';
import WheelPickerBottomSheet from 'components/WheelPickerBottomSheet';
import {
  GestureHandlerRootView,
  Gesture,
  GestureDetector,
} from 'react-native-gesture-handler';
import {
  GA_logEvent,
  GA_playNavClick,
  GA_logScreenViewV2,
  GA_setUserProperty,
} from 'utils/googleAnalytics';
import CustomImageBackground from 'components/CustomImageBackground/CustomImageBackground';
import {convertDistanceFromYards, getAbbreviatedUnit} from 'utils/convert';

const Play = ({navigation, updatePlayLocation}) => {
  const {navigate} = navigation;
  const coordinates = useSelector(state => state?.play?.playLocation);
  const nearbyCourses = useSelector(state => state?.play?.nearbyCourses);
  const appImagesCache = useSelector(state => state?.images);
  const imagePlay = appImagesCache?.data?.[IMAGES.PLAY_BG] || null;
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
  const [loadingNearby, setLoadingNearby] = useState(false);
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );
  const user = useSelector(state => state.user);
  const dispatch = useDispatch();
  const insets = useSafeAreaInsets();
  const timeGetLocationRef = useRef(null);
  const [initGetLocationDone, setInitGetLocationDone] = useState(false);
  const isFocused = useIsFocused();
  const [isLocationPermissionAllowed, setLocationPermissionAllowed] =
    useState(true);
  const [isLocationBlocked, setLocationBlocked] = useState(false);
  const previousLocationAllowed = useRef(null);
  const canPostToUSGA = checkCanPostRoundToUSGA(showHideFeatures, user);
  const ghin = useSelector(state => state.ghin);
  const whs = useSelector(state => state.whs);
  const isGolfCanada = checkConnectedWHS(whs);
  const [courseTeeList, setCourseTeeList] = useState([]);
  const [teeSelected, setTeeSelected] = useState();
  const [numberPar, setNumberPar] = useState('00');
  const [scoreCardCourse, setScoreCardCourse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [averageScore, setAverageScore] = useState(null);
  const [numberOfRounds, setNumberOfRounds] = useState(0);
  const averageScoreRange = user?.golferProfile?.averageScoreRange;
  const [isDeleteRound, setIsDeleteRound] = useState(false);
  const [isShowModalResumeRound, setShowModalResumeRound] = useState(false);
  const [firstLoad, setFirstLoad] = useState(false);
  const [isTeeDataError, setIsTeeDataError] = useState(false);
  const hasOpenedSettings = useRef(false);
  const userDistanceUnit =
    user?.measurementUnits?.toLowerCase?.() === 'meters' ? 'meters' : 'yards';

  let pickerTeeRef = useRef(null);
  let scoreStatRef = useRef(null);
  const offset = useSharedValue(0);

  const animatedStyles = useAnimatedStyle(() => {
    const scale = interpolate(offset.value, [0, 100], [1, 1.2], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const opacity = interpolate(offset.value, [0, 100], [1, 0], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      transform: [{translateX: offset.value}, {scale}],
      opacity,
    };
  });

  const animatedScaleStyles = useAnimatedStyle(() => {
    const scale = interpolate(offset.value, [0, 50], [1, 0], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      transform: [{scale}],
    };
  });

  useEffect(() => {
    if (playService === ARCCOS) {
      updateService();
    }
    setFirstLoad(true);
    getModeFakeGpsStorage();
    getCourseOutlineStorage();
    checkPermissionAndGetLocation();
  }, []);

  //Track if the coordinates changes the next time
  useEffect(() => {
    if (
      isLocationPermissionAllowed &&
      Object.keys(coordinates).length > 0 &&
      initGetLocationDone &&
      !firstLoad &&
      isFocused
    ) {
      getDataNearbyCourse(coordinates);
    }
  }, [coordinates, initGetLocationDone]);

  useEffect(() => {
    //check with initGetLocationDone to prevent duplicate call with useEffect
    if (isFocused && initGetLocationDone) {
      checkPermissionAndGetLocation();
    }
    if (isFocused) {
      scoreStatRef?.current?.reloadScoreStatsData?.();
    } else {
      setFirstLoad(false);
      hasOpenedSettings.current = false;
    }
  }, [isFocused]);

  //Check if location permission changed
  useEffect(() => {
    if (
      isLocationPermissionAllowed &&
      previousLocationAllowed.current === false
    ) {
      previousLocationAllowed.current = true;
      getLocation();
    } else {
      previousLocationAllowed.current = false;
    }
  }, [isLocationPermissionAllowed]);

  const updateService = async () => {
    try {
      const response = await updateServicePreference(MYTMOC);
      if (response?.success) {
        let currentUser = {
          ...user,
          tmUserIds: {
            ...user.tmUserIds,
            playServicePreference: MYTMOC,
          },
        };
        dispatch(addCurrentUser(currentUser));
        return showToast({
          type: 'success',
          message: t('play.changeService_MyTMOC_done'),
        });
      } else {
        return showToast({
          type: 'error',
          message: t('An_error_occurred_updating_service_preference'),
        });
      }
    } catch (error) {
      return showToast({
        type: 'error',
        message: t('An_error_occurred_updating_service_preference'),
      });
    }
  };
  const showLocationErrorToast = () => {
    //Only show error when has no nearby courses
    if (nearbyCourses?.length === 0) {
      showToast({
        type: 'error',
        message: t('error.unable_get_current_location'),
      });
    }
  };
  const showGetCoursesError = () => {
    //Only show error when has no nearby courses
    if (nearbyCourses?.length === 0) {
      showToast({
        type: 'error',
        message: t('error.unable_retrieve_nearby_courses'),
      });
      setLoadingNearby(false);
    }
  };
  useAppState({
    onForeground: () => {
      let lastTimeGetLocationErr = timeGetLocationRef.current;
      //(ANDROID ONLY) This will check the case popup location services appears if user deny location permission
      //This fix the duplicate popup on Android
      if (lastTimeGetLocationErr && Platform.OS === 'android') {
        let duration = moment.duration(moment().diff(lastTimeGetLocationErr));
        let timebetween2Errors = duration.asSeconds();
        if (timebetween2Errors > 10) {
          checkPermissionAndGetLocation(true);
        }
      } else {
        checkPermissionAndGetLocation(true);
      }
    },
    onBackground: () => {
      setFirstLoad(false);
    },
  });

  const GA_logEnableLocationEvent = (isSetTrue = true) => {
    if (isSetTrue) {
      GA_logEvent(GA_EVENT_NAME.ENABLE_LOCATION, {
        callout_name: 'location enable',
        click_location: 'play',
        page_type: SCREEN_TYPES.PLAY,
        page_category: PAGE_CATEGORY.PLAY_TRACK,
        page_name: PAGE_NAME.PLAY,
        screen_type: SCREEN_TYPES.PLAY,
      });
      GA_setUserProperty('location_enabled', 'true');
    } else {
      GA_setUserProperty('location_enabled', 'false');
    }
  };

  const requestPermission = async () => {
    try {
      const permissionRequest = await request(locationPermissionTitle);
      GA_logScreenViewV2(
        PAGE_NAME.ACCOUNT_LOCATION_SETTING,
        PAGE_CATEGORY.ACCOUNT_LOCATION,
        SCREEN_TYPES.ACCOUNT,
        SCREEN_TYPES.ACCOUNT,
      );
      //When allow ALWAYS location permission
      if (permissionRequest === RESULTS.GRANTED) {
        setLocationPermissionAllowed(true);
        GA_logEnableLocationEvent();
      }
      if (permissionRequest === RESULTS.BLOCKED) {
        //When allow ONCE location permission (IOS)
        const locationWhenInUsePermission = await check(
          PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        );
        if (
          locationWhenInUsePermission === RESULTS.GRANTED &&
          Platform.OS === 'ios'
        ) {
          setLocationPermissionAllowed(true);
          GA_logEnableLocationEvent();
        } else {
          //(Android)If not allow location permission, then take user to device settings page
          if (Platform.OS === 'android') {
            hasOpenedSettings.current = true;
            openSettings();
          } else {
            //This process for IOS
            //If not allow location permission,the next time user request permission, then take user to device settings page
            if (isLocationBlocked) {
              hasOpenedSettings.current = true;
              openSettings();
            } else {
              //If user denied the permission for the first time, then setLocationBlocked true
              setLocationBlocked(true);
            }
          }
        }
      }
    } catch (error) {
      throw error;
    }
  };

  const checkPermissionAndGetLocation = async (isFromBackground = false) => {
    const locationPermission = await check(locationPermissionTitle);
    if (locationPermission === RESULTS.DENIED) {
      setLocationPermissionAllowed(false);
      if (isFromBackground && hasOpenedSettings.current) {
        GA_logEnableLocationEvent(false);
      }
      showLocationErrorToast();
      setInitGetLocationDone(true);
    } else if (locationPermission === RESULTS.BLOCKED) {
      const locationWhenInUsePermission = await check(
        PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      );
      if (
        locationWhenInUsePermission === RESULTS.GRANTED &&
        Platform.OS === 'ios'
      ) {
        setLocationPermissionAllowed(true);
        if (isFromBackground && hasOpenedSettings.current) {
          GA_logEnableLocationEvent();
        }
        getLocation();
      } else {
        setLocationBlocked(true);
        if (isFromBackground && hasOpenedSettings.current) {
          GA_logEnableLocationEvent(false);
        }
        setLocationPermissionAllowed(false);
        showLocationErrorToast();
        setInitGetLocationDone(true);
      }
    } else {
      //When allow ALWAYS location permission
      if (isFromBackground && hasOpenedSettings.current) {
        GA_logEnableLocationEvent();
      }
      setLocationPermissionAllowed(true);
      getLocation();
    }
  };

  const getDataNearbyCourse = async (coords = null) => {
    const checkNetwork = isConnectedNetwork();
    if (!checkNetwork) {
      showToastErrorInternet();
      return;
    }
    setLoadingNearby(true);
    let retrieveCoordinates = coords || coordinates;
    try {
      if (Object.keys(retrieveCoordinates)?.length) {
        const nearbyCoursesFromApi = await getNearbyCoursesList({
          latitude: retrieveCoordinates?.latitude,
          longitude: retrieveCoordinates?.longitude,
          dispatch,
          isGettingFirst3Courses: true,
        });
        if (
          nearbyCourses?.[0]?.idCourse === nearbyCoursesFromApi?.[0]?.idCourse
        ) {
          setLoadingNearby(false);
        }
      } else {
        showGetCoursesError();
      }
    } catch (error) {
      showGetCoursesError();
    }
  };

  const nearestCourseId = nearbyCourses?.[0]?.idCourse;

  useEffect(() => {
    if (nearestCourseId) {
      getDataCourse(nearestCourseId);
    }
  }, [nearestCourseId]);

  const getParCourse = rpScoreCard => {
    const scoreCard =
      rpScoreCard?.[
        user.gender === 'male' ? 'menScorecardList' : 'wmnScorecardList'
      ]?.[0];
    setScoreCardCourse(scoreCard);
    setNumberPar(
      scoreCard?.parTotal?.toString() ||
        (scoreCard?.parIn || scoreCard?.parOut
          ? `${get(scoreCard, 'parIn', 0) + get(scoreCard, 'parOut', 0)}`
          : '00'),
    );
  };

  const getDataCourse = async idCourse => {
    const checkNetwork = isConnectedNetwork();
    if (!checkNetwork) {
      showToastErrorInternet();
      return;
    }
    setLoadingNearby(true);
    try {
      const [
        rpScoreCard,
        responseTees,
        courseElevationDataDetails,
        courseGPSVectorDetails,
      ] = await Promise.all([
        getCourseScoreCard(idCourse),
        getCourseTeeDetails(idCourse),
        getCourseElevationDetails(idCourse),
        getCourseGPSVector(idCourse, false),
      ]);
      dispatch(getGpsVectorDetails(courseGPSVectorDetails));
      dispatch(setScoreCardDetails(rpScoreCard));

      if (courseElevationDataDetails?.jsonFullUrl) {
        try {
          let response = await getAWSApi(
            courseElevationDataDetails?.jsonFullUrl,
          );
          if (response.status === 200) {
            dispatch(setElevationAwsRequest(response?.data || false));
          } else {
            dispatch(setElevationAwsRequestError());
          }
        } catch (error) {}
      } else {
        dispatch(setElevationAwsRequest(false));
      }
      getParCourse(rpScoreCard);
      // const genderUser = user.gender === 'female' ? 'wmn' : 'men';
      const teeFilterGender = responseTees?.teesList;
      const tee = teeFilterGender?.sort?.((a, b) => {
        return b.ydsTotal - a.ydsTotal;
      })[0];
      setTeeSelected(tee ? tee : teeFilterGender?.[0]);
      setCourseTeeList(teeFilterGender);
      if (responseTees?.errorDetails) {
        setIsTeeDataError(true);
      } else {
        setIsTeeDataError(false);
      }
    } catch (error) {
      showErrorToast({
        error: '',
        title: error?.response?.data?.message || error + '',
      });
    } finally {
      setLoadingNearby(false);
    }
  };
  //initGetLocationDone: This flag will prevent the useFocus effect when component first rendering
  const getLocation = () => {
    Geolocation.getCurrentPosition(
      position => {
        let currentLocation = getUserLocation();
        const posCoordinates = position.coords;
        let distanceFromPreviousCoordinate = getDistance(
          currentLocation,
          posCoordinates,
        );
        //Only update the location's coordinates when distance greater than 500m
        if (
          (distanceFromPreviousCoordinate >= 500 &&
            Object.values(currentLocation)?.length) ||
          Object.values(currentLocation)?.length === 0
        ) {
          setUserLocation(posCoordinates);
          updatePlayLocation(posCoordinates);
        }
        //get location successfully so set timeGetLocationRef
        timeGetLocationRef.current = null;
        if (!initGetLocationDone) {
          //set timeout 0.3s for sure updatePlayLocation to redux done
          setTimeout(() => setInitGetLocationDone(true), 300);
        }
      },
      error => {
        showLocationErrorToast();
        let lastTimeGetLocationErr = moment();
        timeGetLocationRef.current = lastTimeGetLocationErr;
        if (!initGetLocationDone) {
          setInitGetLocationDone(true);
        }
      },
      {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
    );
  };

  const onPressWITB = () => {
    GA_playNavClick('my bag');
    navigate('MyBag', {screen: 'MyBagScreens'});
  };

  const onPostScore = () => {
    GA_playNavClick('post a score');
    navigate('ScoresStats', {
      screen: 'ScoreAddCourse',
      params: {origin: 'play'},
    });
  };

  const renderBlurView = () => {
    return Platform.OS === 'ios' ? (
      <BlurView
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          borderRadius: 22,
        }}
        blurType="light"
        blurAmount={10}
        reducedTransparencyFallbackColor="white"
      />
    ) : (
      <BlurView
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          borderRadius: 24,
        }}
        blurType="light"
        blurAmount={20}
        reducedTransparencyFallbackColor="white"
      />
    );
  };

  const renderButtons = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          marginTop: 8,
          justifyContent: 'space-between',
          marginBottom: 8,
        }}
      >
        <TouchableOpacity onPress={onPostScore} style={{flex: 0.49}}>
          <View
            style={[
              {
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.3)',
                overflow: Platform.OS === 'android' ? 'hidden' : 'visible',
              },
              appStyles.viewShadowLight,
            ]}
          >
            {renderBlurView()}
            <Text
              Din79Font
              size={12}
              style={{
                fontWeight: '700',
                letterSpacing: 1.62,
                paddingVertical: 13,
                textTransform: 'uppercase',
              }}
              black
            >
              {canPostToUSGA
                ? 'play.btn.post_to_usga'
                : isGolfCanada
                ? 'play.btn.post_to_whs'
                : 'post_a_score'}
            </Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity style={{flex: 0.49}} onPress={onPressWITB}>
          <View
            style={[
              {
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.3)',
                overflow: Platform.OS === 'android' ? 'hidden' : 'visible',
              },
              appStyles.viewShadowLight,
            ]}
          >
            {renderBlurView()}
            <Text
              Din79Font
              size={12}
              style={{
                fontWeight: '700',
                letterSpacing: 1.62,
                paddingVertical: 13,
                textTransform: 'uppercase',
              }}
              black
            >
              play.btn.my_bag
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const onPressCancelPreviousRound = async () => {
    setInprogressHoleSubmitArray([]);
    const coursePlaying = await getCoursePlaying();
    if (coursePlaying) {
      const parTotal = coursePlaying?.scoreCardCourse?.parHole?.reduce?.(
        (accumulator, currentValue) => accumulator + currentValue,
        0,
      );
      const teeDistanceObj = convertDistanceFromYards({
        distanceInYards: coursePlaying?.teeSelected?.ydsTotal,
        userUnit: userDistanceUnit,
      });
      GA_logEvent(GA_EVENT_NAME.CANCEL_PREVIOUS_ROUND, {
        tee_color: `${coursePlaying?.teeSelected?.teeName || ''} - ${
          teeDistanceObj?.value || NOT_APPLICABLE
        } ${teeDistanceObj?.unit}`,
        course_par: coursePlaying?.scoreCardCourse?.parTotal || parTotal || '0',
        number_of_holes:
          (coursePlaying?.roundData?.holes?.length || 0) + ' holes',
        date_played: moment(coursePlaying?.roundData?.played_on).format('LL'),
        location: coursePlaying?.roundData?.course_name || '',
        click_location: 'play',
        page_type: SCREEN_TYPES.PLAY,
        page_category: PAGE_CATEGORY.PLAY_TRACK,
        page_name: PAGE_NAME.PLAY_TRACK,
        screen_type: SCREEN_TYPES.PLAY,
      });
    }
    await deleteCoursePlaying();
    onPressPlay();
  };

  const onPressPlay = async () => {
    const checkNetwork = isConnectedNetwork();
    if (!checkNetwork) {
      showToastErrorInternet();
      return;
    }
    const coursePlaying = await getCoursePlaying();
    if (coursePlaying) {
      setShowModalResumeRound(true);
      return;
    }
    // if (!teeSelected) {
    //   showErrorToast({
    //     error: {},
    //     title: t('play.please_select_tee'),
    //   });
    //   return;
    // }
    setLoading(true);
    try {
      let idCourse = nearbyCourses?.[0]?.idCourse;
      let igolfTeeName = teeSelected?.teeName;

      const [courseGPS, courseDetail, gpsList] = await Promise.all([
        getCourseGPSVector(idCourse),
        getCourseDetails(idCourse),
        getCourseDetailsGps(idCourse),
      ]);
      if (courseGPS?.errorDetails || gpsList?.errorDetails) {
        showErrorToast({
          error: '',
          title: t('play.error.missing_course_data'),
          description: t('play.error.missing_course_data.detail'),
        });
        return;
      } else if (isTeeDataError) {
        showErrorToast({
          error: '',
          title: t('play.error.missing_tee_or_course_data'),
          description: t('play.error.missing_tee_or_course_data.detail'),
        });
      }
      const gpsGreens = parseDataGps(gpsList);
      const dataHolesDefault = parseListDataDefault(scoreCardCourse, gpsGreens);
      let roundData = {};
      let userToCourse = 0;
      if (courseDetail?.latitude && courseDetail?.longitude) {
        userToCourse = getDistance(
          {
            latitude: coordinates?.latitude,
            longitude: coordinates?.longitude,
          },
          {
            latitude: courseDetail?.latitude,
            longitude: courseDetail?.longitude,
          },
        );
      }
      roundData = {
        ...createRoundDefault(
          user?.tmUserIds?.mrp,
          nearbyCourses?.[0]?.courseName,
          idCourse,
          MODE_ROUND.CLASSIC,
          igolfTeeName,
          new Date(),
          undefined,
          userToCourse?.toString?.(),
        ),
        holes: dataHolesDefault,
      };
      if (roundData) {
        setModePlayed(MODE_ROUND.CLASSIC);
        const newRound = await addRound(roundData);
        const parTotal = scoreCardCourse?.parHole?.reduce?.(
          (accumulator, currentValue) => accumulator + currentValue,
          0,
        );
        const teeDistanceObj = convertDistanceFromYards({
          distanceInYards: teeSelected?.ydsTotal,
          userUnit: userDistanceUnit,
        });
        GA_logEvent(GA_EVENT_NAME.TRACK_ROUND, {
          tee_color: `${teeSelected?.teeName || ''} - ${
            teeDistanceObj?.value || NOT_APPLICABLE
          } ${teeDistanceObj?.unit}`,
          course_par: scoreCardCourse?.parTotal || parTotal || '0',
          number_of_holes: (roundData?.holes?.length || 0) + ' holes',
          date_played: moment(roundData?.played_on).format('LL'),
          location: roundData?.course_name || '',
          click_location: 'play',
          page_type: SCREEN_TYPES.PLAY,
          page_category: PAGE_CATEGORY.PLAY_TRACK,
          page_name: PAGE_NAME.PLAY_TRACK,
          screen_type: SCREEN_TYPES.PLAY,
        });
        GA_playNavClick('start a round');
        navigation.navigate('ScoresStats', {
          screen: 'CourseMap',
          params: {
            idCourse,
            teeSelected,
            scoreCardCourse,
            courseGPS,
            courseDetail,
            idRound: newRound?.roundId,
            roundData,
          },
        });
      }
    } catch (error) {
      showErrorToast({
        error: {},
        title: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const onOpenScore = () => {
    GA_playNavClick('scores & stats');
    GA_logScreenViewV2(
      PAGE_NAME.PLAY_TRACK_ROUND_OVERVIEW_TRACKED,
      PAGE_CATEGORY.PLAY_TRACK_ROUND_OVERVIEW,
      SCREEN_CLASS.PLAY,
      SCREEN_CLASS.PLAY,
    );
    if (canPostToUSGA || isGolfCanada) {
      offset.value = withTiming(100, {duration: 700});
    }
  };

  const onCloseScore = () => {
    if (canPostToUSGA || isGolfCanada) {
      offset.value = withTiming(0, {duration: 700});
    }
  };

  const pan = Gesture.Pan()
    .runOnJS(true)
    .onChange(event => {
      if (scoreStatRef.current) {
        runOnJS(scoreStatRef.current.onChangePan)(event);
      }
    })
    .onFinalize(event => {
      if (scoreStatRef.current) {
        runOnJS(scoreStatRef.current.onFinalizePan)(event);
      }
    });

  const openHandicapScreen = () => {
    navigation?.navigate('HandicapHorizontal', {
      screen: 'HandicapUSGA',
    });
  };

  const renderAverageHeaderSection = () => {
    return (
      <Animated.View
        style={[
          {
            top: insets.top,
            backgroundColor: 'transparent',
            position: 'absolute',
            width: '100%',
            paddingTop: Platform.OS === 'ios' ? 3 : 12,
            zIndex: 4,
          },
          animatedStyles,
        ]}
      >
        <TouchableOpacity
          disabled={!canPostToUSGA && !isGolfCanada}
          style={[appStyles.pHSm]}
          onPress={openHandicapScreen}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <LogoMyTM style={{marginRight: 8}} />
            {canPostToUSGA && <USGALogo />}
            {isGolfCanada && (
              <Text white size={16} style={{fontWeight: '400'}}>
                <Text Din79Font style={{fontWeight: '800'}} size={22}>
                  {ghin?.hiDisplay || whs?.displayHandicp}
                </Text>{' '}
                H.I.™ Low H.I.™{' '}
                {ghin?.lowHiDisplay || whs?.hiDetail?.displayLowValue
                  ? ghin?.lowHiDisplay || whs?.hiDetail?.displayLowValue
                  : '-'}
              </Text>
            )}
          </View>
          {canPostToUSGA ? (
            <Text white size={16} style={{fontWeight: '400'}}>
              <Text Din79Font style={{fontWeight: '800'}} size={22}>
                {ghin?.hiDisplay || whs?.displayHandicp}
              </Text>{' '}
              H.I.™ Low H.I.™{' '}
              {ghin?.lowHiDisplay || whs?.hiDetail?.displayLowValue
                ? ghin?.lowHiDisplay || whs?.hiDetail?.displayLowValue
                : '-'}
            </Text>
          ) : !isGolfCanada ? (
            (numberOfRounds || 0) !== 0 ? (
              <Text size={16} white style={{marginTop: 8}}>
                Avg Score{' '}
                <Text size={22} style={{fontWeight: '800'}}>
                  {(numberOfRounds || 0) === 0 ? 0 : averageScore}
                </Text>{' '}
                / {numberOfRounds || 0} Rds
              </Text>
            ) : null
          ) : null}
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const onPressConfirm = async () => {
    setLoading(true);
    setShowModalResumeRound(false);
    const coursePlaying = await getCoursePlaying();
    if (coursePlaying) {
      const parTotal = coursePlaying?.scoreCardCourse?.parHole?.reduce?.(
        (accumulator, currentValue) => accumulator + currentValue,
        0,
      );
      const teeDistanceObj = convertDistanceFromYards({
        distanceInYards: coursePlaying?.teeSelected?.ydsTotal,
        userUnit: userDistanceUnit,
      });
      GA_logEvent(GA_EVENT_NAME.RESUME_PREVIOUS_ROUND, {
        tee_color: `${coursePlaying?.teeSelected?.teeName || ''} - ${
          teeDistanceObj?.value || NOT_APPLICABLE
        } ${teeDistanceObj?.unit}`,
        course_par: coursePlaying?.scoreCardCourse?.parTotal || parTotal || '0',
        number_of_holes:
          (coursePlaying?.roundData?.holes?.length || 0) + ' holes',
        date_played: moment(coursePlaying?.roundData?.played_on).format('LL'),
        location: coursePlaying?.roundData?.course_name || '',
        click_location: 'play',
        page_type: SCREEN_TYPES.PLAY,
        page_category: PAGE_CATEGORY.PLAY_TRACK,
        page_name: PAGE_NAME.PLAY_TRACK,
        screen_type: SCREEN_TYPES.PLAY,
      });
    }
    await getDataCourseCache({navigation});
    setLoading(false);
  };

  const renderModalConfirmCoursePlaying = () => {
    return (
      <ConfirmModal
        visible={isShowModalResumeRound}
        backdropPress={() => setShowModalResumeRound(false)}
        textConfirm={'play.resume_previous_round'}
        textCancel={'play.cancel_previous_round'}
        title={'play.resume_round_confirm_message'}
        onPressCancel={onPressCancelPreviousRound}
        onPressConfirm={onPressConfirm}
      />
    );
  };

  const renderLoadingProcess = () => {
    return loading || isDeleteRound ? (
      <ActivityIndicator
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 1000,
        }}
        size={'large'}
        color={'black'}
      />
    ) : null;
  };

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <CustomImageBackground
        style={[appStyles.flex, {justifyContent: 'space-between'}]}
        source={
          imagePlay
            ? {uri: imagePlay}
            : require('assets/imgs/play-image-background.png')
        }
      >
        <GestureDetector gesture={pan}>
          <View style={{flex: 1}}>
            {renderAverageHeaderSection()}
            <View
              style={[
                appStyles.flex,
                {
                  paddingTop: insets.top + (Platform.OS === 'ios' ? 30 : 40),
                  zIndex: 1,
                },
              ]}
            >
              <View style={{marginHorizontal: 10}}>
                <CoursePlaySection
                  navigation={navigation}
                  onPressPlay={onPressPlay}
                  nearbyCourses={nearbyCourses}
                  teeSelected={teeSelected}
                  pickerTeeRef={pickerTeeRef}
                  loadingNearby={loadingNearby}
                  requestPermission={requestPermission}
                  isLocationPermissionAllowed={isLocationPermissionAllowed}
                />
                {renderButtons()}
                <ButtonConnectUSGA navigation={navigation} />
              </View>
            </View>
            <Animated.View
              style={[
                styles.headerContainer,
                {
                  top: insets.top + (Platform.OS === 'ios' ? 5 : 15),
                },
                animatedScaleStyles,
              ]}
            >
              <Header
                navigate={navigate}
                titleColor={appStyles.white}
                headerStyle={{alignItems: 'flex-start'}}
              />
            </Animated.View>
            <ScoreStats
              navigation={navigation}
              setIsDeleteRound={setIsDeleteRound}
              ref={scoreStatRef}
              onOpenScore={onOpenScore}
              onCloseScore={onCloseScore}
              offsetAnimation={offset}
              setAvgScoreForMainPlay={setAverageScore}
              setNumberOfRoundsForMainPlay={setNumberOfRounds}
            />
          </View>
        </GestureDetector>
        {renderModalConfirmCoursePlaying()}
        <WheelPickerBottomSheet
          navigation={navigation}
          options={courseTeeList}
          onChange={valueChange => {
            setTeeSelected(valueChange);
          }}
          currentValue={teeSelected}
          ref={pickerTeeRef}
          pickerTitle={t('play.select_tee')}
          keyProp={'teeName'}
          labelExpression={`{1} - {2} ${getAbbreviatedUnit(userDistanceUnit)}`}
          labelPropArray={['teeName', 'ydsTotal']}
          labelReplaceConditions={[
            {searchStr: `Ladies'`, replaceStr: 'Ladies'},
          ]}
          shouldHideNumberZero
          userDistanceUnit={userDistanceUnit}
        />
        {renderLoadingProcess()}
      </CustomImageBackground>
    </GestureHandlerRootView>
  );
};
const mapStateToProps = state => ({
  playLocation: state.play.playLocation,
});
const styles = StyleSheet.create({
  headerContainer: {
    position: 'absolute',
    right: 16,
    flex: 1,
    zIndex: 4,
  },
});

const mapDispatchToProps = {updatePlayLocation};
export default connect(mapStateToProps, mapDispatchToProps)(Play);
