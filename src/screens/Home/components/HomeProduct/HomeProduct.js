import React, {useState, useImperativeHandle, useEffect, useRef} from 'react';
import {View, StyleSheet, TouchableOpacity, Image} from 'react-native';
import Text from 'components/Text';
import {useDispatch, useSelector} from 'react-redux';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Carousel from 'react-native-snap-carousel';
import appStyles from 'styles/global';
import {SvgUri} from 'react-native-svg';
import LogoMyTMGray from 'assets/imgs/logo-no-plus-gray.svg';
import {getAuth0AccessToken} from 'utils/user';
import {getProductIdFromUrl, openEcomWebview, prepareLink} from 'utils/shop';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import {getHomeProduct} from 'requests/home';
import {
  CACHE_KEY,
  COUNTRY_CODE,
  GA_EVENT_NAME,
  WEBVIEW_PAGE_TYPE,
} from 'utils/constant';
import {
  GA_logEvent,
  GA_logViewItemList,
  GA_selectPromotionWithEcomData,
  getProductIds,
} from 'utils/googleAnalytics';
import {isEmpty} from 'lodash';
import FastImage from 'react-native-fast-image/src';
import {updateHomeProductsData} from 'reducers/dataCache';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

const HomeProduct = ({setLoadingHome, onLayout, isVisibleToUser}, ref) => {
  const [productItemsData, setProductItemsData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [alignment, setAlignment] = useState('start');
  const navigation = useNavigation();
  const {navigate} = navigation;
  const dispatch = useDispatch();
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const [dataTexts, setDataTexts] = useState({
    HOME_PRODUCT_TITLE: '',
    SUB_HOME_PRODUCT_TITLE: '',
  });
  const isScreenFocused = useIsFocused();
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const homeProductsCache = useSelector(state => state.dataCache?.homeProducts);
  const customTextsCache = useSelector(state => state.dataCache?.customTexts);
  const currentCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.HOME_PRODUCT,
  )?.version;
  const {getEcomProductDetail, getMultipleEcomProductDetails} =
    useEcomProductDetail();
  const isFirstRender = useRef(true);

  useImperativeHandle(ref, () => ({
    refreshData: async () => {
      getDataText();
    },
  }));

  useEffect(() => {
    if (appCacheVersions) {
      refreshDataIfNeeded();
    }
  }, [appCacheVersions]);

  useEffect(() => {
    try {
      if (isVisibleToUser && isScreenFocused) {
        let listProductId = getProductIds(productItemsData);
        if (listProductId.length > 0) {
          GA_logViewItemList(
            listProductId,
            'home-product',
            dataTexts?.HOME_PRODUCT_TITLE,
            undefined,
            getMultipleEcomProductDetails,
          );
        }
        logEventPromotion({
          products: productItemsData,
          eventName: GA_EVENT_NAME.VIEW_PROMOTION,
        });
      }
    } catch (error) {}
  }, [isVisibleToUser, isScreenFocused]);

  useEffect(() => {
    if (homeProductsCache) {
      const data = homeProductsCache?.data?.overwrite_products || [];
      const activeProducts =
        data.filter(item => (item.status + '').toUpperCase() === 'ACTIVE') ||
        [];
      setProductItemsData(activeProducts);
      isFirstRender.current = false;
    } else {
      setProductItemsData([]);
    }
  }, [homeProductsCache]);

  const refreshDataIfNeeded = async () => {
    try {
      if (
        currentCacheVersion === homeProductsCache?.version &&
        appCacheVersions?.country === homeProductsCache?.country &&
        currentCacheVersion != null
      ) {
        if (isScreenFocused && !isFirstRender.current) {
          //if the data is not changed from the Admin Portal, so re-assign old value from cache to trigger the Google Analytics events
          dispatch(
            updateHomeProductsData({
              country: userCountry,
              version: currentCacheVersion,
              data: homeProductsCache?.data,
            }),
          );
        }
      } else {
        setLoading(true);
        const homeProductItems = await getHomeProduct({country: userCountry});
        dispatch(
          updateHomeProductsData({
            country: userCountry,
            version: currentCacheVersion,
            data: homeProductItems,
          }),
        );
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (customTextsCache) {
      getDataText(customTextsCache?.data);
    }
  }, [customTextsCache]);

  const getDataText = async customTexts => {
    try {
      let title = '';
      let subTitle = '';
      if (customTexts) {
        switch (userCountry) {
          case COUNTRY_CODE.CAN:
            title = customTexts?.CA_HOME_PRODUCT_TITLE;
            subTitle = customTexts?.CA_SUB_HOME_PRODUCT_TITLE;
            break;
          default:
            title = customTexts?.US_HOME_PRODUCT_TITLE;
            subTitle = customTexts?.US_SUB_HOME_PRODUCT_TITLE;
            break;
        }
        setDataTexts({
          HOME_PRODUCT_TITLE: title,
          SUB_HOME_PRODUCT_TITLE: subTitle,
        });
      }
    } catch (error) {}
  };

  const onItemPress = async (item, index) => {
    if (
      item?.link &&
      item?.link?.length > 0 &&
      (item?.link?.includes('http') || item?.link?.includes('www.'))
    ) {
      setLoadingHome(true);
      const accessToken = await getAuth0AccessToken(dispatch);
      setLoadingHome(false);
      let options = null;
      try {
        options = JSON.parse(item?.options);
      } catch (error) {}
      const linkUrl = await prepareLink(
        item?.link,
        accessToken,
        options?.ctaLinkType,
      );
      logGASelectPromotion(item, index);
      openEcomWebview(
        navigate,
        {
          title: item?.productName,
          uri: linkUrl,
          canGoBack: true,
          originUri: item?.link,
          imageUrl: item?.image,
          clickLocation: 'home-product',
          origin: 'JustInWeb',
          isLogSelectItem: false,
        },
        getEcomProductDetail,
      );

      return;
    }
    if (
      item?.ctaLink &&
      item?.ctaLink?.length > 0 &&
      (item?.ctaLink?.includes('http') || item?.ctaLink?.includes('www.'))
    ) {
      setLoadingHome(true);
      const accessToken = await getAuth0AccessToken(dispatch);
      setLoadingHome(false);
      let options = null;
      try {
        options = JSON.parse(item?.options);
      } catch (error) {}
      const linkUrl = await prepareLink(
        item?.ctaLink,
        accessToken,
        options?.ctaLinkType,
      );
      logGASelectPromotion(item, index);
      openEcomWebview(
        navigate,
        {
          title: item?.productName,
          uri: linkUrl,
          canGoBack: true,
          originUri: item?.ctaLink,
          imageUrl: item?.image,
          clickLocation: 'home-product',
          origin: 'JustInWeb',
          isLogSelectItem: false,
        },
        getEcomProductDetail,
      );
    }
  };

  const logGASelectPromotion = async (item, index) => {
    if (item) {
      const listProductId = [];
      const options =
        typeof item?.options === 'object'
          ? item?.options
          : JSON.parse(item?.options);
      if (options?.ctaLinkType === WEBVIEW_PAGE_TYPE.PDP) {
        let productId = getProductIdFromUrl(item?.link);
        try {
          let productDetails = await getEcomProductDetail(productId);
          if (productDetails) {
            listProductId.push(productId);
          }
        } catch (error) {}
      }
      const tileTitle = item?.productName;
      if (!isEmpty(listProductId)) {
        const paramGA = {
          promotion_id: item?.id,
          promotion_name: item?.productName,
          creative_name: 'Full Banner Image',
          creative_slot: 'Home > Just-In',
          location_id: 'Home > Just-In',
        };
        const rsLogGA = await GA_selectPromotionWithEcomData({
          listProductId,
          paramGA,
          index,
          tileTitle,
          getMultipleEcomProductDetails,
        });
        if (!rsLogGA) {
          logEventPromotion({
            products: [item],
            eventName: GA_EVENT_NAME.SELECT_PROMOTION,
            clickIndex: index,
            tileTitle,
          });
        }
      } else {
        logEventPromotion({
          products: [item],
          eventName: GA_EVENT_NAME.SELECT_PROMOTION,
          clickIndex: index,
          tileTitle,
        });
      }
    }
  };

  const logEventPromotion = ({
    products,
    eventName,
    clickIndex = 0,
    tileTitle,
  }) => {
    if (products && products.length) {
      const items = [];
      products.map(val => {
        const item = {
          promotion_id: val?.id,
          promotion_name: val?.productName,
          creative_name: 'Full Banner Image',
          creative_slot: 'Home > Just-In',
          location_id: 'Home > Just-In',
        };
        if (eventName === GA_EVENT_NAME.SELECT_PROMOTION) {
          item.index = clickIndex + 1 + '';
        }
        items.push(item);
      });
      GA_logEvent(eventName, {
        currency: 'USD',
        tile_title: tileTitle,
        items,
      });
    }
  };

  const renderBrandLogo = item => {
    if (item.brandLogo) {
      if (item?.brandLogo?.includes?.('.svg')) {
        return <SvgUri uri={item.brandLogo} width={12} height={12} />;
      }
      return (
        <Image style={[styles.brandLogo]} source={{uri: item.brandLogo}} />
      );
    }
    return <LogoMyTMGray style={[styles.brandLogo]} />;
  };

  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity
        delayPressIn={100}
        delayPressOut={100}
        activeOpacity={0.8}
        onPress={() => onItemPress(item, index)}
        style={[
          styles.itemWrapper,
          appStyles.viewShadowLightBig,
          alignment === 'start'
            ? {marginLeft: 8, marginRight: 0}
            : {marginRight: 8, marginLeft: 0},
        ]}
      >
        <FastImage style={[styles.itemImage]} source={{uri: item.image}} />

        <View style={{marginHorizontal: 8, alignItems: 'center'}}>
          <View style={styles.brandContainer}>
            {renderBrandLogo(item)}

            <Text
              size={12}
              numberOfLines={1}
              style={{color: 'rgba(0, 0, 0, 0.6)'}}
            >
              | {item?.tag || 'TAYLORMADE'}
            </Text>
          </View>
          <Text
            Din79Font
            style={styles.copyText}
            black
            size={22}
            numberOfLines={2}
          >
            {item?.productName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
  if (productItemsData.length === 0 && !loading) {
    return null;
  }

  if (productItemsData.length === 0 && loading) {
    return (
      <View style={{marginTop: 24}}>
        <ShimmerPlaceholder
          LinearGradient={LinearGradient}
          width={150}
          height={20}
          shimmerStyle={{marginHorizontal: 16}}
        />
        <ShimmerPlaceholder
          LinearGradient={LinearGradient}
          width={200}
          height={20}
          shimmerStyle={{marginHorizontal: 16, marginTop: 4, marginBottom: 8}}
        />
        <View style={[appStyles.row, {marginBottom: 10}]}>
          <ShimmerPlaceholder
            LinearGradient={LinearGradient}
            width={wp(88)}
            height={wp(90) * 1.28}
            shimmerStyle={{marginHorizontal: 8, borderRadius: 16}}
          />
          <ShimmerPlaceholder
            LinearGradient={LinearGradient}
            width={wp(88)}
            height={wp(90) * 1.28}
            shimmerStyle={{borderRadius: 16}}
          />
        </View>
      </View>
    );
  }

  return (
    <View
      style={{marginTop: 24}}
      onLayout={e => {
        onLayout?.(e.nativeEvent.layout.y, e.nativeEvent.layout.height);
      }}
    >
      <View style={{marginHorizontal: 16}}>
        {dataTexts?.HOME_PRODUCT_TITLE && (
          <Text size={16} black>
            {dataTexts?.HOME_PRODUCT_TITLE || ''}
          </Text>
        )}
        {dataTexts?.SUB_HOME_PRODUCT_TITLE && (
          <Text size={16} black style={{marginBottom: 14, fontWeight: '700'}}>
            {dataTexts?.SUB_HOME_PRODUCT_TITLE || ''}
          </Text>
        )}
      </View>
      <Carousel
        data={productItemsData}
        renderItem={renderItem}
        sliderWidth={wp(100)}
        itemWidth={wp(90)}
        inactiveSlideScale={1}
        inactiveSlideOpacity={1}
        enableMomentum={true}
        decelerationRate={0.9}
        swipeThreshold={40}
        activeSlideAlignment={alignment}
        onBeforeSnapToItem={index => {
          if (index === 0) {
            setAlignment('start');
          } else if (index === productItemsData.length - 1) {
            setAlignment('end');
          }
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  itemWrapper: {
    marginLeft: 8,
    borderRadius: 24,
    backgroundColor: '#fff',
    marginBottom: 10,
    paddingHorizontal: 4,
    paddingTop: 4,
    paddingBottom: 16,
  },
  itemImage: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: wp(90) - 16,
    width: wp(90) - 16,
    marginBottom: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
  },
  brandLogo: {
    height: 12,
    width: 12,
    marginRight: 4,
  },
  copyText: {
    fontWeight: '800',
    textAlign: 'center',
    minHeight: 56,
  },
  brandContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
  },
});

export default React.forwardRef(HomeProduct);
