import React, {useEffect, useImperativeHandle, useState} from 'react';
import {View} from 'react-native';
import Animated, {
  useSharedValue,
  withTiming,
  useAnimatedStyle,
} from 'react-native-reanimated';

const AnimatedView = Animated.createAnimatedComponent(View);

const PercentageProgress = (
  {percentageValue, maxWidth, isSelectedAnswer},
  ref,
) => {
  const linePercentWidth = useSharedValue(0);
  const animateValue = () => {
    linePercentWidth.value = withTiming((maxWidth * percentageValue) / 100, {
      duration: 400,
    });
  };
  useImperativeHandle(ref, () => ({
    animateValue: () => {
      animateValue();
    },
    reset: () => {
      linePercentWidth.value = 0;
    },
  }));
  const linePercentStyle = useAnimatedStyle(() => {
    return {
      width: linePercentWidth.value,
      backgroundColor: isSelectedAnswer
        ? 'rgba(3, 168, 0, 1)'
        : 'rgba(153, 153, 153, 1)',
      position: 'absolute',
      left: 0,
      zIndex: 1,
      height: '100%',
      borderTopLeftRadius: 16,
      borderBottomLeftRadius: 16,
    };
  });

  return <AnimatedView style={linePercentStyle} />;
};

export default React.forwardRef(PercentageProgress);
