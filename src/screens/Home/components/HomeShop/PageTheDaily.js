import React, {useState, useRef, useImperativeHandle, useEffect} from 'react';
import {View, TouchableOpacity, Image, StyleSheet} from 'react-native';
import Text from 'components/Text';
import {getTheDaily} from 'requests/home';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Video from 'react-native-video';
import {
  useFocusEffect,
  useIsFocused,
  useNavigation,
} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {getSingleCmsContent} from 'requests/content';
import {getAuth0AccessToken} from 'utils/user';
import {getProductIdFromUrl, openEcomWebview, prepareLink} from 'utils/shop';
import {GA_logSelectContentEvent} from 'utils/article';
import {
  GA_logEvent,
  GA_logViewItemList,
  GA_selectPromotionWithEcomData,
  getProductIds,
} from 'utils/googleAnalytics';
import {
  CACHE_KEY,
  checkMainCountry,
  COUNTRY_CODE,
  GA_EVENT_NAME,
  WEBVIEW_PAGE_TYPE,
} from 'utils/constant';
import {setDeepLink} from 'reducers/app';
import CustomImage from 'components/CustomImage/CustomImage';
import {updateDailyData} from 'reducers/dataCache';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';
import {isEmpty} from 'lodash';
import { useInAppReview } from 'hooks/useInAppReview';

const PageTheDaily = ({setHasData}, ref) => {
  const [data, setData] = useState(null);
  const [cmsVideoData, setCmsVideoData] = useState(null);
  const [loading, setLoading] = useState(false);

  const dataVideo = useRef(null);
  const [pause, setPause] = useState(false);
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const isScreenFocused = useIsFocused();
  const {navigate} = navigation;
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const dailyDataCache = useSelector(state => state.dataCache?.homeDaily);
  const currentCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.HOME_THE_DAILY,
  )?.version;
  const {getEcomProductDetail, getMultipleEcomProductDetails} =
    useEcomProductDetail();
  const isFirstRender = useRef(true);
  const hasOpenedDailyTile = useRef(false);
  const {triggerInAppReview} = useInAppReview();

  useEffect(() => {
    if (isScreenFocused && hasOpenedDailyTile.current) {
      setTimeout(() => {
        triggerInAppReview();
      }, 500);
      hasOpenedDailyTile.current = false;
    }
  }, [isScreenFocused]);

  useEffect(() => {
    if (appCacheVersions) {
      refreshDataIfNeeded();
    }
  }, [appCacheVersions]);

  useEffect(() => {
    if (dailyDataCache) {
      if (checkMainCountry(user?.userCountry)) {
        getData();
      }
      isFirstRender.current = false;
    }
  }, [dailyDataCache]);

  const refreshDataIfNeeded = async () => {
    try {
      let response = null;
      if (checkMainCountry(user?.userCountry)) {
        if (
          currentCacheVersion === dailyDataCache?.version &&
          appCacheVersions?.country === dailyDataCache?.country &&
          currentCacheVersion != null
        ) {
          if (isScreenFocused && !isFirstRender.current) {
            //if the data is not changed from the Admin Portal, so re-assign old value from cache to trigger the Google Analytics events
            dispatch(
              updateDailyData({
                country: userCountry,
                version: currentCacheVersion,
                data: dailyDataCache?.data,
              }),
            );
          }
        } else {
          setLoading(true);
          try {
            const params = {
              page: 1,
              take: 30,
              country: userCountry,
            };
            response = await getTheDaily(params);
            dispatch(
              updateDailyData({
                country: userCountry,
                version: currentCacheVersion,
                data: response?.widgets || [],
              }),
            );
          } catch (error) {
          } finally {
            setLoading(false);
          }
        }
      } else {
        getTheDailyDataForOtherCountry();
      }
    } catch (error) {
      console.log('error fresh daily data', error.message);
    }
  };

  useImperativeHandle(ref, () => ({
    playVideo: () => {
      if (pause && !loading && data?.videoLink) {
        setPause(false);
      }
    },
    pauseVideo: () => {
      if (!pause && !loading && data?.videoLink) {
        setPause(true);
      }
    },
  }));

  useFocusEffect(
    React.useCallback(() => {
      return () => {
        setPause(true);
      };
    }, []),
  );

  useEffect(() => {
    if (data) {
      let listProductId = getProductIds([data]);
      if (listProductId.length > 0) {
        GA_logViewItemList(
          listProductId,
          'home-page-the-daily',
          undefined,
          undefined,
          getMultipleEcomProductDetails,
        );
      }
      logEventPromotion({
        product: data,
        eventName: GA_EVENT_NAME.VIEW_PROMOTION,
      });
    }
  }, [data]);

  const getTheDailyDataForOtherCountry = () => {
    try {
      let ecomLink = '';
      switch (userCountry) {
        case COUNTRY_CODE.GBR:
          ecomLink =
            'https://www.taylormadegolf.co.uk/on/demandware.store/Sites-TMaG-UK-Site/en_GB/Home-Show?utm_source=teamtaylormade&utm_medium=tmapp&utm_campaign=gen+col+gen+tmapp+App';
          break;
        case COUNTRY_CODE.DEU:
          ecomLink =
            'https://de.taylormadegolf.eu/home?lang=de_DE&utm_source=teamtaylormade&utm_medium=tmapp&utm_campaign=gen%20col%20TM%20tmapp%20App';
          break;
        case COUNTRY_CODE.FRA:
          ecomLink =
            'https://www.taylormadegolf.fr/home?lang=fr_FR&utm_source=teamtaylormade&utm_medium=tmapp&utm_campaign=gen%20col%20TM%20tmapp%20App';
          break;
        case COUNTRY_CODE.AUS:
          ecomLink =
            'https://www.taylormadegolf.com.au/home?utm_source=teamtaylormade&utm_medium=tmapp&utm_campaign=gen+col+TM+tmapp+App';
          break;
        case COUNTRY_CODE.SWE:
          ecomLink =
            'https://www.taylormadegolf.eu/home?utm_source=teamtaylormade&utm_medium=tmapp&utm_campaign=gen+col+TM+tmapp+App';
          break;
        default:
          break;
      }
      if (ecomLink) {
        setHasData(true);
        setData({
          countries: userCountry,
          ctaLink: ecomLink,
          ctaText: 'Shop Now',
          description:
            'Select a number, a logo, and add text to personalize your golf balls when you subscribe',
          id: 'AA3965E5-BEEF-4951-B153-E05146F5CBB3',
          imageLink:
            'https://teamtmassets.taylormadegolf.com/uploads/2025/01/1736879910_0_TM25MWD_TC370_Qi35_Hero_Studio_v1.jpeg',
          options: {
            category: 'WEB_LINK',
            ctaBackgroundColor: '#fff',
            ctaLinkType: 'OTHER',
            ctaTextColor: '#000',
            titleColor: '#fff',
          },
          sortOrder: 1,
          status: 'ACTIVE',
          title: 'The New Qi35 Drivers',
          type: 'THE_DAILY',
          videoLink: null,
        });
      } else {
        setHasData(false);
        setData(null);
      }
    } catch (error) {}
  };

  const getData = async () => {
    try {
      setHasData(true);
      if (dailyDataCache?.data?.length > 0) {
        const dataResponse = dailyDataCache?.data?.[0];
        const options = JSON.parse(dataResponse?.options);
        setData({
          ...dataResponse,
          options,
        });
        setHasData(true);

        // handle case cms video
        if (['videos', 'partnerVideos'].includes(options?.cmsType)) {
          const videoContent = await getSingleCmsContent(
            options?.cmsType,
            options?.ctaCMS,
          );
          setCmsVideoData({
            id: videoContent?.data?.video_id,
            title: videoContent?.data?.title,
            host: videoContent?.data?.video_type,
            contentId: videoContent?.data?.id,
          });
        }
      } else {
        setHasData(false);
        setData(null);
      }
    } catch (error) {
      if (!data) {
        setHasData(false);
      }
    }
  };

  const onLoad = () => {
    dataVideo?.current?.seek(0.01);
  };

  const onEnd = () => {
    dataVideo?.current?.seek(0.01);
  };

  const logGASelectPromotion = async item => {
    if (item) {
      const listProductId = [];
      const options =
        typeof item?.options === 'object'
          ? item?.options
          : JSON.parse(item?.options);
      if (options?.ctaLinkType === WEBVIEW_PAGE_TYPE.PDP) {
        let productId = getProductIdFromUrl(item?.ctaLink);
        try {
          let productDetails = await getEcomProductDetail(productId);
          if (productDetails) {
            listProductId.push(productId);
          }
        } catch (error) {}
      }
      const tileTitle = item?.title;
      if (!isEmpty(listProductId)) {
        const paramGA = {
          promotion_id: item?.id,
          promotion_name: item?.title,
          creative_name: `${item?.ctaText} CTA`,
          creative_slot: 'Home > The Daily',
          location_id: 'Home > The Daily',
        };
        const rsLogGA = await GA_selectPromotionWithEcomData({
          listProductId,
          paramGA,
          tileTitle,
          getMultipleEcomProductDetails,
        });
        if (!rsLogGA) {
          logEventPromotion({
            product: item,
            eventName: GA_EVENT_NAME.SELECT_PROMOTION,
            tileTitle,
          });
        }
      } else {
        logEventPromotion({
          product: item,
          eventName: GA_EVENT_NAME.SELECT_PROMOTION,
          tileTitle,
        });
      }
    }
  };

  const logEventPromotion = ({product, eventName, tileTitle}) => {
    if (product) {
      const items = [
        {
          promotion_id: product?.id,
          promotion_name: product?.title,
          creative_name: `${product?.ctaText} CTA`,
          creative_slot: 'Home > The Daily',
          location_id: 'Home > The Daily',
        },
      ];
      GA_logEvent(eventName, {
        currency: 'USD',
        tile_title: tileTitle,
        items,
      });
    }
  };

  const onPressCTA = async () => {
    if (data?.options?.category === 'CMS') {
      const formatData = {...data, backgroundImage: data?.imageLink};
      if (['videos', 'partnerVideos'].includes(data?.options?.cmsType)) {
        GA_logSelectContentEvent({
          contentName: cmsVideoData?.title,
          contentType: 'video',
          clickLocation: 'home-page-the-daily',
        });
        navigation.navigate('Video', {
          video: {
            ...cmsVideoData,
            clickLocation: 'home-page-the-daily',
            contentName: cmsVideoData?.title,
          },
        });
      } else {
        GA_logSelectContentEvent({
          contentName: data?.title,
          contentType: 'article',
          clickLocation: 'home-page-the-daily',
        });
        navigation.navigate('FeedArticle', {
          screen: 'FeedArticle',
          params: {
            item: formatData,
            clickLocation: 'home-page-the-daily',
          },
        });
      }
    } else if (data?.options?.category === 'DEEP_LINK') {
      const paramsDeelinks = data?.options?.ctaDeepLink.split('/');
      if (paramsDeelinks?.length > 0) {
        const lastItem = paramsDeelinks[paramsDeelinks.length - 1];
        if (lastItem && lastItem?.length > 0) {
          dispatch(setDeepLink(lastItem));
        }
      }
    } else if (
      data?.ctaLink &&
      data?.ctaLink?.length > 0 &&
      (data?.ctaLink?.includes('http') || data?.ctaLink?.includes('www.'))
    ) {
      logGASelectPromotion(data);
      const accessToken = await getAuth0AccessToken(dispatch);
      let options = data?.options;
      try {
        options = JSON.parse(data?.options);
      } catch (error) {}
      const linkUrl = await prepareLink(
        data?.ctaLink,
        accessToken,
        options?.ctaLinkType,
      );
      openEcomWebview(
        navigate,
        {
          uri: linkUrl,
          canGoBack: true,
          originUri: data?.ctaLink,
          imageUrl: data?.imageLink,
          clickLocation: 'home-page-the-daily',
          origin: 'HomeDaily',
          title: data?.title,
          isLogSelectItem: false,
        },
        getEcomProductDetail,
      );
      hasOpenedDailyTile.current = true;
    } else {
      navigation.navigate('WebView', {
        screen: 'WebView',
        params: {
          title: data?.title,
          uri: data?.ctaLink,
          origin: 'HomeDaily',
        },
      });
      hasOpenedDailyTile.current = true;
    }
  };

  if (!data && !loading) {
    return null;
  }
  if (loading) {
    return (
      <ShimmerPlaceholder
        LinearGradient={LinearGradient}
        width={wp(100)}
        height={wp(100) * 1.61}
      />
    );
  }
  return (
    <View style={styles.container}>
      {data?.videoLink ? (
        <Video
          source={{
            uri: data?.videoLink?.replace(/ /g, '%20'),
          }} // Can be a URL or a local file.
          ref={dataVideo} // Store reference
          style={{flex: 1}}
          paused={pause}
          onLoad={onLoad}
          onEnd={onEnd}
          resizeMode={'cover'}
          ignoreSilentSwitch={'ignore'}
          repeat
        />
      ) : (
        <CustomImage
          source={{
            uri: data?.imageLink,
          }}
          style={{width: wp(100), aspectRatio: 1 / 1.55}}
        />
      )}
      <View style={styles.viewLinear}>
        <Text
          size={22}
          Din79Font
          weight={800}
          style={{
            color: data?.options?.titleColor || '#fff',
            paddingHorizontal: 16,
            marginTop: 25,
            textAlign: 'center',
          }}
        >
          {data?.title}
        </Text>
        {data?.ctaText && (
          <TouchableOpacity
            style={[
              {
                backgroundColor: data?.options?.ctaBackgroundColor || '#fff',
              },
              styles.touchCTA,
            ]}
            onPress={onPressCTA}
          >
            <Text
              Din79Font
              weight={700}
              size={12}
              style={{
                color: data?.options?.ctaTextColor || '#000',
                letterSpacing: 1.2,
              }}
            >
              {data?.ctaText}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    width: wp(100),
    height: wp(100) * 1.55,
    marginTop: -1,
  },
  touchCTA: {
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 40,
    alignSelf: 'center',
    marginTop: 24,
    marginBottom: 24,
  },
  viewLinear: {
    left: 0,
    right: 0,
    bottom: 0,
    position: 'absolute',
    overflow: 'hidden',
    zIndex: 2,
  },
});

export default React.forwardRef(PageTheDaily);
