import React, {useRef, useEffect, useState} from 'react';
import {
  View,
  Platform,
  TouchableOpacity,
  TouchableWithoutFeedback,
  StyleSheet,
} from 'react-native';
import BottomSheet from 'reanimated-bottom-sheet';

import USGA_LOGO from 'assets/imgs/usga_logo_large.svg';
import CANADA_GOLF_LOGO from 'assets/imgs/canadagolf.svg';

import appStyles from 'styles/global';
import Icon from 'react-native-vector-icons/AntDesign';
import {moderateScale} from 'react-native-size-matters';
import {useDispatch, useSelector} from 'react-redux';
import Text from 'components/Text';
import Button from 'components/Button';
import {addCurrentUser} from 'reducers/user';
import {canShowGHIN, checkShowPopupGHIN} from 'utils/user';
import {t} from 'i18next';
import {GA_logCtaEvent, GA_logScreenViewV2} from 'utils/googleAnalytics';
import {
  COUNTRY_CODE,
  PAGE_NAME,
  SCREEN_CLASS,
  SCREEN_TYPES,
} from 'utils/constant';
import useMembershipLevel from 'hooks/useMembershipLevel';
import {
  getCountry,
  getNewUser,
  isCanadaMarket,
  setNewUser,
} from 'utils/commonVariable';
import {GOLF_CANADA_COLOR} from 'config';
import {widthPercentageToDP} from 'react-native-responsive-screen';

const PopupConnectHandicap = ({navigation}) => {
  const [showOverlay, setShowOverlay] = useState(false);
  const sheetRef = useRef(null);
  const user = useSelector(state => state?.user);
  const ghin = useSelector(state => state?.ghin);
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);

  const {membershipLevel} = useMembershipLevel();
  const SCREEN_NAME = 'home - handicap index pop-up';
  const dispatch = useDispatch();
  const heightBottomTab = widthPercentageToDP(22) / 2;
  useEffect(() => {
    if (
      getNewUser() &&
      checkShowPopupGHIN(ghin, user) &&
      (canShowGHIN(showHideFeatures, user) || getCountry() === COUNTRY_CODE.CAN)
    ) {
      openBottomSheet();
    }
    return () => {};
  }, [user, ghin]);

  useEffect(() => {
    if (showOverlay) {
      let screenName = PAGE_NAME.HOME_CONNECT_USGA;
      let screenClass = SCREEN_CLASS.HOME;
      let screenType = SCREEN_TYPES.HOME;
      GA_logScreenViewV2(screenName, screenClass, screenType, screenType);
    }
  }, [showOverlay]);

  const openBottomSheet = () => {
    sheetRef.current?.snapTo(0); // Open bottom sheet
    setShowOverlay(true);
  };
  const closeBottomSheet = () => {
    setNewUser(false);
    sheetRef.current?.snapTo(1); // Close bottom sheet
    setShowOverlay(false);
  };
  const connectHandicap = () => {
    closeBottomSheet();
    GA_logCtaEvent(
      'connect_usga',
      t('ghin.connect_your_usga_handicap'),
      SCREEN_NAME,
      SCREEN_CLASS.HOME,
      SCREEN_TYPES.HOME,
    );
    navigation.navigate('Profile', {
      screen: 'HandicapUSGA',
      params: {
        clickLocation: 'home',
      },
    });
  };

  const openSignUpHandicapIndex = () => {
    closeBottomSheet();
    GA_logCtaEvent(
      'handicap_signup',
      t('ghin.get_a_handicap_index'),
      SCREEN_NAME,
      SCREEN_CLASS.HOME,
      SCREEN_TYPES.HOME,
    );
    if (isCanadaMarket()) {
      navigation.navigate('WebView', {
        screen: 'WebView',
        params: {
          title: 'Handicap Index',
          uri: `https://join.golfcanada.ca/`,
          origin: 'GolfCanada',
        },
      });
    } else {
      navigation.navigate('WebView', {
        screen: 'WebView',
        params: {
          title: 'Handicap Index',
          uri: `https://getahandicap.usga.org/v2app/#/golfer-registration/sign-up`,
          origin: 'HandicapUSGA',
        },
      });
    }
  };

  return (
    <>
      {showOverlay ? (
        <TouchableWithoutFeedback
          onPress={() => {
            closeBottomSheet();
          }}
        >
          <View
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(17, 17, 17, 0.3)',
            }}
          />
        </TouchableWithoutFeedback>
      ) : null}
      <BottomSheet
        ref={sheetRef}
        snapPoints={
          isCanadaMarket()
            ? [460 + heightBottomTab, 0]
            : [440 + heightBottomTab, 0]
        }
        borderRadius={appStyles.borderRadius.borderRadius}
        initialSnap={1}
        renderHeader={() => (
          <TouchableOpacity
            onPress={() => {
              closeBottomSheet();
            }}
            style={[
              appStyles.mLAuto,
              {paddingBottom: 24, alignItems: 'flex-end'},
            ]}
          >
            <View
              style={[
                styles.closeIcon,
                appStyles.vCenter,
                appStyles.hCenter,
                {marginRight: 20},
              ]}
            >
              <Icon name="close" size={moderateScale(20)} color="#111111" />
            </View>
          </TouchableOpacity>
        )}
        renderContent={() => (
          <View style={[appStyles.fullHeight, appStyles.whiteBg]}>
            <View style={[appStyles.pHSm, {marginTop: 36}]}>
              <View style={[appStyles.hCenter, {marginBottom: 36}]}>
                {isCanadaMarket() ? <CANADA_GOLF_LOGO /> : <USGA_LOGO />}
                <Text black style={[appStyles.bold, appStyles.mTSm]}>
                  {isCanadaMarket()
                    ? 'golfCanada.golf_Canada'
                    : 'ghin.handicap_index'}
                </Text>
                <View style={{width: 241, height: 64}}>
                  <Text
                    style={[
                      appStyles.textCenter,
                      appStyles.mTSm,
                      appStyles.grey,
                      {fontSize: 13},
                    ]}
                  >
                    {isCanadaMarket()
                      ? 'golfcanada.do_you_have_a_handicap'
                      : 'ghin.do_you_have_a_handicap'}
                  </Text>
                </View>
              </View>
              <Button
                text={
                  isCanadaMarket()
                    ? 'golfcanada.connect_your_whs_handicap'
                    : 'ghin.connect_your_usga_handicap'
                }
                textColor="white"
                backgroundColor={
                  isCanadaMarket() ? GOLF_CANADA_COLOR : '#003764'
                }
                borderColor={isCanadaMarket() ? GOLF_CANADA_COLOR : '#003764'}
                onPress={connectHandicap}
                style={{marginHorizontal: 10, height: 46}}
                textStyle={{fontWeight: '500', fontSize: 18}}
                centered
                DINbold
              />
              <TouchableOpacity
                onPress={openSignUpHandicapIndex}
                style={[
                  {borderBottomWidth: 1},
                  appStyles.alignCenter,
                  {marginTop: 20},
                ]}
              >
                <Text
                  size={13}
                  style={[
                    {
                      color: '#111111',
                      fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
                    },
                  ]}
                >
                  {isCanadaMarket()
                    ? 'golfcanada.get_a_handicap_index'
                    : 'ghin.get_a_handicap_index'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      />
    </>
  );
};

const styles = StyleSheet.create({
  closeIcon: {
    backgroundColor: '#FFFFFF',
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(18),
  },
});

export default PopupConnectHandicap;
