/* eslint-disable react-hooks/exhaustive-deps */
import React, {useEffect, useRef, useState} from 'react';
import {useIsFocused} from '@react-navigation/native';
import {
  Dimensions,
  RefreshControl,
  Linking,
  Platform,
  View,
  ActivityIndicator,
  StyleSheet,
  Image,
} from 'react-native';
import {connect, useSelector} from 'react-redux';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import useAppState from 'hooks/useAppState';

import {setDeepLink} from 'reducers/app';
import {updateNotificationCount} from 'reducers/home';
import {updateBagList, updateClubCategories} from 'reducers/myBag';
import {
  updatePlanCoach,
  updatePlanContentStrings,
  updatePlanContentBatch,
} from 'reducers/plans';
import {updateCurrentBasket} from 'reducers/basket';
import {updateTTBOrders} from 'reducers/ttb';
import analytics from '@react-native-firebase/analytics';

import appStyles from 'styles/global';
import {navigateToDeepLink} from 'utils/deepLinks';
import {checkRefreshAuthToken, forceLogoutApp, loadResources} from 'utils/home';
import {useDispatch} from 'react-redux';
import {t} from 'i18next';
import {getLocalization} from 'utils/localization';
import {refreshConfigureFeatures} from 'utils/configureFeatures';
import {getDateCanPostScoreUSGA} from 'requests/play-stats';
import {getImageConfigs, getMyBag, updateUser} from 'requests/accounts';
import {getTotalNotificationsUnRead} from 'requests/notifications';
import {
  getLanguage,
  getCountry,
  isCanadaMarket,
  getAskPermissions,
  setAskPermissions,
  setUserLocation,
  getUserLocation,
  getNearbyCourses,
  setNearbyCourses,
} from 'utils/commonVariable';
import moment from 'moment';
import Permissions from 'react-native-permissions';
import {
  isConnectedNetwork,
  monitorNetworkAndSyncRoundData,
} from 'utils/queueAndNetwork';
import VersionCheck from 'react-native-version-check';
import AppUpdateModal from 'components/AppUpdateModal';
import {getIdToken} from 'utils/user';
import {watchEvents} from 'react-native-watch-connectivity';
import {getClubInfoForWatch, getUserInfoForWatch} from 'utils/watch';
import {getDataCourseCache} from 'utils/home';
import {NativeEventEmitter, NativeModules} from 'react-native';
import {getConfig, decryptClientIDMyTM} from 'config/env';
import PopupConnectHandicap from './components/PopupConnectHandicap';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {getGolferProfile} from 'requests/ghin';
import {clearGHINHandicapIndex, updateGHINHandicapIndex} from 'reducers/ghin';
import {addCurrentUser} from 'reducers/user';
import {setImages} from 'reducers/images';
import {clearWHSHandicapIndex, updateWHSHandicapIndex} from 'reducers/whs';
import {getWHSGolferProfile} from 'requests/whs';
import {updateUserLoyaltyData, updateRewardProducts} from 'utils/loyalty';
import AsyncStorage from '@react-native-async-storage/async-storage';
import HomeShop from './components/HomeShop/HomeShop';
import HomeProduct from './components/HomeProduct/HomeProduct';
import HomeDrillOfTheWeek from './components/HomeDrill/HomeDrillOfTheWeek';
import HomeStories from './components/HomeStories/HomeStories';
import LinearGradient from 'react-native-linear-gradient';
import PermissionsOverlay from 'components/PermissionsOverlay';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import Geolocation from 'react-native-geolocation-service';
import {updatePlayLocation} from 'reducers/play';
import checkLocationPermission from 'utils/checkLocationPermission';
import {getDistance} from 'geolib';
import {getNearbyCoursesList} from 'utils/play';
import {CACHE_KEY, getCurrentLocationEnabled, IMAGES} from 'utils/constant';
import {heightPercentageToDP} from 'react-native-responsive-screen';
import {loadTrackingOrdersData} from 'utils/shop';
import FastImage from 'react-native-fast-image/src';
import {getTourorStories} from 'requests/club-house';
import {preloadImages} from 'utils/image';
import CartnerConvos from './components/CartnerConvos/CartnerConvos';
import * as ScreenOrientation from 'expo-screen-orientation';
import {getDataTexts} from 'requests/shop';
import {
  cleanEcomProducts,
  updateAppTextData,
  updateTourStoryData,
} from 'reducers/dataCache';
import {cloneDeep} from 'lodash';
import {getModuleCacheData} from 'requests/moduleCache';
import {updateCacheVersionData} from 'reducers/cacheVersion';

const Home = ({
  navigation,
  route,
  updateBagList,
  updateClubCategories,
  updatePlanCoach,
  updatePlanContentStrings,
  updatePlanContentBatch,
  updateCurrentBasket,
  setDeepLink,
  updateTTBOrders,
  updateNotificationCount,
}) => {
  const tabBarheight = useBottomTabBarHeight();
  const user = useSelector(state => state?.user);
  const deepLink = useSelector(state => state?.app?.deepLink);
  const basket = useSelector(state => state?.basket);
  const permissions = useSelector(state => state?.app?.permissions);
  const bagList = useSelector(state => state.myBag?.bagList);
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );
  const [showConnectHandicap, setShowConnectHandicap] = useState(false);

  const forceUserLogout = useSelector(state => state?.app?.forceUserLogout);
  const [storeUrl, setStoreUrl] = useState(null);
  const [updateModalVisible, setUpdateModalVisible] = useState(false);
  const [loadingHome, setLoadingHome] = useState(false);
  const [loading, setLoading] = useState(false);
  const clubCategories = useSelector(state => state.myBag?.clubCategories);
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
  const homeShopRef = useRef();
  const homeProductRef = useRef();
  const homeStoriesRef = useRef();
  const homeDrillOfTheWeekRef = useRef();
  const cartnerConvosRef = useRef();
  const refIsFocus = useRef(false);
  const nearbyCourses = useSelector(state => state?.play?.nearbyCourses);
  const dispatch = useDispatch();
  const requestPermission = getAskPermissions();
  const offset = useSharedValue(0);
  const appImagesCache = useSelector(state => state?.images);
  const customTextsCache = useSelector(state => state.dataCache?.customTexts);
  const imagePlay = appImagesCache?.data?.[IMAGES.PLAY_BG] || null;
  const [homeProductYPosition, setHomeProductYPosition] = useState(0);
  const [homeProductHeight, setHomeProductHeight] = useState(0);
  const [isHomeProductVisible, setHomeProductVisible] = useState(false);
  const timeCallData = useRef();
  const isFocused = useIsFocused();
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(true);
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const currentImagesCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.CONTENT_IMAGE,
  )?.version;
  const currentTextsCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.CONTENT_TEXT,
  )?.version;
  const loyalty = useSelector(state => state?.loyalty);
  const tourStoryDataCache = useSelector(state => state.dataCache?.tourStory);
  const currentTourStoryCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.CLUB_TOUR_STORIES,
  )?.version;
  useEffect(() => {
    if (appCacheVersions) {
      refreshImagesCacheDataIfNeeded();
      refreshTextsCacheDataIfNeeded();
      refreshTourStoryDataIfNeeded();
    }
  }, [appCacheVersions]);

  const refreshImagesCacheDataIfNeeded = async () => {
    try {
      if (
        currentImagesCacheVersion === appImagesCache?.version &&
        appCacheVersions?.country === appImagesCache?.country &&
        currentImagesCacheVersion != null
      ) {
        return;
      } else {
        try {
          const images = await getImageConfigs();
          dispatch(
            setImages({
              country: user?.userCountry,
              version: currentImagesCacheVersion,
              data: images,
            }),
          );
        } catch (error) {
          console.log('error getImageApp');
        }
      }
    } catch (error) {}
  };

  const refreshTextsCacheDataIfNeeded = async () => {
    try {
      if (
        currentTextsCacheVersion === customTextsCache?.version &&
        appCacheVersions?.country === customTextsCache?.country &&
        currentTextsCacheVersion != null
      ) {
        return;
      } else {
        try {
          const texts = await getDataTexts();
          dispatch(
            updateAppTextData({
              country: user?.userCountry,
              version: currentTextsCacheVersion,
              data: texts,
            }),
          );
        } catch (error) {
          console.log('error get Text App');
        }
      }
    } catch (error) {}
  };

  const refreshTourStoryDataIfNeeded = async () => {
    try {
      if (
        currentTourStoryCacheVersion === tourStoryDataCache?.version &&
        appCacheVersions?.country === tourStoryDataCache?.country &&
        currentTourStoryCacheVersion != null
      ) {
        return;
      } else {
        try {
          const stories = await getTourorStories(user?.userCountry);
          dispatch(
            updateTourStoryData({
              country: user?.userCountry,
              version: currentTourStoryCacheVersion,
              data: stories || {},
            }),
          );
        } catch (error) {}
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (forceUserLogout) {
      forceLogoutApp(dispatch, navigation);
    }
  }, [forceUserLogout]);

  useEffect(() => {
    return () => {
      setAskPermissions(false);
    };
  }, []);

  useEffect(() => {
    monitorNetworkAndSyncRoundData();
    getDataCourseCache({navigation, fromHome: true});
    setNearbyCourses(nearbyCourses);
    //run only one time when open app, if any api need to refresh, then take them to reloadData function
    //setTimeOut for non-critical api call
    setTimeout(() => {
      updateRewardProducts(dispatch, user?.userCountry);
      updateUserGameProfile();
    }, 2000);
  }, []);
  const cleanupEcomProductsCache = () => {
    dispatch(cleanEcomProducts());
  };
  useEffect(() => {
    (async () => {
      try {
        cleanupEcomProductsCache();
        checkUpdateFromStore();
        //setTimeOut for non-critical api call
        setTimeout(() => {
          getTotalNotificationUnread();
          // Pre-load other resources and save to redux
          loadResources({
            updateBagList,
            updateClubCategories,
            updatePlanCoach,
            updatePlanContentStrings,
            updatePlanContentBatch,
            email: user.email,
            updateCurrentBasket,
            updateTTBOrders,
            basket,
            playService,
            userInfo: {...user, ...showHideFeatures?.data},
            dispatch: dispatch,
          });
        }, 1000);
      } catch (error) {}
    })();
  }, []);

  useEffect(() => {
    // Handle deep link routing
    if (deepLink?.length) {
      navigateToDeepLink(
        deepLink,
        navigation?.navigate,
        setDeepLink,
        permissions,
        user,
      );
    }
  }, [deepLink]);

  const checkIfBackToOriginOrientation = async () => {
    try {
      const lockOrientationState =
        await ScreenOrientation.getOrientationLockAsync();
      return (
        lockOrientationState === ScreenOrientation.OrientationLock.PORTRAIT_UP
      );
    } catch (error) {}
  };

  //Hide Connect Handicap USGA Bottom Sheet when rotate the device
  useEffect(() => {
    const handleOrientationChange = async ({window}) => {
      const isBackToPortrait = await checkIfBackToOriginOrientation();
      if (isBackToPortrait) {
        setIsBottomSheetVisible(true);
      } else {
        setIsBottomSheetVisible(false);
      }
    };

    const subscription = Dimensions.addEventListener(
      'change',
      handleOrientationChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  const getTotalNotificationUnread = async () => {
    try {
      const notificationUnread = await getTotalNotificationsUnRead();
      // Update notification unread in redux
      updateNotificationCount(notificationUnread?.total || 0);
    } catch (error) {
      console.log('error ', error);
    }
  };

  useEffect(() => {
    if (playService) {
      timeCallData.current = moment().format('X');
      reloadData(true);
    }
  }, [playService]);

  useEffect(() => {
    if (tourStoryDataCache) {
      preloadStoryImages();
    }
  }, [tourStoryDataCache]);

  const preloadStoryImages = async () => {
    let listDownloadImage = [];
    const stories = cloneDeep(tourStoryDataCache?.data);
    let sortData = stories?.tourStories;
    if (stories?.newStories?.length > 0) {
      const newStories = stories?.newStories.reduce((pre, cur) => {
        return {...pre, [cur]: cur};
      }, {});
      //sort data by newStories. id is new in tourStories so we need move to end
      sortData = stories?.tourStories.sort((a, b) => {
        if (!newStories?.[a?.id] && newStories?.[b?.id]) {
          return -1;
        }
        if (newStories?.[a?.id] && !newStories?.[b?.id]) {
          return 1;
        }
        return 0;
      });
    }
    if (sortData) {
      sortData?.forEach?.(_element => {
        if (_element?.type?.toUpperCase() === 'IMAGE') {
          listDownloadImage = [
            ...listDownloadImage,
            {
              uri: _element?.url,
              priority: FastImage.priority.high,
              id: _element.id,
            },
          ];
        }
      });
    }

    let indexOfNewStory = null;
    if (stories?.newStories?.length > 0) {
      const newStories = stories?.newStories.reduce((pre, cur) => {
        return {...pre, [cur]: cur};
      }, {});
      for (let index = 0; index < listDownloadImage.length; index++) {
        const element = listDownloadImage[index];
        if (newStories?.[element?.id]) {
          indexOfNewStory = index;
          break;
        }
      }
    }
    if (listDownloadImage.length > 0) {
      let imagesToPreload = [...listDownloadImage];
      if (indexOfNewStory >= 0) {
        imagesToPreload = [
          ...listDownloadImage.slice(indexOfNewStory, listDownloadImage.length),
          ...listDownloadImage.slice(0, indexOfNewStory),
        ];
      }
      //preload image  when user open the page first time
      preloadImages(imagesToPreload);
    }
  };

  const updateUserGameProfile = async () => {
    let isCallApiUpdateUser = false;
    const golferProfile = user?.golferProfile;
    const data = {};
    if (!golferProfile?.timePlayingGolf?.value) {
      data.timePlayingGolf = '2 - 3';
      isCallApiUpdateUser = true;
    }
    if (!golferProfile?.roundsPerMonth) {
      data.rpm = 3;
      isCallApiUpdateUser = true;
    }
    if (!golferProfile?.weakestArea) {
      data.weakestArea = 'Driving, Approach, Around the Green, Putting';
      isCallApiUpdateUser = true;
    }
    if (!golferProfile?.misHit) {
      data.misHit = 'Fat';
      isCallApiUpdateUser = true;
    }
    if (!golferProfile?.favoriteTeamMembers) {
      data.favoriteTeamMembers = 'Tiger Woods, Rory McIlroy, Scottie Scheffler';
      isCallApiUpdateUser = true;
    }

    if (isCallApiUpdateUser) {
      try {
        const updatedUser = await updateUser(data);
        // Update user in redux
        dispatch(addCurrentUser(updatedUser));
      } catch (error) {}
    }
  };

  const processLoyaltyJob = () => {
    updateUserLoyaltyData(
      dispatch,
      user?.userCountry,
      loyalty,
      appCacheVersions,
    );
  };

  // first loading and home pull to refresh
  const reloadData = (setShowLoading = false) => {
    if (user && Object.keys(user)?.length && playService !== undefined) {
      recallAPI(setShowLoading);
    }
  };

  useAppState({
    onForeground: () => {
      if (refIsFocus.current) {
        homeShopRef?.current?.playVideo();
      }
      if (
        timeCallData.current &&
        timeCallData.current < moment().format('X') - 60
      ) {
        //Cause when open app it call API 3 times at useEffect, useAppState and useFocusEffect
        //So need check when timing call API at useEffect less more 1 minute then it not recall at here.
        getLocalization();
        refreshConfigureFeatures(dispatch, user, showHideFeatures);
        getTotalNotificationUnread();
        recallAPI();
        getHandicapIndexGHIN();
        getHandicapIndexWHS();
        checkUpdateFromStore();
      }
    },
    onBackground: () => {
      homeShopRef?.current?.pauseVideo();
    },
  });

  useEffect(() => {
    if (isFocused) {
      refIsFocus.current = true;
      if (
        timeCallData.current &&
        timeCallData.current < moment().format('X') - 60
      ) {
        recallAPI();
        getHandicapIndexGHIN();
        getHandicapIndexWHS();
        checkRefreshAuthToken();
      }
      homeShopRef?.current?.playVideo();
    } else {
      refIsFocus.current = false;
      homeShopRef?.current?.pauseVideo();
      setHomeProductVisible(false);
    }
  }, [isFocused]);

  useEffect(() => {
    let eventListener = null;
    let watchUnsubscribe = null;
    if (Platform.OS === 'ios') {
      watchUnsubscribe = watchEvents.on('message', (message, reply) => {
        if (message?.request === 'login') {
          handedLogicEventIOS(reply);
        }
      });
    } else if (Platform.OS === 'android') {
      const eventEmitter = new NativeEventEmitter(NativeModules.WearOSModule);
      eventListener = eventEmitter.addListener('getAuthFromWearOS', event => {
        try {
          const wearosModule = NativeModules.WearOSModule;
          handleLoginEventAndroid(event.nodeId, wearosModule);
        } catch (error) {
          console.log('error', error);
        }
      });
    }

    return () => {
      if (Platform.OS === 'ios' && !!watchUnsubscribe) {
        watchUnsubscribe();
      } else if (Platform.OS === 'android' && eventListener) {
        eventListener.remove();
      }
    };
  }, [user, bagList]);

  const getNearbyCoursesByCurrentLocation = async () => {
    try {
      const checkPermission = await checkLocationPermission();
      if (checkPermission) {
        getLocation();
      }
    } catch (error) {}
  };

  const getDataNearbyCourse = async (coords = null) => {
    let retrieveCoordinates = coords;
    try {
      if (Object.keys(retrieveCoordinates)?.length) {
        await getNearbyCoursesList({
          latitude: retrieveCoordinates?.latitude,
          longitude: retrieveCoordinates?.longitude,
          dispatch,
          isGettingFirst3Courses: true,
        });
      }
    } catch (error) {}
  };

  const getLocation = () => {
    Geolocation.getCurrentPosition(
      position => {
        try {
          const posCoordinates = position.coords;
          if (getNearbyCourses()?.length === 0) {
            setUserLocation(posCoordinates);
            dispatch(updatePlayLocation(posCoordinates));
            getDataNearbyCourse(posCoordinates);
          } else {
            let currentLocation = getUserLocation();
            let distanceFromPreviousCoordinate = getDistance(
              currentLocation,
              posCoordinates,
            );
            //Only update the location's coordinates when distance greater than 500m
            if (
              (distanceFromPreviousCoordinate >= 500 &&
                Object.values(currentLocation)?.length) ||
              Object.values(currentLocation)?.length === 0
            ) {
              setUserLocation(posCoordinates);
              dispatch(updatePlayLocation(posCoordinates));
              getDataNearbyCourse(posCoordinates);
            }
          }
        } catch (error) {
          console.log(error.messsage);
        }
      },
      error => {},
      {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
    );
  };

  const callApiCheckVersion = async () => {
    try {
      const rs = await getModuleCacheData(user.userCountry);
      dispatch(updateCacheVersionData(rs));
    } catch (error) {
    } finally {
    }
  };

  const recallAPI = async (setShowLoading = false) => {
    try {
      setShowLoading && setLoading(true);
      //call api by order of Home components
      homeStoriesRef?.current.refreshData();
      homeDrillOfTheWeekRef?.current.refreshData();
      cartnerConvosRef?.current.refreshData();
      callApiCheckVersion();
      //setTimeout for medium-critical api call
      setTimeout(() => {
        getNearbyCoursesByCurrentLocation();
        processLoyaltyJob();
      }, 500);
      //setTimeout for non-critical api call
      setTimeout(() => {
        getDateForUSGA(dispatch);
        loadTrackingOrdersData(dispatch, user);
      }, 1000);
      setLoading(false);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleLoginEventAndroid = async (nodeId, wearosModule) => {
    if (user?.id) {
      const idToken = await getIdToken();
      wearosModule.checkLogin(nodeId, {
        userId: user?.tmUserIds?.mrp,
        userToken: idToken,
        isUnitYard:
          user?.measurementUnits?.toLowerCase?.() === 'meters' ? false : true,
      });
    } else {
      wearosModule.checkLogin(nodeId, {
        userId: '',
      });
    }
  };

  const handedLogicEventIOS = async reply => {
    try {
      if (user?.id) {
        const idToken = await getIdToken();
        const userInfo = await getUserInfoForWatch(user);
        const expiresIn = await AsyncStorage.getItem('expiresIn');
        const clubs = getClubInfoForWatch(clubCategories, bagList, playService);
        const url = await getConfig('MYTM_API_URL');
        const responseObject = {
          user_id: user?.tmUserIds?.mrp,
          token: idToken,
          url: `${url}/`,
          profile: {
            ...userInfo,
            canPlayAdvancedRound:
              permissions?.myTMPermission?.canPlayAdvancedRound,
          },
          clubs,
          clientId: decryptClientIDMyTM(),
          refreshToken: user?.refreshToken,
          expiresIn: moment(expiresIn).unix(),
        };
        reply({response: responseObject});
      } else {
        reply({response: t('watch.please_login_app_first')});
      }
    } catch (error) {
      reply({response: t('watch.please_login_app_first')});
    }
  };

  const checkUpdateFromStore = async () => {
    try {
      //IF IN DEV MODE, NO NEED FORCE UPDATE
      if (!__DEV__) {
        let res = await VersionCheck.needUpdate();
        if (res) {
          if (res.isNeeded) {
            setStoreUrl(res.storeUrl);
            setUpdateModalVisible(true);
          } else {
            //APP WAS NOT PUBLISH TO STORE
          }
        } else {
          //APP IS UP-TO-DATE
        }
      }
    } catch (error) {
      console.log('err', error);
    }
  };

  const goToStore = () => {
    Linking.canOpenURL(storeUrl).then(
      supported => {
        supported && Linking.openURL(storeUrl);
      },
      err => {
        console.log(err);
      },
    );
  };

  const getMembershipLevel = () => {
    if (
      user?.myTMSubscriptionLevel === 2 ||
      user?.myTMSubscriptionLevel === '2'
    ) {
      return 'Champion';
    } else if (
      user?.myTMSubscriptionLevel === 1 ||
      user?.myTMSubscriptionLevel === '1'
    ) {
      return 'Champion';
    } else {
      return 'Free';
    }
  };

  useEffect(() => {
    if (Object.keys(user)?.length > 0) {
      trackGoogleAnalytics();
    }
  }, [user?.cdmUID]);

  const trackGoogleAnalytics = async () => {
    try {
      if (user?.cdmUID) {
        await analytics().setUserId(user?.cdmUID);
      }
      const handicap =
        user?.golferProfile?.newHandicap?.tmCalculatedHandicap ||
        user?.golferProfile?.newHandicap?.userInputHandicap;
      const rounds_per_month = user?.golferProfile?.roundsPerMonth;

      const gaclient_id = await analytics().getAppInstanceId();

      const userAge = moment().diff(moment(user?.dob), 'years', true);
      const notificationPermissions = await Permissions.checkNotifications();
      let isLocationEnabled = await getCurrentLocationEnabled();
      let memberShipLevel = getMembershipLevel();
      let memberShipPlan = 'lifetime';
      if (memberShipLevel !== 'Free') {
        if (
          user?.myTMPermission?.subscriptionLength > 0 &&
          user?.myTMPermission?.subscriptionLength < 12
        ) {
          memberShipPlan = 'Monthly Membership';
        } else {
          memberShipPlan = 'Annual Membership';
        }
      }
      const lang = getLanguage() ? getLanguage() : 'en';
      const country = getCountry() ? getCountry() : 'Unknown';

      const userProperties = {
        country: country,
        language: lang,
        membership_level: memberShipLevel,
        handicap: handicap ? `${handicap}` : null,
        rounds_per_month: rounds_per_month ? `${rounds_per_month}` : null,
        handedness: user?.golferProfile?.handed || null,
        years_playing: user?.golferProfile?.timePlayingGolf?.value || null,
        target_score: user?.golferProfile?.targetScore
          ? `${user?.golferProfile?.targetScore}`
          : null,
        drive_distance: user?.golferProfile?.maximumDriverDistance || null,
        strongest_area: user?.golferProfile?.strongestArea || null,
        weakest_area: user?.golferProfile?.weakestArea || null,
        shot_shape: user?.golferProfile?.shotShape || null,
        ball_strike: user?.golferProfile?.ballStrike || null,
        mis_hit: user?.golferProfile?.misHit || null,
        eliminate_shot_type: user?.golferProfile?.avoidShot || null,
        scariest_shot: user?.golferProfile?.mostScaredShot || null,
        home_course: user?.golferProfile?.homeCourse || null,
        favorite_players: user?.golferProfile?.favoriteTeamMembers || null,
        gaclient_id,
        // type: 'user',
        user_age: Math.round(userAge) + '',
        user_gender: user?.gender || null,
        average_score: user?.golferProfile?.averageScore
          ? user?.golferProfile?.averageScore + ''
          : null,
        membership_plan: memberShipPlan,
        notifications_enabled:
          notificationPermissions?.status === 'granted' ? 'true' : 'false',
        location_enabled: isLocationEnabled ? 'true' : 'false',
        loyalty_member: 'true',
      };
      for (
        let index = 0;
        index < Object.keys(userProperties)?.length;
        index++
      ) {
        const element = Object.keys(userProperties)[index];
        if (
          typeof userProperties[element] === 'string' &&
          userProperties[element]?.length > 36
        ) {
          userProperties[element] = userProperties[element].substr(0, 36);
        }
      }
      await analytics().setUserProperties(userProperties);
    } catch (error) {
      console.log('===== error  ', error);
    }
  };

  const getDateForUSGA = async () => {
    try {
      const response = await getDateCanPostScoreUSGA();
      dispatch(addCurrentUser({dateCanPostUSGA: response?.date}));
    } catch (error) {
      dispatch(addCurrentUser({dateCanPostUSGA: null}));
      console.log('error getDateForUSGA');
    }
  };

  const getHandicapIndexGHIN = async () => {
    if (isCanadaMarket()) {
      return;
    }
    try {
      const resHandicapIndex = await getGolferProfile();
      if (resHandicapIndex) {
        //Show handicap Entered
        dispatch(updateGHINHandicapIndex(resHandicapIndex));
        dispatch(addCurrentUser({ghinId: resHandicapIndex?.ghin}));
      }
    } catch (error) {
      const checkInternetConnection = isConnectedNetwork();
      if (checkInternetConnection) {
        if (error?.response?.data?.ghin_id) {
          dispatch(
            updateGHINHandicapIndex({
              ghin: error?.response?.data?.ghin_id,
              email: error?.response?.data?.ghinEmail,
            }),
          );
        } else {
          dispatch(clearGHINHandicapIndex());
          dispatch(addCurrentUser({ghinId: null}));
        }
      }
      //Show handicap Pendding
    } finally {
    }
  };

  const getHandicapIndexWHS = async () => {
    if (!isCanadaMarket()) {
      return;
    }
    try {
      const golferProfile = await getWHSGolferProfile();
      if (golferProfile) {
        //Show handicap Entered
        dispatch(updateWHSHandicapIndex(golferProfile));
        dispatch(
          addCurrentUser({
            golfCanadaCardId: golferProfile?.golfCanadaCardId,
          }),
        );
      }
    } catch (error) {
      if (error?.response?.data?.golfCanadaCardId) {
        dispatch(
          updateWHSHandicapIndex({
            golfCanadaCardId: error?.response?.data?.golfCanadaCardId,
          }),
        );
      } else {
        dispatch(clearWHSHandicapIndex());
        dispatch(addCurrentUser({golfCanadaCardId: null}));
      }
      //Show handicap Pendding
    } finally {
    }
  };
  const styleScroll = useAnimatedStyle(() => {
    return {paddingTop: offset.value};
  });

  const onScroll = e => {
    let homeProductValidPosition =
      Math.floor(e.nativeEvent.contentOffset.y) -
      (homeProductYPosition - heightPercentageToDP(60));
    if (
      homeProductValidPosition > 0 &&
      homeProductValidPosition < heightPercentageToDP(60)
    ) {
      if (!isHomeProductVisible) {
        setHomeProductVisible(true);
      }
    }
    if (Platform.OS === 'ios') {
      if (e.nativeEvent.contentOffset.y < 0) {
        if (e.nativeEvent.contentOffset.y > -20) {
          offset.value = e.nativeEvent.contentOffset.y * -1;
        } else {
          offset.value = 20;
        }
      } else {
        offset.value = 0;
      }
    }
  };

  const onLocationAccepted = () => {
    getNearbyCoursesByCurrentLocation();
  };

  return (
    <View style={[appStyles.flex]}>
      <FocusAwareStatusBar barStyle={'dark-content'} />
      <AppUpdateModal
        visible={updateModalVisible}
        title={'app.force_update_version.modal.title'}
        description={t('app.force_update_version.modal.description_v2')}
        button1={
          storeUrl ? 'app.force_update_version.modal.button_update' : null
        }
        button1Action={goToStore}
      />
      <LinearGradient
        start={{x: 1, y: 0.5}}
        end={{x: 1, y: 4}}
        colors={['#ffffff', 'rgba(0, 0, 0, 1)']}
        style={{
          flex: 1,
        }}
      >
        <Animated.ScrollView
          style={[appStyles.flex, styleScroll]}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={[
            {
              paddingBottom: tabBarheight + (Platform.OS === 'ios' ? 30 : 0),
            },
          ]}
          onScroll={onScroll}
          scrollEventThrottle={16}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={() => reloadData(true)}
              tintColor={'black'}
              progressViewOffset={Platform.OS === 'ios' ? 30 : undefined}
            />
          }
        >
          <HomeShop ref={homeShopRef} />
          <HomeStories ref={homeStoriesRef} />
          <HomeDrillOfTheWeek ref={homeDrillOfTheWeekRef} />
          <HomeProduct
            ref={homeProductRef}
            setLoadingHome={setLoadingHome}
            onLayout={(y, height) => {
              setHomeProductYPosition(y);
              setHomeProductHeight(height);
            }}
            isVisibleToUser={isHomeProductVisible}
          />
          <Image style={{width: 1, height: 1}} source={{uri: imagePlay}} />
          <CartnerConvos ref={cartnerConvosRef} />
        </Animated.ScrollView>
      </LinearGradient>
      {requestPermission && (
        <PermissionsOverlay
          navigation={navigation}
          setShowConnectHandicap={setShowConnectHandicap}
          callbackWhenLocationAccepted={onLocationAccepted}
        />
      )}
      {((requestPermission && showConnectHandicap) || !requestPermission) &&
        isBottomSheetVisible && (
          <PopupConnectHandicap navigation={navigation} />
        )}
      {loadingHome && (
        <ActivityIndicator
          style={StyleSheet.absoluteFill}
          size={'large'}
          color={'#000'}
        />
      )}
    </View>
  );
};

const mapDispatchToProps = {
  updateBagList,
  updateClubCategories,
  updatePlanCoach,
  updatePlanContentStrings,
  updatePlanContentBatch,
  updateCurrentBasket,
  setDeepLink,
  updateTTBOrders,
  updateNotificationCount,
};

export default connect(null, mapDispatchToProps)(Home);
