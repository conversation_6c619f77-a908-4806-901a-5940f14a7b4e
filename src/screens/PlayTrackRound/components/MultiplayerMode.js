import React, {useEffect, useState} from 'react';
import {
  View,
  KeyboardAvoidingView,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Keyboard,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import IconSort from 'assets/imgs/ic_sort.svg';
import { TouchableWithoutFeedback } from 'react-native-gesture-handler';
import IconAddPlayer from 'assets/imgs/ic_add_player.svg';
import {heightPercentageToDP as hp, widthPercentageToDP as wp} from 'react-native-responsive-screen';

import appStyles from 'styles/global';
import Text from 'components/Text';
import {GREEN_STATS} from 'config';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Icon from 'react-native-vector-icons/Ionicons';
import TextInput from 'components/TextInput';
import ButtonPlusMinus from 'screens/PlayCourseMap/components/ButtonPlusMinus';
import {shuffle, isEqual} from 'lodash';
import DraggableFlatList, {
  NestableDraggableFlatList,
  ScaleDecorator,
} from 'react-native-draggable-flatlist';
import uuid from 'react-native-uuid';
import {
  GAME_TYPE_MULTIPLAYER,
  GAME_TYPE_MULTIPLAYER_TITLE,
  getNameInitials,
} from 'screens/PlayCourseMap/DataSubmitDefault';
import {t} from 'i18next';
import MultiplayerModeTooltipModal from 'screens/PlayCourseMap/components/MultiplayerModeTooltipModal';

const isTablet = DeviceInfo.isTablet();

const MultiplayerMode = ({
  navigation,
  setDragging,
  listPlayer,
  setListPlayer,
  type,
  setType,
}) => {
  const [isAdding, setAdding] = useState(false);
  const [currentPlayer, setCurrentPlayer] = useState({
    placeholder: 'Player Name',
    name: '',
    strokes: 0,
    id: uuid.v4(),
    initials: '',
    isEditing: false, //true if user is adding/editing a player
  });
  const [isTooltipVisible, setTooltipVisible] = useState(false);
  const isMatchPlay =
    (listPlayer?.length === 4 || listPlayer?.length === 2) &&
    type === GAME_TYPE_MULTIPLAYER.MATCH;
  const isTwoPlayersMatchPlay =
    listPlayer?.length === 2 && type === GAME_TYPE_MULTIPLAYER.MATCH;

  const onEditPlayer = playerIndex => {
    try {
      const newListPlayer = listPlayer.map((item, index) => {
        if (index === playerIndex) {
          item.isEditing = true;
          setCurrentPlayer(item);
        } else {
          //close other player's adding pane
          item.isEditing = false;
        }
        return item;
      });
      setAdding(false);
      setListPlayer([...newListPlayer]);
    } catch (error) {
      console.log(error);
    }
  };

  const onPlus = () => {
    try {
      //remove the decimal part of the stroke
      let currentStroke = Math.trunc(currentPlayer.strokes);
      if (currentStroke < 42) {
        setCurrentPlayer({...currentPlayer, strokes: +currentStroke + 1});
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onMinus = () => {
    try {
      let currentStroke = currentPlayer.strokes;
      if (currentStroke > 0) {
        setCurrentPlayer({...currentPlayer, strokes: +currentStroke - 1});
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onRemovePlayer = playerIndex => {
    try {
      let newListPlayer = [...listPlayer];
      newListPlayer.splice(playerIndex, 1);
      //if user remove 1 player of 4 current players(match play) then set to default game mode(SKINS)
      if (newListPlayer?.length === 3 && type === GAME_TYPE_MULTIPLAYER.MATCH) {
        setType(GAME_TYPE_MULTIPLAYER.SKINS);
      }
      setListPlayer(newListPlayer);
    } catch (error) {
      console.log(error);
    }
  };

  const onStartAddPlayer = () => {
    let newListPlayer = listPlayer.map((item, index) => {
      item.isEditing = false;
      return item;
    });
    setListPlayer([...newListPlayer]);
    setCurrentPlayer({
      placeholder: `Player ${listPlayer?.length + 1} Name`,
      name: '',
      strokes: 0,
      initials: '',
    });

    setAdding(true);
  };

  const onCancelAddPlayer = () => {
    try {
      setAdding(false);
    } catch (error) {
      console.log('error', error);
    }
  };

  const onCancelEditPlayer = index => {
    try {
      const item = listPlayer[index];
      item.isEditing = false;
      setListPlayer([...listPlayer]);
    } catch (error) {
      console.log('error', error);
    }
  };

  const onPressSavePlayer = async playerIndex => {
    if (currentPlayer.name !== '') {
      // const savedPlayer = await getAllPlayer();
      let currentName = currentPlayer.name?.toString().trim();
      let newListPlayer = [...listPlayer];
      // let listToCheckDuplicate = newListPlayer.concat(savedPlayer);
      let listToCheckDuplicate = newListPlayer;
      const checkIfNameExist = listToCheckDuplicate.find(
        item =>
          item.name?.toString().trim() === currentName &&
          item.id !== currentPlayer.id,
      );
      let incomingName = currentName;
      //check if the input name exist or not
      if (checkIfNameExist) {
        let suffixNumber = 2;
        incomingName = currentName + `(${suffixNumber})`;
        let checkIfModNameExist = listToCheckDuplicate.find(
          item =>
            item.name?.toString().trim() === incomingName &&
            item.id !== currentPlayer.id,
        );
        //check if the modified name exist or not
        while (checkIfModNameExist) {
          suffixNumber += 1;
          incomingName = currentName + `(${suffixNumber})`;
          checkIfModNameExist = listToCheckDuplicate.find(
            item =>
              item.name?.toString().trim() === incomingName &&
              item.id !== currentPlayer.id,
          );
        }
      }

      if (playerIndex >= 0) {
        newListPlayer = newListPlayer.map((item, index) => {
          if (playerIndex === index) {
            item = {
              ...currentPlayer,
              initials: getNameInitials(incomingName),
              name: incomingName,
            };
          }
          return item;
        });
      } else {
        const newPlayer = {
          ...currentPlayer,
          initials: getNameInitials(incomingName),
          name: incomingName,
          id: uuid.v4(),
          createAt: new Date(),
        };
        newListPlayer = [...listPlayer, newPlayer];
      }
      setAdding(false);
      setCurrentPlayer({
        placeholder: `Player ${newListPlayer?.length + 1} Name`,
        name: '',
        strokes: 0,
        initials: '',
      });

      newListPlayer = newListPlayer.map((item, index) => {
        item.isEditing = false;
        return item;
      });
      setListPlayer([...newListPlayer]);
    }
  };

  const onPressRecentPlayers = () => {
    navigation.navigate('RecentPlayer', {
      listContact: listPlayer,
      addContact: data => setListPlayer(data),
    });
  };

  const onShufflePlayer = () => {
    try {
      let newListPlayer = [...listPlayer];
      let shuffledListPlayer = shuffle(newListPlayer);
      //check if the result still the same
      if (isEqual(shuffledListPlayer, newListPlayer)) {
        if (shuffledListPlayer?.length >= 2) {
          let b = shuffledListPlayer[1];
          shuffledListPlayer[1] = shuffledListPlayer[0];
          shuffledListPlayer[0] = b;
        }
      }
      setListPlayer(shuffledListPlayer);
    } catch (error) {
      console.log(error);
    }
  };

  const showToolTipModal = () => {
    setTooltipVisible(true);
  };

  const renderItemPlayer = ({item, index, drag, isActive}) => {
    const isFirstTeam4Player = index % 2 === 0 && listPlayer?.length === 4;
    const hasTwoPlayers = listPlayer?.length === 2;
    const shouldRoundBorderTop = isFirstTeam4Player || hasTwoPlayers;
    const shouldRoundBorderBottom = !isFirstTeam4Player && !hasTwoPlayers;
    if (item.isEditing) {
      return renderAddPlayer(index);
    } else {
      return (
        <TouchableOpacity
          activeOpacity={0.8}
          onLongPress={drag}
          disabled={isActive}
          key={item.name + index}
          style={[
            appStyles.row,
            (!isMatchPlay || (isMatchPlay && !isFirstTeam4Player)) &&
              appStyles.viewShadow,
            appStyles.spaceBetween,
            appStyles.hCenter,
            styles.playerCardView,
            {
              shadowOpacity: 0.1,
              padding: isMatchPlay ? 0 : 15,
              paddingVertical: isMatchPlay ? 0 : 20,
              borderBottomLeftRadius: isMatchPlay
                ? shouldRoundBorderBottom
                  ? 8
                  : 0
                : undefined,
              borderBottomRightRadius: isMatchPlay
                ? shouldRoundBorderBottom
                  ? 8
                  : 0
                : undefined,
              borderTopLeftRadius: isMatchPlay
                ? shouldRoundBorderTop
                  ? 8
                  : 0
                : undefined,
              borderTopRightRadius: isMatchPlay
                ? shouldRoundBorderTop
                  ? 8
                  : 0
                : undefined,
              marginBottom: isMatchPlay && isFirstTeam4Player ? 0 : 10,
            },
          ]}
        >
          {isMatchPlay && (
            <>
              <View
                style={[
                  styles.teamLineView,
                  {
                    backgroundColor:
                      index < listPlayer?.length / 2 ? '#FFCC00' : '#3E4D54',
                    borderBottomLeftRadius: shouldRoundBorderBottom ? 8 : 0,
                    borderTopLeftRadius: shouldRoundBorderTop ? 8 : 0,
                  },
                ]}
              />
            </>
          )}
          <View style={[appStyles.hCenter, appStyles.vCenter, appStyles.flex]}>
            <View
              style={[
                appStyles.row,
                appStyles.spaceBetween,
                appStyles.hCenter,
                {
                  flex: 1,
                  padding: isMatchPlay ? 10 : 0,
                  paddingVertical: isMatchPlay ? 20 : 0,
                },
              ]}
            >
              {/* render draggable icon view */}
              {isMatchPlay && !hasTwoPlayers && (
                <View style={{marginRight: 10}}>
                  <View style={styles.arrangeLineAbove} />
                  <View style={styles.arrangeLineBelow} />
                  <View style={styles.arrangeLineAbove} />
                  <View style={styles.arrangeLineBelow} />
                  <View style={styles.arrangeLineAbove} />
                  <View style={styles.arrangeLineBelow} />
                  <View style={styles.arrangeLineAbove} />
                  <View style={styles.arrangeLineBelow} />
                </View>
              )}
              <View style={{flex: 1}}>
                <Text size={24} black style={{fontWeight: 'bold'}}>
                  {item.name}
                </Text>
                <Text
                  size={13}
                  style={{
                    color: isMatchPlay ? '#111111' : '#8C8B8F',
                    paddingTop: 3,
                  }}
                >
                  {`${t('play.player_strokes')} ${item.strokes}`}
                </Text>
              </View>
              <TouchableOpacity
                style={styles.touchEdit}
                onPress={() => onEditPlayer(index)}
              >
                <Text size={13} black style={{fontWeight: 'bold'}}>
                  play.button.edit_player
                </Text>
              </TouchableOpacity>
            </View>
            {isMatchPlay && isFirstTeam4Player && (
              <View style={styles.teamMemberSeparator} />
            )}
          </View>
        </TouchableOpacity>
      );
    }
  };

  const renderAddPlayer = (index = -1) => {
    return (
      <View
        key={index}
        style={[
          appStyles.row,
          appStyles.viewShadow,
          appStyles.spaceBetween,
          appStyles.hCenter,
          styles.addPlayerView,
        ]}
      >
        <View
          style={[{flex: 0.62, paddingBottom: currentPlayer.isEditing ? 9 : 0}]}
        >
          <View style={{flex: 1}}>
            <TextInput
              style={[styles.addPlayerInputContainer, {marginBottom: 12}]}
              placeholder={currentPlayer.placeholder}
              placeholderTextColor={'#BDBDBD'}
              onChangeText={value =>
                setCurrentPlayer({...currentPlayer, name: value})
              }
              defaultValue={currentPlayer.name}
              textContentType={'name'}
              inputProps={{maxLength: 40, autoFocus: index >= 0 ? false : true}}
              stylesInput={styles.addPlayerInput}
              // clearInput={clearSearch}
              autoCapitalize={'words'}
            />
          </View>
          <View style={[appStyles.row, appStyles.hCenter, {width: '75%'}]}>
            <ButtonPlusMinus
              isMinus
              onPress={onMinus}
              buttonStyle={{borderColor: 'black', borderWidth: 2}}
              buttonSize={32}
            />
            <View style={[appStyles.flex, appStyles.hCenter]}>
              <Text style={[{color: '#8C8B8F', fontSize: 13}]}>
                play.player_strokes
              </Text>
              <Text style={{fontWeight: 'bold', fontSize: 24}}>
                {currentPlayer.strokes}
              </Text>
            </View>

            <ButtonPlusMinus
              onPress={onPlus}
              buttonStyle={{borderColor: 'black', borderWidth: 2}}
              buttonSize={32}
            />
          </View>
        </View>
        <View
          style={[
            appStyles.spaceEnd,
            appStyles.alignEnd,
            {flex: 0.38, paddingTop: 15},
          ]}
        >
          <View
            style={[
              appStyles.hCenter,
              currentPlayer.isEditing
                ? appStyles.spaceBetween
                : appStyles.spaceEnd,
              appStyles.alignEnd,
              appStyles.flex,
            ]}
          >
            <TouchableOpacity
              style={[
                styles.touchEdit,
                appStyles.vCenter,
                appStyles.hCenter,
                {
                  backgroundColor: 'black',
                  borderRadius: 22,
                },
              ]}
              onPress={() => onPressSavePlayer(index)}
            >
              <Text size={13} white style={{fontWeight: 'bold'}}>
                {currentPlayer.isEditing
                  ? 'play.button.save_player'
                  : 'play.button.add_player'}
              </Text>
            </TouchableOpacity>
            {currentPlayer.isEditing && !currentPlayer?.isCurrentUser && (
              <Text
                size={13}
                style={[
                  appStyles.underlined,
                  {color: '#FF0000', marginTop: hp(2)},
                ]}
                onPress={() => onRemovePlayer(index)}
              >
                play.button.remove_player
              </Text>
            )}
          </View>
        </View>
        <View style={[appStyles.alignEnd, styles.clearIconView]}>
          <TouchableWithoutFeedback
            onPress={() => {
              Keyboard.dismiss();
              currentPlayer?.isEditing
                ? onCancelEditPlayer(index)
                : onCancelAddPlayer();
            }}
          >
            <Icon
              name={'close-outline'}
              color={'#000'}
              size={isTablet ? wp('3%') : wp('7%')}
            />
          </TouchableWithoutFeedback>
        </View>
      </View>
    );
  };

  const renderAddButtons = () => {
    return (
      <View style={[appStyles.row, appStyles.spaceBetween, {marginBottom: 10}]}>
        <TouchableOpacity style={{flex: 0.48}} onPress={onStartAddPlayer}>
          <View
            style={[
              appStyles.row,
              appStyles.viewShadow,
              appStyles.hCenter,
              appStyles.vCenter,
              styles.addButtonView,
            ]}
          >
            <AntDesign name="plus" size={15} />
            <View>
              <Text size={12} black style={{fontWeight: 'bold', marginLeft: 5}}>
                {`${t('play.button.add_player')} ${listPlayer?.length + 1}`}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
        <TouchableOpacity style={{flex: 0.48}} onPress={onPressRecentPlayers}>
          <View
            style={[
              appStyles.row,
              appStyles.viewShadow,
              appStyles.spaceAround,
              appStyles.hCenter,
              appStyles.vCenter,
              styles.recentPlayersButtonView,
            ]}
          >
            <View>
              <Text
                size={13}
                black
                style={{fontWeight: 'bold', marginRight: 5}}
              >
                play.button.recent_player
              </Text>
            </View>
            <Icon name={'chevron-forward'} size={15} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };
  const renderType = (
    roundType,
    title,
    style,
    playerQuantityRestrict,
    isDisabled,
  ) => {
    return (
      <TouchableOpacity
        style={[
          appStyles.row,
          styles.touchType,
          appStyles.spaceBetween,
          style,
          {background: 'white'},
        ]}
        onPress={() => setType(roundType)}
        disabled={isDisabled}
      >
        <View style={appStyles.row}>
          <Text size={17} style={{color: isDisabled ? '#8C8B8F' : 'black'}}>
            {title}{' '}
          </Text>
          <Text
            size={17}
            style={{color: '#8C8B8F'}}
          >{`(${playerQuantityRestrict} Players)`}</Text>
          <Text
            size={17}
            style={{color: '#8C8B8F', paddingHorizontal: 10}}
            onPress={showToolTipModal}
          >
            {'􀅴'}
          </Text>
        </View>
        <View
          style={[
            styles.viewContainerDot,
            type === roundType
              ? {backgroundColor: GREEN_STATS, borderWidth: 0}
              : {
                  backgroundColor: '#ffffff',
                  borderWidth: 1,
                  borderColor: '#8C8B8F',
                },
            {opacity: isDisabled ? 0.25 : 1},
          ]}
        >
          <View style={styles.viewDot} />
        </View>
      </TouchableOpacity>
    );
  };

  const canShowAddButtons = listPlayer?.length < 4 && !isTwoPlayersMatchPlay;

  return (
    <View style={[appStyles.flex]}>
      <View
        style={[
          appStyles.row,
          appStyles.spaceBetween,
          appStyles.hCenter,
          {marginHorizontal: 25, marginVertical: 15},
        ]}
      >
        <Text DINbold size={26} black>
          play.group_players
        </Text>
        <TouchableOpacity onPress={onShufflePlayer}>
          <IconSort />
        </TouchableOpacity>
      </View>
      <NestableDraggableFlatList
        data={listPlayer}
        onDragEnd={({data}) => {
          setListPlayer(data);
          setDragging(false);
        }}
        onDragBegin={() => {
          setDragging(true);
        }}
        keyExtractor={item => item.id?.toString()}
        renderItem={renderItemPlayer}
        contentContainerStyle={{marginTop: 9, paddingHorizontal: 10}}
      />
      {/* {renderItemPlayer()} */}
      {canShowAddButtons && (
        <View style={{paddingHorizontal: 10}}>
          {isAdding ? renderAddPlayer() : renderAddButtons()}
        </View>
      )}
      <View style={[appStyles.viewShadow, {paddingHorizontal: 10}]}>
        {renderType(
          GAME_TYPE_MULTIPLAYER.SKINS,
          GAME_TYPE_MULTIPLAYER_TITLE.SKINS,
          {
            borderTopStartRadius: 8,
            borderTopEndRadius: 8,
          },
          '2+',
          listPlayer?.length < 2,
        )}
        {renderType(
          GAME_TYPE_MULTIPLAYER.MATCH,
          GAME_TYPE_MULTIPLAYER_TITLE.MATCH,
          {
            borderBottomWidth: 1,
            borderTopWidth: 1,
            borderColor: '#C4C4C4',
          },
          '2, 4',
          listPlayer?.length !== 4 && listPlayer?.length !== 2,
        )}
        {renderType(
          GAME_TYPE_MULTIPLAYER.STROKE,
          GAME_TYPE_MULTIPLAYER_TITLE.STROKE,
          {
            borderBottomStartRadius: 8,
            borderBottomEndRadius: 8,
          },
          '2+',
          listPlayer?.length < 2,
        )}
      </View>
      <MultiplayerModeTooltipModal
        visible={isTooltipVisible}
        backdropPress={() => {
          setTooltipVisible(false);
        }}
      />
    </View>
  );
};
const styles = StyleSheet.create({
  viewDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#ffffff',
  },
  viewContainerDot: {
    width: 26,
    height: 26,
    borderRadius: 13,
    justifyContent: 'center',
    alignItems: 'center',
  },
  touchType: {
    paddingLeft: 15,
    paddingRight: 30,
    paddingVertical: 15,
    backgroundColor: '#ffffff',
    alignItems: 'center',
  },
  touchEdit: {
    borderWidth: 2,
    borderRadius: 25,
    paddingVertical: 10,
    paddingHorizontal: wp(6),
  },
  playerCardView: {
    backgroundColor: '#ffffff',
    padding: 15,
    paddingVertical: 20,
    borderRadius: 8,
    marginBottom: 10,
  },
  addButtonView: {
    backgroundColor: '#ffffff',
    padding: 10,
    borderRadius: 25,
  },
  addPlayerInputContainer: {
    borderWidth: 0,
    paddingLeft: 0,
    marginBottom: 0,
    marginTop: 0,
    paddingRight: 0,
    height: 'auto',
  },
  addPlayerInput: {
    fontSize: 24,
    color: 'black',
    fontWeight: 'bold',
  },
  addPlayerView: {
    backgroundColor: '#ffffff',
    paddingLeft: 15,
    paddingRight: 18,
    paddingTop: 17,
    paddingBottom: 25,
    borderRadius: 8,
    marginBottom: 10,
  },
  recentPlayersButtonView: {
    backgroundColor: '#ffffff',
    padding: 10,
    borderRadius: 25,
  },
  teamLineView: {
    flex: 0.0282,
    borderBottomRightRadius: 0,
    borderTopRightRadius: 0,
    height: '100%',
  },
  teamMemberSeparator: {
    height: 1,
    width: '95%',
    backgroundColor: 'rgba(17, 17, 17, 0.2)',
  },
  arrangeLineAbove: {
    width: 20,
    height: 1,
    backgroundColor: 'rgba(235, 235, 235, 1)',
  },
  arrangeLineBelow: {
    width: 20,
    height: 1,
    backgroundColor: 'rgba(189, 189, 189, 1)',
    marginBottom: 4,
  },
  clearIconView: {
    position: 'absolute',
    top: 5,
    right: 5,
  },
});
export default MultiplayerMode;
