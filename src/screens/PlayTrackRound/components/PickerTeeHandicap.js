import React, {useEffect, useState} from 'react';
import {View, Platform} from 'react-native';
import {Picker as PickerIOS} from '@react-native-picker/picker';
import {WheelPicker} from 'react-native-wheel-picker-android';

import appStyles from 'styles/global';
import {isSmallScreenIphone} from 'utils/constant';

const PickerTeeHandicap = ({
  options,
  setValue,
  value,
  handicapIndex,
  scoreCard,
}) => {
  const [currentValue, setCurrentValue] = useState(value);
  const LENGTH_TO_TRUNCATE = isSmallScreenIphone() ? 12 : 18;
  const LENGTH_TO_TRUNCATE_Android = 14;

  useEffect(() => {
    setValue(currentValue);
  }, [currentValue]);

  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  const parseDataForAndroid = () => {
    const tees = options.map(
      option =>
        `${
          (option.teeName + '')?.length >= LENGTH_TO_TRUNCATE_Android
            ? option.teeName.slice(0, LENGTH_TO_TRUNCATE_Android - 2) + '..'
            : option.teeName
        } - ${option.ydsTotal} yds_${getLabelNewLine(option)}`,
    );
    return tees;
  };

  const getIndexFromItem = () => {
    const index = options.findIndex(
      _item => _item?.teeName === currentValue?.teeName,
    );
    return index === -1 ? 0 : index;
  };

  const getItemFromIndex = index => {
    return options[index];
  };

  const onItemSelected = selectedItem => {
    setCurrentValue(getItemFromIndex(selectedItem));
  };

  const getLabelNewLine = option => {
    let courseGhinRating = option?.ratings?.find?.(
      item => item.ratingType?.toLowerCase() === 'total',
    );
    if (courseGhinRating) {
      return `${courseGhinRating.courseRating || 0} / ${
        courseGhinRating.slopeRating || 0
      } / ${courseGhinRating.courseHandicap || 0}`;
    }
    //Course Handicap = Handicap Index® x (Slope Rating™ / 113) + (Course Rating™ – par)
    let rating = option?.ratingMen;
    let slope = option?.slopeMen;
    if (option?.gender === 'wmn') {
      rating = option?.ratingWomen;
      slope = option?.slopeWomen;
    }
    //if user has no handicap index, course handicap should just be the 18 or 9 hole par
    const par = scoreCard?.parIn + scoreCard?.parOut;
    const courseHandicap = Math.round(
      (handicapIndex ? handicapIndex : 0) * (slope / 113) + (rating - par),
    );
    return `${rating || 0} / ${slope || 0} / ${
      (handicapIndex ? courseHandicap : par) || 0
    }`;
  };
  return (
    <View
      style={[
        Platform.OS === 'android'
          ? {flex: 1, justifyContent: 'center', marginLeft: 45, marginRight: 10}
          : {marginLeft: 45},
        appStyles.row,
      ]}
    >
      {Platform.OS === 'ios' ? (
        <PickerIOS
          style={appStyles.flex}
          lineColor={'#cccccc'}
          itemStyle={{color: '#000000', fontSize: 17}}
          selectedValue={options.indexOf(currentValue)}
          onValueChange={index => setCurrentValue(options[index])}
          numberOfLines={2}
          customSecondLine={true}
          fontSize2={13}
        >
          {options.map((option, i) => (
            <PickerIOS.Item
              key={option.teeName}
              label={`${
                (option.teeName + '')?.length >= LENGTH_TO_TRUNCATE
                  ? option.teeName.slice(0, LENGTH_TO_TRUNCATE - 2) + '..'
                  : option.teeName
              } - ${option.ydsTotal} yds${'\n'}${getLabelNewLine(option)}`}
              value={i}
            />
          ))}
        </PickerIOS>
      ) : (
        <View style={{height: 180}}>
          <WheelPicker
            selectedItem={getIndexFromItem()}
            data={parseDataForAndroid()}
            onItemSelected={onItemSelected}
            itemTextSize={17}
            selectedItemTextSize={17}
            itemTextFontFamily={'SF-Pro'}
            selectedItemTextFontFamily={'SF-Pro'}
          />
        </View>
      )}
    </View>
  );
};

export default PickerTeeHandicap;
