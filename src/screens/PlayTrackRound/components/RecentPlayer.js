import Button from 'components/Button';
import Text from 'components/Text';
import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Animated,
} from 'react-native';
import appStyles from 'styles/global';
import {deletePlayer, getAllPlayer} from 'utils/realmHelper';
import IcChecked from 'assets/imgs/ic_check_square.png';
import IcUnchecked from 'assets/imgs/ic_uncheck_square.png';
import {uniqBy} from 'lodash';
import {showToast} from 'utils/toast';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {FlatList, RectButton} from 'react-native-gesture-handler';
import Ionicons from 'react-native-vector-icons/Ionicons';

const RecentPlayer = ({navigation, route}) => {
  const [listSaved, setListSaved] = useState([]);
  const [listCurrentPlayer, setlistCurrentPlayer] = useState({});
  let swipedCardRef = null;
  let rowRefs = new Map();

  const refreshListRecent = () => {
    if (route.params?.listContact?.length > 0) {
      const list = route.params?.listContact.reduce((pre, current) => {
        return {...pre, [current.id]: current};
      }, []);
      setlistCurrentPlayer(list);
    }
    getData();
  };

  useEffect(() => {
    refreshListRecent();
  }, []);

  const getData = async () => {
    const data = await getAllPlayer();
    data.sort((a, b) => {
      let timeA = new Date(a.createAt);
      let timeB = new Date(b.createAt);
      return timeB - timeA;
    });
    setListSaved(data);
  };

  const onPressItem = (item, isChecking) => {
    const size = Object.keys(listCurrentPlayer);
    if (isChecking) {
      if (size?.length < 4) {
        setlistCurrentPlayer({...listCurrentPlayer, [item.id]: item});
      }
    } else {
      if (listCurrentPlayer[item.id]) {
        delete listCurrentPlayer[item.id];
        setlistCurrentPlayer({...listCurrentPlayer});
      }
    }
  };

  const onPressAddContact = () => {
    const values = Object.values(listCurrentPlayer);
    route.params.addContact(values);
    navigation.goBack();
  };

  const renderRightAction = (text, color, x, progress, itemSelected) => {
    const trans = progress.interpolate({
      inputRange: [0, 1],
      outputRange: [x - 20, -20],
    });
    const pressHandler = async () => {
      let deleteId = itemSelected.id;
      await deletePlayer(deleteId);
      let listCurrentSaved = [...listSaved];
      let deleteIndex = listCurrentSaved.findIndex(
        item => item.id === deleteId,
      );

      if (deleteIndex >= 0) {
        listCurrentSaved.splice(deleteIndex, 1);
        setListSaved([...listCurrentSaved]);
      }
    };

    return (
      <Animated.View
        style={{
          transform: [{translateX: trans}],
          ...styles.itemPlayerView,
        }}
      >
        <View style={{width: 10, backgroundColor: 'white'}} />
        <TouchableOpacity
          style={[
            styles.rightAction,
            {
              backgroundColor: color,
            },
          ]}
          onPress={pressHandler}
        >
          <Ionicons name={'trash-outline'} size={30} color={'white'} />
          <Text size={13} style={styles.actionText}>
            {text}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderRightActions = (progress, _dragAnimatedValue, itemSelected) => (
    <View style={[styles.rightActionContainer]}>
      {renderRightAction(
        'Delete',
        'rgba(rgba(255, 0, 0, 1)',
        80,
        progress,
        itemSelected,
      )}
    </View>
  );
  const onOpen = ref => {
    if (swipedCardRef) {
      swipedCardRef.close();
    }
  };
  const onClose = ref => {
    if (ref === swipedCardRef) {
      swipedCardRef = null;
    }
  };

  const renderItemPlayer = ({item, index}) => {
    const checkInPlayList = listCurrentPlayer[item.id];
    return (
      <Swipeable
        friction={2}
        leftThreshold={80}
        enableTrackpadTwoFingerGesture
        rightThreshold={10}
        renderRightActions={(progress, drag) =>
          renderRightActions(progress, drag, item)
        }
        onSwipeableOpen={onOpen}
        onSwipeableClose={onClose}
        onSwipeableWillOpen={() => {
          [...rowRefs.entries()].forEach(([key, ref]) => {
            if (key !== item.id && ref) {
              ref.close();
            }
          });
        }}
        ref={ref => {
          if (ref && !rowRefs.get(item.id)) {
            rowRefs.set(item.id, ref);
          }
        }}
        key={item.name + index}
        containerStyle={{paddingLeft: 10, paddingRight: 10, paddingTop: 2}}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          key={item.name + index}
          style={[appStyles.hCenter, styles.itemPlayerView]}
          onPress={() => onPressItem(item, !checkInPlayList)}
        >
          {checkInPlayList ? (
            <Image source={IcChecked} style={{width: 26, height: 26}} />
          ) : (
            <Image source={IcUnchecked} style={{width: 26, height: 26}} />
          )}
          <View style={[appStyles.viewShadow, styles.playerCardView]}>
            <Text size={24} black style={{fontWeight: 'bold'}}>
              {item.name}
            </Text>
            <Text size={13} style={styles.strokes}>
              {`Strokes ${item.strokes}`}
            </Text>
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };
//
  return (
    <View style={{flex: 1}}>
      <View style={styles.listPlayerContainer}>
        <FlatList
          data={listSaved}
          keyExtractor={item => item?.id}
          renderItem={renderItemPlayer}
        />
      </View>
      <Button
        text={'play.button.confirm_players'}
        style={styles.touchButton}
        textColor="#ffffff"
        onPress={onPressAddContact}
        DINbold
      />
    </View>
  );
};
const styles = StyleSheet.create({
  touchButton: {
    marginHorizontal: 15,
    marginBottom: 30,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
  },
  playerCardView: {
    backgroundColor: '#ffffff',
    padding: 15,
    paddingVertical: 20,
    borderRadius: 8,
    flex: 1,
    marginLeft: 19,
  },
  itemPlayerView: {
    marginBottom: 8,
    flex: 1,
    flexDirection: 'row',
  },
  strokes: {color: '#8C8B8F', paddingTop: 3},
  listPlayerContainer: {flex: 1, paddingVertical: 20},
  rightAction: {
    alignItems: 'center',
    width: 80,
    justifyContent: 'center',
    borderRadius: 8,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
  },
  actionText: {
    color: 'white',
    marginTop: 2,
  },
  rightActionContainer: {
    width: 80,
    flexDirection: 'row',
  },
});
export default RecentPlayer;
