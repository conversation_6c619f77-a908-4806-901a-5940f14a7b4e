import React, {useEffect, useState} from 'react';
import {View, Platform} from 'react-native';
import Picker from '@gregfrench/react-native-wheel-picker';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';

import appStyles from 'styles/global';

const PickerTee = ({options, setValue, value, pickerStyle}) => {
  const [currentValue, setCurrentValue] = useState(value);

  useEffect(() => {
    setValue(currentValue);
  }, [currentValue]);

  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  return (
    <View
      style={[
        Platform.OS === 'android'
          ? {flex: 1, justifyContent: 'center', marginLeft: 45, marginRight: 10}
          : {marginLeft: 45},
        appStyles.row,
        pickerStyle,
      ]}
    >
      <Picker
        style={appStyles.flex}
        lineColor={'#cccccc'}
        itemStyle={{color: '#000000', fontSize: 17}}
        selectedValue={options.indexOf(currentValue)}
        onValueChange={index => setCurrentValue(options[index])}
      >
        {options.map((option, i) => (
          <Picker.Item
            key={option.teeName}
            label={`${option?.teeName?.replace(`Ladies'`, 'Ladies')} - ${
              option.ydsTotal
            } yds`}
            value={i}
          />
        ))}
      </Picker>
    </View>
  );
};

export default PickerTee;
