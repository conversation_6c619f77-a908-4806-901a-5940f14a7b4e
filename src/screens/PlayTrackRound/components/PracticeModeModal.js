import React from 'react';
import {
  View,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ScrollView,
} from 'react-native';
import Modal from 'react-native-modal';
import {
  heightPercentageToDP,
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import Text from 'components/Text';
import {moderateScale} from 'react-native-size-matters';
import {t} from 'i18next';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Close_Icon from 'assets/imgs/ic_close.svg';
import {Trans} from 'react-i18next';

const PracticeModeModal = ({visible, backdropPress}) => {
  const windowHeight = Dimensions.get('window').height;
  const insets = useSafeAreaInsets();

  return (
    <Modal
      style={{...styles.modal, paddingVertical: hp(1.5) + insets.top}}
      isVisible={visible}
      onBackdropPress={backdropPress}
      animationIn="fadeIn"
      animationOut="fadeOut"
      deviceHeight={windowHeight}
      backdropOpacity={0.5}
      deviceWidth={wp('100%')}
      useNativeDriver
      hideModalContentWhileAnimating
    >
      <View style={styles.viewPopup}>
        <TouchableOpacity
          onPress={backdropPress}
          style={{position: 'absolute', right: 10, top: 15, zIndex: 100}}
        >
          <Close_Icon />
        </TouchableOpacity>
        <ScrollView showsVerticalScrollIndicator={false} style={{flexGrow: 0}}>
          <Text black style={styles.titleText}>
            play.track_round.tooltip.practice_mode
          </Text>
          <Trans
            defaults="play.track_round.tooltip.practice_mode_explanation"
            values={{
              value1: t(
                'play.track_round.tooltip.practice_mode_explanation_bold',
              ),
            }}
            parent={Text}
            style={styles.messageText}
            components={{
              bold: (
                <Text
                  style={{
                    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
                    fontSize: 13,
                  }}
                />
              ),
            }}
          />
        </ScrollView>
      </View>
    </Modal>
  );
};
const styles = StyleSheet.create({
  titleText: {
    fontWeight: '700',
    fontSize: 17,
    marginBottom: 20,
    textAlign: 'center',
    marginTop: 20,
  },
  messageText: {
    fontWeight: '400',
    fontSize: 13,
    color: '#8C8B8F',
    marginBottom: 20,
    lineHeight: 15.51,
    textAlign: 'center',
  },
  viewPopup: {
    backgroundColor: '#ffffff',
    borderRadius: moderateScale(8),
    paddingHorizontal: wp(9),
    paddingVertical: hp(9),
    overflow: 'hidden',
  },
  modal: {
    alignItems: 'center',
    paddingHorizontal: wp(5.2),
  },
});
export default PracticeModeModal;
