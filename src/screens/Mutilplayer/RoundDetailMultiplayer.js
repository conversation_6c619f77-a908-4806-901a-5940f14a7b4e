import Text from 'components/Text';
import React, {useEffect, useState} from 'react';
import {View, StyleSheet, TouchableOpacity, ScrollView} from 'react-native';
import {useSelector} from 'react-redux';
import Icon from 'react-native-vector-icons/FontAwesome5';
import * as Animatable from 'react-native-animatable';
const type = {
  individual: 'individual',
  team: 'Team',
};
const RoundDetailMultiplayer = ({navigation, route}) => {
  const [listContact, setListContact] = useState([]);
  const [typePlayer, setTypePlayer] = useState(type.individual);

  const user = useSelector(state => state?.user);

  useEffect(() => {
    const data = {
      name: user?.firstName,
      strokes: user?.golferProfile?.newHandicap?.userInputHandicap || 0,
      initials: user?.firstName.charAt(0),
      id: user?.id,
    };
    setListContact([data]);
  }, [user]);

  const onPressFillFromContact = () => {
    navigation.navigate('ContactFromDevice', {
      addContact: data => setListContact([...listContact, data]),
    });
  };

  const onPressRecentContact = () => {
    navigation.navigate('RecentPlayer', {
      listContact,
      addContact: data => setListContact(data),
      deleteContact: data => {
        const newList = listContact.filter(_item => _item.id !== data.id);
        setListContact(newList);
      },
      editContact: data => {
        const listEdit = listContact.map(_item => {
          if (data.id === _item.id) {
            return data;
          }
          return _item;
        });
        setListContact(listEdit);
      },
    });
  };

  const onPressAddPlayer = () => {
    navigation.navigate('AddPlayer', {
      addContact: data => setListContact([...listContact, data]),
    });
  };

  const onPressEditPlayer = selectContact => {
    navigation.navigate('AddPlayer', {
      selectContact,
      deleteContact: data => {
        const newList = listContact.filter(_item => _item.id !== data.id);
        setListContact(newList);
      },
      editContact: data => {
        const listEdit = listContact.map(_item => {
          if (data.id === _item.id) {
            return data;
          }
          return _item;
        });
        setListContact(listEdit);
      },
    });
  };

  const onPressRandom = () => {
    if (listContact?.length > 2) {
      shuffleArray(listContact);
    } else {
      listContact.reverse();
    }
    setListContact([...listContact]);
  };

  function shuffleArray(array) {
    for (let i = array?.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  }

  const renderButton = (title, onPress = () => {}) => {
    return (
      <TouchableOpacity style={styles.touchButton} onPress={onPress}>
        <Text>{title}</Text>
      </TouchableOpacity>
    );
  };

  const renderItem = (item, index) => {
    return (
      <Animatable.View
        style={[
          styles.viewItem,
          typePlayer === type.team
            ? index % 2 === 0
              ? styles.viewItemTeamEven
              : styles.viewItemTeamOdd
            : styles.viewItem,
        ]}
        animation="fadeIn"
        key={item?.id}
      >
        <View style={{flexDirection: 'row'}}>
          <View>
            <Text style={{fontWeight: 'bold', fontSize: 30}}>{item.name}</Text>
            <Text>Stroke {item.strokes}</Text>
          </View>
        </View>
        <TouchableOpacity
          style={{
            backgroundColor: '#000000',
            paddingHorizontal: 15,
            paddingVertical: 10,
            borderRadius: 10,
          }}
          onPress={() => onPressEditPlayer(item)}
        >
          <Text style={{color: '#ffffff'}}>Edit</Text>
        </TouchableOpacity>
        {typePlayer === type.team && (
          <View
            style={[
              {
                width: 10,
                backgroundColor: index < 2 ? 'red' : 'blue',
                height: 70,
                position: 'absolute',
                right: -1,
              },
              index % 2 === 0
                ? {borderTopRightRadius: 10}
                : {borderBottomRightRadius: 10},
            ]}
          />
        )}
      </Animatable.View>
    );
  };

  return (
    <View style={{flex: 1}}>
      <Text style={styles.textTitle}>Players</Text>
      {listContact?.length === 4 && (
        <View style={{paddingHorizontal: 20}}>
          <TouchableOpacity
            style={{paddingHorizontal: 10, paddingVertical: 5}}
            onPress={() => setTypePlayer(type.individual)}
          >
            <Text
              style={{
                color: typePlayer === type.individual ? 'green' : 'black',
              }}
            >
              Individual
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{paddingHorizontal: 10, paddingVertical: 5}}
            onPress={() => setTypePlayer(type.team)}
          >
            <Text style={{color: typePlayer === type.team ? 'green' : 'black'}}>
              Team
            </Text>
          </TouchableOpacity>
        </View>
      )}
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingRight: 20,
          marginVertical: 10,
        }}
      >
        <Text style={styles.textGroup}>Group Details</Text>
        <TouchableOpacity onPress={onPressRandom}>
          <Icon name="random" size={20} />
        </TouchableOpacity>
      </View>
      <ScrollView>
        {listContact.map(renderItem)}
        {listContact?.length < 4 && (
          <View style={{flex: 1}}>
            {renderButton('ADD PLAYER', onPressAddPlayer)}
            {renderButton('FILL FROM CONTACTS', onPressFillFromContact)}
            {renderButton('RECENT PLAYERS', onPressRecentContact)}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  textTitle: {
    fontSize: 20,
    paddingHorizontal: 20,
    fontWeight: 'bold',
  },
  textGroup: {
    fontSize: 16,
    paddingHorizontal: 20,
    fontWeight: 'bold',
  },
  touchButton: {
    paddingVertical: 20,
    marginHorizontal: 10,
    marginVertical: 5,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#000000',
  },
  viewItem: {
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 15,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 10,
    marginTop: 10,
    backgroundColor: '#ffffff',
    justifyContent: 'space-between',
    height: 70,
  },
  viewItemTeamEven: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginTop: 10,
    borderBottomWidth: 0,
  },
  viewItemTeamOdd: {
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    marginVertical: 0,
    borderTopWidth: 0,
    marginTop: 0,
  },
});
export default RoundDetailMultiplayer;
