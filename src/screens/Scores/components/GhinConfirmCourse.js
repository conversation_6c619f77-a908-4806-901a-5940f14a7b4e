import React, {useState, useCallback, useEffect, useMemo} from 'react';
import TextInput from 'components/TextInput';
import appStyles from 'styles/global';
import {t} from 'i18next';
import {
  ActivityIndicator,
  FlatList,
  TouchableOpacity,
  View,
  Keyboard,
  SectionList,
  StyleSheet,
} from 'react-native';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import {useDispatch} from 'react-redux';
import debounce from 'lodash.debounce';
import {
  getCourseDetails,
  getGhinCourseDetail,
  searchGhinCourseByLocation,
  searchGhinCourseByName,
} from 'requests/play-stats';
import {showToast} from 'utils/toast';
import {getDistance} from 'geolib';
import {orderBy} from 'lodash';
import LoadingOverlay from 'components/LoadingOverlay';
import {MODE_ROUND} from 'screens/PlayCourseMap/DataSubmitDefault';
import {nameOfMeasureMiWithKM, valueOfMeasureMiWithKM} from 'utils/countries';
import {getSyncRound3rdParty} from 'utils/commonVariable';
import {SYNC_ROUND_TYPE} from 'utils/constant';

const METER_TO_MILES = 0.000621371;

const GhinConfirmCourse = ({navigation, route}) => {
  const {currentRoundInfo, ...otherPropsFromCourse} = route?.params || {};
  const [searchText, setSearchText] = useState('');
  const [searchCourseText, setSearchCourseText] = useState(null);
  const [loading, setLoading] = useState(false);
  const [loadingOverlay, setLoadingOverlay] = useState(false);
  const [golfCourses, setGolfCourses] = useState([]);
  const [currentCourseDetail, setCurrentCourseDetail] = useState(null);
  const [suggestedData, setSuggestedData] = useState(null);
  let syncType = getSyncRound3rdParty();
  const getSuggestedCourses = async () => {
    const courseDetail = await getCourseDetails(
      currentRoundInfo?.igolfCourseId,
    );
    setCurrentCourseDetail(courseDetail);
    setLoading(true);
    try {
      const suggestedCoursesByLocation = await searchGhinCourseByLocation({
        latitude: courseDetail?.latitude,
        longitude: courseDetail?.longitude,
      });
      let coursesByLocation = [];
      if (syncType === SYNC_ROUND_TYPE.WHS) {
        coursesByLocation = suggestedCoursesByLocation?.data || [];
        coursesByLocation.forEach(item => {
          item.distance = item.distanceMiles;
        });
      } else {
        coursesByLocation = suggestedCoursesByLocation || [];
        coursesByLocation.forEach(item => {
          item.address1 = item.geoLocationFormattedAddress;
          item.distance = item.distanceMiles;
        });
      }

      setGolfCourses([...coursesByLocation]);
      setSuggestedData([...coursesByLocation]);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getCourseDetailFromApi = async () => {};

  useEffect(() => {
    getCourseDetailFromApi();
    getSuggestedCourses();
  }, []);

  const debounceValue = useCallback(
    debounce(value => setSearchCourseText(value), 1000),
    [],
  );

  const search = value => {
    setSearchText(value);
    debounceValue(value);
  };

  const clearSearch = () => {
    setSearchText('');
    setSearchCourseText(null);
    Keyboard.dismiss();
  };

  useEffect(() => {
    (async () => {
      if (searchCourseText?.length === 2 || searchCourseText?.length === 1) {
        return;
      }
      if (!searchCourseText) {
        setGolfCourses([...suggestedData]);
        return;
      }
      setLoading(true);
      try {
        // Make request to get courses by name
        const courses = await searchGhinCourseByName(searchCourseText);

        // Stop loading state and set golf courses for our flatlist
        setLoading(false);
        let suggestedCourses = [...courses.courses];
        suggestedCourses.forEach(item => {
          let distanceFromCurrentCourse = null;
          let courseLatitude = item?.geoLocationLatitude;
          let courseLongitude = item?.geoLocationLongitude;
          if (syncType === SYNC_ROUND_TYPE.WHS) {
            courseLatitude = item?.latitude;
            courseLongitude = item?.longitude;
          }
          try {
            distanceFromCurrentCourse = getDistance(
              {
                latitude: currentCourseDetail?.latitude,
                longitude: currentCourseDetail?.longitude,
              },
              {
                latitude: courseLatitude,
                longitude: courseLongitude,
              },
            );
          } catch (error) {
          }
          item.distance = distanceFromCurrentCourse
            ? distanceFromCurrentCourse * METER_TO_MILES
            : null;
        });
        const result = orderBy(suggestedCourses, 'distance', 'asc');
        setGolfCourses(result);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('An_error_occurred_updating_your_home_course'),
        });
      } finally {
        setLoading(false);
      }
    })();
  }, [searchCourseText]);
  const navigateToConfirmTee = async item => {
    try {
      setLoadingOverlay(true);
      let courseId = item.courseId;
      if (syncType === SYNC_ROUND_TYPE.WHS) {
        courseId = item.id;
      }
      //Validate when choose Course
      const ghinCourseDetail = await getGhinCourseDetail(courseId);
      let holesPlayed = currentRoundInfo?.numberOfHolesPlayed || 0;
      if (currentRoundInfo?.roundMode !== MODE_ROUND.SIMPLE) {
        holesPlayed = currentRoundInfo?.holes?.length;
      }
      let courseTees = ghinCourseDetail?.teeSets?.[0];
      let teeHolesNumber = courseTees?.holesNumber;
      if (syncType === SYNC_ROUND_TYPE.WHS) {
        courseTees = ghinCourseDetail?.tees?.[0];
        teeHolesNumber = courseTees?.holes?.length;
      }
      if (teeHolesNumber !== holesPlayed) {
        let msg = t('ghin.score.error.select_9_hole_course');
        if (holesPlayed === 18) {
          msg = t('ghin.score.error.select_18_hole_course');
        }
        showToast({
          type: 'error',
          message: msg,
        });
        return;
      }

      navigation.navigate('GhinConfirmTee', {
        ghinCourseDetail: item,
        currentRoundInfo: currentRoundInfo,
        origin: 'GhinConfirmCourse',
        ...otherPropsFromCourse,
      });
    } catch (error) {
    } finally {
      setLoadingOverlay(false);
    }
  };

  const recentCourseView = ({item, isSelected}) => {
    let textColorProps = {black: true};
    if (isSelected) {
      textColorProps = {gray: true};
    }
    if (!item) return null;
    let courseName = item.courseName;
    if (syncType === SYNC_ROUND_TYPE.WHS) {
      courseName = item.name || item.facilityName || item.courseName;
    }
    return (
      <View
        style={[
          {paddingHorizontal: 15, paddingVertical: '5%'},
          isSelected ? {} : styles.viewHasLineSeparator,
        ]}
      >
        <TouchableOpacity
          onPress={() => navigateToConfirmTee(item)}
          disabled={isSelected}
        >
          {isSelected && (
            <Text DINbold gray style={{fontWeight: '500', marginBottom: 8}}>
              ghin.score.confirm.you_selected
            </Text>
          )}
          <Text {...textColorProps} DINbold style={styles.courseName}>
            {courseName?.toUpperCase?.()}
          </Text>
          {item.address1 && (
            <Text style={[appStyles.grey, styles.courseDetailText]}>
              {item.address1}
            </Text>
          )}
          <View style={[appStyles.row]}>
            {item.city ? (
              <Text style={[appStyles.grey, styles.courseDetailText]}>
                {item.city.trim()}
                {item.stateShort?.length ||
                item.otherState?.length ||
                item.state?.length
                  ? ', '
                  : ''}
              </Text>
            ) : null}
            {item.stateShort?.length ||
            item.otherState?.length ||
            item.state?.length ? (
              <Text style={[appStyles.grey, styles.courseDetailText]}>
                {item.stateShort || item.otherState || item.state}
              </Text>
            ) : null}
          </View>
          {item.distance && (
            <Text style={[appStyles.grey, styles.courseDetailText]}>
              {`${valueOfMeasureMiWithKM(item.distance)?.toFixed?.(
                2,
              )} ${nameOfMeasureMiWithKM()}`}
            </Text>
          )}
        </TouchableOpacity>
      </View>
    );
  };
  const renderHeader = useMemo(() => {
    return (
      <View style={[{paddingHorizontal: 25}]}>
        <TextInput
          style={[
            appStyles.mBSm,
            {backgroundColor: 'rgba(0,0,0, 0.05)', borderWidth: 0},
          ]}
          placeholder={t('home.post_score.enter_course_name')}
          onChangeText={search}
          defaultValue={searchText}
          leftIcon="search"
          rightIcon="times-circle"
          clearInput={clearSearch}
          mode={'light'}
        />
      </View>
    );
  }, [searchText]);
  return (
    <View style={{flex: 1}}>
      <View style={[appStyles.flex, appStyles.whiteBg]}>
        <FocusAwareStatusBar barStyle={'dark-content'} />
        {loadingOverlay ? (
          <LoadingOverlay transparent={loadingOverlay} />
        ) : null}
        <View
          style={{backgroundColor: '#DCDCDE', height: 1, marginHorizontal: 10}}
        />
        <View style={[{paddingHorizontal: 10}]}>
          {recentCourseView({item: currentCourseDetail, isSelected: true})}
        </View>
        {renderHeader}
        {loading ? (
          <ActivityIndicator style={appStyles.pTLg} color={'black'} />
        ) : (
          <View style={[appStyles.flex, appStyles.pBMd]}>
            <SectionList
              style={[{paddingHorizontal: 10, paddingTop: 28}]}
              sections={
                golfCourses.length
                  ? [
                      {
                        title: t('ghin.score.confirm.title.suggested_courses'),
                        data: golfCourses,
                      },
                    ]
                  : []
              }
              renderItem={recentCourseView}
              keyExtractor={item => item.courseId || item.id}
              renderSectionHeader={({section: {title}}) =>
                title ? (
                  <View style={[styles.viewHasLineSeparator]}>
                    <Text
                      style={{
                        alignSelf: 'center',
                        fontSize: 16,
                        paddingBottom: 15,
                      }}
                      DINbold
                      gray
                    >
                      {title}
                    </Text>
                  </View>
                ) : null
              }
              stickySectionHeadersEnabled={false}
              ListEmptyComponent={() => {
                return (
                  <View
                    style={[appStyles.flex, appStyles.hCenter, appStyles.mTLg]}
                  >
                    <Text
                      style={[
                        appStyles.md,
                        appStyles.pHSm,
                        appStyles.textCenter,
                        appStyles.grey,
                      ]}
                    >
                      scoreadd.course_search.no_courses_found
                    </Text>
                  </View>
                );
              }}
            />
          </View>
        )}
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  viewHasLineSeparator: {
    borderBottomColor: '#DCDCDE',
    borderBottomWidth: 1,
  },
  courseName: {
    fontSize: 26,
    fontWeight: '500',
    paddingBottom: 5,
  },
  courseDetailText: {
    fontSize: 13,
    fontWeight: '400',
    paddingBottom: 5,
  },
});
export default GhinConfirmCourse;
