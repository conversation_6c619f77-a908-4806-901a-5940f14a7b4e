import React, {useMemo} from 'react';
import {
  View,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
} from 'react-native';

import Text from 'components/Text';

import appStyles from 'styles/global';
import {
  heightPercentageToDP,
  widthPercentageToDP,
} from 'react-native-responsive-screen';
import {SYNC_ROUND_TYPE} from 'utils/constant';
import {t} from 'i18next';
import {getSyncRound3rdParty} from 'utils/commonVariable';
import {GHIN_MAIN_COLOR, GOLF_CANADA_COLOR} from 'config';

const ButtonUSGA = ({
  style,
  text,
  textColor,
  textSize,
  backgroundColor,
  borderColor,
  onPress,
  disabled,
  loading,
  loadingMode,
  DINbold,
  Din79Font,
  textStyle,
  hasLogo,
  numberOfLines,
  // syncType = SYNC_ROUND_TYPE.USGA,
}) => {
  const syncType = getSyncRound3rdParty();
  const buttonPostText = useMemo(() => {
    switch (syncType) {
      case SYNC_ROUND_TYPE.USGA:
        return t('play.btn.post_to_usga');
      case SYNC_ROUND_TYPE.WHS:
        return t('ghin.score.cta.post_score_to_whs');
      default:
        break;
    }
  }, [syncType]);
  return (
    <TouchableOpacity
      style={[
        hasLogo ? appStyles.pHSm : appStyles.pHMd,
        appStyles.vCenter,
        appStyles.hCenter,
        {
          backgroundColor: backgroundColor
            ? backgroundColor
            : syncType === SYNC_ROUND_TYPE.WHS
            ? GOLF_CANADA_COLOR
            : GHIN_MAIN_COLOR,
          borderColor: borderColor
            ? borderColor
            : syncType === SYNC_ROUND_TYPE.WHS
            ? GOLF_CANADA_COLOR
            : GHIN_MAIN_COLOR,
          borderRadius: widthPercentageToDP('20%'),
          height: heightPercentageToDP('5.7%'),
          opacity: disabled ? 0.5 : 1,
        },
      ].concat(style)}
      onPress={onPress}
      disabled={disabled}
    >
      {loading ? (
        <ActivityIndicator color={loadingMode === 'dark' ? 'white' : 'black'} />
      ) : (
        <View style={[appStyles.hCenter, appStyles.row]}>
          <Text
            style={[
              appStyles.textCenter,
              {color: textColor, letterSpacing: 1.62},
              textSize && appStyles[textSize],
              Platform.OS === 'ios' && DINbold && {marginBottom: -5},
            ].concat(textStyle)}
            DINbold={DINbold}
            Din79Font={Din79Font}
            numberOfLines={numberOfLines || 0}
          >
            {text || buttonPostText}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

export default ButtonUSGA;
