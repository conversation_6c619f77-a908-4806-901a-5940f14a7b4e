import React, {useState, useEffect, useRef, useMemo, useCallback} from 'react';
import {
  Platform,
  TouchableOpacity,
  View,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import {connect, useDispatch, useSelector} from 'react-redux';
import moment from 'moment';
import analytics from '@react-native-firebase/analytics';
import {get} from 'lodash';

import {
  getCourseDetails,
  getCourseTeeDetails,
  getCourseScoreCard,
  addAScore,
  editAScore,
  deleteAScore,
  submitRoundToGhin,
  addRoundUSGA,
} from 'requests/play-stats';
import {getFiveRecentRounds} from 'requests/play-stats';
import {updateScores, shouldReloadData} from 'reducers/play';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Button from 'components/Button';
import Text from 'components/Text';

import appStyles from 'styles/global';
import {showToast, showErrorToast} from 'utils/toast';
import {t} from 'i18next';
import EventEmitter from 'utils/EventEmitter';
import LogoTMPostScore from 'assets/imgs/logo-tm-button-post-score.svg';
import BottomSheetSubmitGhinConfirm from 'components/BottomSheetSubmitGhinConfirm';
import {MODE_ROUND} from 'screens/PlayCourseMap/DataSubmitDefault';
import {validateGhinRoundScore, validateRoundScore} from 'utils/validate';
import ButtonUSGA from './components/ButtonUSGA';
import {
  GA_logCtaEvent,
  GA_logEvent,
  GA_logScreenViewV2,
} from 'utils/googleAnalytics';
import {
  SCREEN_CLASS,
  SCREEN_TYPES,
  GA_EVENT_NAME,
  PAGE_CATEGORY,
  PAGE_NAME,
  SYNC_ROUND_TYPE,
  NOT_APPLICABLE,
} from 'utils/constant';
import {getSyncRound3rdParty} from 'utils/commonVariable';
import {getGhinRoundData, getGolfnetCourseData} from 'utils/play';
import {useIsFocused} from '@react-navigation/native';
import {checkCanPostRoundToUSGA, checkConnectedWHS} from 'utils/user';
import AdjustGrossScoreModal from './components/AdjustGrossScoreModal';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import WheelPickerBottomSheet from 'components/WheelPickerBottomSheet';
import HoleByHoleScoreInput from 'screens/PlayCourseMap/components/HoleByHoleScoreInput';
import PostScoreDetailForm from './components/PostScoreDetailForm';
import {
  isConnectedNetwork,
  showToastErrorInternet,
} from 'utils/queueAndNetwork';
import {convertDistanceFromYards} from 'utils/convert';

function convertUTCToTimezone(utcDate, offsetHours) {
  const dateFormat = new Date(utcDate);
  const timezoneDate = new Date(
    dateFormat.getTime() + offsetHours * 60 * 60 * 1000,
  );
  return timezoneDate.toISOString();
}

const GHIN_POST_SCORE_MODE = {
  HBH: 'Hole by Hole',
  GROSS: 'Gross',
};

const ScoreAddEditDetails = ({
  navigation,
  route: {params},
  updateScores,
  shouldReloadData,
}) => {
  const {
    courseId,
    roundInfo,
    ghinCourseId,
    ghinCourseName,
    igolfRoundId,
    igolfRoundInfo,
    onReloadListUSGA,
    ghinId,
  } = params;
  const user = useSelector(state => state.user);
  const scores = useSelector(state => state.play?.scores);
  const ghin = useSelector(state => state.ghin);

  const isFocused = useIsFocused();
  // States
  const [loading, setLoading] = useState(false);
  const [courseDetails, setCourseDetails] = useState(null);
  const [courseTeeNames, setCourseTeeNames] = useState([]);
  const [modifiedCourseTeeNames, setModifiedCourseTeeNames] = useState([]);
  const [modifiedGhinTeeList, setModifiedGhinTeeList] = useState([]);
  const [teePlayed, setTeePlayed] = useState(null);
  const [frontBackPlayed, setFrontBackPlayed] = useState(null);
  const [holesPlayed, setHolesPlayed] = useState(18);
  const [courseScore, setCourseScore] = useState(null);
  const [scoreType, setScoreType] = useState('Away');
  const [teeList, setTeeList] = useState([]);
  const [ghinTeeList, setGhinTeeList] = useState([]);
  const [ghinTeeSelected, setGhinTeeSelected] = useState();
  const [courseHoleNumber, setCourseHoleNumber] = useState();
  const [coursePar, setCoursePar] = useState();
  const sheetRef = useRef(null);
  const [isTooltipVisible, setTooltipVisible] = useState(false);
  const [courseScoreCard, setCourseScoreCard] = useState({});
  const [isModalShowSubmit, setModalShowSubmit] = useState(false);
  const [postButtonEnabled, setPostButtonEnabled] = useState(true);

  // Refs
  const teesRef = useRef(null);
  const ghinTeesRef = useRef(null);
  const scoreTypeRef = useRef(null);
  const datePlayedRef = useRef(null);
  const formDetailRef = useRef(null);
  const ghinHoleInputRef = useRef(null);

  const insets = useSafeAreaInsets();

  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);

  const canPostToUSGA = checkCanPostRoundToUSGA(showHideFeatures, user);
  const whs = useSelector(state => state.whs);
  const isGolfCanada = checkConnectedWHS(whs);
  const [datePlayed, setDatePlayed] = useState(
    igolfRoundInfo?.playedOn ||
      (isGolfCanada ? moment() : moment().toISOString()),
  );
  let syncType = getSyncRound3rdParty();
  const canShowButtonSaveScore =
    (!isGolfCanada && !canPostToUSGA && params.scoreType === 'add') ||
    params.scoreType !== 'add';
  const canPost3rdParty = isGolfCanada || canPostToUSGA;
  const [isHoleByHoleInputVisible, setHoleByHoleInputVisible] = useState(false);
  const [roundDataSubmit, setRoundDataSubmit] = useState(null);
  const [ghinPostScoreMode, setGhinPostScoreMode] = useState(
    GHIN_POST_SCORE_MODE.GROSS,
  );
  const [isEditFromScoreCard, setEditFromScoreCard] = useState(false);
  const [holeSelected, setHoleSelected] = useState(null);
  const userDistanceUnit =
    user?.measurementUnits?.toLowerCase?.() === 'meters' ? 'meters' : 'yards';

  useEffect(() => {
    if (!isHoleByHoleInputVisible) {
      setEditFromScoreCard(false);
    }
  }, [isHoleByHoleInputVisible]);

  //LOAD TM COURSE DATA
  useEffect(() => {
    (async () => {
      setLoading(true);

      if (roundInfo) {
        setCourseScore(roundInfo?.totalScore);
        setDatePlayed(roundInfo.playedOn);
        setTeePlayed(roundInfo?.teeName);
        setHolesPlayed(roundInfo.numberOfHolesPlayed);
      }

      try {
        if (!courseId && !roundInfo?.igolfCourseId) {
          setLoading(false);
          return;
        }
        Promise.all([
          getCourseDetails(courseId || roundInfo?.igolfCourseId),
          getCourseTeeDetails(courseId || roundInfo?.igolfCourseId),
          getCourseScoreCard(courseId || roundInfo?.igolfCourseId),
        ]).then(values => {
          values.map((value, index) => {
            //if is posting to USGA, ignore the tee error details from TM
            if (
              value.errorDetails &&
              ((index !== 1 && canPostToUSGA) || !canPostToUSGA)
            ) {
              setLoading(false);
              showErrorToast({
                error: '',
                title: t('play.error.missing_tee_or_course_data'),
                description: t('play.error.missing_tee_or_course_data.detail'),
              });
            } else {
              // Set Course Details
              setCourseDetails(values[0]);
              setCourseHoleNumber(values?.[0]?.layoutHoles);
              const scoreCard =
                values[2]?.[
                  user.gender === 'female'
                    ? 'wmnScorecardList'
                    : 'menScorecardList'
                ][0];
              setCourseScoreCard(scoreCard);
              if (holesPlayed === 9 && teePlayed?.includes('Back 9')) {
                return scoreCard?.parIn ? get(scoreCard, 'parIn', 0) : '00';
              } else if (holesPlayed === 9 && teePlayed?.includes('Front 9')) {
                return scoreCard?.parOut ? get(scoreCard, 'parOut', 0) : '00';
              }
              setCoursePar(
                scoreCard?.parTotal?.toString() ||
                  (scoreCard?.parIn || scoreCard?.parOut
                    ? `${
                        get(scoreCard, 'parIn', 0) + get(scoreCard, 'parOut', 0)
                      }`
                    : '00'),
              );
              // Loop through tees to get names and add them to array
              const teeNames = [];
              // const genderUser = user.gender === 'female' ? 'wmn' : 'men';
              const teeFilterGender = values[1]?.teesList;
              teeFilterGender?.map(tee => {
                teeNames.push(tee?.teeName);
              });
              setTeeList(teeFilterGender);
              setCourseTeeNames(teeNames);
              setModifiedCourseTeeNames(teeNames);
              if (!roundInfo?.teeName) {
                setTeePlayed(teeNames[0]);
              }
              setLoading(false);
            }
          });
        });
      } catch (error) {
        console.log('error', error);
        setLoading(false);
        showToast({
          type: 'error',
          message: t('score.add_course_nearby.i_hit_my_drive'),
          subText: t(
            'score.add_edit_details.locate_your_score_details_in_a_moment',
          ),
        });
      } finally {
        setLoading(false);
      }
    })();
  }, [courseId, roundInfo]);

  const createHolesData = (
    scoreCardData,
    holesTotal,
    frontBackPlayedType,
    teeSelected,
  ) => {
    let holes = [];
    if (scoreCardData) {
      scoreCardData?.parHole?.map((item, index) => {
        holes.push({
          holeNumber: index + 1,
          number: index + 1,
          par: item,
          stroke_index: scoreCardData?.hcpHole?.[index],
          // distance: teeSelected?.ydsHole?.[index],
          avg_heartrate: 0,
          calories_burned: 0,
          image_url: null,
          lowest_heartrate: 0,
          peak_heartrate: 0,
          player_score: null,
          steps_count: 0,
          strokes: [],
          strokes_removed: 0,
          completeHole: false,
        });
      });
      if (holesTotal === 9) {
        if (frontBackPlayedType === 'front') {
          holes = holes.slice(0, 9);
        }
        if (frontBackPlayedType === 'back') {
          holes = holes.slice(9, 18);
        }
      }
    }
    return holes;
  };
  const resetRoundData = useCallback(() => {
    if (courseScoreCard) {
      let teeSelected = teeList?.find(
        item => item.teeName === splitTeeNameFromTeePlayed,
      );
      const holes = createHolesData(
        courseScoreCard,
        holesPlayed,
        frontBackPlayed,
        teeSelected,
      );
      const dataSubmit = {
        numberOfHolesPlayed: holesPlayed,
        courseID: courseDetails?.idCourse,
        courseName:
          params?.title || ghinCourseName || courseDetails?.courseName,
        round_mode: MODE_ROUND.BASIC,
        holes,
      };
      formDetailRef?.current?.resetAnimation?.();
      setRoundDataSubmit(dataSubmit);
      setGhinPostScoreMode(GHIN_POST_SCORE_MODE.GROSS);
      setHoleSelected(null);
      setTimeout(() => {
        ghinHoleInputRef?.current?.resetGhinMode?.();
      }, 100);
    }
  }, [
    frontBackPlayed,
    holesPlayed,
    courseScoreCard,
    courseDetails,
    teeList,
    splitTeeNameFromTeePlayed,
  ]);

  useEffect(() => {
    if (courseScoreCard) {
      let teeSelected = teeList?.find(
        item => item.teeName === splitTeeNameFromTeePlayed,
      );
      const holes = createHolesData(
        courseScoreCard,
        holesPlayed,
        frontBackPlayed,
        teeSelected,
      );
      const dataSubmit = {
        ...roundDataSubmit,
        numberOfHolesPlayed: holesPlayed,
        courseID: courseDetails?.idCourse,
        courseName:
          params?.title || ghinCourseName || courseDetails?.courseName,
        holes,
      };
      setRoundDataSubmit(dataSubmit);
    }
  }, [
    frontBackPlayed,
    holesPlayed,
    courseScoreCard,
    courseDetails,
    teeList,
    splitTeeNameFromTeePlayed,
  ]);

  const getDataGhinCourse = async () => {
    setLoading(true);
    try {
      let {ghinCourseDetail, ghinTeeList, ghinTee} = await getGhinRoundData(
        ghinCourseId,
        user,
      );
      setGhinTeeSelected(ghinTee);
      setGhinTeeList(ghinTeeList);
      setModifiedGhinTeeList(ghinTeeList);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getDataGolfNetCourse = async () => {
    setLoading(true);
    try {
      let {ghinCourseDetail, ghinTeeList, ghinTee} = await getGolfnetCourseData(
        ghinCourseId,
        user,
        whs?.hiDetail?.value,
      );
      setCourseDetails(ghinCourseDetail);
      if (ghinTeeList?.length > 0) {
        setGhinTeeSelected(ghinTee);
        setTeeList(ghinTeeList);
        setCourseTeeNames(ghinTeeList.map(item => item.teeName));
        if (!roundInfo?.teeName) {
          setTeePlayed(ghinTeeList?.[0]?.teeName);
        }
      } else {
        setPostButtonEnabled(false);
        showToast({
          type: 'error',
          message: t('score.add_edit_details.title.tees_error'),
          subText: t('score.add_edit_details.can_not_get_the_tee_list'),
        });
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (ghinCourseId && ghinCourseId !== '0') {
      if (isGolfCanada) {
        getDataGolfNetCourse();
      } else {
        getDataGhinCourse();
      }
    }
  }, [ghinCourseId]);

  useEffect(() => {
    if (holesPlayed === 9) {
      if (teePlayed?.toLowerCase().includes('back')) {
        setFrontBackPlayed('back');
        if (!canPost3rdParty && courseScoreCard) {
          setCoursePar(
            courseScoreCard?.parIn ? get(courseScoreCard, 'parIn', 0) : '00',
          );
        }
      } else {
        setFrontBackPlayed('front');
        if (!canPost3rdParty && courseScoreCard) {
          setCoursePar(
            courseScoreCard?.parOut ? get(courseScoreCard, 'parOut', 0) : '00',
          );
        }
      }
    } else {
      setFrontBackPlayed(null);
      if (!canPost3rdParty && courseScoreCard) {
        setCoursePar(courseScoreCard?.parTotal?.toString() || '00');
      }
    }
  }, [teePlayed, courseScoreCard, holesPlayed]);

  useEffect(() => {
    if (ghinTeeSelected) {
      if (holesPlayed === 9) {
        if (ghinTeeSelected?.teeSetRatingName?.toLowerCase().includes('back')) {
          setFrontBackPlayed('back');
        } else {
          setFrontBackPlayed('front');
        }
      } else {
        setFrontBackPlayed(null);
      }
    }
  }, [ghinTeeSelected, holesPlayed]);

  useEffect(() => {
    if (ghinTeeSelected && canPost3rdParty && isFocused) {
      //get number of Holes that can post to USGA/WHS from tee selected
      setCourseHoleNumber(ghinTeeSelected.holesNumber || 18);
      if (isGolfCanada) {
        if (
          (frontBackPlayed && holesPlayed === 9) ||
          (frontBackPlayed == null &&
            holesPlayed === 18 &&
            ghinTeeSelected.holesNumber === 18)
        ) {
          // checkMapIgolfCourse(ghinTeeSelected);
          let currentRatingType = frontBackPlayed?.toLowerCase?.() || 'total';
          let courseGhinRating = ghinTeeSelected?.ratings?.find?.(
            item => item.ratingType?.toLowerCase() === currentRatingType,
          );
          if (courseGhinRating) {
            setCoursePar(
              courseGhinRating?.par || ghinTeeSelected.totalPar || 0,
            );
          } else {
            setCoursePar(ghinTeeSelected.totalPar);
          }
        } else {
          setCoursePar(ghinTeeSelected.totalPar);
        }
      } else {
        if (holesPlayed === 9) {
          if (
            ghinTeeSelected?.teeSetRatingName?.toLowerCase()?.includes?.('back')
          ) {
            setCoursePar(
              courseScoreCard?.parIn ? get(courseScoreCard, 'parIn', 0) : '00',
            );
          } else {
            setCoursePar(
              courseScoreCard?.parOut
                ? get(courseScoreCard, 'parOut', 0)
                : '00',
            );
          }
        } else {
          setCoursePar(courseScoreCard?.parTotal?.toString() || '00');
        }
      }
    }
  }, [
    ghinTeeSelected,
    frontBackPlayed,
    holesPlayed,
    isGolfCanada,
    isFocused,
    courseScoreCard,
  ]);

  useEffect(() => {
    const nineHoleTeeNames = [];
    if (holesPlayed === 9) {
      courseTeeNames.map(tee => {
        if (courseHoleNumber === 18) {
          nineHoleTeeNames.push(`${tee} - Front 9`);
          nineHoleTeeNames.push(`${tee} - Back 9`);
        } else {
          nineHoleTeeNames.push(`${tee}`);
        }
      });

      setModifiedCourseTeeNames(nineHoleTeeNames);
      setTeePlayed(
        roundInfo?.numberOfHolesPlayed === 9
          ? roundInfo?.back9Score
            ? `${roundInfo?.teeName} - Back 9`
            : `${roundInfo?.teeName} - Front 9`
          : nineHoleTeeNames[0],
      );
    } else {
      setModifiedCourseTeeNames(courseTeeNames);
      setTeePlayed(roundInfo?.teeName || courseTeeNames[0]);
    }
  }, [holesPlayed, roundInfo, courseTeeNames]);

  useEffect(() => {
    const nineHoleTeeList = [];
    if (ghinTeeList?.length > 0) {
      if (holesPlayed === 9) {
        ghinTeeList.map(tee => {
          if (courseHoleNumber === 18) {
            nineHoleTeeList.push({
              ...tee,
              teeSetRatingName: `${tee.teeSetRatingName} - Front 9`,
            });
            nineHoleTeeList.push({
              ...tee,
              teeSetRatingName: `${tee.teeSetRatingName} - Back 9`,
            });
          } else {
            nineHoleTeeList.push(tee);
          }
        });

        setModifiedGhinTeeList(nineHoleTeeList);
        setGhinTeeSelected(nineHoleTeeList[0]);
      } else {
        setModifiedGhinTeeList(ghinTeeList);
        setGhinTeeSelected(ghinTeeList[0]);
      }
    }
  }, [holesPlayed, ghinTeeList, courseHoleNumber]);

  const getScoreType = () => {
    try {
      let srcType = '';
      switch (scoreType) {
        case 'Away':
          srcType = 'A';
          break;
        case 'Home':
          srcType = 'H';
          break;
        case 'Comp':
          srcType = 'T';
          break;
        default:
          break;
      }
      return srcType;
    } catch (error) {
      console.log('error', error);
      return '';
    }
  };

  const teeDistanceObj = convertDistanceFromYards({
    distanceInYards: teeTotalYds,
    userUnit: userDistanceUnit,
  });

  const splitTeeNameFromTeePlayed = useMemo(() => {
    try {
      if (courseHoleNumber === 18 && holesPlayed === 9) {
        let teePlayedSplit = teePlayed?.split?.(' - ');
        if (teePlayedSplit?.length > 1) {
          teePlayedSplit?.splice?.(-1);
          return teePlayedSplit?.join?.(' - ');
        } else {
          return teePlayed;
        }
      } else {
        return teePlayed;
      }
    } catch (error) {}
  }, [teePlayed, courseHoleNumber, holesPlayed]);
  const addScore = async isSubmitingTo3rdParty => {
    let submitResult = {};
    let roundScore = courseScore;
    if (
      canPostToUSGA &&
      ghinPostScoreMode === GHIN_POST_SCORE_MODE.HBH &&
      roundDataSubmit?.round_mode &&
      roundDataSubmit?.round_mode !== MODE_ROUND.SIMPLE
    ) {
      roundScore = roundDataSubmit?.total_score || 0;
    }
    if (!isSubmitingTo3rdParty) {
      const validateRs = validateRoundScore(roundScore, holesPlayed);
      if (!validateRs) return;
      setLoading(true);
    }

    try {
      let postBy = igolfRoundInfo?.generatedBy;
      if (!postBy) {
        postBy = Platform.OS === 'ios' ? 'iOS Device' : 'Android Device';
      }
      let userTimezone = igolfRoundInfo?.userTimezone;
      if (!userTimezone) {
        userTimezone = new Date().getTimezoneOffset() / -60;
      }
      let dataSubmit = {
        teeName: splitTeeNameFromTeePlayed,
        datePlayed: datePlayed,
        numberOfHolesPlayed:
          canPostToUSGA && ghinPostScoreMode === GHIN_POST_SCORE_MODE.HBH
            ? roundDataSubmit?.holes?.filter(item => item.completeHole)?.length
            : holesPlayed,
        front9Score: frontBackPlayed === 'front' ? roundScore : null,
        back9Score: frontBackPlayed === 'back' ? roundScore : null,
        eighteenHoleScore: frontBackPlayed ? null : roundScore,
        userTimezone: userTimezone,
        generatedBy: postBy,
        roundType: 'Practice',
        roundMode:
          ghinPostScoreMode === GHIN_POST_SCORE_MODE.HBH
            ? roundDataSubmit?.round_mode
            : MODE_ROUND.SIMPLE,
        courseID: courseDetails?.idCourse,
        courseName: params?.title || courseDetails?.courseName,
      };
      if (isSubmitingTo3rdParty) {
        dataSubmit.sourceType = getScoreType();
        if (syncType === SYNC_ROUND_TYPE.WHS) {
          dataSubmit.datePlayed = convertUTCToTimezone(
            datePlayed,
            userTimezone,
          );
          dataSubmit.datePlayedUTC = datePlayed;
          dataSubmit.postScoreGolfNet = true;
          dataSubmit.golfNetCourseId = ghinCourseId;
          dataSubmit.golfNetCourseName = ghinCourseName;
          dataSubmit.golfNetTeeSetId = ghinTeeSelected?.teeSetRatingId + '';
          dataSubmit.golfNetTeeSetName = ghinTeeSelected?.teeSetRatingName;
          dataSubmit.roundId = igolfRoundId;
        } else {
          const dataSubmitUSGA = {
            round: {
              back_9_score: dataSubmit.back9Score,
              completed: true,
              duration: 0,
              eighteen_holes_score: dataSubmit.eighteenHoleScore,
              event_id: 0,
              front_9_score: dataSubmit.front9Score,
              generated_by: dataSubmit.generatedBy,
              course_name: dataSubmit.courseName,
              igolf_course_id: courseId,
              inprogress: false,
              map_id: 'iGolf',
              played_on: dataSubmit.datePlayed,
              round_mode: dataSubmit.roundMode,
              round_type: dataSubmit.roundType,
              team: false,
              tee_name: ghinTeeSelected?.teeSetRatingName,
              total_score: roundScore,
              user_timezone: dataSubmit.userTimezone,
              ghin_course_id: ghinId,
              ghin_course_name:
                params?.title || ghinCourseName || courseDetails?.courseName,
              ghin_tee_set_id: ghinTeeSelected?.teeSetRatingId
                ? ghinTeeSelected?.teeSetRatingId + ''
                : undefined,
              ghin_tee_set_name: ghinTeeSelected?.teeSetRatingName,
              source_type: getScoreType(),
              number_of_holes_played: dataSubmit.numberOfHolesPlayed,
              holes:
                canPostToUSGA && ghinPostScoreMode === GHIN_POST_SCORE_MODE.HBH
                  ? roundDataSubmit?.holes
                  : undefined,
            },
            round_submit: true,
            post_score_ghin: true,
          };
          submitResult = await addRoundUSGA(dataSubmitUSGA);
        }
      }
      if (!canPostToUSGA) {
        submitResult = await addAScore(dataSubmit);
      }
      if (isSubmitingTo3rdParty) {
        onReloadListUSGA?.();
      }
    } catch (error) {
      console.log(error.message);
      setLoading(false);
      if (isModalShowSubmit) {
        sheetRef?.current?.showErrorToast({
          error,
          title: t('score.add_edit_details.we_lost_the_scorecard'),
          description: t(
            'score.add_edit_details.an_error_occurred_saving_your_score',
          ),
        });
      } else {
        showErrorToast({
          error,
          title: t('score.add_edit_details.we_lost_the_scorecard'),
          description: t(
            'score.add_edit_details.an_error_occurred_saving_your_score',
          ),
        });
      }
      return;
    }
    GA_logEvent(GA_EVENT_NAME.POST_SCORE, {
      score: courseScore + '',
      tee_color: `${teePlayed || ''} - ${
        teeDistanceObj?.value || NOT_APPLICABLE
      } ${teeDistanceObj?.unit}`,
      course_par: coursePar || '0',
      number_of_holes: holesPlayed + ' holes',
      date_played: moment(datePlayed).format('LL'),
      location: courseDetails.courseName || '',
      click_location: 'post-score',
      page_type: SCREEN_TYPES.PLAY,
      page_category: PAGE_CATEGORY.PLAY_POST_SCORE,
      page_name: PAGE_NAME.PLAY_ROUND_DETAILS,
      screen_type: SCREEN_TYPES.PLAY,
    });

    EventEmitter.emit('reloadScore');
    try {
      // Make request to get recent rounds
      const recentRounds = await getFiveRecentRounds(1, playService);
      // Update scores in redux
      updateScores(recentRounds?.data);
    } catch (error) {
      showToast({
        type: 'error',
        message: t('An_error_occurred_retrieving_recent_rounds'),
      });
    }

    // Set timeout so that backend has enough time to update tiles endpoint
    setTimeout(() => {
      shouldReloadData(new Date().getTime());
      setLoading(false);
      if (!isSubmitingTo3rdParty) {
        navigation.navigate('App', {screen: 'Play'});
      }
    }, 2000);
    return submitResult;
  };

  useEffect(() => {
    if (!roundInfo) {
      if (courseHoleNumber === 9) {
        setHolesPlayed(9);
      } else {
        setHolesPlayed(18);
      }
    }
  }, [courseHoleNumber, roundInfo]);

  const updateScore = async (isSubmitingTo3rdParty, currentRoundId) => {
    if (!courseScore) {
      return showToast({
        type: 'error',
        message: t('score.add_edit_details.all_like_to_shoot_0'),
        subText: t('Please_enter_a_score'),
      });
    }

    if (!isSubmitingTo3rdParty) {
      setLoading(true);
    }
    let submitResult = {};
    try {
      const dataSubmit = {
        courseID: courseDetails.idCourse,
        courseName: courseDetails.courseName,
        teeName: teePlayed.split?.(' - ')?.[0],
        datePlayed: datePlayed,
        numberOfHolesPlayed: holesPlayed,
        front9Score: frontBackPlayed === 'front' ? courseScore : null,
        back9Score: frontBackPlayed === 'back' ? courseScore : null,
        eighteenHoleScore: frontBackPlayed ? null : courseScore,
        userTimezone: new Date().getTimezoneOffset() / 60,
        generatedBy: Platform.OS === 'ios' ? 'iOS Device' : 'Android Device',
        roundType: 'Practice',
        roundMode: 'Simple',
        sourceType: getScoreType(),
      };
      if (isSubmitingTo3rdParty) {
        dataSubmit.sourceType = getScoreType();
      }
      submitResult = await editAScore(
        roundInfo?.id || currentRoundId,
        dataSubmit,
      );
      await analytics().logEvent('score_updated');
      EventEmitter.emit('reloadScore');
      // Make request to get recent rounds
      const recentRounds = await getFiveRecentRounds(1, playService);
      // Update scores in redux
      updateScores(recentRounds?.data);
      // Set timeout so that backend has enough time to update tiles endpoint
      setTimeout(() => {
        shouldReloadData(new Date().getTime());
        setLoading(false);
        if (!isSubmitingTo3rdParty) {
          navigation.navigate('App', {screen: 'Play'});
        }
      }, 2000);
    } catch (error) {
      console.log(error.message);
      setLoading(false);
      showErrorToast({
        error,
        title: t('score.add_edit_details.we_lost_the_scorecard'),
        description: t(
          'score.add_edit_details.an_error_occurred_saving_your_score',
        ),
      });
    }

    return submitResult;
  };

  const deleteScore = async () => {
    const roundScores = [...scores];
    setLoading(true);
    try {
      await deleteAScore(roundInfo?.id);
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: t('score.add_edit_details.can_not_erase_the_bad_shots'),
        subText: t(
          'score.add_edit_details.we_had_an_issue_deleting_your_score',
        ),
      });
    } finally {
      // Update scores in redux
      updateScores(
        roundScores.filter(roundScore => roundScore.id !== roundInfo?.id),
      );
      setTimeout(() => {
        shouldReloadData(new Date().getTime());
        setLoading(false);
        navigation.navigate('App', {screen: 'Play'});
      }, 2000);
    }
  };

  const onPressSubmitToGhin = async roundId => {
    try {
      if (roundId) {
        let rsUpdate = await updateScore(true, roundId);
        if (rsUpdate) {
          return await submitRoundToGhin(roundId);
        }
      } else {
        return await addScore(true);
      }
    } catch (error) {
      throw error;
    }
  };
  const onCloseSuccessGhinModal = async () => {
    navigation.navigate('App', {screen: 'Play'});
  };

  const teeRating = useMemo(() => {
    if (canPost3rdParty) {
      if (ghinTeeSelected) {
        let currentRatingType = frontBackPlayed?.toLowerCase?.() || 'total';
        let courseGhinRating = ghinTeeSelected?.ratings?.find?.(
          item => item.ratingType?.toLowerCase() === currentRatingType,
        );
        if (courseGhinRating) {
          const teeRatingDisplay = `${courseGhinRating.courseRating || 0} / ${
            courseGhinRating.slopeRating || 0
          } / ${courseGhinRating.courseHandicap || 0}`;
          return teeRatingDisplay ? teeRatingDisplay : '0 / 0 / 0';
        } else {
          return '0 / 0 / 0';
        }
      } else {
        return '0 / 0 / 0';
      }
    } else {
      const tee = teeList?.find?.(
        item => item.teeName === splitTeeNameFromTeePlayed,
      );
      let courseHandicap = 0;
      let courseRating = 0;
      let slopeRating = 0;
      if (tee) {
        // Handicaps start at 28 for men and 36 for women
        slopeRating =
          user.gender === 'female' ? tee?.slopeWomen : tee?.slopeMen;
        courseRating =
          user.gender === 'female' ? tee?.ratingWomen : tee?.ratingMen;
        courseHandicap =
          (ghin?.hiValue ? ghin?.hiValue : 0) * (slopeRating / 113) +
          (courseRating - coursePar);
      }
      return `${courseRating || 0} / ${slopeRating || 0} / ${
        ghin?.hiValue ? Math.round(courseHandicap) : coursePar
      }`;
    }
  }, [
    isGolfCanada,
    ghinTeeSelected,
    frontBackPlayed,
    teeList,
    ghin?.hiValue,
    coursePar,
    splitTeeNameFromTeePlayed,
    user.gender,
  ]);

  const onSaveScore = () => {
    if (params.scoreType === 'add') {
      if (canPost3rdParty) {
        GA_logCtaEvent(
          'mytm_post_score',
          'Post only to TM+',
          'post score - details',
          SCREEN_CLASS.PLAY,
          SCREEN_TYPES.PLAY,
        );
      }
      addScore();
    } else {
      updateScore();
    }
  };

  const onPostScoreToUSGA = () => {
    try {
      let roundScore = courseScore;
      if (
        canPostToUSGA &&
        ghinPostScoreMode === GHIN_POST_SCORE_MODE.HBH &&
        roundDataSubmit?.round_mode &&
        roundDataSubmit?.round_mode !== MODE_ROUND.SIMPLE
      ) {
        roundScore = roundDataSubmit?.total_score || 0;
      }
      const validateRs = validateGhinRoundScore(
        roundScore,
        holesPlayed,
        canPostToUSGA && ghinPostScoreMode === GHIN_POST_SCORE_MODE.HBH
          ? roundDataSubmit?.holes?.filter(item => item.completeHole)?.length
          : holesPlayed,
        canPostToUSGA && ghinPostScoreMode === GHIN_POST_SCORE_MODE.HBH
          ? roundDataSubmit?.holes
          : null,
      );
      if (validateRs?.success && canPost3rdParty) {
        const checkNetwork = isConnectedNetwork();
        if (!checkNetwork) {
          showToastErrorInternet();
          return;
        }
        GA_logCtaEvent(
          'usga_submit_round',
          t('ghin.score.cta.post_score_to_usga'),
          'post score - details',
          SCREEN_CLASS.PLAY,
          SCREEN_TYPES.PLAY,
        );
        GA_logScreenViewV2(
          PAGE_NAME.POST_SCORE_TO_USGA,
          PAGE_CATEGORY.PLAY_POST_A_SCORE,
          SCREEN_CLASS.PLAY,
          SCREEN_CLASS.PLAY,
        );
        setModalShowSubmit(true);
        setTimeout(() => {
          sheetRef?.current?.snapTo(0);
        }, 300);
      }
    } catch (error) {}
  };

  const teeTextValue = () => {
    if (teePlayed) {
      return teePlayed;
    }
    if (courseHoleNumber === 18) {
      if (holesPlayed === 9) {
        return roundInfo?.back9Score
          ? `${roundInfo?.teeName} - Back 9`
          : `${roundInfo?.teeName || courseTeeNames[0]} - Front 9`;
      } else {
        return roundInfo?.teeName || courseTeeNames[0];
      }
    } else {
      return roundInfo?.teeName || courseTeeNames[0];
    }
  };

  const teeTotalYds = useMemo(() => {
    let selectedTee = teeList?.find?.(
      item => item.teeName === splitTeeNameFromTeePlayed,
    );
    if (courseHoleNumber === 18) {
      if (holesPlayed === 9) {
        return teePlayed?.includes('Back 9')
          ? selectedTee?.yds10To18
          : selectedTee?.yds1To9;
      } else {
        return selectedTee?.ydsTotal;
      }
    } else {
      return selectedTee?.ydsTotal;
    }
  }, [
    holesPlayed,
    courseHoleNumber,
    teeList,
    teePlayed,
    splitTeeNameFromTeePlayed,
  ]);

  const parValueInSubmitUSGA = useMemo(() => {
    if (canPostToUSGA && ghinPostScoreMode === GHIN_POST_SCORE_MODE.HBH) {
      const numberOfHolesPlayed = roundDataSubmit?.holes?.filter?.(
        item => item.completeHole,
      )?.length;
      // Check if only all front 9 holes (holes 1–9) are completed
      const onlyFrontNineCompleted =
        roundDataSubmit?.holes.filter(holeItem => holeItem.completeHole)
          .length === 9 &&
        roundDataSubmit?.holes
          .slice(0, 9)
          .every(holeItem => holeItem.completeHole);

      // Check if only all back 9 holes (holes 10–18) are completed
      const onlyBackNineCompleted =
        roundDataSubmit?.holes.filter(holeItem => holeItem.completeHole)
          .length === 9 &&
        roundDataSubmit?.holes
          .slice(9, 18)
          .every(holeItem => holeItem.completeHole);
      if (numberOfHolesPlayed === 9) {
        if (onlyFrontNineCompleted) {
          return courseScoreCard?.parOut;
        }
        if (onlyBackNineCompleted) {
          return courseScoreCard?.parIn;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }, [canPostToUSGA, ghinPostScoreMode, roundDataSubmit, courseScoreCard]);

  const onPressBack = () => {
    if (params.scoreType === 'edit') {
      navigation.navigate('ScoreDetail', {
        roundInfo: params.roundInfo,
        allowBackToScores: params?.origin === 'PostScore' ? true : false,
      });
      return;
    }
    if (params.scoreType === 'find') {
      navigation.navigate('ScoresStats', {
        screen: 'FindACourse',
        params: {
          screen: 'TabRecentCourses',
        },
      });
      return;
    }
    navigation.goBack();
  };

  const onPressPostScoreHBH = async () => {
    setHoleByHoleInputVisible(true);
  };

  const renderHeader = () => {
    return (
      <View
        style={[appStyles.row, {marginTop: insets.top + 16, minHeight: 60}]}
      >
        <TouchableOpacity
          onPress={onPressBack}
          style={{
            marginHorizontal: 16,
            marginTop: Platform.OS === 'android' ? 5 : 3,
          }}
        >
          <MaterialIcons name={'arrow-back'} color={'black'} size={22} />
        </TouchableOpacity>
        <View style={appStyles.flex}>
          <Text
            dinNext79
            black
            weight={800}
            size={22}
            numberOfLines={2}
            style={{paddingRight: 16, letterSpacing: 1.1}}
          >
            {(
              params.roundInfo?.courseName ||
              params.title ||
              t('scores.stats.score_details')
            ).toUpperCase()}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={[appStyles.flex, appStyles.whiteBg]}>
      <FocusAwareStatusBar barStyle={'dark-content'} />
      {loading ? (
        <ActivityIndicator style={appStyles.pTLg} />
      ) : (
        <View style={appStyles.flex}>
          {renderHeader()}
          <KeyboardAwareScrollView>
            <PostScoreDetailForm
              ghinPostScoreMode={ghinPostScoreMode}
              roundDataSubmit={roundDataSubmit}
              courseHoleNumber={courseHoleNumber}
              setHolesPlayed={setHolesPlayed}
              holesPlayed={holesPlayed}
              courseScoreCard={courseScoreCard}
              teePlayed={teePlayed}
              canPost3rdParty={canPost3rdParty}
              setEditFromScoreCard={setEditFromScoreCard}
              onPressPostScoreHBH={onPressPostScoreHBH}
              setHoleSelected={setHoleSelected}
              setTooltipVisible={setTooltipVisible}
              courseScore={courseScore}
              setCourseScore={setCourseScore}
              deleteScore={deleteScore}
              scoreType={scoreType}
              teeListOptions={
                canPostToUSGA ? modifiedGhinTeeList : modifiedCourseTeeNames
              }
              coursePar={coursePar}
              teeRating={teeRating}
              datePlayed={datePlayed}
              isEditing={params.scoreType === 'edit'}
              setGhinPostScoreMode={setGhinPostScoreMode}
              resetRoundData={resetRoundData}
              scoreTypeRef={scoreTypeRef}
              teesRef={teesRef}
              datePlayedRef={datePlayedRef}
              ref={formDetailRef}
              ghinTeesRef={ghinTeesRef}
              ghinTeeSelected={ghinTeeSelected}
            />
          </KeyboardAwareScrollView>
          <View
            style={[
              {
                position: 'absolute',
                bottom: insets.bottom > 0 ? 34 : 20,
                width: '100%',
              },
            ]}
          >
            {canPost3rdParty && params.scoreType === 'add' && (
              <ButtonUSGA
                textColor={'white'}
                Din79Font
                loading={loading}
                disabled={loading || !postButtonEnabled}
                loadingMode={'dark'}
                onPress={onPostScoreToUSGA}
                textStyle={[appStyles.uppercase, styles.viewTextButton]}
                style={{height: 40, marginHorizontal: 16}}
              />
            )}
            {canShowButtonSaveScore && (
              <Button
                text={
                  params.scoreType === 'add'
                    ? t('home.post_score_detail.cta')
                    : t('common.save')
                }
                backgroundColor={'black'}
                textColor={'white'}
                centered
                Din79Font
                loading={loading}
                disabled={loading}
                loadingMode={'dark'}
                onPress={onSaveScore}
                textStyle={[appStyles.uppercase, styles.viewTextButton]}
                style={{height: 40, marginHorizontal: 16, marginTop: 8}}
                rightComponent={
                  params.scoreType === 'add' &&
                  canPost3rdParty && <LogoTMPostScore style={{marginLeft: 5}} />
                }
              />
            )}
          </View>
        </View>
      )}
      {(canPostToUSGA || isGolfCanada) && (
        <BottomSheetSubmitGhinConfirm
          currentRoundInfo={
            roundInfo || {
              playedOn: datePlayed,
              teeName: splitTeeNameFromTeePlayed,
              coursePar: parValueInSubmitUSGA
                ? parValueInSubmitUSGA
                : coursePar,
              roundMode: roundDataSubmit?.round_mode || MODE_ROUND.SIMPLE,
              totalScore: roundDataSubmit?.total_score || courseScore,
              courseName:
                params?.title || ghinCourseName || courseDetails?.courseName,
              standardHoleNumber: holesPlayed,
              igolfCourseId: roundInfo?.igolfCourseId || courseId,
              holes:
                canPostToUSGA && ghinPostScoreMode === GHIN_POST_SCORE_MODE.HBH
                  ? roundDataSubmit?.holes
                  : undefined,
            }
          }
          teeValueDisplay={teeDistanceObj?.value}
          ref={sheetRef}
          onPressSubmit={onPressSubmitToGhin}
          onCloseSuccessModal={onCloseSuccessGhinModal}
          navigation={navigation}
          origin={'ScoreAddEditDetails'}
          isModalShow={isModalShowSubmit}
          onCloseModal={() => setModalShowSubmit(false)}
        />
      )}
      <AdjustGrossScoreModal
        visible={isTooltipVisible}
        backdropPress={() => {
          setTooltipVisible(false);
        }}
      />
      <WheelPickerBottomSheet
        ref={datePlayedRef}
        type={'date'}
        allowFutureDates={false}
        currentValue={datePlayed}
        onChange={value => setDatePlayed(moment(value).toISOString())}
        pickerTitle={t('play.select_date')}
      />
      <WheelPickerBottomSheet
        ref={teesRef}
        currentValue={teeTextValue()}
        options={modifiedCourseTeeNames}
        onChange={value => {
          setTeePlayed(value);
        }}
        pickerTitle={t('play.select_tee')}
        type={'text'}
      />
      <WheelPickerBottomSheet
        ref={ghinTeesRef}
        currentValue={ghinTeeSelected}
        options={modifiedGhinTeeList}
        onChange={value => {
          setGhinTeeSelected(value);
        }}
        pickerTitle={t('play.select_tee')}
        keyProp={'teeSetRatingId'}
        labelProp={'teeSetRatingName'}
      />
      <WheelPickerBottomSheet
        ref={scoreTypeRef}
        currentValue={scoreType}
        options={['Home', 'Away', 'Comp']}
        onChange={setScoreType}
        pickerTitle={t('play.select_score_type')}
        type={'text'}
      />
      <HoleByHoleScoreInput
        isScoreInputVisible={isHoleByHoleInputVisible}
        scoreCardDefault={courseScoreCard}
        setScoreInputVisible={isVisible => setHoleByHoleInputVisible(isVisible)}
        setRoundData={setRoundDataSubmit}
        roundData={roundDataSubmit}
        resetRoundData={resetRoundData}
        isEditing={isEditFromScoreCard}
        selectedHoleFromScoreCard={holeSelected}
        ref={ghinHoleInputRef}
      />
    </View>
  );
};

const mapDispatchToProps = {
  updateScores,
  shouldReloadData,
};

const styles = StyleSheet.create({
  viewTextButton: {
    fontWeight: '700',
    fontSize: 12,
    letterSpacing: 1.62,
  },
});

export default connect(null, mapDispatchToProps)(ScoreAddEditDetails);
