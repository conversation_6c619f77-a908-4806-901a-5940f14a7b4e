import React, {useState, useEffect} from 'react';
import {View, TouchableOpacity, Linking} from 'react-native';
import {
  heightPercentageToDP,
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import Icon from 'react-native-vector-icons/FontAwesome5';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Text from 'components/Text';
import DeviceInfo from 'react-native-device-info';
import appStyles from 'styles/global';
import {t} from 'i18next';
import {useSelector} from 'react-redux';
import {ARCCOS} from 'utils/constant';
import {ENVIRONMENTS} from 'config/env';
import StatBox from 'components/StatBox';
const isTablet = DeviceInfo.isTablet();

const ScoreDetailStrokesGained = ({roundInfo, scoreDetail}) => {
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );
  const navigateToOnCourse = async () => {
    const env = await AsyncStorage.getItem('env');
    Linking.openURL(
      playService === ARCCOS
        ? `arccosgolf://applink/stats?roundId=${roundInfo?.id}`
        : env === ENVIRONMENTS.PROD
        ? 'https://v28z.app.link'
        : 'https://v28z.test-app.link',
    );
  };
  return (
    <View style={[appStyles.pVSm]}>
      <Text style={[appStyles.md, appStyles.bold, appStyles.pBXs]}>
        home.scores_detail.supporting_copy.strokes_gained_stats.data_from_myroundpro
      </Text>
      <View style={[appStyles.pVXs]}>
        <View
          style={[
            appStyles.pTXs,
            {
              flexDirection: 'row',
              justifyContent: 'space-between',
              flexWrap: 'wrap',
            },
          ]}
        >
          <StatBox
            stat={`${
              scoreDetail?.roundStats?.strokesGainedDriving > 0 ? '+' : ''
            }${
              scoreDetail?.roundStats?.strokesGainedDriving
                ? `${scoreDetail?.roundStats?.strokesGainedDriving}`
                : '- -'
            }`}
            text={t('home.stats.supporting_copy.strokes_averages.driving')}
          />
          <StatBox
            stat={`${
              scoreDetail?.roundStats?.strokesGainedApproach > 0 ? '+' : ''
            }${
              scoreDetail?.roundStats?.strokesGainedApproach
                ? `${scoreDetail?.roundStats?.strokesGainedApproach}`
                : '- -'
            }`}
            text={t('home.stats.supporting_copy.strokes_averages.approach')}
          />

          <StatBox
            stat={`${
              scoreDetail?.roundStats?.strokesGainedShort > 0 ? '+' : ''
            }${
              scoreDetail?.roundStats?.strokesGainedShort
                ? `${scoreDetail?.roundStats?.strokesGainedShort}`
                : '- -'
            }`}
            text={t('home.stats.supporting_copy.strokes_averages.short')}
          />

          <StatBox
            stat={`${
              scoreDetail?.roundStats?.strokesGainedPutting > 0 ? '+' : ''
            }${
              scoreDetail?.roundStats?.strokesGainedPutting
                ? `${scoreDetail?.roundStats?.strokesGainedPutting}`
                : '- -'
            }`}
            text={t('home.stats.supporting_copy.strokes_averages.putting')}
          />
        </View>
      </View>
      <TouchableOpacity onPress={navigateToOnCourse}>
        <View
          style={[
            appStyles.borderRadius,
            appStyles.pSm,
            appStyles.row,
            appStyles.spaceBetween,
            {backgroundColor: '#E9E9EE'},
          ]}
        >
          <Text style={appStyles.bold}>home.stats.cta</Text>
          <Icon
            name={'chevron-right'}
            size={isTablet ? wp('3%') : wp('5%')}
            color={'#8C8C91'}
          />
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default ScoreDetailStrokesGained;
