import React, {useState, useEffect} from 'react';
import {View, ScrollView, Image, StyleSheet, Platform} from 'react-native';
import {
  heightPercentageToDP,
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import Text from 'components/Text';
import appStyles from 'styles/global';
import {moderateScale} from 'react-native-size-matters';
import {SYMBOL} from 'utils/constant';
import HitIcon from 'assets/imgs/hit.svg';
import NotApplicableIcon from 'assets/imgs/not_applicable.svg';
import {convertDistanceFromYards, getAbbreviatedUnit} from 'utils/convert';
import {useSelector} from 'react-redux';

const missUpIcon = require('assets/imgs/scorecard-miss-icon.png');
const UNDEFINED_VALUE = SYMBOL.DOUBLE_DASH;
const ScoreDetailScoreCard = ({scoreDetail, isShowSomeHoles = true}) => {
  const user = useSelector(state => state?.user);
  const userDistanceUnit =
    user?.measurementUnits?.toLowerCase?.() === 'meters' ? 'meters' : 'yards';
  const hasHoleWithYdsGreaterThanZero =
    scoreDetail?.holesData?.some(hole => (hole?.yds || 0) > 0) || false;
  const getFairways = (fwStat, score = 0) => {
    if (score > 0 && !fwStat) {
      return (
        <View style={{paddingTop: 5}}>
          <NotApplicableIcon width={25} height={16} />
        </View>
      );
    }
    switch (true) {
      case (fwStat + '').includes('_hit'):
        return <HitIcon />;
      case (fwStat + '').includes('_missed_left'):
        return (
          <Image
            resizeMode="cover"
            style={styles.fairwayIcon}
            source={missUpIcon}
          />
        );
      case (fwStat + '').includes('_missed_right'):
        return (
          <Image
            resizeMode="cover"
            style={[
              styles.fairwayIcon,
              {
                transform: [{rotate: '180deg'}],
              },
            ]}
            source={missUpIcon}
          />
        );
      case (fwStat + '').includes('_missed_long'):
        return (
          <Image
            resizeMode="cover"
            style={[
              styles.fairwayIcon,
              {
                transform: [{rotate: '90deg'}],
              },
            ]}
            source={missUpIcon}
          />
        );
      case (fwStat + '').includes('_missed_short'):
        return (
          <Image
            resizeMode="cover"
            style={[
              styles.fairwayIcon,
              {
                transform: [{rotate: '270deg'}],
              },
            ]}
            source={missUpIcon}
          />
        );
      default:
        return (
          <Text
            style={[
              styles.scoreCardTextStyle,
              {position: 'absolute', zIndex: 1000},
            ]}
          >
            {fwStat || UNDEFINED_VALUE}
          </Text>
        );
    }
  };

  const getScore = (score, par, isTotal = false, penaltyHit = false) => {
    const scoreDif = score - par;
    switch (true) {
      case score === 0:
        return (
          <>
            <Text
              style={[
                styles.scoreCardTextStyle,
                {position: 'absolute', zIndex: 1000},
              ]}
            >
              {UNDEFINED_VALUE}
            </Text>
          </>
        );
      case scoreDif === 1 && !isTotal:
        return (
          <>
            <View
              style={{
                borderWidth: 1,
                height: wp(7),
                width: wp(7),
                borderColor: '#8C8B8F',
                position: 'absolute',
              }}
            />
            <Text
              style={[
                styles.scoreCardTextStyle,
                {position: 'absolute', zIndex: 1000},
              ]}
            >
              {score}
              {penaltyHit && <Text style={styles.penaltyIcon}>!</Text>}
            </Text>
          </>
        );
      case scoreDif >= 2 && !isTotal:
        return (
          <>
            <View
              style={{
                borderWidth: 1,
                height: wp(9),
                width: wp(9),
                borderColor: '#8C8B8F',
                position: 'absolute',
              }}
            />
            <View
              style={{
                borderWidth: 1,
                height: wp(7),
                width: wp(7),
                borderColor: '#8C8B8F',
                position: 'absolute',
              }}
            />
            <Text
              style={[
                styles.scoreCardTextStyle,
                {position: 'absolute', zIndex: 1000},
              ]}
            >
              {score}
              {penaltyHit && <Text style={styles.penaltyIcon}>!</Text>}
            </Text>
          </>
        );
      case scoreDif === -1 && !isTotal:
        return (
          <>
            <View
              style={{
                borderWidth: 1,
                height: wp(7),
                width: wp(7),
                borderRadius: wp(3.5),
                borderColor: '#8C8B8F',
                position: 'absolute',
              }}
            />
            <Text
              style={[
                styles.scoreCardTextStyle,
                {position: 'absolute', zIndex: 1000},
              ]}
            >
              {score}
              {penaltyHit && <Text style={styles.penaltyIcon}>!</Text>}
            </Text>
          </>
        );
      case scoreDif <= -2 && !isTotal:
        return (
          <>
            <View
              style={{
                borderWidth: 1,
                height: wp(9),
                width: wp(9),
                borderRadius: wp(4.5),
                borderColor: '#8C8B8F',
                position: 'absolute',
              }}
            />
            <View
              style={{
                borderWidth: 1,
                height: wp(7),
                width: wp(7),
                borderRadius: wp(3.5),
                borderColor: '#8C8B8F',
                position: 'absolute',
              }}
            />
            <Text
              style={[
                styles.scoreCardTextStyle,
                {position: 'absolute', zIndex: 1000},
              ]}
            >
              {score}
              {penaltyHit && <Text style={styles.penaltyIcon}>!</Text>}
            </Text>
          </>
        );
      default:
        return (
          <>
            <Text
              style={[
                styles.scoreCardTextStyle,
                {position: 'absolute', zIndex: 1000},
              ]}
            >
              {score}
              {penaltyHit && <Text style={styles.penaltyIcon}>!</Text>}
            </Text>
          </>
        );
    }
  };
  const distanceUnitAbbreviated = getAbbreviatedUnit(userDistanceUnit);
  return (
    <View style={[appStyles.mTSm, appStyles.mLSm]}>
      <View style={[appStyles.row]}>
        <View style={[{width: wp(14)}, appStyles.whiteBg]}>
          <View
            style={[
              {
                flex: 1,
                borderRightWidth: 1,
                borderRightColor: 'white',
                backgroundColor: '#111111',
                height: moderateScale(35),
              },
              appStyles.hCenter,
              appStyles.vCenter,
            ]}
          >
            <Text DINbold style={styles.headerHoleNumber}>
              HOLE
            </Text>
          </View>
          {hasHoleWithYdsGreaterThanZero && (
            <View style={styles.scoreCardCellStyle}>
              <Text DINbold style={styles.scoreCardHTextStyle}>
                {distanceUnitAbbreviated === 'm'
                  ? 'METERS'
                  : distanceUnitAbbreviated?.toUpperCase?.()}
              </Text>
            </View>
          )}
          <View style={styles.scoreCardCellStyle}>
            <Text DINbold style={styles.scoreCardHTextStyle}>
              PAR
            </Text>
          </View>
          <View style={styles.scoreCardCellStyle}>
            <Text DINbold style={styles.scoreCardHTextStyle}>
              HCP
            </Text>
          </View>
          <View style={styles.scoreCardCellStyle}>
            <Text DINbold style={styles.scoreCardHTextStyle}>
              SCORE
            </Text>
          </View>
          {isShowSomeHoles && (
            <>
              <View style={styles.scoreCardCellStyle}>
                <Text DINbold style={styles.scoreCardHTextStyle}>
                  FAIRWAY
                </Text>
              </View>
              <View style={styles.scoreCardCellStyle}>
                <Text DINbold style={styles.scoreCardHTextStyle}>
                  GIR
                </Text>
              </View>
              <View style={styles.scoreCardCellStyle}>
                <Text DINbold style={styles.scoreCardHTextStyle}>
                  PUTTS
                </Text>
              </View>
            </>
          )}
        </View>
        <ScrollView horizontal>
          {scoreDetail?.holesData?.length > 0 &&
            scoreDetail?.holesData?.map((item, index) => {
              const holeDistanceObj = convertDistanceFromYards({
                distanceInYards: item.yds,
                userUnit: userDistanceUnit,
              });
              return (
                <View
                  key={index}
                  style={[
                    {width: item.holeNumber === 'TOTAL' ? wp(15) : wp(14)},
                    !isNaN(item.holeNumber) || item.holeNumber === 'TOTAL'
                      ? appStyles.whiteBg
                      : {backgroundColor: '#F4F5F6'},
                  ]}
                >
                  <View
                    style={[
                      {
                        flex: 1,
                        borderRightWidth: 1,
                        borderColor: 'white',
                        height: moderateScale(35),
                        backgroundColor: '#111111',
                      },
                      appStyles.hCenter,
                      appStyles.vCenter,
                      appStyles.md,
                    ]}
                  >
                    <Text DINbold style={styles.headerHoleNumber}>
                      {item.holeNumber}
                    </Text>
                  </View>
                  {hasHoleWithYdsGreaterThanZero && (
                    <View style={styles.scoreCardCellStyle}>
                      <Text style={styles.scoreCardTextStyle}>
                        {holeDistanceObj?.value}
                      </Text>
                    </View>
                  )}
                  <View style={styles.scoreCardCellStyle}>
                    <Text style={styles.scoreCardTextStyle}>
                      {item.par || UNDEFINED_VALUE}
                    </Text>
                  </View>
                  <View style={styles.scoreCardCellStyle}>
                    <Text style={styles.scoreCardTextStyle}>
                      {item.handicap == null ? UNDEFINED_VALUE : item.handicap}
                    </Text>
                  </View>
                  <View style={styles.scoreCardCellStyle}>
                    {getScore(
                      item.totalScore,
                      item.par,
                      isNaN(item.holeNumber),
                      item.penaltyHit,
                    )}
                  </View>
                  {isShowSomeHoles && (
                    <>
                      <View style={styles.scoreCardCellStyle}>
                        {getFairways(item.fwStats, item.totalScore)}
                      </View>
                      <View style={styles.scoreCardCellStyle}>
                        {getFairways(item.grStats, item.totalScore)}
                      </View>
                      <View style={styles.scoreCardCellStyle}>
                        <Text style={styles.scoreCardTextStyle}>
                          {item.totalScore > 0
                            ? item.puttsNumber == null
                              ? '--'
                              : item.puttsNumber
                            : '--'}
                        </Text>
                      </View>
                    </>
                  )}
                </View>
              );
            })}
        </ScrollView>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  scoreCardHTextStyle: {
    color: '#111111',
    ...appStyles.sm,
    paddingTop: Platform.OS === 'ios' ? 5 : 0,
  },
  scoreCardTextStyle: {
    ...appStyles.black,
    ...appStyles.xsm,
  },

  scoreCardCellStyle: {
    flex: 1,
    borderBottomWidth: 1,
    borderRightWidth: 1,
    height: moderateScale(50),
    borderColor: '#EBEBEB',
    ...appStyles.hCenter,
    ...appStyles.vCenter,
  },
  fairwayIcon: {
    ...appStyles.alignCenter,
    alignSelf: 'center',
    width: moderateScale(23),
    height: moderateScale(28),
  },
  penaltyIcon: {
    color: '#FF0000',
  },
  scoreCardTextNAStyle: {
    color: '#BDBDBD',
    fontSize: moderateScale(33),
    lineHeight: moderateScale(33),
    ...appStyles.boldL,
    maxHeight: moderateScale(48),
    paddingVertical: moderateScale(13),
  },
  headerHoleNumber: [
    appStyles.white,
    appStyles.sm,
    {paddingTop: Platform.OS === 'ios' ? 5 : 0},
  ],
});

export default ScoreDetailScoreCard;
