import React, {useImperativeHandle, useState, useEffect, useRef} from 'react';
import {View, StyleSheet, Image, TouchableOpacity} from 'react-native';
import Text from 'components/Text';
import {useDispatch, useSelector} from 'react-redux';
import appStyles from 'styles/global';
import BGLine from 'assets/imgs/clubhouse/bg_line.jpeg';
import CheckActive from 'assets/imgs/rewards/check_active.svg';
import CheckActiveKillApp from 'assets/imgs/rewards/check_active_killapp.svg';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {t} from 'i18next';
import {getLoyaltyActions} from 'requests/rewards';
import {getUserCompletedActions} from 'requests/loyalty';
import {setDeepLink} from 'reducers/app';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import {getCompletedLoyaltyActions} from 'utils/asyncStorage';
import {getAuth0AccessToken} from 'utils/user';
import {openEcomWebview, prepareLink} from 'utils/shop';
import useAppState from 'hooks/useAppState';
import {checkNotificationPermissions} from 'utils/home';
import analytics from '@react-native-firebase/analytics';
import {
  SCREEN_CLASS,
  SCREEN_TYPES,
  addLoyaltyPointsNotification,
  GA_EVENT_NAME,
  PAGE_CATEGORY,
  PAGE_NAME,
  CACHE_KEY,
} from 'utils/constant';
import {updateLoyaltyData} from 'reducers/loyalty';
import {GA_logEvent, GA_setUserProperty} from 'utils/googleAnalytics';
import {isCanadaMarket} from 'utils/commonVariable';

const TIERCOLORS = [
  {
    name: 'PAR',
    color: '#03A800',
  },
  {
    name: 'BIRDIE',
    color: '#00A3FF',
  },
  {
    name: 'EAGLE',
    color: '#FC0',
  },
];

const EarnPointNows = (
  {setOpenNotify, openNotify, hasOpenedNotiSetting},
  ref,
) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const {navigate} = navigation;
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const loyalty = useSelector(state => state?.loyalty);
  const {currentTier} = loyalty?.tier;
  const {loyaltyActions} = loyalty;
  const userAction = loyalty?.completedActions;
  const refIsFocus = useRef(null);
  const refHasOpenedNotiSetting = useRef(false);
  const appCacheVersions = useSelector(state => state.appCacheVersions);

  const findColorTier = currentTier => {
    if (currentTier) {
      const item = TIERCOLORS.find(
        val => val.name === currentTier.toUpperCase(),
      );
      if (item) {
        return item.color;
      }
    }

    return '#03A800';
  };
  const subColor = findColorTier(currentTier);

  let dataFirst = [
    {
      name: 'Place an Order from Taylormadegolf.com',
      actionPoints: '1 Point per $1 spent',
      feature: 'PLACE_ORDER',
      ctaLink:
        'https://www.taylormadegolf.com/home/<USER>',
    },
    {
      name: 'Upload receipt of purchase',
      actionPoints: '1 Point per $1 spent',
      feature: 'UPLOAD_PURCHASE',
      ctaLink:
        'https://www.taylormadegolf.com/productregistration/??utm_source=teamtaylormade&utm_medium=app&utm_campaign=eqp+fbag+gen+app+tmapp+loy+gen+txt',
    },
  ];

  if (isCanadaMarket()) {
    dataFirst = [
      {
        name: 'Place an Order from Taylormadegolf.ca',
        actionPoints: '1 Point per $1 spent',
        feature: 'PLACE_ORDER',
        ctaLink:
          'https://www.taylormadegolf.ca/home/<USER>',
      },
    ];
  }
  useEffect(() => {
    if (!openNotify && data?.length > 0) {
      setTimeout(() => {
        loadDataFromApi();
      }, 200);
    }
  }, [openNotify]);

  useFocusEffect(
    React.useCallback(() => {
      refIsFocus.current = true;
      loadDataFromApi();
      return () => {
        refIsFocus.current = false;
      };
    }, [appCacheVersions]),
  );

  useEffect(() => {
    refHasOpenedNotiSetting.current = hasOpenedNotiSetting;
  }, [hasOpenedNotiSetting]);

  useAppState({
    onForeground: () => {
      if (refIsFocus.current && refHasOpenedNotiSetting.current) {
        checkPermissionWhenFocus();
      }
    },
  });

  useImperativeHandle(ref, () => ({
    refreshData: () => {
      loadData();
    },
  }));

  const checkPermissionWhenFocus = async () => {
    const result = await checkNotificationPermissions();
    if (result) {
      await addLoyaltyPointsNotification(
        dispatch,
        user,
        loyalty?.completedActions,
        loyalty?.loyaltyActions?.data,
        'rewards',
      );
      GA_logEvent(GA_EVENT_NAME.ENABLE_NOTIFICATIONS, {
        callout_name: 'notifications enable',
        click_location: 'rewards',
        page_type: SCREEN_TYPES.REWARDS,
        page_category: PAGE_CATEGORY.REWARDS,
        page_name: PAGE_NAME.REWARDS_MAIN_INFO,
        screen_type: SCREEN_TYPES.REWARDS,
      });
      GA_setUserProperty('notifications_enabled', 'true');
    } else {
      GA_setUserProperty('notifications_enabled', 'false');
    }
    refHasOpenedNotiSetting.current = false;
  };

  const handleDataEarnPoint = (
    loyaltyActions,
    completedActions,
    userAction,
  ) => {
    return [...loyaltyActions].map(val => {
      let action = {...val};
      const findUserAction =
        userAction &&
        userAction.length > 0 &&
        userAction.find(
          value => Number(value?.actionId) === Number(val?.annexActionId),
        );
      const findActionStorage =
        completedActions &&
        completedActions?.length > 0 &&
        completedActions.find(keyAction => keyAction === val?.feature);
      if (findUserAction) {
        if (findActionStorage) {
          action.activeGold = true;
        } else {
          action.active = true;
        }
        action.actionPoints = `${val.actionPoints} Points`;
      } else {
        action.active = false;
        action.actionPoints = `${val.actionPoints} Points`;
      }
      return action;
    });
  };

  useEffect(() => {
    if (userAction && loyaltyActions) {
      loadData();
    }
  }, [userAction, loyaltyActions]);

  const loadData = async () => {
    try {
      setLoading(true);
      const completedActions = await getCompletedLoyaltyActions();
      if (loyaltyActions?.data && loyaltyActions?.data?.length) {
        const result = handleDataEarnPoint(
          loyaltyActions?.data,
          completedActions,
          userAction,
        );
        setData([...dataFirst, ...result]);
      } else {
        setData([]);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const loadDataFromApi = async () => {
    try {
      const params = {
        country: userCountry,
      };
      const currentLoyaltyActionsCacheVersion =
        appCacheVersions?.features?.find?.(
          item => item.key === CACHE_KEY.REWARD_LOYALTY_ACTIONS,
        )?.version;
      let apiRequests = [
        getCompletedLoyaltyActions(),
        getUserCompletedActions(),
      ];

      if (loyalty && appCacheVersions) {
        const isLoyaltyDataValid =
          loyaltyActions?.version === currentLoyaltyActionsCacheVersion &&
          loyaltyActions?.country === appCacheVersions?.country &&
          currentLoyaltyActionsCacheVersion != null;
        if (!isLoyaltyDataValid) {
          apiRequests.push(getLoyaltyActions(params));
        }
      }
      let [completedActions, userActionFromApi, results] = await Promise.all(
        apiRequests,
      );
      dispatch(
        updateLoyaltyData({
          completedActions: userActionFromApi,
        }),
      );
      if (results) {
        dispatch(
          updateLoyaltyData({
            loyaltyActions: {
              data: results?.length > 0 ? results : [],
              country: userCountry,
              version: currentLoyaltyActionsCacheVersion,
            },
          }),
        );
      }
      if (results || loyaltyActions?.data) {
        let dataEarnPoint = handleDataEarnPoint(
          results || loyaltyActions?.data,
          completedActions,
          userActionFromApi,
        );
        setData([...dataFirst, ...dataEarnPoint]);
      } else {
        setData([]);
      }
    } catch (error) {
      console.log('error', error.message);
      setLoading(false);
    }
  };

  const onPressView = async item => {
    const {ctaLink} = item;
    if (!item.active && !item.activeGold) {
      GA_logEvent(GA_EVENT_NAME.PROMPT_NOTIFICATIONS, {
        callout_name: item?.name,
        click_location: 'earn-points-now',
        page_type: SCREEN_TYPES.REWARDS,
        page_category: PAGE_CATEGORY.REWARDS_MAIN,
        page_name: PAGE_NAME.REWARDS_MAIN_INFO,
        screen_type: SCREEN_TYPES.REWARDS,
      });
      GA_logEvent(GA_EVENT_NAME.NAV_CLICK, {
        screen_type: SCREEN_TYPES.REWARDS,
        page_name: PAGE_NAME.REWARDS_MAIN_INFO,
        page_type: SCREEN_TYPES.REWARDS,
        page_category: PAGE_CATEGORY.REWARDS_MAIN,
        nav_type: 'rewards',
        nav_item_selected: item?.name,
        nav_level: 'earn points',
      });
      if (ctaLink && ctaLink.includes('app.link/mytaylormadeplus/')) {
        const paramsDeelinks = ctaLink?.split('/');
        if (paramsDeelinks.length > 0) {
          const lastItem = paramsDeelinks[paramsDeelinks.length - 1];
          if (lastItem && lastItem?.length > 0) {
            dispatch(setDeepLink(lastItem));
          }
        }
      } else if (
        ctaLink &&
        (ctaLink?.includes('http') || ctaLink?.includes('www.'))
      ) {
        const accessToken = await getAuth0AccessToken(dispatch);
        let options = data?.options;
        try {
          options = JSON.parse(data?.options);
        } catch (error) {}
        const linkUrl = await prepareLink(
          ctaLink,
          accessToken,
          options?.ctaLinkType,
        );
        openEcomWebview(navigate, {
          title: ctaLink,
          uri: linkUrl,
          canGoBack: true,
          originUri: ctaLink,
          clickLocation: 'rewards-earn-point-now',
          origin: item?.feature,
          isLogSelectItem: false,
        });
      } else if (!ctaLink && item?.feature === 'ENABLE_NOTIFICATIONS') {
        setOpenNotify(true);
      }
    }
    return null;
  };

  const renderItem = item => {
    return !item.active && !item.activeGold
      ? renderItemInActive(item)
      : item?.activeGold
      ? renderItemActiveGold(item)
      : renderItemActive(item);
  };

  const renderItemInActive = item => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        delayPressIn={100}
        delayPressOut={100}
        style={styles.viewItem}
        onPress={() => onPressView(item)}
        disabled={!!item.active || !!item.activeGold}
      >
        <View
          style={[appStyles.vCenter, appStyles.flex, {paddingHorizontal: 16}]}
        >
          <Text white Din79Font weight={700} size={12} style={styles.title}>
            {item?.name}
          </Text>
          <Text size={12} weight={400} style={{color: subColor}}>
            {item?.actionPoints}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderItemActiveGold = item => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        delayPressIn={100}
        delayPressOut={100}
        style={styles.viewItemActiveGold}
        onPress={() => onPressView(item)}
        disabled={!!item.active || !!item.activeGold}
      >
        <View style={styles.viewImageActive}>
          <Image source={BGLine} style={{width: wp(96), height: 56}} />
          <View style={styles.viewInImage} />
        </View>
        <View
          style={[appStyles.row, appStyles.center, {paddingHorizontal: 16}]}
        >
          <View style={{flex: 1}}>
            <Text
              white
              Din79Font
              weight={700}
              size={12}
              style={styles.titleActive}
            >
              {item?.name}
            </Text>
            <Text size={12} weight={400} style={styles.subTitleActive}>
              {item?.actionPoints}
            </Text>
          </View>
          <View style={styles.iconActive}>
            <CheckActive />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderItemActive = item => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        delayPressIn={100}
        delayPressOut={100}
        style={styles.viewItemActive}
        onPress={() => onPressView(item)}
        disabled={!!item.active || !!item.activeGold}
      >
        <View
          style={[appStyles.row, appStyles.center, {paddingHorizontal: 16}]}
        >
          <View style={{flex: 1}}>
            <Text
              white
              Din79Font
              weight={700}
              size={12}
              style={styles.titleActive2}
            >
              {item?.name}
            </Text>
            <Text size={12} weight={400} style={styles.subTitleActive2}>
              {item?.actionPoints}
            </Text>
          </View>
          <View style={styles.iconActive2}>
            <CheckActiveKillApp />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.viewContainer}>
      <Text size={16} weight={400} white style={styles.earnPoint}>
        {t('rewards.earn_point_now')}
      </Text>
      {loading && (!data || !data.length) ? (
        <View>
          <View style={styles.viewItem}>
            <View style={styles.viewShimmer}>
              <ShimmerPlaceholder
                LinearGradient={LinearGradient}
                width={223}
                height={12}
                style={{marginBottom: 8}}
              />
              <ShimmerPlaceholder
                width={62}
                height={12}
                LinearGradient={LinearGradient}
              />
            </View>
          </View>
          <View style={styles.viewItem}>
            <View style={styles.viewShimmer}>
              <ShimmerPlaceholder
                LinearGradient={LinearGradient}
                width={223}
                height={12}
                style={{marginBottom: 8}}
              />
              <ShimmerPlaceholder
                width={62}
                height={12}
                LinearGradient={LinearGradient}
              />
            </View>
          </View>
          <View style={styles.viewItem}>
            <View style={styles.viewShimmer}>
              <ShimmerPlaceholder
                LinearGradient={LinearGradient}
                width={223}
                height={12}
                style={{marginBottom: 8}}
              />
              <ShimmerPlaceholder
                width={62}
                height={12}
                LinearGradient={LinearGradient}
              />
            </View>
          </View>
          <View style={styles.viewItem}>
            <View style={styles.viewShimmer}>
              <ShimmerPlaceholder
                LinearGradient={LinearGradient}
                width={223}
                height={12}
                style={{marginBottom: 8}}
              />
              <ShimmerPlaceholder
                width={62}
                height={12}
                LinearGradient={LinearGradient}
              />
            </View>
          </View>
          <View style={styles.viewItem}>
            <View style={styles.viewShimmer}>
              <ShimmerPlaceholder
                LinearGradient={LinearGradient}
                width={223}
                height={12}
                style={{marginBottom: 8}}
              />
              <ShimmerPlaceholder
                width={62}
                height={12}
                LinearGradient={LinearGradient}
              />
            </View>
          </View>
        </View>
      ) : (
        data && data?.length > 0 && data?.map(val => renderItem(val))
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  viewContainer: {},
  viewShimmer: {
    flex: 1,
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  viewItem: {
    width: wp(96),
    height: 56,
    backgroundColor: '#000',
    borderRadius: 8,
    marginHorizontal: 8,
    marginBottom: 8,
  },
  viewItemActiveGold: {
    width: wp(96),
    height: 56,
    borderRadius: 8,
    marginHorizontal: 8,
    marginBottom: 8,
  },
  viewItemActive: {
    width: wp(96),
    height: 56,
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    marginHorizontal: 8,
    marginBottom: 8,
  },
  earnPoint: {
    lineHeight: 18,
    marginHorizontal: 16,
    paddingBottom: 12,
    paddingTop: 2,
  },
  title: {
    textTransform: 'uppercase',
    lineHeight: 17.74,
    letterSpacing: 1.62,
  },
  titleActive: {
    textTransform: 'uppercase',
    lineHeight: 17.74,
    letterSpacing: 1.62,
    color: '#000',
  },
  subTitleActive: {
    color: '#000',
  },
  titleActive2: {
    textTransform: 'uppercase',
    lineHeight: 17.74,
    letterSpacing: 1.62,
    color: '#808080',
  },
  subTitleActive2: {
    color: '#808080',
  },
  image: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
  },
  iconActive: {
    width: 24,
    height: 24,
    borderRadius: 24,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconActive2: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewImageActive: {
    position: 'absolute',
    flex: 1,
    borderRadius: 8,
    overflow: 'hidden',
  },
  viewInImage: {
    width: wp(96),
    height: 56,
    position: 'absolute',
    backgroundColor: 'rgba(255, 204, 0, 0.61)',
  },
});

export default React.forwardRef(EarnPointNows);
