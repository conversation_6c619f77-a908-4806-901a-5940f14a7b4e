import React from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import appStyles from 'styles/global';
import Text from 'components/Text';
import {useDispatch, useSelector} from 'react-redux';
import {requestNotificationPermissions} from 'utils/home';
import Modal from 'react-native-modal';
const PopupPermission = ({
  setOpenNotify,
  openNotify,
  setHasOpenedNotiSetting,
}) => {
  const user = useSelector(state => state?.user);
  const loyalty = useSelector(state => state?.loyalty);
  const dispatch = useDispatch();

  const openPermission = async () => {
    const checkRequest = await requestNotificationPermissions({
      dispatch,
      user,
      loyalty,
      origin: 'rewards',
    });
    if (!checkRequest) {
      setHasOpenedNotiSetting?.(true);
    }
    setTimeout(() => {
      setOpenNotify(false);
    }, 300);
  };

  return (
    <Modal
      isVisible={openNotify}
      style={{margin: 0, justifyContent: 'center', marginHorizontal: 8}}
      onBackdropPress={() => {
        setOpenNotify(false);
      }}
      onBackButtonPress={() => {
        setOpenNotify(false);
      }}
    >
      <View>
        <View style={styles.viewContainerNotify}>
          <Text black size={16} weight={700}>
            home.permissions_notifications.headline
          </Text>
          <Text
            size={16}
            weight={400}
            style={{
              textAlign: 'center',
              color: 'rgba(0, 0, 0, 0.6)',
              paddingTop: 10,
            }}
          >
            intro.be_the_first_to_know_about
          </Text>
          <TouchableOpacity
            style={styles.touchNotification}
            onPress={openPermission}
          >
            <Text
              size={12}
              Din79Font
              weight={700}
              white
              style={{
                textTransform: 'uppercase',
                letterSpacing: 1,
              }}
            >
              home.permissions_notifications.cta
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};
const styles = StyleSheet.create({
  touchNotification: {
    backgroundColor: '#000',
    paddingVertical: 14,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 14,
    borderRadius: 24,
    ...appStyles.viewShadowLight,
  },
  viewContainerNotify: {
    backgroundColor: '#fff',
    borderRadius: 24,
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 16,
  },
});
export default PopupPermission;
