import React, {useEffect, useImperativeHandle, useState} from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import Accordion from 'react-native-collapsible/Accordion';
import {useSelector} from 'react-redux';
import {getFAQs} from 'requests/rewards';
import PlusIcon from 'assets/imgs/rewards/plus.svg';
import MinusIcon from 'assets/imgs/rewards/minus.svg';
import Text from 'components/Text';
import {t} from 'i18next';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {
  BOTTOM_BAR_REAL_HEIGHT,
  GA_EVENT_NAME,
  NOT_APPLICABLE,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_TYPES,
} from 'utils/constant';
import {GA_logEvent} from 'utils/googleAnalytics';

const FAQs = ({scrollToEnd}, ref) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeSections, setActiveSections] = useState([]);
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;

  useImperativeHandle(ref, () => ({
    refreshData: () => {
      loadData();
    },
  }));

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const params = {
        country: userCountry,
      };

      const results = await getFAQs(params);
      if (results && results.length) {
        const handleData = results.map((val, key) => {
          return {
            key: key,
            title: val?.title,
            content: val?.desc,
          };
        });
        setData(handleData);
      } else {
        setData([]);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const renderHeader = section => {
    let open = false;
    if (activeSections.length && section?.key === activeSections[0]) {
      open = true;
    }
    return (
      <View style={styles.header}>
        <Text size={12} style={styles.headerText}>
          {section?.title}
        </Text>
        {open ? (
          <MinusIcon style={styles.btnIcon} />
        ) : (
          <PlusIcon style={styles.btnIcon} />
        )}
      </View>
    );
  };

  const renderContent = section => {
    let open = false;
    if (activeSections.length && section?.key === activeSections[0]) {
      open = true;
    }
    return (
      <TouchableOpacity
        style={styles.content}
        onPress={() => updateSections([section?.key], open)}
      >
        <Text size={12} style={styles.contentText}>
          {section?.content}
        </Text>
      </TouchableOpacity>
    );
  };

  const updateSections = (activeSections, open = false) => {
    const lastItem = data[data.length - 1] || null;
    if (lastItem && lastItem?.key && lastItem.key === activeSections[0]) {
      setTimeout(() => {
        scrollToEnd();
      }, 300);
    }
    if (open) {
      setActiveSections([]);
    } else {
      GA_logEvent(GA_EVENT_NAME.NAV_CLICK, {
        screen_type: SCREEN_TYPES.REWARDS,
        page_name: PAGE_NAME.REWARDS_MAIN_INFO,
        page_type: SCREEN_TYPES.REWARDS,
        page_category: PAGE_CATEGORY.REWARDS_MAIN,
        nav_type: 'rewards',
        nav_item_selected:
          data?.find?.(item => item.key === activeSections[0])?.title ||
          NOT_APPLICABLE,
        nav_level: 'faq',
      });
      setActiveSections(activeSections);
    }
  };

  return (
    <View>
      {data && data?.length > 0 ? (
        <View style={{paddingBottom: BOTTOM_BAR_REAL_HEIGHT}}>
          <Text size={16} weight={400} white style={styles.faq}>
            {t('rewards.faq')}
          </Text>
          <Accordion
            sections={data}
            activeSections={activeSections}
            renderHeader={renderHeader}
            renderContent={renderContent}
            containerStyle={styles.containerStyle}
            sectionContainerStyle={styles.sectionContainerStyle}
            onChange={updateSections}
          />
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  faq: {
    color: '#fff',
    lineHeight: 18,
    marginHorizontal: 16,
    marginBottom: 14,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
  },
  headerText: {
    color: '#e5e5e5',
    lineHeight: 16.5,
    fontWeight: '700',
    flex: 1,
    marginRight: 10,
  },
  content: {
    paddingBottom: 14,
    paddingTop: 3,
    paddingHorizontal: 16,
  },
  contentText: {
    color: '#e5e5e5',
    lineHeight: 16.5,
    fontWeight: '400',
  },
  containerStyle: {
    flex: 1,
    marginHorizontal: 8,
  },
  sectionContainerStyle: {
    minHeight: 57,
    backgroundColor: '#000',
    marginBottom: 8,
    borderRadius: 8,
    justifyContent: 'center',
    display: 'flex',
  },
  btnIcon: {
    padding: 4,
  },
});

export default React.forwardRef(FAQs);
