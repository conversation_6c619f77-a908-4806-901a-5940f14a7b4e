import React, {useEffect, useState} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import {isEmpty} from 'validator';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';
import {getClubResults} from 'requests/club-recommender';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';

const ClubDesiredShotShape = ({
  navigation,
  updateClubRecommender,
  clubRecommender,
}) => {
  const [desiredShotShape, setDesiredShotShape] = useState('');
  const [loading, setLoading] = useState(false);
  const validated = !isEmpty(desiredShotShape || '');

  useEffect(() => {
    updateClubRecommender({desiredDriverShotShape: desiredShotShape});
  }, [desiredShotShape]);

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return desiredShotShape === type ? 'white' : GREY;
      case 'border':
        return desiredShotShape === type ? GREEN : GREY;
      case 'background':
        return desiredShotShape === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const getResults = async () => {
    setLoading(true);
    try {
      const clubResults = await getClubResults(clubRecommender);
      navigation.navigate('ClubResults', {clubResults});
      setLoading(false);
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: t('club.desired_shot_shape.it_a_lot_of_data'),
        subText: t('club.desired_shot_shape.result_in_a_moment'),
      });
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHMd]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[
                appStyles.sm,
                appStyles.white,
                appStyles.textCenter,
                appStyles.mBMd,
              ]}
            >
              club.desired.what_is_your_desired_driver_shot_shape
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.shot_shape.supporting_copy.draw"
              textColor={getButtonActiveColor('draw', 'text')}
              borderColor={getButtonActiveColor('draw', 'border')}
              backgroundColor={getButtonActiveColor('draw', 'background')}
              onPress={() => setDesiredShotShape('draw')}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              style={[appStyles.mBSm]}
              text="club.small_draw"
              textColor={getButtonActiveColor('small_draw', 'text')}
              borderColor={getButtonActiveColor('small_draw', 'border')}
              backgroundColor={getButtonActiveColor('small_draw', 'background')}
              onPress={() => setDesiredShotShape('small_draw')}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={300}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.shot_shape.supporting_copy.straight"
              textColor={getButtonActiveColor('straight', 'text')}
              borderColor={getButtonActiveColor('straight', 'border')}
              backgroundColor={getButtonActiveColor('straight', 'background')}
              onPress={() => setDesiredShotShape('straight')}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              style={[appStyles.mBSm]}
              text="club.small_fade"
              textColor={getButtonActiveColor('small_fade', 'text')}
              borderColor={getButtonActiveColor('small_fade', 'border')}
              backgroundColor={getButtonActiveColor('small_fade', 'background')}
              onPress={() => setDesiredShotShape('small_fade')}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={500}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.avoid.supporting_copy.fade"
              textColor={getButtonActiveColor('fade', 'text')}
              borderColor={getButtonActiveColor('fade', 'border')}
              backgroundColor={getButtonActiveColor('fade', 'background')}
              onPress={() => setDesiredShotShape('fade')}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={600}>
          <Button
            text="club.get_results"
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            onPress={getResults}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubRecommender: state.clubRecommender.clubRecommender,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ClubDesiredShotShape);
