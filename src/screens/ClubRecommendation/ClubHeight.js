import React, {useState, useRef} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Selector from 'components/Selector';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {GREY} from 'config';
import {convertHeight} from 'utils/convert';

const ClubHeight = ({user, navigation, updateClubRecommender}) => {
  const [height, setHeight] = useState(user.golferProfile?.height || null);
  const sheetRef = useRef(null);

  const goNext = () => {
    updateClubRecommender({height: height?.toString()});
    navigation.navigate('ClubDriverData');
  };

  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                quiz.ht.headline
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={100}>
              <Button
                textColor={height ? 'white' : GREY}
                borderColor={GREY}
                onPress={() => sheetRef.current?.snapTo(0)}
                text={convertHeight('centimetersToText', height)}
                rightIcon="chevron-down"
              />
            </Animatable.View>
          </View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              text={'common.next'}
              backgroundColor="white"
              onPress={goNext}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="height"
        value={height}
        onChange={setHeight}
      />
    </>
  );
};

const mapStateToProps = state => ({
  user: state.user,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubHeight);
