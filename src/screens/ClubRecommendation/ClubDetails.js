import React from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Feather';
import {moderateScale} from 'react-native-size-matters';
import DeviceInfo from 'react-native-device-info';
import ProductImageCarousel from 'components/ProductImageCarousel';
import CustomShopSelector from 'components/CustomShopSelector';

import appStyles from 'styles/global';

const hasNotch = DeviceInfo.hasNotch();

const ClubDetails = ({navigation, route}) => {
  const clubConfig = route?.params?.clubConfig;
  const setShowToastError = route?.params?.setShowToastError;

  return (
    <SafeAreaView style={appStyles.flex} edges={['right', 'bottom', 'left']}>
      <View style={appStyles.flex}>
        <ProductImageCarousel
          images={[
            {
              link: clubConfig.headModelImageUrl,
            },
          ]}
          goBack={() => navigation.goBack()}
        />
        <CustomShopSelector
          setShowToastError={setShowToastError}
          isBottomSheet={false}
          clubConfig={clubConfig}
          model={clubConfig?.model}
          navigate={navigation.navigate}
          origin="clubRecommender"
        />
      </View>
      <TouchableOpacity
        style={styles.iconContainer}
        onPress={() => navigation.goBack()}
      >
        <View style={[styles.closeIcon, appStyles.vCenter, appStyles.hCenter]}>
          <Icon name="x" size={moderateScale(18)} />
        </View>
      </TouchableOpacity>
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  closeIcon: {
    backgroundColor: 'rgba(200, 200, 200, 0.8)',
    width: moderateScale(26),
    height: moderateScale(26),
    borderRadius: moderateScale(12),
  },
  iconContainer: {
    width: moderateScale(26),
    height: moderateScale(26),
    top: hasNotch ? 45 : 25,
    right: 15,
    position: 'absolute',
  },
});

export default ClubDetails;
