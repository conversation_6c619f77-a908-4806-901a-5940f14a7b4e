import React, {useState, useRef} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import Selector from 'components/Selector';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {GREY} from 'config';

const ClubHandicap = ({user, navigation, updateClubRecommender}) => {
  const [handicap, setHandicap] = useState(
    user?.golferProfile?.newHandicap?.tmCalculatedHandicap ||
      user?.golferProfile?.newHandicap?.userInputHandicap ||
      16,
  );
  const sheetRef = useRef(null);

  const goNext = () => {
    updateClubRecommender({handicap: handicap?.toString()});
    navigation.navigate('ClubHeight');
  };

  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                club.what_your_handicap
              </Text>
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={100}>
              <Button
                textColor={handicap ? 'white' : GREY}
                borderColor={GREY}
                onPress={() => sheetRef.current?.snapTo(0)}
                text={`${handicap < 0 ? `+${-handicap}` : handicap}`}
                rightIcon="chevron-down"
              />
            </Animatable.View>
          </View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              text="common.next"
              backgroundColor="white"
              onPress={goNext}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="handicap"
        value={handicap}
        onChange={setHandicap}
      />
    </>
  );
};

const mapStateToProps = state => ({
  user: state.user,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubHandicap);
