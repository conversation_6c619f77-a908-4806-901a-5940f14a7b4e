import React, {useEffect, useImperativeHandle, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import Text from 'components/Text';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import appStyles from 'styles/global';
import {FlatList} from 'react-native-gesture-handler';
import CustomImage from 'components/CustomImage/CustomImage';
import { GA_logEvent } from 'utils/googleAnalytics';
import { GA_EVENT_NAME, NOT_APPLICABLE, PAGE_CATEGORY, PAGE_NAME, SCREEN_TYPES } from 'utils/constant';

const CoachProfile = ({coachData, navigation}) => {
  const onItemPress = async item => {
    GA_logEvent(GA_EVENT_NAME.SORTING_CONTENT, {
      sort_by: 'coach',
      sort_value: item?.title,
      content_type: 'video',
      content_category: NOT_APPLICABLE,
      content_name: NOT_APPLICABLE,
      click_location: 'sort-drills-by-coach',
      page_type: SCREEN_TYPES.CLUBHOUSE,
      page_category: PAGE_CATEGORY.CLUBHOUSE_DRILL,
      page_name: PAGE_NAME.CLUBHOUSE_DRILL_INFO,
      screen_type: SCREEN_TYPES.CLUBHOUSE,
    });
    navigation.navigate('DrillsStack', {
      screen: 'CoachDetail',
      params: {
        item,
      },
    });
  };

  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity
        delayPressIn={100}
        delayPressOut={100}
        activeOpacity={0.8}
        onPress={() => onItemPress(item)}
        style={[styles.itemWrapper, appStyles.viewShadowLightBig]}
      >
        <CustomImage style={styles.itemImage} source={{uri: item?.imageLink}} />
        <View style={{marginHorizontal: 4}}>
          <Text style={styles.copyText} white size={16}>
            {item?.title}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return coachData?.length > 0 ? (
    <View style={styles.viewContainer}>
      <View style={styles.textCoach}>
        <Text size={16} white style={{marginBottom: 10, fontWeight: '400'}}>
          clubhouse.drills.coach
        </Text>
      </View>
      <FlatList
        data={coachData}
        horizontal
        keyExtractor={(item, index) => item?.id}
        renderItem={renderItem}
        style={{width: wp(100)}}
        contentContainerStyle={{paddingRight: 8}}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  viewContainer: {
    marginBottom: 0,
  },
  itemWrapper: {
    width: wp(43),
    marginLeft: 8,
    flexDirection: 'column',
    borderRadius: 16,
    backgroundColor: '#333333',
    marginBottom: 10,
    paddingHorizontal: 4,
    paddingTop: 4,
    paddingBottom: 5,
  },
  itemImage: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    height: wp(51),
    width: wp(41),
    marginBottom: 16,
  },
  copyText: {
    fontWeight: '700',
    marginBottom: 14,
    lineHeight: 18,
  },
  textCoach: {
    marginHorizontal: 16,
    marginTop: 24,
    marginBottom: 0,
  },
});

export default React.forwardRef(CoachProfile);
