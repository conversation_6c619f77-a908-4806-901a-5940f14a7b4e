import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import useAppState from 'hooks/useAppState';
import React, {useEffect, useRef, useState} from 'react';
import {View, ScrollView, Platform, RefreshControl} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';
import LoyaltyPointHeader from 'screens/Shop/components/ShopHeader';
import appStyles from 'styles/global';
import EntertainmentDrillsSection from './components/Entertainment&Drills/EntertainmentDrillsSection';
import JustIn from './components/JustIn/JustIn';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedScrollHandler,
} from 'react-native-reanimated';
import {updateCacheVersionData} from 'reducers/cacheVersion';
import {getModuleCacheData} from 'requests/moduleCache';
let paddingBottom = 20;
const ClubHouse = ({navigation, route}) => {
  const needScrollToEntertainment = route?.params?.needScrollToEntertainment;
  const articleFocusTab = route?.params?.articleFocusTab;
  const [loading, setLoading] = useState(false);
  const justInRef = useRef();
  const entertainmentRef = useRef();
  const insets = useSafeAreaInsets();
  const [entertainmentYPosition, setEntertainmentYPosition] = useState(0);
  const focusTab = useSelector(state => state.app?.focusTab);
  const isFocused = useIsFocused();
  const isInitialized = useRef(false);
  const dispatch = useDispatch();
  const user = useSelector(state => state.user);

  const scrollViewRef = useRef(null);
  useEffect(() => {
    //use Flag isInitialized.current to prevent re-rendering when trigger useFocusEffect
    if (!isInitialized.current) {
      setTimeout(() => {
        isInitialized.current = true;
      }, 500);
    }
  }, []);
  const callApiCheckVersion = async () => {
    try {
      setLoading(true);
      const rs = await getModuleCacheData(user.userCountry);
      dispatch(updateCacheVersionData(rs));
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const recallAPI = async (showLoading = false) => {
    showLoading && setLoading(true);
    callApiCheckVersion();
    try {
      const hasPadding = await justInRef?.current?.refreshData();
      if (hasPadding) {
        paddingBottom = 80;
      } else {
        paddingBottom = 20;
      }
      entertainmentRef?.current?.refreshData?.();
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const heightItem = useSharedValue(0);
  useFocusEffect(
    //prevent back behavior from this screen
    React.useCallback(() => {
      if (isInitialized.current) {
        recallAPI();
      }
      return () => {
        navigation.setParams({
          needScrollToEntertainment: null,
          articleFocusTab: null,
        });
      };
    }, []),
  );

  useEffect(() => {
    if (!loading && needScrollToEntertainment && entertainmentYPosition > 100) {
      setTimeout(() => {
        scrollViewRef?.current?.scrollTo?.({
          x: 0,
          y: entertainmentYPosition,
          animated: true,
        });
      }, 500);
    }
  }, [loading, entertainmentYPosition, needScrollToEntertainment]);

  useEffect(() => {
    entertainmentRef?.current?.setFocusTab?.(articleFocusTab);
  }, [articleFocusTab]);

  useAppState({
    onForeground: () => {
      recallAPI();
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      height:
        heightItem.value +
        (Platform.OS === 'ios' ? insets.top + paddingBottom : 26),
    };
  });
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      heightItem.value = event.contentOffset.y * -1;
    },
  });

  return (
    <View style={{flex: 1, backgroundColor: '#fff'}}>
      <LinearGradient
        start={{x: 1, y: 0.5}}
        end={{x: 1, y: 1}}
        colors={['rgba(61, 61, 61, 1)', 'rgba(0, 0, 0, 1)']}
        style={{
          flex: 1,
        }}
      >
        <Animated.View
          style={[
            {position: 'absolute', left: 0, right: 0, backgroundColor: '#fff'},
            animatedStyle,
          ]}
        />
        <FocusAwareStatusBar barStyle={'dark-content'} />
        <Animated.ScrollView
          style={[
            appStyles.flex,
            {
              width: '100%',
            },
          ]}
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={() => recallAPI(true)}
              tintColor={'black'}
              progressViewOffset={Platform.OS === 'ios' ? 30 : undefined}
            />
          }
          ref={scrollViewRef}
          scrollsToTop={isFocused}
        >
          <LoyaltyPointHeader
            colorPTS={'#ccc'}
            headerStyle={{
              marginTop: Platform.OS === 'ios' ? insets.top + 5 : 16,
              backgroundColor: '#fff',
              paddingBottom: 12,
              marginBottom: 0,
            }}
          />
          <JustIn ref={justInRef} />
          <EntertainmentDrillsSection
            ref={entertainmentRef}
            onLayout={y => {
              setEntertainmentYPosition(y);
            }}
          />
        </Animated.ScrollView>
      </LinearGradient>
    </View>
  );
};

export default ClubHouse;
