import React, {useEffect, useState} from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Platform,
  ScrollView,
  Image,
  TouchableOpacity,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {connect, useSelector} from 'react-redux';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';

import {getBrowseContent} from 'requests/drills';
import {updateCollectionContent} from 'reducers/drills';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import BackButton from 'assets/imgs/clubhouse/btn_back.png';
import LinearGradient from 'react-native-linear-gradient';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {drillPressed} from 'utils/article';
import ImageDrill from './components/ImageDrill';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

const DrillsByCategory = ({navigation, route, updateCollectionContent}) => {
  const videos = useSelector(state => state?.drills?.collectionContent);
  const permissions = useSelector(state => state?.app?.permissions);
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );

  const tags = route.params?.tags;
  const isTablet = DeviceInfo.isTablet();
  const [loading, setLoading] = useState();
  const insets = useSafeAreaInsets();

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const contentVideos = await getBrowseContent(tags, playService);
        if (contentVideos && contentVideos.length) {
          updateCollectionContent(
            contentVideos.map(content => {
              return {
                ...content,
                playable: permissions?.myTMSubscriptionLevel
                  ? true
                  : content.playable,
              };
            }),
          );
        }
        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('drills.browse.working_on_our_swing'),
          subText: t('drills.collections.videos_ready_soon'),
        });
      }
    })();
  }, []);
  const onPressDrill = async item => {
    drillPressed(
      navigation.navigate,
      item,
      'DrillsCollections',
      'drills-by-category',
      'drills:' + route?.params?.title?.toLowerCase?.(),
    );
  };
  const renderVideos = ({item, index}) => (
    <TouchableWithoutFeedback style={[]} onPress={() => onPressDrill(item)}>
      <View
        style={[
          appStyles.flex,
          appStyles.viewShadow,
          appStyles.row,
          styles.tileContainer,
        ]}
      >
        <ImageDrill drillData={item} style={styles.image} />
        <View style={[appStyles.flex, {paddingHorizontal: 8}]}>
          {item?.tags?.mytmInstructor?.[0]?.title && (
            <Text Din79Font size={12} weight={700} style={styles.coachText}>
              {item?.tags?.mytmInstructor?.[0]?.title}
            </Text>
          )}
          <Text
            size={16}
            weight={700}
            white
            style={{lineHeight: 18, marginVertical: 8}}
            numberOfLines={3}
          >
            {item.title}
          </Text>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );

  const renderHeader = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          marginRight: 16,
          marginLeft: 6,
          minHeight: 30,
          marginBottom: 24,
          alignItems: 'center',
          overflow: 'hidden',
        }}
      >
        <TouchableOpacity
          style={{marginRight: 10, paddingHorizontal: 10}}
          onPress={() => {
            navigation.goBack();
          }}
        >
          <Image source={BackButton} style={{width: 24, height: 24}} />
        </TouchableOpacity>
        <Text
          white
          Din79Font
          size={22}
          weight={800}
          style={{
            letterSpacing: 1.1,
            textTransform: 'uppercase',
            marginTop: -2,
            flex: 1,
          }}
          numberOfLines={2}
        >
          {route.params?.title?.toUpperCase?.()}
        </Text>
      </View>
    );
  };

  return (
    <View style={{flex: 1}}>
      <LinearGradient
        start={{x: 1, y: 0}}
        end={{x: 1, y: 1}}
        colors={['rgba(60, 60, 60, 1)', 'rgba(0, 0, 0, 1)']}
        style={{
          flex: 1,
          paddingTop: insets.top + (Platform.OS === 'ios' ? 0 : 10),
        }}
      >
        <FocusAwareStatusBar barStyle={'light-content'} />
        <ScrollView>
          {renderHeader()}
          {loading ? (
            <ActivityIndicator style={appStyles.pTLg} color="white" />
          ) : (
            <FlatList
              style={[
                appStyles.flex,
                {paddingHorizontal: 8, paddingBottom: 63.5},
              ]}
              data={videos}
              renderItem={renderVideos}
              keyExtractor={item => item.id}
            />
          )}
        </ScrollView>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  image: {
    width: wp(46),
    aspectRatio: 1.79,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    marginRight: 8,
  },
  coachText: {
    letterSpacing: 1.62,
    textTransform: 'uppercase',
    color: 'rgba(179, 179, 179, 1)',
  },
  tileContainer: {
    marginBottom: 8,
    padding: 4,
    borderRadius: 16,
    backgroundColor: 'rgba(51,51,51,1)',
    alignItems: 'center',
  },
});

const mapDispatchToProps = {updateCollectionContent};

export default connect(null, mapDispatchToProps)(DrillsByCategory);
