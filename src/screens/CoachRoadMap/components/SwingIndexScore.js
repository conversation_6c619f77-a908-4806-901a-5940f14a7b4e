import React from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import {useSelector} from 'react-redux';
import analytics from '@react-native-firebase/analytics';
import appStyles from '../../../styles/global';
import Text from '../../../components/Text';
import { SCREEN_CLASS, SCREEN_TYPES } from 'utils/constant';

const SwingIndexScore = ({navigate, roadMap}) => {
  const swingIndex = useSelector(state => state.plans.planSwingProfile);

  const GA_SwingIndexScoreClick = async () => {
    try {
      await analytics().logEvent('swing_index_score_breakdown', {
        roadmap_lessons: `${roadMap?.roadmapStats?.passedCount} of ${roadMap?.roadmapStats?.count}`, //e.g 3 of 3, or 2 of 4
        elements_passed: `${roadMap?.roadmapStats?.passedCount} of ${roadMap?.roadmapStats?.count}`, //e.g 3 of 3, or 2 of 4
        percent_completed: `${Math.round(
          (roadMap?.roadmapStats?.passedCount / roadMap?.roadmapStats?.count) *
            100,
        )}%`, //e.g 25%, 50%..100%
        swing_index_score: roadMap?.roadmapStats?.currentAvg, //e.g 8.95
        app_screen_name: 'coach - roadmap', //e.g home, my orders
        screen_type: SCREEN_CLASS.COACH, //e.g checkout, pdp, plp, account
        page_name: 'coach - roadmap', //e.g home, my orders
        page_type: SCREEN_TYPES.COACH, //e.g basket, home, order
        page_category: SCREEN_TYPES.COACH,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  return (
    <TouchableOpacity
      style={appStyles.mTMd}
      onPress={() => {
        GA_SwingIndexScoreClick();
        navigate('NativeCoachHorizontal', {screen: 'PlanSwingProfile'});
      }}
    >
      <View
        style={[
          appStyles.whiteBg,
          appStyles.pHSm,
          appStyles.pTSm,
          appStyles.borderRadius,
          appStyles.row,
          styles.container,
        ]}
      >
        <Text
          style={[appStyles.green, appStyles.xxxl, appStyles.alignEnd]}
          DINbold
        >
          {swingIndex?.currentAvg}
        </Text>
        <View style={styles.textContainer}>
          <Text
            DINbold
            style={[
              appStyles.mBSm,
              appStyles.uppercase,
              appStyles.grey,
              appStyles.alignEnd,
              {fontSize: 26, fontWeight: '400'},
            ]}
          >
            swing_index_score
          </Text>
          <Text style={[appStyles.grey, styles.breakDown]}>
            see_the_breakdown
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    shadowOffset: {width: 1, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
    shadowColor: '#000000',
    height: 91,
  },
  textContainer: {
    marginLeft: 20,
    marginTop: 4,
  },
  breakDown: {
    // color: '#8C8B8F',
    marginTop: 6,
    fontSize: 13,
    fontWeight: '600',
  },
});

export default SwingIndexScore;
