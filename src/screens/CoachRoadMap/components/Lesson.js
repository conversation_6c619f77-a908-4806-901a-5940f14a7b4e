import React, {useEffect, useImperativeHandle, useState} from 'react';
import {
  View,
  Pressable,
  TouchableWithoutFeedback,
  ImageBackground,
  StyleSheet,
  Image,
} from 'react-native';
import {moderateScale} from 'react-native-size-matters';
import Text from '../../../components/Text';
import stylesFont from '../../../components/Text/styles';

import Button from '../../../components/Button';
import PlayButton from '../../../../assets/imgs/icon-play-button.svg';
import appStyles from '../../../styles/global';
import {t} from 'i18next';
import Images from '../../../../assets/imgs/Images';
import AnnotationList from 'screens/PlanRelevantLessons/components/AnnotationList';
import {useSelector} from 'react-redux';
import PlanStore from 'screens/PlanRelevantLessons/store/PlanStore';
import {COACH_SWING_SHOT_TYPE} from 'json/describeShot';
import ProgressCircle from 'components/ProgressCircle';
import moment from 'moment';
import FastImage from 'react-native-fast-image/src';
import Svg, {Path} from 'react-native-svg';
import {
  GA_LessonResume,
  GA_LessonVideoRecommendedList,
  GA_Upload_New_Swing,
} from 'utils/constant';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {getCountryDateWithFormat} from 'utils/countries';

const Lesson = (
  {
    roadMapItem,
    swingSubElement,
    navigation,
    detailData,
    setShowUpcommingAlert,
    baselineVideoSummaries,
    dataInfotrust,
    fireDataSaved,
  },
  ref,
) => {
  const [expand, setExpand] = useState(roadMapItem?.currentStep ? true : false);
  const planCurrentStep = useSelector(state => state.plans.planCurrentStep);
  const planContentBatch = useSelector(state => state?.plans?.planContentBatch);
  // add lessonName field into dataInfotrust
  const lessonName = `${t('plan.road.map.lesson')} ${
    roadMapItem?.lessonTitleNumber
  } - ${swingSubElement?.name}`;
  dataInfotrust.lessonName = lessonName;

  useImperativeHandle(ref, () => ({
    expand: () => {
      setExpand(true);
    },
    collapse: () => {
      setExpand(false);
    },
  }));

  useEffect(() => {
    const baselineVideo = {
      ...baselineVideoSummaries[0],
      description:
        t('plan_tab.relevant_lessons.setup') + ' > ' + swingSubElement?.name,
      name: t('plan_tab.relevant_lessons.your_swing_review'),
    };
    const lessons = [baselineVideo].concat(
      detailData?.lessons?.sort((a, b) => a?.sequence - b?.sequence),
    );
    if (Object.keys(fireDataSaved).length > 0 && lessons?.length > 0) {
      if (lessonName === Object.keys(fireDataSaved)[0]) {
        lessons.map((video, index) => {
          let videoInformation = {
            lesson_type: dataInfotrust?.lessonType,
            lesson_name: lessonName,
            roadmap_step: dataInfotrust?.roadmapStep?.toUpperCase(),
            video_title: video?.name,
            video_url: video?.videoUrl,
          };
          // use set time out to prevent each event have many video's information.
          setTimeout(() => {
            GA_LessonVideoRecommendedList(videoInformation);
          }, 100 * index);
        });
      }
    }
  }, [fireDataSaved]);

  const GA_LessonResumedClick = async id => {
    try {
      let lessonResume = null;
      let lessonCompleted = await AsyncStorage.getItem('lessonCompleted');
      let listCompleted = JSON.parse(lessonCompleted) || [];
      let lessonStarted = await AsyncStorage.getItem('lessonStarted');
      let listStarted = JSON.parse(lessonStarted) || [];

      if (listCompleted.length > 0) {
        lessonResume = listCompleted.find(item => item.id === id);
        if (lessonResume !== undefined && !expand) {
          await GA_LessonResume(lessonResume, roadMapItem);
        }
      }
      if (listStarted.length > 0) {
        lessonResume = listStarted.find(item => item.id === id);
        if (lessonResume !== undefined && !expand) {
          await GA_LessonResume(lessonResume, roadMapItem);
        }
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const uriModal = useSelector(
    state => state?.plans?.planCoach.profileImageUrl,
  );
  const planContentStrings = useSelector(
    state => state?.plans?.planContentStrings,
  );

  const displayScore =
    roadMapItem?.passed || detailData?.returnedScore !== 0
      ? detailData?.returnedScore
      : detailData?.baselineScore;

  useEffect(() => {
    setExpand(roadMapItem?.currentStep ? true : false);
  }, [roadMapItem?.currentStep]);

  const getSwingSubElement = () => {
    // Find the swing element
    const element = planContentBatch?.swingElement?.find(element => {
      return element?.swingSubElements?.find(swingSubElement => {
        return swingSubElement?.id === detailData?.swingSubElementId;
      });
    });
    // Find the swing sub element
    const swingSubElement = element?.swingSubElements?.find(
      swingSubElement => swingSubElement?.id === detailData?.swingSubElementId,
    );
    return swingSubElement?.name;
  };

  const getSwingName = () => {
    let listName = {};
    // Find the swing element
    const element = planContentBatch?.swingElement?.find(element => {
      return element?.swingSubElements?.find(swingSubElement => {
        return swingSubElement?.id === detailData?.swingSubElementId;
      });
    });
    // Find the swing sub element
    const swingSubElement = element?.swingSubElements?.find(
      swingSubElement => swingSubElement?.id === detailData?.swingSubElementId,
    );
    listName.swingName = element?.name;
    listName.swingSubName = swingSubElement?.name;
    return listName;
  };

  const getSwingSubElementDescription = () => {
    // Get swing sub element name
    const swingSubElement = getSwingSubElement();
    if (displayScore > detailData?.failureThreshold) {
      let message = planContentStrings?.lesson_info_passed?.replace(
        '{{swingSubElement.name}}',
        swingSubElement,
      );
      message = message?.replace('{{returnedScore}}', displayScore);
      return message;
    } else {
      // Replace content string with correct values
      let message = planContentStrings?.lesson_info_initial?.replace(
        '{{swingSubElement.name}}',
        swingSubElement,
      );
      message = message?.replace('{{baselineScore}}', displayScore);
      message = message?.replace(
        '{{passingScore}}',
        detailData?.failureThreshold + 1,
      );

      return message;
    }
  };

  const returnSwing = () => {
    const swingSubElementId = planCurrentStep?.currentStep?.swingSubElementId;
    const videoTypeId = PlanStore.getShotTypeId(swingSubElementId);
    GA_Upload_New_Swing();
    navigation.navigate('NativeCoach', {
      screen: 'SelectVideoMenu',
      params: {
        swingSubElementId,
        swingShotTypeId: COACH_SWING_SHOT_TYPE.RETURNED,
        videoTypeId,
        planCurrentStep,
        swingName: getSwingName().swingName,
        swingSubName: getSwingName().swingSubName,
        lessonType: 'full lesson plan',
      },
    });
  };
  // setShowUpcommingAlert
  const renderVideoLessons = () => {
    const baselineVideo = {
      ...baselineVideoSummaries[0],
      description:
        t('plan_tab.relevant_lessons.setup') + ' > ' + swingSubElement?.name,
      name: t('plan_tab.relevant_lessons.your_swing_review'),
    };
    const lessons = [baselineVideo].concat(
      detailData?.lessons?.sort((a, b) => a?.sequence - b?.sequence),
    );
    return lessons?.map((lesson, i) => {
      let lessonTitle = `${t('plan.road.map.lesson')} ${
        roadMapItem?.lessonTitleNumber
      } - ${swingSubElement?.name}`;
      return (
        <View
          key={lesson?.name}
          style={[
            appStyles.flex,
            appStyles.row,
            appStyles.hCenter,
            i !== detailData?.lessons?.length ? appStyles.mBSm : {},
          ]}
        >
          <TouchableWithoutFeedback
            onPress={() => {
              if (!roadMapItem?.locked) {
                navigation.navigate('Video', {
                  video: {
                    id: lesson?.videoHash,
                    title: lesson?.name,
                    host: 'wistia',
                    lesson: true,
                    lessonName: lessonTitle,
                    roadmapStep: dataInfotrust?.roadmapStep?.toUpperCase(),
                    lessonType: dataInfotrust?.lessonType,
                    origin: 'PlanRoadMap',
                    isVideoRecommended: true,
                  },
                });
              } else {
                setShowUpcommingAlert(true);
              }
            }}
          >
            <View style={appStyles.mRXs}>
              <ImageBackground
                source={{uri: lesson?.thumbnailUrl}}
                imageStyle={appStyles.borderRadius}
                style={[styles.image, appStyles.hCenter, appStyles.vCenter]}
              >
                <PlayButton />
              </ImageBackground>
            </View>
          </TouchableWithoutFeedback>
          <View style={appStyles.flex}>
            <Text style={[stylesFont.font, {fontSize: 13}]}>
              {lesson?.name}
            </Text>
            <Text style={[appStyles.grey, {fontSize: 13}]}>
              {lesson?.description}
            </Text>
          </View>
        </View>
      );
    });
  };

  const renderSwingShotSent = () => {
    if (roadMapItem.currentStep && !detailData?.allowReturn) {
      // score != nil && failureThreshold != nil && swingSubElement != nil
      const date = new Date(roadMapItem?.videoSummary?.uploadedDate);
      const uploadDate = moment(date).format(
        getCountryDateWithFormat('M/D/YY'),
      );
      const title = t('your_swingshot_already_sent', {
        uploadedDate: uploadDate,
        swingElementName: getSwingSubElement(),
        interpolation: {escapeValue: false},
      });
      return (
        <View
          style={[appStyles.row, appStyles.pVSm, {backgroundColor: 'white'}]}
        >
          <View style={[appStyles.alignEnd]}>
            <FastImage
              source={{
                uri: uriModal,
                cache: FastImage.cacheControl.web,
              }}
              style={[
                appStyles.mBSm,
                {
                  width: moderateScale(50),
                  height: moderateScale(50),
                  borderRadius: 25,
                },
              ]}
            />
          </View>
          <View style={[styles.item, appStyles.mLXs]}>
            <View style={[styles.balloon]}>
              <Text
                style={[
                  appStyles.white,
                  {fontSize: 14, paddingVertical: 3, paddingHorizontal: 3},
                ]}
              >
                {title}
              </Text>
              <View style={[styles.arrowContainer, styles.arrowLeftContainer]}>
                <Svg
                  style={styles.arrowLeft}
                  width={moderateScale(15.5, 0.6)}
                  height={moderateScale(17.5, 0.6)}
                  viewBox="32.484 17.5 15.515 17.5"
                  enable-background="new 32.485 17.5 15.515 17.5"
                >
                  <Path
                    d="M38.484,17.5c0,8.75,1,13.5-6,17.5C51.484,35,52.484,17.5,38.484,17.5z"
                    fill={'#3E4D54'}
                    x="0"
                    y="0"
                  />
                </Svg>
              </View>
            </View>
          </View>
        </View>
      );
    }
    return null;
  };

  const renderRightIcon = () => {
    if (!roadMapItem?.locked) {
      if (roadMapItem?.passed) {
        return (
          <Image
            style={styles.complete}
            source={Images.lesson_complete}
            resizeMode="contain"
          />
        );
      } else {
        return (
          <Image
            style={styles.lock}
            source={Images.unlocked}
            resizeMode="contain"
          />
        );
      }
    } else {
      return (
        <Image
          style={styles.lock}
          source={Images.locked}
          resizeMode="contain"
        />
      );
    }
  };

  return (
    <View key={roadMapItem?.lessonTitleNumber} style={styles.lessonContainer}>
      <Pressable
        style={[appStyles.row, appStyles.hCenter, styles.titleContainer]}
        onPress={() => {
          GA_LessonResumedClick(roadMapItem?.lessonTitleNumber);
          setExpand(!expand);
        }}
      >
        {expand ? (
          <Image
            style={styles.icon}
            source={Images.minus}
            resizeMode="contain"
          />
        ) : (
          <Image
            style={styles.icon}
            source={Images.lesson_plus}
            resizeMode="contain"
          />
        )}
        <Text style={[appStyles.smm, {flex: 1, marginLeft: 16}]}>
          {t('plan.road.map.lesson')} {roadMapItem?.lessonTitleNumber} -{' '}
          {swingSubElement?.name}
        </Text>
        {renderRightIcon()}
      </Pressable>
      <View style={{}}>
        {expand ? (
          <View style={{paddingBottom: 20}}>
            <AnnotationList
              navigation={navigation}
              data={detailData}
              origin={'PlanRoadMap'}
              dataInfotrust={dataInfotrust}
            />
            {/*
            {detailData?.annotations?.length === 0 &&
            roadMapItem?.lessons?.length ? (
              <>{renderVideoLessons()}</>
            ) : null} */}

            <View
              style={[
                appStyles.whiteBg,
                appStyles.mTMd,
                appStyles.pVSm,
                appStyles.borderRadius,
              ]}
            >
              <Text
                DINbold
                style={[
                  appStyles.bold,
                  appStyles.mBXs,
                  appStyles.uppercase,
                  {fontSize: 26},
                ]}
                params={{XX: getSwingSubElement()}}
              >
                {`${t(
                  'plan.relevant_lessons.your_swing_sub_element_score.your',
                )} ${getSwingSubElement()} ${t(
                  'plan.relevant_lessons.your_swing_sub_element_score.score',
                )}`}
              </Text>
              <Text style={[appStyles.mBMd, stylesFont.font, {fontSize: 13}]}>
                {getSwingSubElementDescription()}
              </Text>

              <View style={[appStyles.hCenter, appStyles.mBMd]}>
                <ProgressCircle
                  progress={(displayScore / 10) * 100 || 0}
                  value={displayScore}
                  potential={10}
                  passingScore={detailData?.failureThreshold + 1}
                  hasBall={roadMapItem?.passed ? false : true}
                />
              </View>
            </View>

            <View style={appStyles.mTMd}>
              <Text
                DINbold
                style={[
                  appStyles.mBSm,
                  appStyles.uppercase,
                  {fontSize: 26, fontWeight: '500'},
                ]}
              >
                {getSwingSubElement()} {t('plan.relevant_lessons.drill_video')}
              </Text>
              <Text
                style={[appStyles.grey, {fontSize: 13, marginBottom: 14}]}
                params={{
                  XXX: getSwingSubElement(),
                  value2: getSwingSubElement(),
                }}
              >
                {`${t(
                  'plan.relevant_lessons.practice_swing_sub_element.title',
                )} ${getSwingSubElement()} ${t(
                  'plan.relevant_lessons.practice_swing_sub_element.description',
                )} ${getSwingSubElement()}`}
              </Text>

              {renderVideoLessons()}
            </View>
            {renderSwingShotSent()}
            {roadMapItem.currentStep && detailData?.allowReturn ? (
              <View style={[appStyles.mTMLg, appStyles.mBSm]}>
                <Button
                  text="return_swing_button_title"
                  textColor="white"
                  backgroundColor={'black'}
                  loadingMode="dark"
                  onPress={returnSwing}
                  centered
                  DINbold
                />
              </View>
            ) : null}
          </View>
        ) : null}
      </View>
    </View>
  );
};

export default React.forwardRef(Lesson);

const styles = StyleSheet.create({
  image: {
    height: moderateScale(80),
    width: moderateScale(145),
    borderRadius: 8,
  },
  lessonContainer: {
    marginTop: 10,
    backgroundColor: 'white',
    minHeight: 60,
    borderRadius: 8,
    shadowOffset: {width: 1, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
    shadowColor: '#000000',
    paddingHorizontal: 14,
  },
  titleContainer: {
    minHeight: 60,
    // backgroundColor: 'green'
  },
  textLesson: {
    fontWeight: '400',
    fontSize: 13,
  },
  icon: {
    width: 14,
    height: 14,
  },
  lock: {
    width: 22,
    height: 20,
  },
  complete: {
    width: 20,
    height: 20,
  },
  item: {
    // marginVertical: moderateScale(7, 2),
    flexDirection: 'row',
  },
  balloon: {
    maxWidth: moderateScale(230, 2),
    paddingHorizontal: moderateScale(10, 2),
    paddingTop: moderateScale(5, 2),
    paddingBottom: moderateScale(7, 2),
    borderRadius: 13,
  },
  arrowLeft: {
    left: moderateScale(-6, 0.5),
  },
  arrowContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
    flex: 1,
  },
  arrowLeftContainer: {
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    backgroundColor: '#3E4D54',
    borderRadius: 8,
  },
});
