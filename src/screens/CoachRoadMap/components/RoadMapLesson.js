import React, {useImperativeHandle, useMemo} from 'react';
import {View, StyleSheet} from 'react-native';

import Text from '../../../components/Text';

import appStyles from '../../../styles/global';
import {t} from 'i18next';
import stylesFont from '../../../components/Text/styles';
import Lesson from './Lesson';

const RoadMapLesson = (
  {
    roadmapSwingElement,
    element,
    valuesPieChart,
    lessonData,
    navigation,
    setShowUpcommingAlert,
    baselineVideoSummaries,
    dataInfotrust,
    layoutY,
    triggerData,
    setTriggerData,
    fireDataSaved,
  },
  ref,
) => {
  const percentSetup = valuesPieChart?.find(x => x.label === element?.name);
  const description = `${percentSetup?.value}% ${t(
    'plan.roadmap.description.percent',
  )} ${element?.name}.`;

  const lessonRefs = useMemo(() => {
    const refs = [];
    roadmapSwingElement?.roadmapItems.forEach((item, index) => {
      refs.push(React.createRef(null));
    });
    return refs;
  }, [roadmapSwingElement?.roadmapItems]);

  useImperativeHandle(ref, () => ({
    expand: () => {
      try {
        lessonRefs.forEach(function (key, index) {
          lessonRefs[index].current?.expand?.();
        });
      } catch (error) {
        console.log(error);
      }
    },
    collapse: () => {
      try {
        lessonRefs.forEach(function (key, index) {
          lessonRefs[index].current?.collapse?.();
        });
      } catch (error) {
        console.log(error);
      }
    },
  }));

  return (
    <View key={element?.name}>
      <View style={[appStyles.mTMd, appStyles.mBSm]}>
        <Text
          DINbold
          style={[
            appStyles.bold,
            appStyles.mBXxs,
            appStyles.uppercase,
            {fontSize: 28},
            appStyles.mLSm,
          ]}
        >
          {`${t('FIXING_YOUR')} ${element?.name}`}
        </Text>
        <Text
          style={[
            appStyles.grey,
            appStyles.xs,
            stylesFont.font,
            styles.textYourRoadmapStats,
            appStyles.mLSm,
          ]}
        >
          {description}
        </Text>
      </View>

      {roadmapSwingElement?.roadmapItems?.map((roadMapItem, index) => {
        const swingSubElement = element?.swingSubElements?.find(
          swingSubElement =>
            swingSubElement?.id === roadMapItem?.swingSubElementId,
        );
        const detailData = lessonData?.find(
          item => item.swingSubElementId === roadMapItem.swingSubElementId,
        );
        const isUnlockedLesson = !roadMapItem.locked && !roadMapItem.passed;
        dataInfotrust.roadmapStep = `${t('FIXING_YOUR')} ${element?.name}`;
        return (
          <View
            onLayout={event => {
              let lessonTitle = `${t('plan.road.map.lesson')} ${
                roadMapItem?.lessonTitleNumber
              } - ${swingSubElement?.name}`;

              if (event?.nativeEvent?.layout?.height > 80) {
                const cloneData = {...triggerData};
                cloneData[lessonTitle] =
                  event?.nativeEvent?.layout?.y +
                  event?.nativeEvent?.layout?.height +
                  layoutY -
                  400;
                setTriggerData(cloneData);
              }
            }}
          >
            <Lesson
              key={index}
              roadMapItem={roadMapItem}
              swingSubElement={swingSubElement}
              detailData={detailData}
              baselineVideoSummaries={baselineVideoSummaries}
              navigation={navigation}
              elementName={element?.name}
              setShowUpcommingAlert={setShowUpcommingAlert}
              ref={isUnlockedLesson ? lessonRefs[index] : null}
              dataInfotrust={dataInfotrust}
              fireDataSaved={fireDataSaved}
            />
          </View>
        );
      })}
    </View>
  );
};
export default React.forwardRef(RoadMapLesson);

const styles = StyleSheet.create({
  textYourRoadmapStats: {
    fontWeight: '400',
  },
});
