import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  ImageBackground,
  TouchableOpacity,
  FlatList,
  Platform,
} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {connect, useDispatch, useSelector} from 'react-redux';
import analytics from '@react-native-firebase/analytics';

import Text from 'components/Text';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import LoadingOverlay from 'components/LoadingOverlay';

import appStyles from 'styles/global';
import {getConfig} from 'config/env';
import {getAuth0AccessToken} from 'utils/user';
import {refreshConfigureFeatures} from 'utils/configureFeatures';
import {t} from 'i18next';
import LinearGradient from 'react-native-linear-gradient';
import Images from 'assets/imgs/Images';
import {FittingEvents, Recommender, VirtualFittings} from 'json/fittingDisplay';
import {checkMainCountry} from 'utils/constant';

const FittingOption = ({navigation: {navigate}, user}) => {
  const dispatch = useDispatch();
  const [loadingWebView, setLoadingWebView] = useState(false);
  const [displayData, setDisplayData] = useState([]);
  const launchFeaturesSelector = useSelector(
    state => state.app?.launchFeatures,
  );
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);

  useEffect(() => {
    refreshConfigureFeatures(dispatch, user, showHideFeatures);
  }, []);

  useEffect(() => {
    const data = [];

    if (showHideFeatures?.data?.FITTINGEVENTS) {
      data.push(FittingEvents);
    }
    if (showHideFeatures?.data?.VIRTUALFITTINGS) {
      data.push(VirtualFittings);
    }
    if (
      showHideFeatures?.data?.CLUBREC &&
      checkMainCountry(user?.userCountry)
    ) {
      data.push(Recommender);
    }
    setDisplayData(data);
  }, [showHideFeatures]);

  const renderCards = ({item}) => {
    const navigateToFittingOption = async () => {
      if (item.id !== 3) {
        navigateToWebView();
      } else {
        navigate('ClubRecommender', {screen: 'ClubWelcome'});
      }

      await analytics().logEvent('fittings_cta_open', {
        name: item.title,
      });
      await analytics().logEvent('cta_open', {
        name: item.title,
      });
    };

    const navigateToWebView = async () => {
      setLoadingWebView(true);
      const accessToken = await getAuth0AccessToken(dispatch);
      const MFE_URL = await getConfig('MFE_URL');
      const uri =
        item.id === 1
          ? `${MFE_URL}/events/find?accessToken=${accessToken}&iframe=true`
          : `https://app.acuityscheduling.com/schedule.php?owner=20239807&email=${user.email}&firstName=${user.firstName}&lastName=${user.lastName}&iframe=true`;

      navigate('WebView', {
        screen: 'WebView',
        params: {
          title: item.screenTitle,
          uri,
          canGoBack: true,
          origin: item.id === 1 ? 'FittingOptions' : 'BookNow',
        },
      });
      setLoadingWebView(false);
    };

    return (
      <View style={{marginBottom: 10}}>
        {loadingWebView ? (
          <LoadingOverlay transparent={loadingWebView} />
        ) : null}
        <View>
          <ImageBackground
            source={Images[item?.image]}
            style={[appStyles.spaceBetween, {aspectRatio: 3 / 4}]}
          >
            <LinearGradient
              colors={['rgba(17, 17, 17, 0) 100%)', 'rgba(17, 17, 17, 0.5)']}
              style={[
                {
                  width: wp('100%'),
                  height: wp('100%') * 1.33 - (wp('100%') - 40),
                  bottom: 0,
                  position: 'absolute',
                },
              ]}
            />
            <View style={[styles.cardDescriptions]}>
              <View style={[styles.cardBottomDescription]}>
                <Text style={[appStyles.white, {fontSize: 40}]} DINbold>
                  {(t(`${item.title}`) + '').toUpperCase()}
                </Text>
                <Text
                  style={[
                    appStyles.white,
                    {
                      fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
                      fontSize: 13,
                      marginVertical: 10,
                    },
                  ]}
                >
                  {item.description}
                </Text>
                <TouchableOpacity
                  style={[
                    styles.button,
                    {
                      opacity:
                        item.id === 3 && !launchFeaturesSelector?.CLUBREC
                          ? 0.5
                          : 1,
                    },
                  ]}
                  onPress={navigateToFittingOption}
                  disabled={item.id === 3 && !launchFeaturesSelector?.CLUBREC}
                >
                  <Text
                    style={[
                      {
                        fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
                        color: '#111111',
                      },
                      appStyles.xs,
                    ]}
                  >
                    {item.buttonText}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </ImageBackground>
        </View>
      </View>
    );
  };

  return (
    <>
      <FocusAwareStatusBar barStyle={'dark-content'} />
      <View>
        <FlatList
          contentContainerStyle={{
            backgroundColor: 'black',
          }}
          data={displayData}
          renderItem={renderCards}
          keyExtractor={item => item.id}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  cardDescriptions: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    paddingLeft: 25,
  },
  cardBottomDescription: {
    display: 'flex',
    justifyContent: 'center',
    alignContent: 'center',
    paddingVertical: wp('8.4%'),
  },
  button: {
    textAlign: 'center',
    paddingHorizontal: 25,
    paddingVertical: 10,
    backgroundColor: '#ffffff',
    width: 'auto',
    alignSelf: 'flex-start',
    borderRadius: 100,
    overflow: 'hidden',
  },
});

const mapStateToProps = state => ({
  user: state.user,
});

export default connect(mapStateToProps, null)(FittingOption);
