import React from 'react';
import {ScrollView, View, TouchableOpacity} from 'react-native';
import {hasNotch, isTablet} from 'react-native-device-info';
import {SafeAreaView} from 'react-native-safe-area-context';
import {moderateScale} from 'react-native-size-matters';
import FormItem from 'components/FormItem';
import {StyleSheet} from 'react-native';

import appStyles from 'styles/global';
import Icon from 'react-native-vector-icons/Feather';
import FastImage from 'react-native-fast-image/src';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {t} from 'i18next';

const FittingClubSpecsTab = ({navigation, route}) => {
  const clubData = route?.params?.data?.clubConfig;
  const clubImage = route?.params?.clubImage;

  const getShaftImage = () => {
    return clubData?.shaftModelImageUrl
      ? {
          uri: clubData?.shaftModelImageUrl,
          cache: FastImage.cacheControl.web,
        }
      : require('assets/imgs/silhouettes-shaft.jpg');
  };
  const getGripImage = () => {
    return clubData?.gripModelImageUrl
      ? {
          uri: clubData?.gripModelImageUrl,
          cache: FastImage.cacheControl.web,
        }
      : require('assets/imgs/silhouettes-grip.jpg');
  };
  return (
    <SafeAreaView edges={['right', 'bottom', 'left']}>
      <TouchableOpacity
        style={styles.iconContainer}
        onPress={() => navigation.goBack()}
      >
        <View style={[styles.closeIcon, appStyles.vCenter, appStyles.hCenter]}>
          <Icon name="x" size={moderateScale(18)} />
        </View>
      </TouchableOpacity>
      <ScrollView>
        <FastImage
          style={[
            appStyles.whiteBg,
            appStyles.viewShadow,
            {
              borderRadius: isTablet ? wp('2%') : wp('3%'),
              height: wp(100),
              width: wp(100),
              alignSelf: 'center',
            },
          ]}
          source={{
            uri: clubImage?.[0]?.link,
            cache: FastImage.cacheControl.web,
          }}
          resizeMode="contain"
        />
        <View>
          <FormItem
            label={t('shop.cart.supporting_copy.hand')}
            value={clubData.handCode}
            bottomBorder
            topBorder
          />
          <FormItem
            label={t('shop.cart.supporting_copy.loft')}
            value={
              clubData.headLoft ||
              clubData.headWedgeLoft ||
              clubData.headIronIndividualLofts ||
              clubData.headFairwayIndividualLofts ||
              'N/A'
            }
            bottomBorder
          />
          <FormItem
            label={t('fitting.club.specs.tab.sleeve_adjustment')}
            value={clubData.headSleeveAdj ? clubData.headSleeveAdj : 'N/A'}
            bottomBorder
          />
          <FormItem
            label={t('fitting.club.specs.tab.front_track')}
            value={
              clubData.headFrontTrack ? clubData.headFrontTrack : 'Not Used'
            }
            bottomBorder
          />
          <FormItem
            label={t('fitting.club.specs.tab.back_track')}
            value={clubData.headBackTrack ? clubData.headBackTrack : 'Not Used'}
            bottomBorder
          />
        </View>
        <View style={[appStyles.mTMd]}>
          <View
            style={[appStyles.hr, appStyles.pVXs, {backgroundColor: 'white'}]}
          >
            <FastImage
              style={{
                width: wp(100),
                height: moderateScale(20),
                borderRadius: isTablet ? wp('2%') : wp('3%'),
              }}
              source={getShaftImage()}
              resizeMode="cover"
            />
          </View>
          <FormItem
            label={t('fitting.club.specs.tab.vendor')}
            value={clubData.shaftVendorName ? clubData.shaftVendorName : 'N/A'}
            bottomBorder
          />
          <FormItem
            label={t('fitting.club.specs.tab.model')}
            value={clubData.shaftModelName ? clubData.shaftModelName : 'N/A'}
            bottomBorder
          />
          <FormItem
            label={t('shop.cart.supporting_copy.flex')}
            value={clubData.shaftFlex ? clubData.shaftFlex : 'N/A'}
            bottomBorder
          />
          <FormItem
            label={t('custom.shop.selector.tipping_adjustment')}
            value={clubData.shaftTippingAdj ? clubData.shaftTippingAdj : 'N/A'}
            bottomBorder
          />
          <FormItem
            label={t('fitting.club.specs.tab.length_adjustment')}
            value={clubData.shaftLength ? clubData.shaftLength : 'N/A'}
            bottomBorder
          />
          <FormItem
            label={t('fitting.club.specs.tab.material')}
            value={clubData.shaftMaterial ? clubData.shaftMaterial : 'N/A'}
            bottomBorder
          />
        </View>
        <View style={[appStyles.mTMd]}>
          <View
            style={[appStyles.hr, appStyles.pVXs, {backgroundColor: 'white'}]}
          >
            <FastImage
              style={{
                width: wp(100),
                height: moderateScale(20),
                borderRadius: isTablet ? wp('2%') : wp('3%'),
              }}
              source={getGripImage()}
              resizeMode="cover"
            />
          </View>
          <FormItem
            label={t('fitting.club.specs.tab.vendor')}
            value={clubData.gripVendor ? clubData.gripVendor : 'N/A'}
            bottomBorder
          />
          <FormItem
            label={t('fitting.club.specs.tab.model')}
            value={clubData.gripModel ? clubData.gripModel : 'N/A'}
            bottomBorder
          />
          <FormItem
            label={t('fitting.club.specs.tab.wraps')}
            value={clubData.gripWraps ? clubData.gripWraps : 'N/A'}
            bottomBorder
          />
          <FormItem
            label={t('fitting.club.specs.tab.logo')}
            value={clubData.gripLogo ? clubData.gripLogo : 'N/A'}
            bottomBorder
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    position: 'absolute',
    width: moderateScale(26),
    height: moderateScale(26),
    top: hasNotch ? 50 : 25,
    right: 15,
    zIndex: 1,
  },
  closeIcon: {
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(22),
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
});

export default FittingClubSpecsTab;
