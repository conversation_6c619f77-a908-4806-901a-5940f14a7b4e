import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';

import Text from 'components/Text';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';

import {getShotData} from 'requests/fittings';

import {showToast} from 'utils/toast';

import {covertShotDataKeyValue} from 'utils/convert';

import appStyles from 'styles/global';
import {t} from 'i18next';

const FittingsShotDataTab = ({navigation, data}) => {
  const isTablet = DeviceInfo.isTablet();
  const [shotIndex, setShotIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [shotData, setShotData] = useState(false);
  const [sortedShotData, setSortedShotData] = useState([]);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const fittingShotData = await getShotData(
          data.clubConfig.originalClubConfigId,
        );
        setShotData(fittingShotData);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('An_error_occurred_retrieving_shot_data'),
        });
      }
    })();
  }, [data]);

  useEffect(() => {
    const compare = (x, y) => {
      return x.average === y.average ? 0 : x.average ? -1 : 1;
    };

    if (shotData) {
      const sortedData = shotData.sort(compare);
      setSortedShotData(sortedData);
    }
  }, [shotData]);

  return (
    <SafeAreaView
      // style={status ? appStyles.flex : {display: 'none'}}
      edges={['right', 'bottom', 'left']}
    >
      <FocusAwareStatusBar barStyle={'dark-content'} />
      {loading ? (
        <ActivityIndicator style={appStyles.pTLg} />
      ) : (
        <>
          <View style={[styles.shotsContainer]}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{paddingHorizontal: '3%'}}
            >
              {sortedShotData.length > 0 &&
                sortedShotData.map((item, index) => {
                  return (
                    <TouchableOpacity
                      key={`shotButton-${index}`}
                      onPress={() => setShotIndex(index)}
                    >
                      <View
                        style={
                          shotIndex === index
                            ? styles.shotButtonActive
                            : styles.shotButton
                        }
                      >
                        <Text
                          style={
                            shotIndex === index
                              ? styles.shotButtonTextActive
                              : styles.shotButtonText
                          }
                        >
                          {index === 0
                            ? t('home.target.supporting_copy.average')
                            : `Shot ${item.number}`}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  );
                })}
            </ScrollView>
          </View>
          {sortedShotData.length > 0 &&
            sortedShotData.map((item, index) => {
              return (
                <View
                  key={`shot-${index}`}
                  style={[
                    styles.dataContainer,
                    shotIndex === index ? {display: 'flex'} : {display: 'none'},
                  ]}
                >
                  {Object.keys(item).map((keyName, value) => {
                    if (covertShotDataKeyValue(keyName)) {
                      return (
                        <View
                          key={keyName}
                          style={[
                            styles.data,
                            {borderRadius: isTablet ? wp('1%') : wp('2%')},
                          ]}
                        >
                          <Text
                            style={[
                              appStyles.white,
                              appStyles.alignCenter,
                              appStyles.xs,
                              appStyles.pBSm,
                            ]}
                          >
                            {covertShotDataKeyValue(keyName)}
                          </Text>
                          <Text
                            style={[
                              appStyles.white,
                              appStyles.alignCenter,
                              appStyles.xl,
                              {paddingTop: Platform.OS === 'ios' ? 5 : 0},
                            ]}
                            DINbold
                          >
                            {/* {item[keyName]} */}
                            {Math.round(item[keyName] * 100) / 100}
                          </Text>
                          <Text
                            style={[
                              appStyles.grey,
                              appStyles.alignCenter,
                              appStyles.xs,
                            ]}
                          >
                            {covertShotDataKeyValue(keyName).split(' ').pop()}
                          </Text>
                        </View>
                      );
                    } else {
                      return;
                    }
                  })}
                </View>
              );
            })}
        </>
      )}
    </SafeAreaView>
  );
};

export default FittingsShotDataTab;

const styles = StyleSheet.create({
  shotsContainer: {
    paddingTop: '5%',
  },
  shotButtonActive: {
    backgroundColor: '#00BE4B',
    paddingHorizontal: wp('5%'),
    paddingVertical: hp('1.2%'),
    borderRadius: 22,
    marginRight: hp('1%'),
  },
  shotButton: {
    backgroundColor: 'white',
    paddingHorizontal: wp('5%'),
    paddingVertical: hp('1.2%'),
    borderRadius: 22,
    marginRight: hp('1%'),
  },
  shotButtonTextActive: {
    textAlign: 'center',
    color: 'white',
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
  },
  shotButtonText: {
    textAlign: 'center',
    color: 'rgba(17, 17, 17, 1)',
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
  },
  dataContainer: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginTop: '5%',
    paddingHorizontal: '3%',
    justifyContent: 'space-between',
  },
  data: {
    backgroundColor: 'black',
    width: '48.5%',
    alignContent: 'center',
    height: hp('19.5%'),
    paddingVertical: hp(3.5),
    marginBottom: '3%',
    justifyContent: 'space-between',
  },
});
