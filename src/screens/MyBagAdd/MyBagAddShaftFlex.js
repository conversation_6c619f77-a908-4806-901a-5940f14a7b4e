import React, {useEffect, useState} from 'react';
import {
  View,
  StatusBar,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';

import appStyles from 'styles/global';
import Text from 'components/Text';
import {getAllClubShaftFlex} from 'requests/accounts';
import AntDesign from 'react-native-vector-icons/AntDesign';

const MyBagAddShaftFlex = ({navigation, route}) => {
  const dataClub = route.params.dataClub;
  const [dataShaftFlex, setDataShaftFlex] = useState();
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    getDataShaftFlex();
  }, []);
  const getDataShaftFlex = async () => {
    setLoading(true);
    try {
      const data = await getAllClubShaftFlex();
      setDataShaftFlex(data);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const nextScreen = item => {
    navigation.push('MyBagAddSpecs', {
      dataClub: {...dataClub, flex: item},
    });
  };
  const getCharShaft = value => {
    switch (value.toLocaleLowerCase()) {
      case 'x-stiff':
        return 'X';
      case 'stiff':
        return 'S';
      case 'regular':
        return 'R';
      case 'japan':
        return 'J';
      case 'womens':
      case 'ladies':
        return 'L';
      case 'senior':
        return 'A';
      default:
        return '';
    }
  };
  const changeWomensToLadies = value => {
    if (value.toLocaleLowerCase() === 'womens') {
      return 'Ladies';
    }
    return value;
  };
  const renderItem = ({item, index}) => {
    return (
      <View style={{borderBottomWidth: 1, borderBottomColor: '#C4C4C4'}}>
        <TouchableOpacity
          style={[
            appStyles.row,
            appStyles.spaceBetween,
            {padding: 15, alignItems: 'center'},
          ]}
          onPress={() => nextScreen(item)}
        >
          <Text black size={17}>
            <Text style={{fontWeight: 'bold'}}>
              {getCharShaft(item?.value)}
            </Text>
            {getCharShaft(item?.value) ? ' - ' : ''}
            {changeWomensToLadies(item?.value)}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };
  return (
    <View style={appStyles.flex}>
      <StatusBar barStyle="dark-content" />
      {loading ? (
        <ActivityIndicator style={appStyles.pTLg} color="black" />
      ) : (
        <View style={[appStyles.flex]}>
          {dataShaftFlex?.length > 0 && (
            <FlatList
              data={dataShaftFlex}
              renderItem={renderItem}
              initialNumToRender={50}
              keyExtractor={item => item.id}
              alwaysBounceVertical={false}
            />
          )}
        </View>
      )}
    </View>
  );
};

export default MyBagAddShaftFlex;
