/* eslint-disable react-hooks/exhaustive-deps */
import React, {useState, useEffect, useRef, useMemo} from 'react';
import {View, FlatList, StatusBar, ActivityIndicator} from 'react-native';

import {getAllClubBrands} from 'requests/accounts';
import {showToast} from 'utils/toast';

import appStyles from 'styles/global';
import {t} from 'i18next';
import ItemClub from './component/ItemClub';
import SearchView from './component/SearchView';

const MyBagAddBrand = ({navigation, route}) => {
  const clubCategory = route.params.clubCategory;

  const [allClubBrands, setAllClubBrands] = useState([]);
  const [loading, setLoading] = useState(false);
  const brandOrigin = useRef([]);
  const refFlatlist = useRef();
  useEffect(() => {
    callAllClubBrands();
  }, []);

  const nextScreen = item => {
    navigation.push('MyBagAddModel', {
      dataClub: {...clubCategory, brand: item},
    });
  };
  const callAllClubBrands = async () => {
    setLoading(true);
    try {
      // Make request to pull clubs in category
      const brands = await getAllClubBrands(clubCategory.id);
      if (brands) {
        let modifiedBrands = [...brands];
        modifiedBrands = modifiedBrands.filter(item => item.name !== 'Other');
        modifiedBrands.push({
          id: null,
          clubCategoryId: '',
          name: 'Other',
        });
        setAllClubBrands(modifiedBrands);
        brandOrigin.current = modifiedBrands;
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: t('mybagadd.bag_room_needs_organizing'),
        subText: t('mybag.add_brand.full_list_of_club_brands_shortly'),
      });
    } finally {
      setLoading(false);
    }
  };

  const onChangeText = value => {
    const data = brandOrigin.current.filter(
      _item =>
        _item?.name?.toLocaleLowerCase().indexOf(value?.toLocaleLowerCase()) >
        -1,
    );
    setAllClubBrands(data);
  };

  const searchBrandBar = useMemo(
    () => (
      <View
        style={{
          paddingTop: '6%',
          paddingBottom: '4%',
          borderBottomWidth: 1,
          borderBottomColor: '#C4C4C4',
        }}
      >
        <SearchView
          placeholder={t('my_bag.add_club.find_brand')}
          onChangeText={onChangeText}
        />
      </View>
    ),
    [allClubBrands],
  );

  const renderItem = ({item, index}) => {
    return <ItemClub item={item} index={index} onPress={nextScreen} />;
  };

  return (
    <View style={appStyles.flex}>
      <StatusBar barStyle="dark-content" />
      {loading ? (
        <ActivityIndicator style={appStyles.pTLg} color="black" />
      ) : (
        <View style={[appStyles.flex]}>
          <FlatList
            ref={refFlatlist}
            contentContainerStyle={{
              paddingBottom: 40,
            }}
            data={allClubBrands}
            renderItem={renderItem}
            initialNumToRender={30}
            keyExtractor={item => item.id}
            ListHeaderComponent={searchBrandBar}
          />
        </View>
      )}
    </View>
  );
};

export default MyBagAddBrand;
