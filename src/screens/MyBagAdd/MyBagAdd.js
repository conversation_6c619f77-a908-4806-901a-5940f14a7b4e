/* eslint-disable react-hooks/exhaustive-deps */
import React, {useState, useEffect, useRef} from 'react';
import {View, StatusBar, FlatList, ActivityIndicator} from 'react-native';
import {getAllClubsWithCategoryType} from 'requests/accounts';
import {showToast} from 'utils/toast';

import appStyles from 'styles/global';
import {BLUE_LIGHT} from 'config';
import {useSelector} from 'react-redux';
import BackButton from 'components/BackButton';
import {t} from 'i18next';
import ItemClub from './component/ItemClub';

const MyBagAdd = ({navigation, route}) => {
  const clubCategories = useSelector(state => state?.myBag?.clubCategories);
  const [allClubs, setAllClubs] = useState([]);
  const [listCategoryType, setListCategoryType] = useState([]);
  const [loading, setLoading] = useState(false);
  const refFlatList = useRef(null);

  useEffect(() => {
    getData();
  }, []);

  const goBack = () => {
    navigation.goBack();
  };

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => <BackButton color="black" onPress={goBack} />,
    });

    loadData();
  }, [listCategoryType]);

  const getData = async () => {
    setLoading(true);
    try {
      const categoryTypes = await getAllClubsWithCategoryType();
      setListCategoryType(categoryTypes);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: t('mybagadd.bag_room_needs_organizing'),
        subText: t('mybagadd.we_could_not_locate_your_clubs'),
      });
    }
  };
  const loadData = () => {
    const driver = clubCategories.filter(val => val.name === 'Driver');
    const fairway = clubCategories.filter(val => val.name === 'Fairway');
    const hybrid = clubCategories
      .filter(val => val.name === 'Rescue')
      .map(_val => ({..._val, name: 'Hybrid'}));
    const irons = clubCategories.filter(val => val.name === 'Irons');
    const wedge = clubCategories.filter(val => val.name === 'Wedge');
    const putter = clubCategories.filter(val => val.name === 'Putter');
    const ball = clubCategories.filter(val => val.name === 'Ball');

    setAllClubs([
      ...driver,
      ...fairway,
      ...hybrid,
      ...irons,
      ...wedge,
      ...putter,
      ...ball,
    ]);
  };

  const onPress = item => {
    navigation.push('MyBagAddBrand', {clubCategory: item});
  };
  const renderItem = ({item, index}) => {
    return <ItemClub item={item} index={index} onPress={onPress} />;
  };
  return (
    <View style={appStyles.flex}>
      <StatusBar barStyle="dark-content" />
      {loading ? (
        <ActivityIndicator style={appStyles.pTLg} color="black" />
      ) : (
        <View style={[appStyles.flex, appStyles.whiteBg]}>
          <FlatList
            ref={refFlatList}
            data={allClubs}
            renderItem={renderItem}
            keyExtractor={item => item?.id}
            style={[{backgroundColor: BLUE_LIGHT, paddingmargiTop: '10%'}]}
            alwaysBounceVertical={false}
          />
        </View>
      )}
    </View>
  );
};

export default MyBagAdd;
