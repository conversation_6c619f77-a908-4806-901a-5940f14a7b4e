import React from 'react';
import {View, TouchableOpacity} from 'react-native';

import appStyles from 'styles/global';
import Text from 'components/Text';
import AntDesign from 'react-native-vector-icons/AntDesign';

const ItemClub = ({item, index, onPress}) => {
  return (
    <View style={{borderBottomWidth: 1, borderBottomColor: '#C4C4C4'}}>
      <TouchableOpacity
        style={[
          appStyles.row,
          appStyles.spaceBetween,
          {padding: 15, alignItems: 'center'},
        ]}
        onPress={() => onPress(item)}
      >
        <Text black size={17}>
          {item?.name || item?.value}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default ItemClub;
