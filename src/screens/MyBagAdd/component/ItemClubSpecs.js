import React from 'react';
import {View, TouchableOpacity, Image} from 'react-native';

import appStyles from 'styles/global';
import Text from 'components/Text';
import AntDesign from 'react-native-vector-icons/AntDesign';
import logoTaylor from 'assets/imgs/WITBTMLogo.png';
import {t} from 'i18next';

const ItemClubSpecs = ({title, item, onPress, dataClub}) => {
  const checkDisableTouch = () => {
    switch (title) {
      case t('my_bag.add_club.club_type'):
      case t('my_bag.add_brand.hedline'):
        return true;
      case t('my_bag.add_model.headline'):
        if (dataClub?.name === 'Ball') {
          return false;
        }
        return true;
      default:
        return false;
    }
  };
  return (
    <View
      style={{
        borderBottomWidth: 1,
        borderBottomColor: '#C4C4C4',
        backgroundColor: '#ffffff',
      }}
    >
      <TouchableOpacity
        style={[
          appStyles.row,
          appStyles.spaceBetween,
          {padding: 15, alignItems: 'center'},
        ]}
        disabled={checkDisableTouch()}
        onPress={() => onPress && onPress(item)}
      >
        <Text black size={17}>
          {title}
        </Text>
        <View style={[appStyles.row, {alignItems: 'center'}]}>
          {item?.name === 'TaylorMade' ? (
            <Image
              style={{
                width: 92,
                height: 17,
                resizeMode: 'contain',
              }}
              source={logoTaylor}
            />
          ) : (
            <Text
              size={17}
              style={{
                color: '#8C8C90',
                marginRight: checkDisableTouch() ? 0 : 10,
              }}
            >
              {item?.name || (item?.value ?? 'overview.select')}
            </Text>
          )}
          {!checkDisableTouch() && (
            <AntDesign name="right" size={10} color={'#8C8B8F'} />
          )}
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default ItemClubSpecs;
