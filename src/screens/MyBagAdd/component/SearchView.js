import React from 'react';
import {View, TextInput, StyleSheet} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import appStyles from 'styles/global';

const SearchView = ({placeholder, onChangeText}) => {
  return (
    <View style={[appStyles.row, style.viewContainerInput]}>
      <AntDesign name="search1" size={20} color={'#111111'} />
      <TextInput
        style={style.viewTextInput}
        placeholder={placeholder}
        placeholderTextColor={'#111111'}
        onChangeText={onChangeText}
        returnKeyType="done"
        autoCorrect={false}
      />
    </View>
  );
};
const style = StyleSheet.create({
  viewTextInput: {
    color: '#111111',
    flex: 1,
    fontSize: 17,
    paddingLeft: 10,
    paddingVertical: 0,
    android: {
      fontFamily: 'SF-Pro',
    },
    ios: {
      fontFamily: 'SF Pro',
    },
  },
  viewContainerInput: {
    marginHorizontal: 25,
    padding: 15,
    borderRadius: 30,
    backgroundColor: '#ffffff',
    alignItems: 'center',
  },
});
export default SearchView;
