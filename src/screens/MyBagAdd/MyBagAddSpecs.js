/* eslint-disable react-hooks/exhaustive-deps */
import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  StatusBar,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  Platform,
} from 'react-native';
import {connect, useDispatch, useSelector} from 'react-redux';

import {
  getAllClubShaftFlex,
  getAllClubLieAdjustments,
  getAllClubLoftAdjustments,
  addClubToBag,
  getAllClubLofts,
  getAllClubShaftLength,
} from 'requests/accounts';
import {updateBagList} from 'reducers/myBag';
import {showToast} from 'utils/toast';
import analytics from '@react-native-firebase/analytics';

import Selector from 'components/Selector';
import Button from 'components/Button';
import {t} from 'i18next';
import appStyles from 'styles/global';
import * as Animatable from 'react-native-animatable';
import {
  GA_EVENT_NAME,
  LOYALTY_ACTION_KEY,
  NOT_APPLICABLE,
  PAGE_CATEGORY,
  SELECTOR_TYPE,
  SCREEN_TYPES,
  PAGE_NAME,
} from 'utils/constant';
import {getMyBagTab} from 'utils/commonVariable';
import AndroidNumberPicker from 'components/AndroidNumberPicker/AndroidNumberPicker';
import ItemClubSpecs from './component/ItemClubSpecs';
import {saveCompletedLoyaltyAction} from 'utils/asyncStorage';
import {updateUserLoyaltyData} from 'utils/loyalty';
import {GA_logEvent} from 'utils/googleAnalytics';

const MyBagAddSpecs = ({navigation, route, updateBagList}) => {
  const isBall = route.params?.dataClub?.name === 'Ball';
  const isPutter = route.params?.dataClub?.name === 'Putter';
  const [isShow, setIsShow] = useState(false);
  const [isShowStart, setIsShowStart] = useState(false);
  const [dataSelector, setDataSelector] = useState(null);
  const [dataOptionClub, setDataOptionClub] = useState({
    flex: [],
    lieAdj: [],
    loftAdj: [],
    length: [],
    loft: [],
  });
  const [dataClub, setDataClub] = useState(route.params.dataClub);

  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [isShowModal, setIsShowModal] = useState(false);

  const sheetRef = useRef(null);
  const bagList = useSelector(state => {
    return Array.isArray(state.myBag?.bagList) ? state.myBag?.bagList : [];
  });
  const loyalty = useSelector(state => state?.loyalty);
  const user = useSelector(state => state.user);
  const dispatch = useDispatch();
  const appCacheVersions = useSelector(state => state.appCacheVersions);

  useEffect(() => {
    callAllClubLofts();
  }, []);

  useEffect(() => {
    if (dataSelector && sheetRef?.current) {
      sheetRef.current?.snapTo(0);
      setIsShowStart(true);
      setIsShow(true);
    } else if (!dataSelector) {
      onHide();
    }
  }, [dataSelector]);
  const onHide = () => {
    setIsShowStart(false);
    setTimeout(() => {
      setIsShow(false);
    }, 100);
  };

  const callAllClubLofts = async () => {
    try {
      const hasLoft = dataClub?.name !== 'Ball' && dataClub?.name !== 'Putter';
      setLoadingData(true);
      const allFlex = getAllClubShaftFlex();
      const allLieAdjustments = getAllClubLieAdjustments();
      const allLoftAdjustments = getAllClubLoftAdjustments();
      const allLofts = getAllClubLofts(dataClub?.id);
      const allLength = getAllClubShaftLength(dataClub?.id);
      const allData = await Promise.all([
        allFlex,
        allLieAdjustments,
        allLoftAdjustments,
        allLength,
        hasLoft && allLofts,
      ]);
      const updateLadies = allData[0]?.map(_item => {
        if (_item.value === 'Womens') {
          return {..._item, value: 'Ladies'};
        }
        return _item;
      });
      setDataOptionClub({
        flex: updateLadies ?? [],
        lieAdj: allData[1] ?? [],
        loftAdj: allData[2] ?? [],
        length: allData[3] ?? [],
        loft: hasLoft ? allData[4] ?? [] : [],
      });
      let lieAdj, length, loftAdj;
      if (allData[1]) {
        lieAdj = allData[1].find(_item => _item.value === 'Standard');
      }
      if (allData[2]) {
        loftAdj = allData[2].find(_item => _item.value === 'Standard');
      }
      if (allData[3]) {
        length = allData[3].find(_item => _item.value === 'Standard');
      }
      setDataClub({...dataClub, lieAdj, loftAdj, length});
    } catch (error) {
      showToast({
        type: 'error',
        message: t('mybagadd.bag_room_needs_organizing'),
        subText: t('mybag.add_specs.we_have_our_top_pro_on_it'),
      });
    } finally {
      setLoadingData(false);
    }
  };

  const addClub = async () => {
    if (loading) {
      return;
    }
    setLoading(true);
    try {
      let arrAddClub = [];
      let dataTracking = [];
      if (isBall || isPutter || dataClub?.name === 'Driver') {
        const addToBag = () =>
          addClubToBag({
            categoryId: dataClub?.id,
            categoryTypeId: null,
            brandId: dataClub?.brand?.id,
            modelId: dataClub?.model?.id,
            loft: dataClub?.loft?.id ?? null,
            shaftFlex: dataClub?.flex?.id,
            shaftLength: dataClub?.length?.id,
            faceLieAdjustment: dataClub?.lieAdj?.id,
            faceLoftAdjustment: dataClub?.loftAdj?.id,
            inBag: getMyBagTab() === 'ActiveClubs',
            completeAction: true,
          });
        arrAddClub.push(addToBag);
        dataTracking.push({
          club_active: getMyBagTab() === 'ActiveClubs' ? 'true' : 'false',
          club_type: dataClub?.name,
          club_brand: dataClub?.brand?.name,
          club_model: dataClub?.model?.name,
          club_loft: dataClub?.loft?.value ?? null,
          club_shaft_flex: dataClub?.flex?.value,
          club_shaft_length: dataClub?.length?.value,
          club_face_lie_adjustment: dataClub?.lieAdj?.value,
          club_face_loft_adjustment: dataClub?.loftAdj?.value,
        });
      } else {
        const loft = Object.values(dataClub.loft);
        loft.forEach((element, index) => {
          const completeAction = index === loft?.length - 1 ? true : false;
          const addToBag = () =>
            addClubToBag({
              categoryId: element?.clubCategoryId,
              categoryTypeId: element?.id,
              brandId: dataClub?.brand?.id,
              modelId: dataClub?.model?.id,
              loft: null,
              shaftFlex: dataClub?.flex?.id,
              shaftLength: dataClub?.length?.id,
              faceLieAdjustment: dataClub?.lieAdj?.id,
              faceLoftAdjustment: dataClub?.loftAdj?.id,
              inBag: getMyBagTab() === 'ActiveClubs',
              completeAction: completeAction,
            });
          arrAddClub.push(addToBag);
        });
        dataTracking.push({
          club_active: getMyBagTab() === 'ActiveClubs' ? 'true' : 'false',
          club_type: dataClub?.name,
          club_brand: dataClub?.brand?.name,
          club_model: dataClub?.model?.name,
          club_loft: handleLoftData()?.name,
          club_shaft_flex: dataClub?.flex?.value,
          club_shaft_length: dataClub?.length?.value,
          club_face_lie_adjustment: dataClub?.lieAdj?.value,
          club_face_loft_adjustment: dataClub?.loftAdj?.value,
        });
      }
      let numberSuccess = 0;
      for (let index = 0; index < arrAddClub?.length; index++) {
        const element = arrAddClub[index];
        setTimeout(() => {
          element()
            .then(result => {
              let clubAddedText = t('mybag.add_specs.msg_club');
              switch (true) {
                case result?.length - bagList?.length > 1:
                  clubAddedText = t('mybag.add_specs.msg_clubs');
                  break;
                case isBall:
                  clubAddedText = t('mybag.add_specs.msg_ball');
                  break;
                default:
                  break;
              }
              numberSuccess++;
              if (numberSuccess === arrAddClub?.length) {
                trackingGoogleAnalytics(dataTracking[0]);
                updateBagList(result);
                setLoading(false);
                setIsShowModal(false);
                showToast({
                  type: 'success',
                  message: `${
                    !isBall ? result?.length - bagList?.length : ''
                  } ${t('mybag.add_specs.msg_new')} ${clubAddedText} ${t(
                    'mybag.add_specs.msg_added',
                  )}`,
                });
                saveCompletedLoyaltyAction(
                  LOYALTY_ACTION_KEY.COMPLETE_WITB,
                  loyalty?.completedActions,
                  loyalty?.loyaltyActions?.data,
                  'my-bag',
                );
                updateUserLoyaltyData(
                  dispatch,
                  user?.userCountry,
                  loyalty,
                  appCacheVersions,
                );
                navigation.navigate(`MyBagScreens`);
              }
            })
            .catch(error => {
              setLoading(false);
              setIsShowModal(false);
              showToast({
                type: 'error',
                message: t('mybag.add_specs.bag_room_is_full'),
                subText: t('mybag.add_specs.we_could_not_save_your_club_info'),
              });
              navigation.navigate(`MyBagScreens`);
            });
        }, index * 100);
      }
    } catch (error) {
      setLoading(false);
      setIsShowModal(false);
      showToast({
        type: 'error',
        message: t('mybag.add_specs.bag_room_is_full'),
        subText: t('mybag.add_specs.we_could_not_save_your_club_info'),
      });
      navigation.navigate(`MyBagScreens`);
    }
  };

  const trackingGoogleAnalytics = async clubInfo => {
    try {
      if (clubInfo) {
        await GA_logEvent(GA_EVENT_NAME.ADD_CLUB, {
          screen_type: SCREEN_TYPES.WITB,
          page_name: PAGE_NAME.ACCOUNT_WITB_ADD_CLUB_INFO,
          page_type: SCREEN_TYPES.WITB,
          page_category: PAGE_CATEGORY.ACCOUNT_WITB_ADD_CLUB,
          club_active: clubInfo.club_active || NOT_APPLICABLE,
          club_type: clubInfo.club_type || NOT_APPLICABLE,
          club_brand: clubInfo.club_brand || NOT_APPLICABLE,
          club_model: clubInfo.club_model || NOT_APPLICABLE,
          club_loft: clubInfo.club_loft || NOT_APPLICABLE,
          club_shaft_flex: clubInfo.club_shaft_flex || NOT_APPLICABLE,
          club_shaft_length: clubInfo.club_shaft_length || NOT_APPLICABLE,
          club_face_lie_adjustment:
            clubInfo.club_face_lie_adjustment || NOT_APPLICABLE,
          club_face_loft_adjustment:
            clubInfo.club_face_loft_adjustment || NOT_APPLICABLE,
        });
      }
    } catch (error) {
      console.log('analytics error', error?.message);
    }
  };

  const formatTitle = () => {
    if (dataSelector) {
      switch (dataSelector.selectorType) {
        case SELECTOR_TYPE.LOFT:
          return t('my.bag.add_specs.select_loft');
        case SELECTOR_TYPE.SHAFT_FLEX:
          return t('my.bag.add_specs.select_shaft_flex');
        case SELECTOR_TYPE.SHAFT_LENGTH:
          return t('my.bag.add_specs.select_shaft_length');
        case SELECTOR_TYPE.FACE_LIE_ADJUSTMENT:
          return t('my.bag.add_specs.select_lie_adjustment');
        case SELECTOR_TYPE.FACE_LOFT_ADJUSTMENT:
          return t('my.bag.add_specs.select_loft_adjustment');
      }
    }
    return null;
  };
  const onToggle = value => {
    setDataSelector(null);
  };

  const onChangeValue = (type, value) => {
    let updateItem = {...dataClub};
    switch (type) {
      case SELECTOR_TYPE.LOFT:
        const loft = dataOptionClub.loft.find(val => val.value === value);
        if (loft) {
          updateItem = {...dataClub, loft};
        }
        break;
      case SELECTOR_TYPE.SHAFT_FLEX:
        const flex = dataOptionClub.flex.find(val => val.value === value);
        if (flex) {
          updateItem = {...dataClub, flex};
        }
        break;
      case SELECTOR_TYPE.SHAFT_LENGTH:
        const length = dataOptionClub?.length.find(val => val.value === value);
        if (length) {
          updateItem = {...dataClub, length};
        }
        break;
      case SELECTOR_TYPE.FACE_LIE_ADJUSTMENT:
        const lieAdj = dataOptionClub.lieAdj.find(val => val.value === value);
        if (lieAdj) {
          updateItem = {...dataClub, lieAdj};
        }
        break;
      case SELECTOR_TYPE.FACE_LOFT_ADJUSTMENT:
        const loftAdj = dataOptionClub.loftAdj.find(val => val.value === value);
        if (loftAdj) {
          updateItem = {...dataClub, loftAdj};
        }
        break;
    }
    setDataClub({...updateItem});
  };
  const handleLoftData = () => {
    if (
      dataClub.name === 'Fairway' ||
      dataClub.name === 'Hybrid' ||
      dataClub.name === 'Irons' ||
      dataClub.name === 'Wedge'
    ) {
      /*Irons: if consecutive, #i - #i
      if not, separate with commas: 2i, 4i - 9i, SW
      All other clubs, separate lofts with commas: 3FW, 9FW or 3H, 4H, 9H*/
      const arrayLoft = Object.values(dataClub.loft).sort(
        (a, b) => a.sortOrder - b.sortOrder,
      );
      if (dataClub.name === 'Irons') {
        let num = 0,
          name = '';
        for (let i = 0; i < arrayLoft?.length; i++) {
          if (i === 0) {
            name = arrayLoft[0].type;
          }
          if (
            i < arrayLoft?.length - 1 &&
            arrayLoft[i + 1].sortOrder === arrayLoft[i].sortOrder + 1
          ) {
            if (num === 0 && i > 0) {
              name = name + ', ' + arrayLoft[i].type;
            }
            num += 1;
          } else {
            if (i > 0) {
              name =
                num > 1
                  ? name + ' - ' + arrayLoft[i].type
                  : name + ', ' + arrayLoft[i].type;
            }

            num = 0;
          }
        }
        return {name};
      } else {
        const dataLoft = arrayLoft.map(_item => _item.type)?.toString();
        return {name: addSpaceForString(dataLoft)};
      }
    }
    return dataClub?.loft;
  };
  const addSpaceForString = value => {
    return value.replace(/,/g, ', ');
  };
  const updateDataClub = data => {
    setDataClub({...data});
  };
  const onPressLoft = () => {
    if (route.params?.dataClub?.name === 'Driver') {
      setDataSelector({
        selectorType: SELECTOR_TYPE.LOFT,
        value: dataClub?.loft?.value,
        options: dataOptionClub.loft,
        onChange: valueChange => {
          onChangeValue(SELECTOR_TYPE.LOFT, valueChange);
        },
        toggled: true,
        setToggled: toggle => onToggle(toggle),
      });
    } else {
      navigation.push('MyBagAddLoft', {
        dataClub,
        updateDataClub,
      });
    }
  };
  const onPressModel = () => {
    navigation.push('MyBagAddModel', {
      dataClub,
      updateDataClub,
    });
  };
  const renderContent = () => {
    const dataLoft = handleLoftData();
    return (
      <View style={appStyles.flex}>
        <ItemClubSpecs title={t('my_bag.add_club.club_type')} item={dataClub} />
        <ItemClubSpecs
          title={t('my_bag.add_brand.hedline')}
          item={dataClub.brand}
        />
        <ItemClubSpecs
          title={t('my_bag.add_model.headline')}
          item={dataClub.model}
          dataClub={dataClub}
          onPress={onPressModel}
        />
        {!isBall && (
          <View style={appStyles.flex}>
            {!isPutter && (
              <ItemClubSpecs
                dataClub={dataClub}
                title={t('my.bag.add_specs.select_loft')}
                item={dataLoft}
                onPress={onPressLoft}
              />
            )}
            {!isPutter && (
              <ItemClubSpecs
                title={t('my.bag.add_specs.select_shaft_flex')}
                item={dataClub?.flex}
                onPress={() => {
                  setDataSelector({
                    selectorType: SELECTOR_TYPE.SHAFT_FLEX,
                    value: dataClub?.flex?.value,
                    options: dataOptionClub.flex,
                    onChange: valueChange => {
                      onChangeValue(SELECTOR_TYPE.SHAFT_FLEX, valueChange);
                    },
                    toggled: true,
                    setToggled: toggle => onToggle(toggle),
                  });
                }}
              />
            )}
            <ItemClubSpecs
              title={t('my.bag.add_specs.select_shaft_length')}
              item={dataClub?.length}
              onPress={() => {
                setDataSelector({
                  dataType: dataClub,
                  selectorType: SELECTOR_TYPE.SHAFT_LENGTH,
                  value: dataClub?.length?.value,
                  options: dataOptionClub?.length,
                  onChange: valueChange => {
                    onChangeValue(SELECTOR_TYPE.SHAFT_LENGTH, valueChange);
                  },
                  toggled: true,
                  setToggled: toggle => onToggle(toggle),
                });
              }}
            />
            <ItemClubSpecs
              title={t('my.bag.add_specs.select_lie_adjustment')}
              item={dataClub?.lieAdj}
              onPress={() => {
                setDataSelector({
                  selectorType: SELECTOR_TYPE.FACE_LIE_ADJUSTMENT,
                  value: dataClub?.lieAdj?.value,
                  options: dataOptionClub.lieAdj,
                  onChange: valueChange => {
                    onChangeValue(
                      SELECTOR_TYPE.FACE_LIE_ADJUSTMENT,
                      valueChange,
                    );
                  },
                  toggled: true,
                  setToggled: toggle => onToggle(toggle),
                });
              }}
            />
            <ItemClubSpecs
              title={t('my.bag.add_specs.select_loft_adjustment')}
              item={dataClub?.loftAdj}
              onPress={() => {
                setDataSelector({
                  selectorType: SELECTOR_TYPE.FACE_LOFT_ADJUSTMENT,
                  value: dataClub?.loftAdj?.value,
                  options: dataOptionClub.loftAdj,
                  onChange: valueChange => {
                    onChangeValue(
                      SELECTOR_TYPE.FACE_LOFT_ADJUSTMENT,
                      valueChange,
                    );
                  },
                  toggled: true,
                  setToggled: toggle => onToggle(toggle),
                });
              }}
            />
          </View>
        )}
      </View>
    );
  };
  return (
    <View style={[appStyles.flex, appStyles.spaceBetween]}>
      <StatusBar barStyle="dark-content" />

      {loadingData ? (
        <ActivityIndicator style={appStyles.pTLg} color="black" />
      ) : (
        <View style={appStyles.flex}>
          {renderContent()}
          <Button
            text={
              isBall
                ? 'my_bag.add_club.add_ball_to_bag'
                : 'my_bag.add_club.add_club_to_bag'
            }
            textColor="white"
            backgroundColor={'#111111'}
            borderColor={'#111111'}
            onPress={addClub}
            loading={loading}
            loadingMode={'dark'}
            textSize={'mds'}
            style={[appStyles.mBMd, appStyles.mTSm, appStyles.mHSm]}
            centered
            DINbold
          />
          {isShow && (
            <Animatable.View
              animation={isShowStart ? 'fadeIn' : 'fadeOut'}
              style={[appStyles.absoluteFill, {backgroundColor: '#00000050'}]}
            >
              <TouchableOpacity
                style={appStyles.absoluteFill}
                onPress={() => {
                  sheetRef.current?.snapTo(1);
                  onHide();
                }}
              />
            </Animatable.View>
          )}
          {dataSelector?.toggled && Platform.OS === 'ios' && (
            <Selector
              dataType={dataSelector?.dataType}
              selectorType={dataSelector?.selectorType}
              ref={sheetRef}
              type="number"
              value={dataSelector?.value}
              options={dataSelector?.options || []}
              onChange={dataSelector?.onChange}
              toggled={dataSelector?.toggled}
              setToggled={dataSelector?.setToggled}
              title={formatTitle()}
            />
          )}
        </View>
      )}
      {isShowModal && (
        <Modal visible transparent={true} animationType="fade">
          <View
            style={[
              appStyles.flex,
              appStyles.hCenter,
              appStyles.vCenter,
              {backgroundColor: '#00000050'},
            ]}
          >
            <ActivityIndicator style={appStyles.pTLg} color="black" />
          </View>
        </Modal>
      )}
      {dataSelector?.toggled && Platform.OS === 'android' && (
        <AndroidNumberPicker
          dataType={dataSelector?.dataType}
          selectorType={dataSelector?.selectorType}
          value={dataSelector?.value}
          options={dataSelector?.options || []}
          visible={dataSelector?.toggled}
          setValue={dataSelector?.onChange}
          onClose={() => dataSelector?.setToggled(false)}
          title={formatTitle()}
        />
      )}
    </View>
  );
};

const mapDispatchToProps = {updateBagList};

export default connect(null, mapDispatchToProps)(MyBagAddSpecs);
