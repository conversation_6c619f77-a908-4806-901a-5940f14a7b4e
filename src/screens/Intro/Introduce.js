import React, {useEffect, useRef, useState} from 'react';
import {View} from 'react-native';
import appStyles from 'styles/global';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import {COUNTRY_CODE} from 'utils/constant';
import {useSelector} from 'react-redux';
import TourUS from './component/TourUS';
import TourCA from './component/TourCA';
import TourRegions from './component/TourRegions';
const Introduce = ({}) => {
  const user = useSelector(state => state?.user);
  const renderTourPages = () => {
    try {
      switch (user?.userCountry) {
        case COUNTRY_CODE.USA:
          return <TourUS />;
        case COUNTRY_CODE.CAN:
          return <TourCA />;
        default:
          return <TourRegions />;
      }
    } catch (error) {}
  };

  return (
    <View style={appStyles.flex}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      {renderTourPages()}
    </View>
  );
};

export default Introduce;
