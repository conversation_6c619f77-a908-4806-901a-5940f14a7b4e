import React from 'react';
import {View, StyleSheet} from 'react-native';
import Text from 'components/Text';
import Ic_access from 'assets/imgs/intro/ic_access.svg';
const AccessGranted = ({style}) => {
  return (
    <View style={[styles.viewContainerNotify, style]}>
      <Text black size={16} weight={700} style={{marginBottom: 12}}>
        intro.access_granted
      </Text>
      <Ic_access />
    </View>
  );
};
const styles = StyleSheet.create({
  viewContainerNotify: {
    backgroundColor: '#fff',
    borderRadius: 24,
    alignItems: 'center',
    marginHorizontal: 8,
    paddingVertical: 24,
    paddingHorizontal: 16,
    flex: 1,
  },
});
export default AccessGranted;
