import React, {useEffect, useState} from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Text from 'components/Text';
import IcPolygon from 'assets/imgs/intro/ic_polygon.svg';
import FooterIntro from './FooterIntro';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolation,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import TabSelected from './TabSelected';
import {
  TYPE_INTRO_PAGE,
  checkRatioScreen,
  getTopTooltipShop,
  getHomeSubTractHeight,
  getScaleImage,
  getTranYImage,
} from '../dimension';
import {t} from 'i18next';
import {GA_logScreenViewV2} from 'utils/googleAnalytics';
import {PAGE_CATEGORY, PAGE_NAME, SCREEN_TYPES} from 'utils/constant';
import {useSelector} from 'react-redux';
const width = wp(100);
const ShopPage = ({onNext, indexPage}) => {
  const user = useSelector(state => state?.user);
  const [isShow, setShow] = useState(false);

  const offset = useSharedValue(0);
  const scaleImage = getScaleImage();
  const transY = getTranYImage();
  useEffect(() => {
    if (indexPage === 3) {
      GA_logScreenViewV2(
        PAGE_NAME.SHOP_INTRO,
        PAGE_CATEGORY.ONBOARDING,
        SCREEN_TYPES.ONBOARDING,
        SCREEN_TYPES.ONBOARDING,
      );
      setTimeout(() => {
        setShow(true);
        offset.value = withTiming(1, {
          duration: 300,
          easing: Easing.elastic(1),
        });
      }, 300);
    }
  }, [indexPage]);

  const styleImage = useAnimatedStyle(() => {
    const scale = interpolate(
      offset.value,
      [0, 1],
      [1, scaleImage],
      Extrapolation.CLAMP,
    );
    const translateY = interpolate(
      offset.value,
      [0, 1],
      [0, -transY],
      Extrapolation.CLAMP,
    );
    return {transform: [{scale}, {translateY}]};
  });
  const styleTooltip = useAnimatedStyle(() => {
    const translateX = interpolate(
      offset.value,
      [0, 1],
      [-width, 0],
      Extrapolation.CLAMP,
    );
    return {transform: [{translateX}]};
  });
  const renderTooltip = () => {
    return (
      <Animated.View
        style={[
          {
            position: 'absolute',
            top: getTopTooltipShop(),
            width: '100%',
            alignSelf: 'center',
          },
          styleTooltip,
        ]}
      >
        <View
          style={{
            left: width / 2 - 13.5,
            position: 'absolute',
          }}
        >
          <IcPolygon />
        </View>
        <View style={styles.viewTooltip}>
          <Text
            size={16}
            weight={400}
            style={{color: 'rgba(0, 0, 0, 0.6)', textAlign: 'center'}}
          >
            intro.view_the_latest_product
          </Text>
        </View>
      </Animated.View>
    );
  };
  return (
    <View
      key="Shop"
      style={{
        backgroundColor: '#000',
        width: wp(100),
        height: hp(100),
        justifyContent: 'center',
      }}
    >
      <Animated.Image
        source={checkRatioScreen({
          pageName: TYPE_INTRO_PAGE.SHOP,
          isSubtrack: false,
          userCountry: user?.userCountry,
        })}
        style={[
          {
            width: wp(100),
            height: hp(100),
          },
          styleImage,
        ]}
        resizeMode={'stretch'}
      />
      {isShow && (
        <Animated.Image
          source={checkRatioScreen({
            pageName: TYPE_INTRO_PAGE.SHOP,
            isSubtrack: true,
            userCountry: user?.userCountry,
          })}
          style={{
            width: wp(100),
            height: getHomeSubTractHeight(),
            position: 'absolute',
            top: 0,
          }}
        />
      )}
      {isShow && renderTooltip()}
      {isShow && <TabSelected index={3} title={t('app.tab_label.shop')} />}
      {isShow && <FooterIntro index={3} onNext={onNext} />}
    </View>
  );
};
const styles = StyleSheet.create({
  viewTooltip: {
    borderRadius: 24,
    marginHorizontal: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    marginTop: 11,
    flex: 1,
  },
});
export default ShopPage;
