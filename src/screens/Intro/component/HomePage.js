import React, {useEffect, useRef, useState} from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import appStyles from 'styles/global';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Text from 'components/Text';
import IcPolygon from 'assets/imgs/intro/ic_polygon.svg';
import FooterIntro from './FooterIntro';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolation,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import TabSelected from './TabSelected';
import {useDispatch, useSelector} from 'react-redux';
import {
  checkNotificationPermissions,
  requestNotificationPermissions,
} from 'utils/home';
import AccessGranted from './AccessGranted';
import {
  TYPE_INTRO_PAGE,
  checkRatioScreen,
  getTopTooltipHome,
  getHomeSubTractHeight,
  getScaleImage,
  getTranYImage,
  getLeftTooltipIndicator,
} from '../dimension';
import {t} from 'i18next';
import {
  GA_EVENT_NAME,
  PAGE_NAME,
  SCREEN_TYPES,
  PAGE_CATEGORY,
  checkMainCountry,
} from 'utils/constant';
import {
  GA_logEvent,
  GA_setUserProperty,
  GA_logScreenViewV2,
} from 'utils/googleAnalytics';
import useAppState from 'hooks/useAppState';
import Permissions from 'react-native-permissions';

const width = wp(100);
const HomePage = ({onNext, indexPage}) => {
  const [isShow, setShow] = useState(false);
  const [isGranted, setGranted] = useState(false);
  const user = useSelector(state => state?.user);
  const loyalty = useSelector(state => state?.loyalty);
  const dispatch = useDispatch();
  const offset = useSharedValue(0);
  const scaleImage = getScaleImage();
  const transY = getTranYImage();
  const hasOpenedSetting = useRef(false);
  useEffect(() => {
    if (indexPage === 0) {
      GA_logScreenViewV2(
        PAGE_NAME.ENABLE_NOTIFICATIONS,
        PAGE_CATEGORY.ONBOARDING,
        SCREEN_TYPES.ONBOARDING,
        SCREEN_TYPES.ONBOARDING,
      );
      setTimeout(() => {
        setShow(true);
        offset.value = withTiming(1, {
          duration: 300,
          easing: Easing.elastic(1),
        });
      }, 300);
    }
  }, [indexPage]);

  const openPermission = async () => {
    const checkRequest = await requestNotificationPermissions({
      dispatch,
      user,
      loyalty,
      origin: 'tour-intro',
    });
    if (checkRequest) {
      setGranted(true);
    } else {
      hasOpenedSetting.current = true;
    }
    setTimeout(() => {
      onNext(0);
    }, 300);
  };

  const checkNotificationPermission = async () => {
    const notificationPermissions = await checkNotificationPermissions();
    if (notificationPermissions) {
      GA_logEvent(GA_EVENT_NAME.ENABLE_NOTIFICATIONS, {
        callout_name: 'notifications enable',
        click_location: 'tour-intro',
        page_type: SCREEN_TYPES.ONBOARDING,
        page_category: PAGE_CATEGORY.ONBOARDING,
        page_name: PAGE_NAME.ONBOARDING,
        screen_type: SCREEN_TYPES.ONBOARDING,
      });
      GA_setUserProperty('notifications_enabled', 'true');
    } else {
      GA_setUserProperty('notifications_enabled', 'false');
    }
  };

  useAppState({
    onForeground: () => {
      if (hasOpenedSetting.current) {
        checkNotificationPermission();
        hasOpenedSetting.current = false;
      }
    },
  });

  const styleImage = useAnimatedStyle(() => {
    const scale = interpolate(
      offset.value,
      [0, 1],
      [1, scaleImage],
      Extrapolation.CLAMP,
    );
    const translateY = interpolate(
      offset.value,
      [0, 1],
      [0, -transY],
      Extrapolation.CLAMP,
    );
    return {transform: [{scale}, {translateY}]};
  });

  const styleTooltip = useAnimatedStyle(() => {
    const translateX = interpolate(
      offset.value,
      [0, 1],
      [-width, 0],
      Extrapolation.CLAMP,
    );
    return {transform: [{translateX}]};
  });

  const renderTooltip = () => {
    return (
      <Animated.View
        style={[
          {
            position: 'absolute',
            top: getTopTooltipHome(user?.userCountry),
            width: '100%',
            alignSelf: 'center',
          },
          styleTooltip,
        ]}
      >
        <View
          style={{
            left: getLeftTooltipIndicator(),
            position: 'absolute',
          }}
        >
          <IcPolygon />
        </View>
        <View style={styles.viewTooltip}>
          <Text
            size={16}
            weight={400}
            style={{color: 'rgba(0, 0, 0, 0.6)', textAlign: 'center'}}
          >
            {checkMainCountry(user?.userCountry)
              ? t('intro.see_your_points_overview')
              : t('intro.see_your_points_overview_regions')}
          </Text>
        </View>
      </Animated.View>
    );
  };

  const renderPermission = () => {
    return (
      <Animated.View
        style={[{width: '100%', position: 'absolute'}, styleTooltip]}
      >
        {isGranted ? (
          <AccessGranted />
        ) : (
          <View style={styles.viewContainerNotify}>
            <Text black size={16} weight={700}>
              home.permissions_notifications.headline
            </Text>
            <Text
              size={16}
              weight={400}
              style={{
                textAlign: 'center',
                color: 'rgba(0, 0, 0, 0.6)',
                paddingTop: 10,
                paddingHorizontal: 3,
              }}
            >
              intro.be_the_first_to_know_about
            </Text>
            <TouchableOpacity
              style={styles.touchNotification}
              onPress={openPermission}
            >
              <Text
                size={12}
                Din79Font
                weight={700}
                white
                style={{
                  textTransform: 'uppercase',
                  letterSpacing: 1.62,
                }}
              >
                home.permissions_notifications.cta
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </Animated.View>
    );
  };

  return (
    <View
      key="Home"
      style={{
        backgroundColor: '#000',
        width: wp(100),
        height: hp(100),
        justifyContent: 'center',
      }}
    >
      <Animated.Image
        source={checkRatioScreen({
          pageName: TYPE_INTRO_PAGE.HOME,
          isSubtrack: false,
          userCountry: user?.userCountry,
        })}
        style={[
          {
            width: wp(100),
            height: hp(100),
          },
          styleImage,
        ]}
        resizeMode={'stretch'}
      />
      {isShow && (
        <Animated.Image
          source={checkRatioScreen({
            pageName: TYPE_INTRO_PAGE.HOME,
            isSubtrack: true,
            userCountry: user?.userCountry,
          })}
          style={{
            width: wp(100),
            height: getHomeSubTractHeight(),
            position: 'absolute',
            top: 0,
          }}
        />
      )}
      {renderTooltip()}
      {isShow && <TabSelected index={0} title={t('app.tab_label.home')} />}
      {renderPermission()}
      {isShow && <FooterIntro index={0} onNext={onNext} />}
    </View>
  );
};
const styles = StyleSheet.create({
  viewTooltip: {
    borderRadius: 24,
    marginHorizontal: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    marginTop: 11,
    flex: 1,
  },
  touchNotification: {
    backgroundColor: '#000',
    paddingVertical: 14,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 14,
    borderRadius: 24,
    ...appStyles.viewShadowLight,
  },
  viewContainerNotify: {
    backgroundColor: '#fff',
    borderRadius: 24,
    alignItems: 'center',
    marginHorizontal: 8,
    paddingVertical: 24,
    paddingHorizontal: 16,
    flex: 1,
  },
});
export default HomePage;
