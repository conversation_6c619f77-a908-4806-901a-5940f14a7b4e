import React, {useEffect, useState} from 'react';
import {
  View,
  Switch,
  Platform,
  NativeEventEmitter,
  NativeModules,
} from 'react-native';
import {connect, useDispatch, useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Video from 'react-native-video';
import {SafeAreaView} from 'react-native-safe-area-context';
import {CommonActions} from '@react-navigation/native';
import analytics from '@react-native-firebase/analytics';

import Logo from '../components/Logo';
import FocusAwareStatusBar from '../components/FocusAwareStatusBar';
import Button from '../components/Button';
import LoadingOverlay from '../components/LoadingOverlay';
import Text from '../components/Text';

import {addCurrentUser} from '../reducers/user';
import {getUser, login, sendOTP, updateUser} from '../requests/accounts';
import {updateCurrentBasket} from '../reducers/basket';
import {updatePermissions} from '../reducers/app';

import appStyles from '../styles/global';
import onboarding from '../json/onboarding';
import {screenHeight, DARK_GREY} from '../config';
import {showToast} from '../utils/toast';
import {ENVIRONMENTS, prefetchConfigs} from '../config/env';
import {getAuth0Client} from '../utils/auth0';
import {GA_logEvent, GA_logScreenViewV2} from '../utils/googleAnalytics';
import {t} from 'i18next';
import {
  IS_UPGRADE_FROM_29,
  REQUEST_PERMISSION,
  SCREEN_CLASS,
  SCREEN_TYPES,
  SHOWED_INTRODUCE,
  GA_EVENT_NAME,
  PAGE_NAME,
  PAGE_CATEGORY,
  API_TOKEN_EXPIRATION_TIME,
  AUTH0_TOKEN_EXPIRATION_TIME,
  checkMainCountry,
} from 'utils/constant';
import * as RNLocalize from 'react-native-localize';
import {getGolferProfile} from 'requests/ghin';
import {updateGHINHandicapIndex} from 'reducers/ghin';
import {
  isCanadaMarket,
  setAskPermissions,
  setCountry,
  setDeviceCountry,
  setLanguage,
} from 'utils/commonVariable';
import {updateWHSHandicapIndex} from 'reducers/whs';
import {getWHSGolferProfile} from 'requests/whs';
import {refreshConfigureFeatures} from 'utils/configureFeatures';
import TaylorMadeWhite from 'assets/imgs/onBoarding/TaylorMadeWhite.svg';
import Animated, {
  Extrapolation,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {uploadLogsToServer} from 'utils/logging';
import iso31661 from 'iso-3166-1';
import {watchEvents} from 'react-native-watch-connectivity';
import {getModuleCacheData} from 'requests/moduleCache';
import {updateCacheVersionData} from 'reducers/cacheVersion';

const Onboarding = ({
  navigation,
  addCurrentUser,
  updatePermissions,
  route,
  userSaved,
  updateGHINHandicapIndex,
}) => {
  const [loading, setLoading] = useState(false);
  const [environment, setEnvironment] = useState(ENVIRONMENTS.DEV);
  const [videoFadeAnim] = useState(new Animated.Value(1));
  const [logoFadeAnim] = useState(new Animated.Value(0));
  const isForceLogout = route?.params?.isForceLogout;
  const isLogoDown = !!route?.params?.fromSignUp;
  const dispatch = useDispatch();
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);

  useEffect(() => {
    let eventListener = null;
    let watchUnsubscribe = null;
    if (Platform.OS === 'ios') {
      watchUnsubscribe = watchEvents.on('message', (message, reply) => {
        if (message?.request === 'login') {
          handedLogicEventIOSWhenNotLoggedIn(reply);
        }
      });
    } else if (Platform.OS === 'android') {
      const eventEmitter = new NativeEventEmitter(NativeModules.WearOSModule);
      eventListener = eventEmitter.addListener('getAuthFromWearOS', event => {
        try {
          const wearosModule = NativeModules.WearOSModule;
          handedLogicEventAndroidWhenNotLoggedIn(event.nodeId, wearosModule);
        } catch (error) {
          console.log('error', error);
        }
      });
    }

    return () => {
      if (Platform.OS === 'ios' && !!watchUnsubscribe) {
        watchUnsubscribe();
      } else if (Platform.OS === 'android' && eventListener) {
        eventListener.remove();
      }
    };
  }, []);

  const handedLogicEventAndroidWhenNotLoggedIn = async (
    nodeId,
    wearosModule,
  ) => {
    try {
      wearosModule.checkLogin(nodeId, {
        userId: '',
      });
    } catch (error) {}
  };

  const handedLogicEventIOSWhenNotLoggedIn = async reply => {
    try {
      reply?.({response: t('watch.please_login_app_first')});
    } catch (error) {
      reply?.({response: 'Please login app in your phone app first'});
    }
  };

  const clearWebAuthSession = async () => {
    const auth0 = await getAuth0Client(environment);
    setTimeout(() => {
      auth0.webAuth.clearSession();
    }, 1000);
  };

  const GA_signInFromOnBoarding = async (status, errorMessage) => {
    try {
      await GA_logScreenViewV2(
        PAGE_NAME.LOGIN,
        PAGE_CATEGORY.LOGIN,
        SCREEN_TYPES.LOGIN,
        SCREEN_TYPES.LOGIN,
      );
      if (status === 'success') {
        await GA_logEvent(GA_EVENT_NAME.LOGIN, {
          authentication_status: status,
          signup_method: 'email',
          page_type: SCREEN_TYPES.LOGIN,
          page_category: PAGE_CATEGORY.LOGIN,
          page_name: PAGE_NAME.LOGIN,
          screen_type: SCREEN_TYPES.LOGIN,
        });
      } else {
        await GA_logEvent(GA_EVENT_NAME.LOGIN, {
          authentication_status: status,
          signup_method: 'email',
          error_message: errorMessage,
          page_type: SCREEN_TYPES.LOGIN,
          page_category: PAGE_CATEGORY.LOGIN,
          page_name: PAGE_NAME.LOGIN,
          screen_type: SCREEN_TYPES.LOGIN,
        });
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    (async () => {
      if (!!isForceLogout) {
        await clearWebAuthSession();
      }
    })();
  }, []);

  useEffect(() => {
    // Set environment
    AsyncStorage.setItem('env', environment).then(() => {
      prefetchConfigs(['APP_ASSETS_URL']);
    });
  }, [environment]);

  const openAuth0 = async () => {
    const auth0 = await getAuth0Client(environment);
    setLoading(true);
    // Open browser and navigate to Auth0 Universal Login page
    auth0.webAuth
      .authorize({scope: 'openid profile email offline_access'})
      .then(credentials => userLogin(credentials))
      .catch(error => {
        setLoading(false);
      });
  };

  const countryAlpha2 = RNLocalize.getCountry(); // Get code Alpha-2
  const countryData = iso31661.whereAlpha2(countryAlpha2); // Get code Alpha-3
  setDeviceCountry(countryData?.alpha3 || 'Unknown');
  const userLogin = async credentials => {
    try {
      // Make request to login endpoint to get user data
      const user = await login(credentials.accessToken);
      // Save current user's email for jwt renewal
      await AsyncStorage.setItem('userEmail', user.email);
      // Update permissions in redux
      updatePermissions({
        myTMSubscriptionLevel: user?.myTMSubscriptionLevel,
        myTMPermission: user?.myTMPermission,
        subscriptionService: user?.subscriptionService,
        isTrialSubscription: user?.isTrialSubscription,
      });

      setLanguage(user?.language || 'en');
      // Set auth0 tokens to AsyncStorage
      await AsyncStorage.setItem('auth0AccessToken', credentials.accessToken);
      await AsyncStorage.setItem('auth0RefreshToken', credentials.refreshToken);
      await AsyncStorage.setItem(
        'auth0ExpiresIn',
        moment().add(AUTH0_TOKEN_EXPIRATION_TIME, 'm').toISOString(),
      );
      await AsyncStorage.setItem(
        'refreshApp',
        moment().add(29, 'days').toISOString(),
      );
      // Set TM tokens to AsyncStorage
      await AsyncStorage.setItem('idToken', user.idToken);
      await AsyncStorage.setItem('refreshToken', user.refreshToken);
      await AsyncStorage.setItem(
        'expiresIn',
        moment().add(API_TOKEN_EXPIRATION_TIME, 'm').toISOString(),
      );
      setCountry(user?.userCountry);
      addCurrentUser({...user});
      getUser();

      await refreshConfigureFeatures(dispatch, user, showHideFeatures);
      if (isCanadaMarket()) {
        try {
          // Add user to redux
          const golferProfile = await getWHSGolferProfile();

          if (golferProfile) {
            //Show handicap Entered
            updateWHSHandicapIndex(golferProfile);
            addCurrentUser({
              golfCanadaCardId: golferProfile?.golfCanadaCardId,
            });
          }
        } catch (error) {
          if (error?.response?.data?.golfCanadaCardId) {
            updateWHSHandicapIndex({
              golfCanadaCardId: error?.response?.data.golfCanadaCardId,
            });
          }
        }
      } else {
        try {
          // Add user to redux
          const golferProfile = await getGolferProfile();

          if (golferProfile) {
            //Show handicap Entered
            updateGHINHandicapIndex(golferProfile);
            addCurrentUser({ghinId: golferProfile.ghin});
          }
        } catch (error) {
          if (error?.response?.data?.ghin_id) {
            updateGHINHandicapIndex({
              ghin: error?.response?.data.ghin_id,
              email: error?.response?.data?.ghinEmail,
            });
          }
          //Show handicap Pendding
        }
      }
      // Check user auth status
      const showedIntro = await AsyncStorage.getItem(SHOWED_INTRODUCE);
      const upgradeFrom29 = await AsyncStorage.getItem(IS_UPGRADE_FROM_29);
      const isRequest = await AsyncStorage.getItem(REQUEST_PERMISSION);
      const isRequestPermissionOnHome =
        !showedIntro && !upgradeFrom29 && !isRequest;
      setAskPermissions(isRequestPermissionOnHome);

      GA_signInFromOnBoarding('success', 'N/A');
      // Stop loading state and navigate to next screen
      setLoading(false);
      uploadLogsToServer();
      if (!user?.userCountry) {
        // If country information is missing, redirect to CountryQuiz screen
        navigation.replace('IntroQuiz', {screen: 'CountryQuiz'});
        return;
      }

      // Check if user is from a main country
      const isMainCountry = checkMainCountry(user.userCountry);

      // Handle unverified email for main country users
      if (user && !user.emailVerified && isMainCountry) {
        setLoading(false);
        sendOTP(user.email).catch(() => {});
        navigation.navigate('VerifyOtp', {
          screen: 'VerifyOtp',
          params: {email: user.email},
        });
        return;
      }

      // Handle missing date of birth for non-main country users
      if (user && !user.dob && !isMainCountry) {
        navigation.navigate('VerifyBirthDay', {
          screen: 'BirthdayVerificationScreen',
          params: {},
        });
        return;
      }
      const rs = await getModuleCacheData(user.userCountry);
      dispatch(updateCacheVersionData(rs));
      // Default case: navigate to Home screen
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [
            {
              name: 'App',
              screen: 'Home',
            },
          ],
        }),
      );
    } catch (error) {
      setLoading(false);
      if (error?.response?.data?.internalErrorCode === 'UNAUTHORIZED') {
        showToast({
          type: 'error',
          message: error?.response?.data?.errorMessage,
        });
        GA_signInFromOnBoarding('failure', error?.response?.data?.errorMessage);
        clearWebAuthSession();
      } else {
        showToast({
          type: 'error',
          message: t('login.confirmation.backup_on_the_first_tee'),
          subText: t('login.confirmation.your_group_will_be_teeing_off_soon'),
        });
        GA_signInFromOnBoarding(
          'failure',
          t('login.confirmation.backup_on_the_first_tee'),
        );
      }
    }
  };

  const offset = useSharedValue(0);

  if (isLogoDown) {
    offset.value = withTiming(0, {duration: 900});
  }

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: interpolate(offset.value, [0, 1], [0, -200], {
            extrapolateRight: Extrapolation.CLAMP,
          }),
        },
      ],
    };
  });

  return (
    <View style={appStyles.flex}>
      {loading ? <LoadingOverlay transparent={loading} /> : null}
      <SafeAreaView style={appStyles.flex}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <Animated.View
          style={[
            appStyles.absoluteFill,
            {
              backgroundColor: 'black',
              height: screenHeight,
            },
          ]}
        >
          <Animated.View
            style={[
              appStyles.absoluteFill,
              appStyles.flex,
              appStyles.hCenter,
              appStyles.vCenter,
              {
                zIndex: 100,
              },
              animatedStyles,
            ]}
          >
            <TaylorMadeWhite />
          </Animated.View>
          <Animated.View
            style={[
              appStyles.absoluteFill,
              {
                opacity: videoFadeAnim,
              },
            ]}
          >
            <View
              style={[
                appStyles.absoluteFill,
                {
                  backgroundColor: 'black',
                  opacity: 0.4,
                  height: screenHeight,
                  zIndex: 1,
                },
              ]}
            />
            <Video
              source={require('../../assets/videos/Taylormade_video.m4v')}
              style={[
                {
                  height: screenHeight,
                },
              ]}
              muted={true}
              repeat={true}
              resizeMode={'cover'}
              rate={1.0}
              ignoreSilentSwitch={'obey'}
              paused={false}
              playWhenInactive={true}
            />
          </Animated.View>
        </Animated.View>
        <View style={[appStyles.row, appStyles.hCenter, appStyles.pLMd]}>
          <Switch
            ios_backgroundColor={DARK_GREY}
            value={environment === ENVIRONMENTS.PROD}
            onValueChange={() => setEnvironment(ENVIRONMENTS.PROD)}
          />
          <Text
            style={[
              appStyles.xs,
              appStyles.white,
              appStyles.mLXxs,
              appStyles.mRSm,
              appStyles.bold,
            ]}
          >
            PROD
          </Text>
          <Switch
            ios_backgroundColor={DARK_GREY}
            value={environment === ENVIRONMENTS.STAGING}
            onValueChange={() => setEnvironment(ENVIRONMENTS.STAGING)}
          />
          <Text
            style={[
              appStyles.xs,
              appStyles.white,
              appStyles.mLXxs,
              appStyles.mRSm,
              appStyles.bold,
            ]}
          >
            STAGING
          </Text>
          <Switch
            ios_backgroundColor={DARK_GREY}
            value={environment === ENVIRONMENTS.DEV}
            onValueChange={() => setEnvironment(ENVIRONMENTS.DEV)}
          />
          <Text
            style={[
              appStyles.xs,
              appStyles.white,
              appStyles.mLXxs,
              appStyles.bold,
            ]}
          >
            DEV
          </Text>
        </View>
        <Animated.View
          style={[
            {
              opacity: logoFadeAnim,
            },
          ]}
        >
          <Logo style={[appStyles.alignCenter, appStyles.mTMd]} />
        </Animated.View>
      </SafeAreaView>
      <View
        style={[
          appStyles.fullWidth,
          appStyles.hCenter,
          appStyles.bottom,
          appStyles.pHMd,
          appStyles.pBMd,
        ]}
      >
        <View style={[appStyles.row, {width: wp(91.8)}]}>
          <Button
            style={[appStyles.flex, {marginRight: 9, height: 40}]}
            text="onboard.walkthru1.cta.join_us"
            textColor="black"
            backgroundColor="white"
            borderColor="white"
            centered
            onPress={() => {
              offset.value = withTiming(1, {duration: 500});
              navigation.navigate('SignUp', {
                screen: 'SignUp',
                params: {isOnLaunch: true},
              });
            }}
            Din79Font
            textStyle={{
              fontSize: 12,
              fontWeight: '700',
              letterSpacing: 1.62,
            }}
          />
          <Button
            style={[
              appStyles.flex,
              {
                height: 40,
                borderRadius: 24,
                backgroundColor:
                  '#ffffff' + Platform.OS === 'android' ? '70' : '30',
              },
            ]}
            text="onboard.walkthru1.cta.log_in"
            textColor="white"
            borderColor="white"
            onPress={openAuth0}
            centered
            Din79Font
            textStyle={{
              fontSize: 12,
              fontWeight: '700',
              letterSpacing: 1.62,
            }}
            isBlurView={true}
            borderRadiusBlur={20}
          />
        </View>
      </View>
    </View>
  );
};
const mapStateToProps = state => ({
  userSaved: state.user,
});
const mapDispatchToProps = {
  addCurrentUser,
  updateCurrentBasket,
  updatePermissions,
  updateGHINHandicapIndex,
};

export default connect(mapStateToProps, mapDispatchToProps)(Onboarding);
