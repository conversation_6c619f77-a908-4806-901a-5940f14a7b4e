import React, {useEffect, useState} from 'react';
import {View, StyleSheet, Image} from 'react-native';
import Text from 'components/Text';
import appStyles from '../../../styles/global';
import GraphPhoto from '../../../../assets/imgs/graph/graph';
import stylesFont from '../../../components/Text/styles';
import ProgressCircle from 'components/ProgressCircle';
import {useSelector} from 'react-redux';
import {t} from 'i18next';

const PuttingIndex = props => {
  const {data} = props;
  const planContentBatch = useSelector(state => state?.plans?.planContentBatch);

  const [tierName, setTierName] = useState('');
  const [assetGraph, setAssetGraph] = useState(null);

  useEffect(() => {
    const tier = planContentBatch?.puttChallengeTier?.find(
      item => item.id === data?.scoreTierId,
    );
    let assetFileName = '';
    if (tier?.code) {
      setTierName(tier?.name);
      switch (tier?.code) {
        case 'BELOW_AVERAGE':
          if (data.tierPercentile < 25) {
            assetFileName = 'putt0pct_graph';
          } else if (data.tierPercentile < 50) {
            assetFileName = 'putt10pct_graph';
          } else if (data.tierPercentile < 70) {
            assetFileName = 'putt20pct_graph';
          } else {
            assetFileName = 'putt30pct_graph';
          }
          break;
        case 'AVERAGE':
          if (data.tierPercentile < 25) {
            assetFileName = 'putt40pct_graph';
          } else if (data.tierPercentile < 50) {
            assetFileName = 'putt50pct_graph';
          } else if (data.tierPercentile < 70) {
            assetFileName = 'putt60pct_graph';
          } else {
            assetFileName = 'putt70pct_graph';
          }

          break;
        case 'ABOVE_AVERAGE':
          if (data.tierPercentile < 33) {
            assetFileName = 'putt80pct_graph';
          } else if (data.tierPercentile < 66) {
            assetFileName = 'putt90pct_graph';
          } else {
            assetFileName = 'putt100pct_graph';
          }
          break;

        default:
          break;
      }
      setAssetGraph(assetFileName);
    }
  }, [data, planContentBatch]);

  const getSuffixForPercentile = percentile => {
    let adjustedPercentile = Math.floor(percentile * 100);
    let tensPlace = adjustedPercentile / 10;
    let onesPlace = adjustedPercentile % 10;

    switch (tensPlace) {
      case 1:
        return 'th';
      default:
        switch (onesPlace) {
          case 1:
            return 'st';
          case 2:
            return 'nd';
          case 3:
            return 'rd';
          default:
            return 'th';
        }
    }
  };

  const getTextDesScore = value => {
    if (value === 1) {
      return t('putter_index_tier', {
        average: tierName,
        score: data?.challengeScore,
      });
    }
    if (value === 2) {
      return t('putter_index_percentile', {
        percentile1: data?.tierPercentile,
        suffix1: getSuffixForPercentile(data?.tierPercentile),
        percentile2: data?.globalPercentile,
        suffix2: getSuffixForPercentile(data?.globalPercentile),
      });
    }
  };

  const renderGraph = () => {
    return (
      <View
        style={[
          appStyles.whiteBg,
          appStyles.mTMd,
          appStyles.pSm,
          appStyles.borderRadius,
        ]}
      >
        <Text style={[appStyles.bold, appStyles.mBXs, appStyles.black]}>
          putting_challenge.summary
        </Text>
        <Text style={[stylesFont.font, {fontSize: 13}]}>
          {getTextDesScore(1)}
        </Text>
        {!!data?.tierPercentile && !!data?.globalPercentile ? (
          <Text style={[stylesFont.font, {fontSize: 13, marginTop: 30}]}>
            {getTextDesScore(2)}
          </Text>
        ) : null}
        <View style={styles.imgContainer}>
          <Image style={styles.img} source={GraphPhoto[assetGraph]} />
        </View>
      </View>
    );
  };

  return (
    <View style={[appStyles.pHSm, appStyles.mTXs, appStyles.pBSm]}>
      <View
        style={[
          appStyles.whiteBg,
          appStyles.mTMd,
          appStyles.pSm,
          appStyles.borderRadius,
        ]}
      >
        <Text
          DINbold
          style={[
            appStyles.bold,
            appStyles.mBXs,
            appStyles.uppercase,
            {fontSize: 26},
          ]}
        >
          PUTTING_INDEX
        </Text>
        <Text
          style={[appStyles.mBMd, stylesFont.font, {fontSize: 13}]}
          params={{score: data?.indexScore}}
        >
          putting_index_score_des
        </Text>

        <View style={[appStyles.hCenter, appStyles.mBMd]}>
          <ProgressCircle
            progress={(data?.indexScore / 10) * 100 || 0}
            value={data?.indexScore}
            potential={10}
            passingScore={data?.failureThreshold + 1}
            hasBall={false}
            showPotential={true}
          />
        </View>
      </View>

      {renderGraph()}
    </View>
  );
};

export default PuttingIndex;

const styles = StyleSheet.create({
  imgContainer: {
    overflow: 'hidden',
  },
  img: {
    marginTop: -30,
    maxWidth: '100%',
    alignSelf: 'center',
    height: 300,
  },
});
