import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  ScrollView,
  Linking,
  NativeModules,
  Platform,
  StyleSheet,
  TouchableWithoutFeedback,
  TouchableOpacity,
} from 'react-native';
import {connect, useDispatch, useSelector} from 'react-redux';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import FormItemProfile from 'components/FormItemProfile';

import appStyles from 'styles/global';
import {convertBirthday, convertHeight, capFirstLetter} from 'utils/convert';
import {t} from 'i18next';
import {
  ARCCOS,
  COUNTRY_CODE,
  GA_EVENT_NAME,
  getCurrentLocationEnabled,
  MYTMOC,
  PAGE_CATEGORY,
  PAGE_NAME,
  QUIZ_TYPE,
  SCREEN_TYPES,
} from 'utils/constant';
import {CommonActions} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ALREADY_FORCE_LOGOUT} from 'utils/constant';
import {getAuth0Client} from 'utils/auth0';
import {clearUser} from 'reducers/user';
import {clearHomeTiles} from 'reducers/home';
import {clearBasket} from 'reducers/basket';
import {
  clearClubLaunchMonitor,
  clearClubRecommender,
} from 'reducers/clubRecommender';
import {
  clearDrillsCollectionContent,
  clearDrillsForYou,
  clearDrillsSavedContent,
} from 'reducers/drills';
import {clearBagList} from 'reducers/myBag';
import {
  clearCurrentStep,
  clearPlanZeroState,
  clearSwingProfile,
  clearSwingRoadMap,
  setLoadingCoachingPlan,
  clearSIUserProfile,
} from 'reducers/plans';
import {clearQuiz} from 'reducers/quiz';
import {
  clearNearbyCourses,
  clearRecentCourses,
  clearScores,
} from 'reducers/play';
import {clearFocusTab, clearPermissions} from 'reducers/app';
import {clearTTBOrders} from 'reducers/ttb';
import {setAvgScore, setCountry, setLanguage} from 'utils/commonVariable';
import {deleteMyAccount} from 'requests/accounts';
import BottomSheet from 'reanimated-bottom-sheet';
import Text from 'components/Text';
import Button from 'components/Button';
import analytics from '@react-native-firebase/analytics';
import {showToast} from 'utils/toast';
import {moderateScale} from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import {
  clearAsyncStorage,
  getResultPermission,
  saveResultPermission,
} from 'utils/asyncStorage';
import {getAddressStringFromJsonString} from 'utils/user';
import {clearLoyalty} from 'reducers/loyalty';
import IconBack from '../../../assets/imgs/profile/icon_back.svg';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import useAppState from 'hooks/useAppState';
import {
  GA_logEvent,
  GA_setUserProperty,
  GA_logScreenViewV2,
} from 'utils/googleAnalytics';
import Permissions, {
  check,
  PERMISSIONS,
  RESULTS,
} from 'react-native-permissions';
import {checkNotificationPermissions} from 'utils/home';
import {clearTrackingOrders} from 'reducers/trackingOrders';
import {resetTourStory} from 'reducers/dataCache';
import {clearDataTourTrash} from 'reducers/rewards';

const smallScreenIphone = hp(100) < 700 ? true : false;
const Settings = ({navigation}) => {
  const sheetRef = useRef(null);
  const user = useSelector(state => state?.user);
  const [showOverlay, setShowOverlay] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const hasOpenedNotiSettings = useRef(false);
  const hasOpenedLocationSettings = useRef(false);
  const currentNotificationSetting = useRef('enabled');
  const currentLocationSetting = useRef('enabled');
  useEffect(() => {
    checkCurrentNotiAndLocationSettings();
  }, []);

  useEffect(() => {
    if (showOverlay) {
      GA_logScreenViewV2(
        PAGE_NAME.ACCOUNT_DELETE_SETTING,
        PAGE_CATEGORY.ACCOUNT_DELETE,
        SCREEN_TYPES.ACCOUNT,
        SCREEN_TYPES.ACCOUNT,
      );
    }
  }, [showOverlay]);

  const checkCurrentNotiAndLocationSettings = async () => {
    const notificationPermissions = await Permissions.checkNotifications();
    if (notificationPermissions?.status === RESULTS.GRANTED) {
      currentNotificationSetting.current = 'enabled';
    } else {
      currentNotificationSetting.current = 'disabled';
    }

    let isLocationEnabled = await getCurrentLocationEnabled();
    if (isLocationEnabled) {
      currentLocationSetting.current = 'enabled';
    } else {
      currentLocationSetting.current = 'disabled';
    }
  };

  const openSettings = type => {
    if (type === 'notification') {
      hasOpenedNotiSettings.current = true;
      GA_logScreenViewV2(
        PAGE_NAME.ACCOUNT_NOTIFICATIONS_SETTING,
        PAGE_CATEGORY.ACCOUNT_NOTIFICATIONS,
        SCREEN_TYPES.ACCOUNT,
        SCREEN_TYPES.ACCOUNT,
      );
    } else {
      hasOpenedLocationSettings.current = true;
      GA_logScreenViewV2(
        PAGE_NAME.ACCOUNT_LOCATION_SETTING,
        PAGE_CATEGORY.ACCOUNT_LOCATION,
        SCREEN_TYPES.ACCOUNT,
        SCREEN_TYPES.ACCOUNT,
      );
    }
    if (Platform.OS === 'ios') {
      Linking.openURL('app-settings:');
    } else {
      if (type === 'notification') {
        NativeModules.OpenNotificationSettings.open();
      } else {
        NativeModules.OpenLocationSettings.open();
      }
    }
  };

  useAppState({
    onForeground: () => {
      if (hasOpenedNotiSettings.current) {
        checkNotificationPermission();
      }
      if (hasOpenedLocationSettings.current) {
        checkLocationPermission();
      }
    },
  });

  const checkNotificationPermission = async () => {
    const notificationPermissions = await checkNotificationPermissions(user);
    let newSetting = 'enabled';
    if (notificationPermissions) {
      GA_setUserProperty('notifications_enabled', 'true');
      newSetting = 'enabled';
    } else {
      GA_setUserProperty('notifications_enabled', 'false');
      newSetting = 'disabled';
    }
    if (newSetting !== currentNotificationSetting.current) {
      if (newSetting === 'enabled') {
        GA_logEvent(GA_EVENT_NAME.ENABLE_NOTIFICATIONS, {
          callout_name: 'notifications enable',
          click_location: 'settings',
          page_type: SCREEN_TYPES.ACCOUNT,
          page_category: PAGE_CATEGORY.ACCOUNT_NOTIFICATIONS,
          page_name: PAGE_NAME.ACCOUNT_NOTIFICATIONS_SETTING,
          screen_type: SCREEN_TYPES.ACCOUNT,
        });
      }
      GA_logEvent(GA_EVENT_NAME.ACCOUNT_SETTINGS_CHANGED, {
        setting_name: 'notification settings',
        previous_setting: currentNotificationSetting.current,
        new_setting: newSetting,
        page_type: SCREEN_TYPES.ACCOUNT,
        page_category: PAGE_CATEGORY.ACCOUNT_NOTIFICATIONS,
        page_name: PAGE_NAME.ACCOUNT_NOTIFICATIONS_SETTING,
        screen_type: SCREEN_TYPES.ACCOUNT,
      });
      currentNotificationSetting.current = newSetting;
    }
    hasOpenedNotiSettings.current = false;
    hasOpenedLocationSettings.current = false;
  };

  const GA_logEnableLocationEvent = (isSetTrue = true) => {
    if (isSetTrue) {
      GA_logEvent(GA_EVENT_NAME.ENABLE_LOCATION, {
        callout_name: 'location enable',
        click_location: 'settings',
        page_type: SCREEN_TYPES.ACCOUNT,
        page_category: PAGE_CATEGORY.ACCOUNT_LOCATION,
        page_name: PAGE_NAME.SETTINGS,
        screen_type: SCREEN_TYPES.ACCOUNT,
      });
      GA_setUserProperty('location_enabled', 'true');
    } else {
      GA_setUserProperty('location_enabled', 'false');
    }
  };

  const checkLocationPermission = async () => {
    try {
      let newSetting = 'enabled';
      let isLocationEnabled = await getCurrentLocationEnabled();
      if (isLocationEnabled) {
        GA_setUserProperty('location_enabled', 'true');
        newSetting = 'enabled';
      } else {
        GA_logEnableLocationEvent(false);
        newSetting = 'disabled';
      }
      if (newSetting !== currentLocationSetting.current) {
        if (newSetting === 'enabled') {
          GA_logEnableLocationEvent();
        }
        GA_logEvent(GA_EVENT_NAME.ACCOUNT_SETTINGS_CHANGED, {
          setting_name: 'location settings',
          previous_setting: currentLocationSetting.current,
          new_setting: newSetting,
          page_type: SCREEN_TYPES.ACCOUNT,
          page_category: PAGE_CATEGORY.ACCOUNT_LOCATION,
          page_name: PAGE_NAME.ACCOUNT_LOCATION_SETTING,
          screen_type: SCREEN_TYPES.ACCOUNT,
        });
        currentLocationSetting.current = newSetting;
      }
      hasOpenedNotiSettings.current = false;
      hasOpenedLocationSettings.current = false;
    } catch (error) {}
  };

  const logOut = async () => {
    const auth0 = await getAuth0Client();
    const resultPermission = await getResultPermission();
    // Clear redux, async storage, and auth0
    clearUser();
    clearHomeTiles();
    clearBasket();
    clearClubRecommender();
    clearClubLaunchMonitor();
    clearDrillsForYou();
    clearDrillsSavedContent();
    clearBagList();
    clearSwingProfile();
    clearSwingRoadMap();
    clearCurrentStep();
    clearQuiz();
    clearPlanZeroState();
    clearScores();
    clearRecentCourses();
    clearNearbyCourses();
    clearPermissions();
    clearDrillsCollectionContent();
    clearTTBOrders();
    setLoadingCoachingPlan(false);
    clearSIUserProfile();
    setAvgScore(0);
    clearLoyalty();
    clearTrackingOrders();
    clearFocusTab();
    resetTourStory();
    clearDataTourTrash();
    auth0.webAuth.clearSession();
    await clearAsyncStorage();
    await AsyncStorage.setItem(ALREADY_FORCE_LOGOUT, 'true');
    await saveResultPermission(resultPermission);
    setCountry(COUNTRY_CODE.USA);
    setLanguage('en');
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{name: 'Onboarding'}],
      }),
    );
  };

  const deleteAccount = async () => {
    try {
      setLoadingDelete(true);
      const deleteRs = await deleteMyAccount();
      GA_logEvent(GA_EVENT_NAME.ACCOUNT_DELETED, {
        page_type: SCREEN_TYPES.ACCOUNT,
        page_category: PAGE_CATEGORY.ACCOUNT_DELETE,
        page_name: PAGE_NAME.ACCOUNT_DELETE_SETTING,
        screen_type: SCREEN_TYPES.ACCOUNT,
      });
      logOut();
      setLoadingDelete(false);
    } catch (error) {
      setLoadingDelete(false);
      showToast({
        type: 'error',
        message: t('An_error_occurred_deleting_your_account'),
      });
    }
  };

  return (
    <View style={[appStyles.flex, appStyles.whiteBg]}>
      <View
        style={[
          appStyles.row,
          appStyles.hCenter,
          {marginLeft: 16, marginTop: Platform.OS === 'android' ? '5%' : '11%'},
        ]}
      >
        <TouchableOpacity
          style={{marginRight: 10}}
          onPress={() => {
            navigation.navigate('Profile', {screen: 'Profile'});
          }}
        >
          <IconBack />
        </TouchableOpacity>
        <View>
          <Text
            size={22}
            black
            Din79Font
            weight={'800'}
            style={{letterSpacing: 1.1, textTransform: 'uppercase'}}
          >
            {t('profile.settings')}
          </Text>
        </View>
      </View>
      <ScrollView style={appStyles.flex}>
        <FocusAwareStatusBar barStyle={'dark-content'} />
        <View style={{marginTop: 20}}>
          <FormItemProfile
            label={t('settings.headline.first_name')}
            value={user?.firstName}
            bottomBorder
            topBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
          <FormItemProfile
            label={t('settings.headline.last_name')}
            value={user?.lastName}
            bottomBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
          <FormItemProfile
            label={t('settings.headline.email')}
            value={user?.email}
            bottomBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
          <FormItemProfile
            label={t('settings.headline.gender')}
            value={capFirstLetter(user?.gender)}
            onPress={() =>
              navigation.navigate('IntroQuiz', {
                screen: 'UserQuiz',
                params: {
                  origin: 'Setting',
                  quizType: QUIZ_TYPE.GENDER,
                  gender: user?.gender,
                },
              })
            }
            icon="chevron-right"
            iconColor="black"
            bottomBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
          <FormItemProfile
            label={t('settings.headline.distance_preference')}
            value={capFirstLetter(user?.measurementUnits)}
            onPress={() =>
              navigation.navigate('IntroQuiz', {
                screen: 'UserQuiz',
                params: {
                  origin: 'Setting',
                  quizType: QUIZ_TYPE.DISTANCE,
                  measurementUnits: user?.measurementUnits,
                },
              })
            }
            icon="chevron-right"
            iconColor="black"
            bottomBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
          <FormItemProfile
            label="settings.headline.Location_settings"
            icon="chevron-right"
            onPress={() => openSettings('location')}
            bottomBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
          <FormItemProfile
            label={t('settings.headline.notification_settings')}
            icon="chevron-right"
            onPress={() => openSettings('notification')}
            bottomBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
          <FormItemProfile
            label={t('settings.term_services')}
            icon="chevron-right"
            onPress={() => {
              navigation.navigate('WebView', {
                screen: 'WebView',
                params: {
                  title: t('settings.term_conditions'),
                  uri: 'https://www.taylormadegolf.com/mytaylormadeplus-terms-and-conditions.html?lang=en_US',
                  origin: 'TERMCONDITION',
                  canGoBack: true,
                },
              });
            }}
            bottomBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
          <FormItemProfile
            label={t('settings.privacy_policy')}
            icon="chevron-right"
            onPress={() =>
              navigation.navigate('WebView', {
                screen: 'WebView',
                params: {
                  title: t('settings.privacy_policy'),
                  uri: 'https://www.taylormadegolf.com/shared-customer-service-customer-legal/privacy.html?lang=en_US',
                  origin: 'POLICY',
                  canGoBack: true,
                },
              })
            }
            bottomBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
          <FormItemProfile
            label={t('settings.headline.reset_password')}
            icon="chevron-right"
            onPress={() =>
              navigation.navigate('ResetPassword', {screen: 'ResetPassword'})
            }
            bottomBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
          <FormItemProfile
            label={t('settings.delete_account')}
            onPress={() => {
              sheetRef.current?.snapTo(0);
              setShowOverlay(true);
            }}
            bottomBorder
            borderBottomWidth={1}
            borderBottomColor={'#********'}
            style={styles.viewItem}
          />
        </View>
      </ScrollView>
      {showOverlay ? (
        <TouchableWithoutFeedback
          onPress={() => {
            sheetRef.current?.snapTo(1);
            setShowOverlay(false);
          }}
        >
          <View
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(0,0,0,0.7)',
            }}
          />
        </TouchableWithoutFeedback>
      ) : null}
      <BottomSheet
        ref={sheetRef}
        snapPoints={[smallScreenIphone ? '55%' : '50%', 0]}
        borderRadius={appStyles.borderRadius.borderRadius}
        initialSnap={1}
        onCloseEnd={() => {
          setShowOverlay(false);
        }}
        renderHeader={() => (
          <TouchableOpacity
            onPress={() => {
              sheetRef.current?.snapTo(1);
              setShowOverlay(false);
            }}
            style={[
              appStyles.mLAuto,
              appStyles.mRXxs,
              {paddingBottom: '3%', alignItems: 'flex-end'},
            ]}
          >
            <View
              style={[
                styles.closeIcon,
                appStyles.vCenter,
                appStyles.hCenter,
                appStyles.mRXs,
              ]}
            >
              <Icon name="x" size={moderateScale(18)} />
            </View>
          </TouchableOpacity>
        )}
        renderContent={() => (
          <View
            style={[
              appStyles.fullHeight,
              appStyles.whiteBg,
              appStyles.hCenter,
              {paddingHorizontal: '4%', paddingVertical: '6%'},
            ]}
          >
            <View style={[appStyles.pHSm, {height: '21%'}]}>
              <Text
                style={[
                  appStyles.mdms,
                  appStyles.textCenter,
                  appStyles.green,
                  appStyles.bold,
                ]}
              >
                settings.delete_account.delete_title
              </Text>
            </View>
            <View style={[{paddingHorizontal: '2%', height: '22%'}]}>
              <Text
                style={[appStyles.textCenter, appStyles.grey, appStyles.xsm]}
              >
                settings.delete_account.delete_message
              </Text>
            </View>
            <Button
              text={t('settings.delete_account.alert.delete')}
              textColor="white"
              backgroundColor={'black'}
              borderColor={'black'}
              onPress={deleteAccount}
              centered
              DINbold
              style={[appStyles.mTMd, {width: '100%', marginBottom: '6%'}]}
              loading={loadingDelete}
              loadingMode="dark"
            />
            <TouchableOpacity
              onPress={() => {
                sheetRef.current?.snapTo(1);
                setShowOverlay(false);
              }}
              style={{height: '17%'}}
            >
              <Text
                style={[appStyles.underlined, appStyles.xsm, appStyles.black]}
              >
                settings.delete_account.alert.cancel
              </Text>
            </TouchableOpacity>
            <View style={[appStyles.row, appStyles.vCenter, appStyles.hCenter]}>
              <Text>
                <Text
                  style={[
                    appStyles.green,
                    appStyles.xs,
                    appStyles.underlined,
                    appStyles.semiBold,
                  ]}
                  onPress={async () => {
                    navigation.navigate('WebView', {
                      screen: 'WebView',
                      params: {
                        title: t(
                          'settings.delete_account.webview_title.contact_us',
                        ),
                        uri: 'https://help.taylormadegolf.com',
                        origin: 'DELETE_SUPPORT',
                        canGoBack: true,
                      },
                    });
                    await analytics().logEvent('profile_cta_open', {
                      name: 'Contact us',
                    });
                    await analytics().logEvent('cta_open', {
                      name: 'Contact us',
                    });
                  }}
                >
                  {t('settings.delete_account.contact_us_link')}
                </Text>

                <Text style={[appStyles.xs, appStyles.black]}>
                  {''} {t('settings.delete_account.contact_us_to_help')}
                </Text>
              </Text>
            </View>
          </View>
        )}
      />
    </View>
  );
};

const mapStateToProps = state => ({
  user: state.user,
  clearUser,
  clearHomeTiles,
  clearBasket,
  clearClubRecommender,
  clearClubLaunchMonitor,
  clearDrillsForYou,
  clearDrillsSavedContent,
  clearBagList,
  clearSwingProfile,
  clearSwingRoadMap,
  clearCurrentStep,
  clearQuiz,
  clearPlanZeroState,
  clearScores,
  clearRecentCourses,
  clearNearbyCourses,
  clearPermissions,
  clearDrillsCollectionContent,
  setLoadingCoachingPlan,
  clearTTBOrders,
  clearSIUserProfile,
  clearLoyalty,
  resetTourStory,
  clearDataTourTrash,
});

const styles = StyleSheet.create({
  closeIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    width: moderateScale(24),
    height: moderateScale(24),
    borderRadius: moderateScale(12),
  },
  viewItem: {
    paddingTop: Platform.OS === 'android' || smallScreenIphone ? 14 : 18,
    paddingBottom: Platform.OS === 'android' || smallScreenIphone ? 14 : 18,
    paddingHorizontal: 16,
  },
});

export default connect(mapStateToProps, null)(Settings);
