import React, {useState, useEffect} from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Keyboard,
  Animated,
  Platform,
} from 'react-native';
import appStyles from '/styles/global';
import {connect} from 'react-redux';
import {addCurrentUser} from '/reducers/user';
import {getAllPlayer, addUpdatePlayer, deletePlayer} from 'utils/realmHelper';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import ButtonPlusMinus from 'screens/PlayCourseMap/components/ButtonPlusMinus';
import TextInput from 'components/TextInput';
import IconAnt from 'react-native-vector-icons/AntDesign';
import uuid from 'react-native-uuid';
import Text from 'components/Text';
import DeviceInfo from 'react-native-device-info';
import {TouchableWithoutFeedback} from 'react-native-gesture-handler';
import Icon from 'react-native-vector-icons/Ionicons';
import {getNameInitials} from 'screens/PlayCourseMap/DataSubmitDefault';
import {showToast} from 'utils/toast';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {NestableScrollContainer} from 'react-native-draggable-flatlist';
import {
  useSafeAreaInsets,
  initialWindowMetrics,
} from 'react-native-safe-area-context';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';

const isTablet = DeviceInfo.isTablet();

const MyFourSome = ({navigation, addCurrentUser}) => {
  const [listSaved, setListSaved] = useState([]);
  const [isAdding, setAdding] = useState(false);
  let swipedCardRef = null;
  let rowRefs = new Map();
  const [currentPlayer, setCurrentPlayer] = useState({
    placeholder: 'Player Name',
    name: '',
    strokes: 0,
    id: uuid.v4(),
    initials: '',
  });
  useEffect(() => {
    getData();
  }, []);

  const insets = useSafeAreaInsets();
  const topInset =
    Platform.OS === 'ios' ? insets?.top : initialWindowMetrics?.insets?.top;

  const getData = async () => {
    const data = await getAllPlayer();
    data.sort((a, b) => {
      let timeA = new Date(a.createAt);
      let timeB = new Date(b.createAt);
      return timeB - timeA;
    });
    setListSaved(data);
  };

  const onEditPlayer = playerIndex => {
    try {
      const newListPlayer = listSaved.map((item, index) => {
        if (index === playerIndex) {
          item.isEditing = true;
          setCurrentPlayer(item);
        } else {
          item.isEditing = false;
        }
        return item;
      });
      setListSaved([...newListPlayer]);
    } catch (error) {
      console.log(error);
    }
  };

  const onMinus = () => {
    try {
      let currentStroke = currentPlayer.strokes;
      if (currentStroke > 0) {
        setCurrentPlayer({...currentPlayer, strokes: +currentStroke - 1});
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onPlus = () => {
    try {
      let currentStroke = Math.trunc(currentPlayer.strokes);
      if (currentStroke < 42) {
        setCurrentPlayer({...currentPlayer, strokes: +currentStroke + 1});
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onCancelEditPlayer = index => {
    try {
      const item = listSaved[index];
      item.isEditing = false;
      setListSaved([...listSaved]);
    } catch (error) {
      console.log('error', error);
    }
  };

  const onPressSavePlayer = async playerIndex => {
    if (currentPlayer.name !== '') {
      // const savedPlayer = await getAllPlayer();
      let currentName = currentPlayer.name?.toString().trim();
      let newListPlayer = [...listSaved];
      // let listToCheckDuplicate = newListPlayer.concat(savedPlayer);
      let listToCheckDuplicate = newListPlayer;
      const checkIfNameExist = listToCheckDuplicate.find(
        item =>
          item.name?.toString().trim() === currentName &&
          item.id !== currentPlayer.id,
      );
      let incomingName = currentName;
      //check if the input name exist or not
      if (checkIfNameExist) {
        let suffixNumber = 2;
        incomingName = currentName + `(${suffixNumber})`;
        let checkIfModNameExist = listToCheckDuplicate.find(
          item =>
            item.name?.toString().trim() === incomingName &&
            item.id !== currentPlayer.id,
        );
        //check if the modified name exist or not
        while (checkIfModNameExist) {
          suffixNumber += 1;
          incomingName = currentName + `(${suffixNumber})`;
          checkIfModNameExist = listToCheckDuplicate.find(
            item =>
              item.name?.toString().trim() === incomingName &&
              item.id !== currentPlayer.id,
          );
        }
      }

      if (playerIndex >= 0) {
        newListPlayer = newListPlayer.map((item, index) => {
          if (playerIndex === index) {
            item = {
              ...currentPlayer,
              initials: getNameInitials(incomingName),
              name: incomingName,
            };
          }
          addUpdatePlayer(item);
          return item;
        });
      } else {
        const newPlayer = {
          ...currentPlayer,
          initials: getNameInitials(incomingName),
          name: incomingName,
          id: uuid.v4(),
          createAt: new Date(),
        };
        newListPlayer = [...listSaved, newPlayer];
        addUpdatePlayer(newPlayer);
      }
      setAdding(false);
      setCurrentPlayer({
        placeholder: `Player ${newListPlayer.length + 1} Name`,
        name: '',
        strokes: 0,
        initials: '',
      });

      newListPlayer = newListPlayer.map((item, index) => {
        item.isEditing = false;
        return item;
      });
      setListSaved([...newListPlayer]);
      return showToast({
        type: 'success',
        message:
          playerIndex >= 0
            ? 'Update Player Successfully!'
            : 'Add Player Successfully',
      });
    }
  };

  const onCancelAddPlayer = () => {
    try {
      setAdding(false);
    } catch (error) {
      console.log('error', error);
    }
  };

  const onStartAddPlayer = () => {
    let newListPlayer = listSaved.map((item, index) => {
      item.isEditing = false;
      return item;
    });
    setListSaved([...newListPlayer]);
    setCurrentPlayer({
      placeholder: `Player Name`,
      name: '',
      strokes: 0,
      initials: '',
    });

    setAdding(true);
  };

  const renderRightAction = (text, color, x, progress, itemSelected) => {
    const trans = progress.interpolate({
      inputRange: [0, 1],
      outputRange: [x - 20, -20],
    });
    const pressHandler = async () => {
      try {
        let deleteId = itemSelected.id;
        await deletePlayer(deleteId);
        let listCurrentSaved = [...listSaved];
        let deleteIndex = listCurrentSaved.findIndex(
          item => item.id === deleteId,
        );

        if (deleteIndex >= 0) {
          listCurrentSaved.splice(deleteIndex, 1);
          setListSaved([...listCurrentSaved]);
        }
        return showToast({
          type: 'success',
          message: 'Delete Player Successfully!',
        });
      } catch (error) {
        console.log('error', error);
      }
    };

    return (
      <Animated.View
        style={{
          transform: [{translateX: trans}],
          ...styles.itemPlayerView,
        }}
      >
        <View style={{width: 10, backgroundColor: 'white'}} />
        <TouchableOpacity
          style={[
            styles.rightAction,
            {
              backgroundColor: color,
            },
          ]}
          onPress={pressHandler}
        >
          <Icon name={'trash-outline'} size={30} color={'white'} />
          <Text size={13} style={styles.actionText}>
            {text}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderRightActions = (progress, _dragAnimatedValue, itemSelected) => (
    <View style={[styles.rightActionContainer]}>
      {renderRightAction(
        'Delete',
        'rgba(rgba(255, 0, 0, 1)',
        80,
        progress,
        itemSelected,
      )}
    </View>
  );
  const onOpen = ref => {
    if (swipedCardRef) {
      swipedCardRef.close();
    }
  };
  const onClose = ref => {
    if (ref === swipedCardRef) {
      swipedCardRef = null;
    }
  };

  const renderAddPlayer = (index = -1) => {
    return (
      <View
        key={index}
        style={[
          appStyles.row,
          appStyles.viewShadow,
          appStyles.spaceBetween,
          appStyles.hCenter,
          styles.addPlayerView,
        ]}
      >
        <View
          style={[{flex: 0.62, paddingBottom: currentPlayer.isEditing ? 9 : 0}]}
        >
          <View>
            <TextInput
              style={[styles.addPlayerInputContainer, {marginBottom: 12}]}
              placeholder={currentPlayer.placeholder}
              placeholderTextColor={'#BDBDBD'}
              onChangeText={value =>
                setCurrentPlayer({...currentPlayer, name: value})
              }
              defaultValue={currentPlayer.name}
              textContentType={'name'}
              inputProps={{maxLength: 40, autoFocus: index >= 0 ? false : true}}
              stylesInput={styles.addPlayerInput}
              // clearInput={clearSearch}
              autoCapitalize={'words'}
            />
          </View>
          <View style={[appStyles.row, appStyles.hCenter, {width: '75%'}]}>
            <ButtonPlusMinus
              isMinus
              onPress={onMinus}
              buttonStyle={{borderColor: 'black', borderWidth: 2}}
              buttonSize={32}
            />
            <View style={[appStyles.flex, appStyles.hCenter]}>
              <Text style={[{color: '#8C8B8F', fontSize: 13}]}>
                play.player_strokes
              </Text>
              <Text style={{fontWeight: 'bold', fontSize: 24}}>
                {currentPlayer.strokes}
              </Text>
            </View>

            <ButtonPlusMinus
              onPress={onPlus}
              buttonStyle={{borderColor: 'black', borderWidth: 2}}
              buttonSize={32}
            />
          </View>
        </View>
        <View
          style={[
            appStyles.spaceEnd,
            appStyles.alignEnd,
            {flex: 0.38, paddingTop: 15},
          ]}
        >
          <View
            style={[
              appStyles.hCenter,
              currentPlayer.isEditing
                ? appStyles.spaceBetween
                : appStyles.spaceEnd,
              appStyles.alignEnd,
              appStyles.flex,
            ]}
          >
            <TouchableOpacity
              style={[
                styles.touchEdit,
                appStyles.vCenter,
                appStyles.hCenter,
                {
                  backgroundColor: 'black',
                  borderRadius: 22,
                  marginTop: 20,
                },
              ]}
              onPress={() => onPressSavePlayer(index)}
            >
              <Text size={13} white style={{fontWeight: 'bold'}}>
                {currentPlayer.isEditing
                  ? 'play.button.save_player'
                  : 'play.button.add_player'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <View style={[appStyles.alignEnd, styles.clearIconView]}>
          <TouchableWithoutFeedback
            onPress={() => {
              Keyboard.dismiss();
              currentPlayer?.isEditing
                ? onCancelEditPlayer(index)
                : onCancelAddPlayer();
            }}
          >
            <Icon
              name={'close-outline'}
              color={'#000'}
              size={isTablet ? wp('3%') : wp('7%')}
            />
          </TouchableWithoutFeedback>
        </View>
      </View>
    );
  };

  const renderAddItemPlayer = () => {
    return (
      <View
        key={0}
        style={[appStyles.viewShadow, styles.playerCardView, appStyles.row]}
      >
        <View style={[appStyles.column]}>
          <Text size={24} black style={{fontWeight: 'bold'}}>
            profile.user_profile.my_foursome.add_a_player
          </Text>
          <Text size={13} style={styles.strokes}>
            profile.user_profile.my_foursome.strokes_0
          </Text>
        </View>
        <View
          style={[{position: 'absolute', right: 20}, appStyles.alignCenter]}
        >
          <TouchableOpacity onPress={() => onStartAddPlayer()}>
            <IconAnt name={'plus'} size={45} color={'#111111'} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderItemPlayer = ({item, index}) => {
    if (item.isEditing) {
      return renderAddPlayer(index);
    } else {
      return (
        <Swipeable
          friction={2}
          leftThreshold={80}
          enableTrackpadTwoFingerGesture
          rightThreshold={10}
          renderRightActions={(progress, drag) =>
            renderRightActions(progress, drag, item)
          }
          onSwipeableOpen={onOpen}
          onSwipeableClose={onClose}
          onSwipeableWillOpen={() => {
            [...rowRefs.entries()].forEach(([key, ref]) => {
              if (key !== item.id && ref) {
                ref.close();
              }
            });
          }}
          ref={ref => {
            if (ref && !rowRefs.get(item.id)) {
              rowRefs.set(item.id, ref);
            }
          }}
          key={item.name + index}
          //   containerStyle={{paddingTop: 2}}
        >
          <View
            key={item.id}
            style={[appStyles.viewShadow, styles.playerCardView, appStyles.row]}
          >
            <View style={[appStyles.column]}>
              <Text size={24} black style={{fontWeight: 'bold'}}>
                {item.name}
              </Text>
              <Text size={13} style={styles.strokes}>
                {`Strokes ${item.strokes}`}
              </Text>
            </View>
            <View
              style={[{position: 'absolute', right: 20}, appStyles.alignCenter]}
            >
              <TouchableOpacity
                style={[styles.touchEdit]}
                onPress={() => onEditPlayer(index)}
              >
                <Text size={13} black style={{fontWeight: 'bold'}}>
                  play.button.edit_player
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Swipeable>
      );
    }
  };

  return (
    <View style={[appStyles.flex]}>
      <NestableScrollContainer>
        <KeyboardAwareScrollView
          contentContainerStyle={{
            flex: 1,
          }}
          style={{minHeight: hp('100%') - topInset - wp('7%')}}
          extraScrollHeight={hp(14)}
          nestedScrollEnabled
          scrollEnabled={true}
        >
          <View style={styles.listPlayerContainer}>
            <FlatList
              data={listSaved}
              keyExtractor={item => item?.id}
              renderItem={renderItemPlayer}
            />
            {isAdding ? renderAddPlayer() : renderAddItemPlayer()}
          </View>
        </KeyboardAwareScrollView>
      </NestableScrollContainer>
    </View>
  );
};

const mapDispatchToProps = {addCurrentUser};
const styles = StyleSheet.create({
  playerCardView: {
    backgroundColor: '#ffffff',
    padding: 15,
    paddingVertical: 20,
    borderRadius: 8,
    marginHorizontal: 10,
    marginBottom: 10,
  },
  itemPlayerView: {
    marginBottom: 10,
    flex: 1,
    flexDirection: 'row',
  },
  strokes: {color: '#8C8B8F', paddingTop: 3},
  listPlayerContainer: {paddingVertical: 20},
  rightAction: {
    alignItems: 'center',
    width: 80,
    justifyContent: 'center',
    borderRadius: 8,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
  },
  actionText: {
    color: 'white',
    marginTop: 2,
  },
  rightActionContainer: {
    width: 80,
    flexDirection: 'row',
  },
  touchEdit: {
    borderWidth: 2,
    borderRadius: 25,
    paddingVertical: 10,
    paddingHorizontal: wp(6),
  },
  addPlayerInputContainer: {
    borderWidth: 0,
    paddingLeft: 0,
    marginBottom: 0,
    marginTop: 0,
    paddingRight: 0,
    height: 'auto',
  },
  addPlayerInput: {
    fontSize: 24,
    color: 'black',
    fontWeight: 'bold',
  },
  addPlayerView: {
    backgroundColor: '#ffffff',
    paddingLeft: 15,
    paddingRight: 18,
    paddingTop: 17,
    paddingBottom: 25,
    borderRadius: 8,
    marginBottom: 10,
    marginHorizontal: 10,
  },
  clearIconView: {
    position: 'absolute',
    top: 5,
    right: 5,
  },
});

export default connect(null, mapDispatchToProps)(MyFourSome);
