import React, {useState, useCallback, useEffect, useRef} from 'react';
import {FlatList, ScrollView, StyleSheet, View} from 'react-native';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import appStyles from 'styles/global';
import {useDispatch, useSelector} from 'react-redux';
import {
  getApproachStats,
  getApproachStatsByCourses,
  getApproachStatsByDate,
  getApproachStatsByRoundMode,
  getApproachStatsByRounds,
  getApproachStatsProximity,
  getApproachStatsProximityByCourses,
  getApproachStatsProximityByDate,
  getApproachStatsProximityByRoundMode,
  getApproachStatsProximityByRounds,
} from 'requests/play-stats';
import LoadingOverlay from 'components/LoadingOverlay';
import Text from 'components/Text';
import ApproachStatsOverall from 'screens/Stats/components/ApproachStats/ApproachStatsOverall';
import {TouchableOpacity} from 'react-native-gesture-handler';
import { GRAY_BACKGROUND } from 'config';

const DATA = [
  {id: 1, label: '100-150 yds', selected: true},
  {id: 2, label: '150-200 yds', selected: false},
  {id: 3, label: '200-250 yds', selected: false},
  {id: 4, label: '>250 yds', selected: false},
];

function ApproachStatsTabScreen({navigation, route}) {
  const [loading, setLoading] = useState(false);
  const [dataOverall, setDataOverall] = useState(null);
  const [dataProximity, setDataProximity] = useState(null);

  const [dataOverallApproach, setDataOverallApproach] = useState(null);
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );
  const [itemSelected, setItemSelected] = useState(DATA[0].id);
  const filterData = route?.params?.filterData;

  useEffect(() => {
    getApproachStatsData();
    getOverallData();
  }, []);

  useEffect(() => {
    if (itemSelected && dataOverall) {
      switch (itemSelected) {
        case 1:
          setDataOverallApproach(dataOverall?.dt100150);
          break;
        case 2:
          setDataOverallApproach(dataOverall?.dt150200);
          break;
        case 3:
          setDataOverallApproach(dataOverall?.dt200250);
          break;
        case 4:
          setDataOverallApproach(dataOverall?.dt250);
          break;
        default:
          break;
      }
    }
  }, [dataOverall, itemSelected]);

  const getOverallData = async () => {
    let dataAll = {};
    try {
      switch (filterData?.type) {
        case 'D':
          dataAll = await getApproachStatsByDate(
            playService,
            filterData?.startDate,
            filterData?.endDate,
          );
          break;
        case 'R':
          dataAll = await getApproachStatsByRounds(
            playService,
            filterData?.filterRounds?.map(item => item.roundId),
          );
          break;
        case 'C':
          dataAll = await getApproachStatsByCourses(
            playService,
            filterData?.filterCourses?.map(item => item.igolfCourseId),
          );
          break;
        case 'M':
          dataAll = await getApproachStatsByRoundMode(
            playService,
            filterData?.roundMode,
          );
          break;
        default:
          dataAll = await getApproachStats(playService);
          break;
      }
      if (dataAll) {
        let dtOverall = {
          dt100150: {
            strokesGainedByRound: dataAll.strokesGainedByRound?.['100150'],
            strokesGainedByShot: dataAll.strokesGainedByShot?.['100150'],
            greensHit: dataAll.greensHit?.['100150'],
            missedLeft: dataAll.missedLeft?.['100150'],
            missedRight: dataAll.missedRight?.['100150'],
            missedShort: dataAll.missedShort?.['100150'],
            missedLong: dataAll.missedLong?.['100150'],
          },
          dt150200: {
            strokesGainedByRound: dataAll.strokesGainedByRound?.['150200'],
            strokesGainedByShot: dataAll.strokesGainedByShot?.['150200'],
            greensHit: dataAll.greensHit?.['150200'],
            missedLeft: dataAll.missedLeft?.['150200'],
            missedRight: dataAll.missedRight?.['150200'],
            missedShort: dataAll.missedShort?.['150200'],
            missedLong: dataAll.missedLong?.['150200'],
          },
          dt200250: {
            strokesGainedByRound: dataAll.strokesGainedByRound?.['200250'],
            strokesGainedByShot: dataAll.strokesGainedByShot?.['200250'],
            greensHit: dataAll.greensHit?.['200250'],
            missedLeft: dataAll.missedLeft?.['200250'],
            missedRight: dataAll.missedRight?.['200250'],
            missedShort: dataAll.missedShort?.['200250'],
            missedLong: dataAll.missedLong?.['200250'],
          },
          dt250: {
            strokesGainedByRound: dataAll.strokesGainedByRound?.['250'],
            strokesGainedByShot: dataAll.strokesGainedByShot?.['250'],
            greensHit: dataAll.greensHit?.['250'],
            missedLeft: dataAll.missedLeft?.['250'],
            missedRight: dataAll.missedRight?.['250'],
            missedShort: dataAll.missedShort?.['250'],
            missedLong: dataAll.missedLong?.['250'],
          },
        };
        setDataOverall(dtOverall);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const getApproachStatsData = async (distanceIndex = 0) => {
    let dtProximity = {};
    let distance = '';
    switch (distanceIndex) {
      case 0:
        distance = '100-150';
        break;
      case 1:
        distance = '150-200';
        break;
      case 2:
        distance = '200-250';
        break;
      case 3:
        distance = '250-1000';
        break;
      default:
        break;
    }
    setLoading(true);
    try {
      switch (filterData?.type) {
        case 'D':
          dtProximity = await getApproachStatsProximityByDate(
            playService,
            filterData?.startDate,
            filterData?.endDate,
            distance,
          );
          break;
        case 'R':
          dtProximity = await getApproachStatsProximityByRounds(
            playService,
            filterData?.filterRounds?.map(item => item.roundId),
            distance,
          );
          break;
        case 'C':
          dtProximity = await getApproachStatsProximityByCourses(
            playService,
            filterData?.filterCourses?.map(item => item.igolfCourseId),
            distance,
          );
          break;
        case 'M':
          dtProximity = await getApproachStatsProximityByRoundMode(
            playService,
            filterData?.roundMode,
            distance,
          );
          break;
        default:
          dtProximity = await getApproachStatsProximity(playService, distance);
          break;
      }
      setDataProximity(dtProximity);
      setLoading(false);
    } catch (error) {
      console.log('er', error);
      setLoading(false);
    }
  };

  const handleSetItemSelected = item => {
    setItemSelected(item.id);
    switch (item.id) {
      case 1:
        getApproachStatsData();
        break;
      case 2:
        getApproachStatsData(1);
        break;
      case 3:
        getApproachStatsData(2);
        break;
      case 4:
        getApproachStatsData(3);
        break;
      default:
        break;
    }
  };

  const renderApproachStatsTab = ({item}) => {
    return (
      <TouchableOpacity
        onPress={() => handleSetItemSelected(item)}
        style={[
          appStyles.hCenter,
          appStyles.vCenter,
          styles.statsTab,
          {
            backgroundColor: itemSelected === item.id ? '#3ABA56' : 'white',
          },
        ]}
      >
        <Text
          style={[
            appStyles.pHSm,
            appStyles.pVXs,
            itemSelected === item.id ? appStyles.white : appStyles.black,
            {fontSize: 13, fontWeight: '600'},
          ]}
        >
          {item.label}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={{backgroundColor: GRAY_BACKGROUND}}>
      {loading ? <LoadingOverlay transparent={loading} /> : null}
      <ScrollView>
        <FlatList
          horizontal={true}
          data={DATA}
          keyExtractor={item => item.id}
          renderItem={renderApproachStatsTab}
          showsHorizontalScrollIndicator={false}
          style={[{marginLeft: 15, marginTop: 15}]}
        />
        <View style={[appStyles.mTSm]}>
          <ApproachStatsOverall
            dataOverall={dataOverallApproach}
            dataProximity={dataProximity}
          />
        </View>
      </ScrollView>
    </View>
  );
}
const styles = StyleSheet.create({
  statsTab: {
    width: 118,
    height: 36,
    borderRadius: 22,
    marginRight: 10,
  },
});

export default ApproachStatsTabScreen;
