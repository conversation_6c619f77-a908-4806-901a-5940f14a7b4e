import React, {useState, useCallback, useRef, useEffect, useMemo} from 'react';
import {
  View,
  ScrollView,
  SafeAreaView,
  StyleSheet,
  Platform,
} from 'react-native';
import appStyles from 'styles/global';
import Text from 'components/Text';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';

import {GREEN_COLUMN} from 'config';
import StrokesGainedView from '../StrokesGainedView';
import GreenAccuracy from './GreenAccuracy';
const DASH_WIDTH = 0.75;
const STROKE_SIZE = 6.5; //Screen width percent
const CHART_HEIGHT = 100; //Screen width percent
const CHART_WIDTH = 100;
const CHART_BOX_WIDTH = 88;
const CHART_BOX_HEIGHT = 88;
const TEXT_SIZE = hp(1.9);
const TEXT_RATIO_BY_CHART_HEIGHT = ((TEXT_SIZE + 2) / wp(CHART_HEIGHT)) * 100; //TEXT_SIZE+2 cause the actual text height is more 2 pixel than the hp() result
const Y_AXIS_INTERVAL = 12.5;

const ApproachStatsOverall = ({
  navigation,
  dataOverall,
  dataProximity,
  chartType = 'Approach',
}) => {
  const CHART_MIN_VALUE = chartType === 'Approach' ? 80 : 40;
  const xAxis = [
    {value: '-80', leftPos: 8},
    {value: '-60', leftPos: 19},
    {value: '-40', leftPos: 30},
    {value: '-20', leftPos: 41},
    {value: '0', leftPos: 52},
    {value: '20', leftPos: 63},
    {value: '40', leftPos: 74},
    {value: '60', leftPos: 85},
    {value: '80', leftPos: 96},
  ];
  const firstPosition =
    0 -
    TEXT_RATIO_BY_CHART_HEIGHT / 2 -
    (Platform.OS === 'ios' ? (TEXT_RATIO_BY_CHART_HEIGHT * 15) / 100 : 0); //because the DINBold in IOS, the text has small bottom padding, about 30%
  const yAxis = [
    {value: '-80', yPos: `${firstPosition}%`},
    {value: '-60', yPos: `${firstPosition + Y_AXIS_INTERVAL}%`},
    {value: '-40', yPos: `${firstPosition + Y_AXIS_INTERVAL * 2}%`},
    {value: '-20', yPos: `${firstPosition + Y_AXIS_INTERVAL * 3}%`},
    {value: '0', yPos: `${firstPosition + Y_AXIS_INTERVAL * 4}%`},
    {value: '20', yPos: `${firstPosition + Y_AXIS_INTERVAL * 5}%`},
    {value: '40', yPos: `${firstPosition + Y_AXIS_INTERVAL * 6}%`},
    {value: '60', yPos: `${firstPosition + Y_AXIS_INTERVAL * 7}%`},
    {value: '80', yPos: `${firstPosition + Y_AXIS_INTERVAL * 8}%`},
  ];
  const maxProximity =
    Math.max(dataProximity?.average, dataProximity?.proAverage) ||
    CHART_MIN_VALUE;
  let maxAxisValue = 0;
  maxAxisValue = Math.ceil(maxProximity / 4 / 5) * 5 * 4;
  if (maxAxisValue <= CHART_MIN_VALUE) {
    maxAxisValue = CHART_MIN_VALUE;
  }

  //rewrite xAxis values
  xAxis[0].value = maxAxisValue * -1;
  xAxis[1].value = ((maxAxisValue * 3) / 4) * -1;
  xAxis[2].value = ((maxAxisValue * 2) / 4) * -1;
  xAxis[3].value = ((maxAxisValue * 1) / 4) * -1;
  xAxis[4].value = 0;
  xAxis[5].value = (maxAxisValue * 1) / 4;
  xAxis[6].value = (maxAxisValue * 2) / 4;
  xAxis[7].value = (maxAxisValue * 3) / 4;
  xAxis[8].value = maxAxisValue;

  //rewrite yAxis values
  yAxis[0].value = maxAxisValue * -1;
  yAxis[1].value = ((maxAxisValue * 3) / 4) * -1;
  yAxis[2].value = ((maxAxisValue * 2) / 4) * -1;
  yAxis[3].value = ((maxAxisValue * 1) / 4) * -1;
  yAxis[4].value = 0;
  yAxis[5].value = (maxAxisValue * 1) / 4;
  yAxis[6].value = (maxAxisValue * 2) / 4;
  yAxis[7].value = (maxAxisValue * 3) / 4;
  yAxis[8].value = maxAxisValue;

  const renderChart = () => {
    return (
      <View
        style={[
          appStyles.whiteBg,
          appStyles.mTSm,
          appStyles.pTXs,
          {width: '100%', overflow: 'hidden'},
        ]}
      >
        <View
          style={[
            appStyles.hCenter,
            {
              width: wp(CHART_WIDTH),
              height: wp(CHART_BOX_HEIGHT),
              alignItems: 'flex-end',
              paddingRight: wp(4),
            },
          ]}
        >
          <View
            style={[
              appStyles.hCenter,
              appStyles.vCenter,
              {
                width: wp(CHART_BOX_WIDTH),
                height: wp(CHART_BOX_HEIGHT),
                overflow: 'hidden',
              },
            ]}
          >
            {/* Draw the chart border*/}
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp(CHART_BOX_WIDTH),
                  height: wp(CHART_BOX_HEIGHT),
                },
              ]}
            />
            {/* Draw center line (Top and Left) */}
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp(CHART_BOX_WIDTH),
                  height: wp(CHART_BOX_HEIGHT),
                  bottom: wp(CHART_BOX_HEIGHT / 2),
                  left: -wp(CHART_BOX_WIDTH / 2),
                },
              ]}
            />
            {/* Draw center line (Bottom and Right) */}
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp(CHART_BOX_WIDTH),
                  height: wp(CHART_BOX_HEIGHT),
                  top: wp(CHART_BOX_HEIGHT / 2) - DASH_WIDTH,
                  right: -wp(CHART_BOX_WIDTH / 2) + DASH_WIDTH,
                },
              ]}
            />
            {/* Draw vertical lines */}
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp((CHART_BOX_WIDTH * 3) / 4),
                  height: wp(CHART_HEIGHT),
                },
              ]}
            />
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp((CHART_BOX_WIDTH * 1) / 2),
                  height: wp(CHART_HEIGHT),
                },
              ]}
            />
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp((CHART_BOX_WIDTH * 1) / 4),
                  height: wp(CHART_HEIGHT),
                },
              ]}
            />
            {/* Draw horizontal lines */}
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp(CHART_WIDTH),
                  height: wp((CHART_BOX_WIDTH * 3) / 4),
                },
              ]}
            />
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp(CHART_WIDTH),
                  height: wp((CHART_BOX_WIDTH * 1) / 2),
                },
              ]}
            />
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp(CHART_WIDTH),
                  height: wp((CHART_BOX_WIDTH * 1) / 4),
                },
              ]}
            />
            {renderAvgCircles()}
            {renderApproachStrokes()}
          </View>
          {renderYAxis()}
        </View>
        <View style={styles.xAxisContainer}>{renderXAxis()}</View>

        <Text style={styles.chartNote}>stats.stroke_gained.chart.note</Text>
      </View>
    );
  };

  const renderApproachStrokes = () => {
    const totalDistance = maxAxisValue * 2;
    const STROKE_SIZE_PERCENT_IN_CHART = (STROKE_SIZE * 100) / CHART_HEIGHT;
    const HALF_STROKE_SIZE_PERCENT = STROKE_SIZE_PERCENT_IN_CHART / 2;
    return dataProximity?.strokes?.map(item => {
      return (
        <View
          key={item.strokeId}
          style={[
            styles.scatterBoxAbsoluteBgView,
            appStyles.hCenter,
            appStyles.vCenter,
            item.x >= 0
              ? {
                  left: `${
                    (100 / totalDistance) * item.x +
                    50 -
                    HALF_STROKE_SIZE_PERCENT
                  }%`,
                }
              : {
                  left: `${
                    50 -
                    (100 / totalDistance) * -item.x -
                    HALF_STROKE_SIZE_PERCENT
                  }%`,
                },
            item.y >= 0
              ? {
                  bottom: `${
                    (100 / totalDistance) * item.y +
                    50 -
                    HALF_STROKE_SIZE_PERCENT
                  }%`,
                }
              : {
                  bottom: `${
                    50 -
                    (100 / totalDistance) * -item.y -
                    HALF_STROKE_SIZE_PERCENT
                  }%`,
                },
          ]}
        >
          <View
            style={[
              styles.scatterBoxAbsolute,
              {
                // backgroundColor: item.color || 'gray',
                backgroundColor: '#3E4D54',
              },
            ]}
          />
        </View>
      );
    });
  };

  const renderAvgCircles = () => {
    if (dataProximity) {
      let avgRad = dataProximity?.average;
      let proAvgRad = dataProximity?.proAverage;
      const yourAvgSize = wp((proAvgRad / maxAxisValue) * CHART_BOX_WIDTH);
      const proAvgSize = wp((avgRad / maxAxisValue) * CHART_BOX_WIDTH);
      return (
        <>
          <View
            style={{
              width: yourAvgSize,
              height: yourAvgSize,
              borderRadius: yourAvgSize / 2,
              backgroundColor: 'rgba(58, 186, 86, 0.4)',
              position: 'absolute',
              zIndex: proAvgRad > avgRad ? 1 : 0,
            }}
          />
          <View
            style={{
              width: proAvgSize,
              height: proAvgSize,
              borderRadius: proAvgSize / 2,
              backgroundColor: 'rgba(111, 207, 151, 0.3)',
              position: 'absolute',
              zIndex: proAvgRad > avgRad ? 0 : 1,
            }}
          />
          <View style={styles.centerWhiteCircle} />
        </>
      );
    } else {
      return null;
    }
  };
  const renderYAxis = () => {
    return yAxis.map(item => {
      return (
        <View
          key={item.value}
          style={{
            position: 'absolute',
            left: 0,
            bottom: item.yPos,
            zIndex: 100,
            width: '7%',
          }}
        >
          <Text
            DINbold
            style={[
              appStyles.greyQuizHanded,
              {textAlign: 'right', fontSize: TEXT_SIZE},
            ]}
          >
            {item.value}
          </Text>
        </View>
      );
    });
  };

  const renderXAxis = () => {
    return (
      <View style={[appStyles.mTSm, appStyles.mBSm, {width: '100%'}]}>
        {xAxis.map(item => {
          return (
            <Text
              key={item.value}
              style={[
                appStyles.greyQuizHanded,
                {
                  position: 'absolute',
                  left: `${item.leftPos - 3.4}%`, //minus 3.5 cause the Text width is 7% => center the text
                  width: '7%',
                  textAlign: 'center',
                  fontSize: TEXT_SIZE,
                },
              ]}
              DINbold
            >
              {item.value}
            </Text>
          );
        })}
      </View>
    );
  };

  const renderAverage = () => {
    return (
      <View style={[appStyles.whiteBg, {width: '100%'}]}>
        <View
          style={[
            appStyles.row,
            appStyles.vCenter,
            appStyles.hCenter,
            appStyles.pVSm,
            {
              marginLeft: 25,
              marginRight: 20,
            },
          ]}
        >
          <View style={[appStyles.row]}>
            <View
              style={[
                appStyles.mRMd,
                appStyles.alignCenter,
                styles.yourAVGColor,
              ]}
            />
            <Text style={[appStyles.black, {fontSize: 17}]}>
              stats.approach.your_avg_proximity
            </Text>
          </View>
          <View style={[appStyles.flex, {paddingRight: 35}]}>
            <Text
              DINbold
              style={[appStyles.black, appStyles.textRight, {fontSize: 26}]}
            >
              {dataProximity?.average != null
                ? `${Math.round10(dataProximity?.average, -1).toFixed(1)}'`
                : '--'}
            </Text>
          </View>
        </View>

        <View
          style={[
            appStyles.row,
            appStyles.vCenter,
            appStyles.hCenter,
            appStyles.pVSm,
            styles.borderBottomList,
          ]}
        >
          <View style={[appStyles.row]}>
            <View
              style={[
                appStyles.mRMd,
                appStyles.alignCenter,
                styles.proAVGColor,
              ]}
            />
            <Text style={[appStyles.black, {fontSize: 17}]}>
              stats.approach.pro_avg_proximity
            </Text>
          </View>
          <View style={[appStyles.flex, {paddingRight: 35}]}>
            <Text
              DINbold
              style={[appStyles.black, appStyles.textRight, {fontSize: 26}]}
            >
              {dataProximity?.proAverage != null
                ? `${Math.round10(dataProximity?.proAverage, -1).toFixed(1)}'`
                : '--'}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={[appStyles.whiteBg, appStyles.pBMd]}>
      <View style={[appStyles.hCenter, appStyles.vCenter, appStyles.pBMd]}>
        <StrokesGainedView
          byRoundVal={dataOverall?.strokesGainedByRound}
          byShotVal={dataOverall?.strokesGainedByShot}
        />
        {renderChart()}
        {renderAverage()}
      </View>
      <View style={[appStyles.whiteBg, appStyles.mBLg]}>
        <GreenAccuracy
          data={{
            grMissedLeftPercent: dataProximity?.percentageMissedLeft,
            grMissedRightPercent: dataProximity?.percentageMissedRight,
            grMissedLongPercent: dataProximity?.percentageMissedLong,
            grMissedShortPercent: dataProximity?.percentageMissedShort,
            classicStatsGreensInRegulation: dataProximity?.percentageGreensHit,
          }}
          toFixedNo={0}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  dashedBorderBox: {
    borderWidth: DASH_WIDTH,
    borderStyle: 'dashed',
    borderColor: '#8C8C91',
    zIndex: -1,
    position: 'absolute',
  },
  scatterBoxAbsolute: {
    width: wp((STROKE_SIZE * 2) / 3),
    height: wp((STROKE_SIZE * 2) / 3),
    borderRadius: wp(STROKE_SIZE / 3),
  },
  scatterBoxAbsoluteBgView: {
    width: wp(STROKE_SIZE),
    height: wp(STROKE_SIZE),
    borderRadius: wp(STROKE_SIZE / 2),
    position: 'absolute',
    zIndex: 3,
  },
  proAVGColor: {
    height: 18,
    width: 18,
    borderRadius: 9,
    backgroundColor: GREEN_COLUMN,
  },
  borderBottomList: {
    borderTopWidth: 1,
    borderTopColor: '#C4C4C4',
    marginLeft: 25,
    marginRight: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#C4C4C4',
  },
  yourAVGColor: {
    height: 18,
    width: 18,
    borderRadius: 9,
    backgroundColor: '#AEEFC9',
  },
  centerWhiteCircle: {
    width: wp(5),
    height: wp(5),
    borderRadius: wp(2.5),
    backgroundColor: 'white',
    position: 'absolute',
    zIndex: 2,
  },
  chartNote: {
    color: '#8C8B8F',
    fontSize: 8,
    fontWeight: '500',
    alignSelf: 'center',
  },
  xAxisContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    width: wp(100),
    alignSelf: 'flex-end',
  },
});

export default ApproachStatsOverall;
