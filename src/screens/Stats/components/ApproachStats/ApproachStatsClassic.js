import React, {useEffect} from 'react';
import {SafeAreaView, StatusBar, ScrollView} from 'react-native';
import GreenAccuracy from './GreenAccuracy';
import appStyles from 'styles/global';

const ApproachStatsClassic = ({
  route: {
    params: {data},
  },
}) => {
  return (
    <SafeAreaView style={[appStyles.flex, appStyles.whiteBg]}>
      <StatusBar barStyle={'dark-content'} />
      <ScrollView style={[appStyles.whiteBg, appStyles.pTSm]}>
        <GreenAccuracy data={data} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default ApproachStatsClassic;
