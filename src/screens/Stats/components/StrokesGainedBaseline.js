import React, {useState, useCallback, useRef, useEffect} from 'react';
import {View, ScrollView, SafeAreaView} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import appStyles from 'styles/global';
import {t} from 'i18next';
import Text from 'components/Text';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {moderateScale} from 'react-native-size-matters';
import CheckButton from 'components/CheckButton';
import Button from 'components/Button';
import {useDispatch} from 'react-redux';
import {updateUser} from 'requests/accounts';
import {addCurrentUser} from 'reducers/user';
import {showToast} from 'utils/toast';
import LoadingOverlay from 'components/LoadingOverlay';

const StrokesGainedBaseline = ({navigation, route}) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [listBaseline, setListBaseline] = useState(route?.params?.options);
  const setOptionChecked = baseline => {
    const cloneBaseLine = [...listBaseline];
    for (
      let index = 0, length = cloneBaseLine?.length;
      index < length;
      ++index
    ) {
      const thisElement = cloneBaseLine[index];
      if (thisElement.value === baseline) {
        // Already have one
        thisElement.ischecked = true;
      } else {
        thisElement.ischecked = false;
      }
    }
    setListBaseline(cloneBaseLine);
  };

  const saveBaseline = async () => {
    try {
      setLoading(true);
      const baseline = listBaseline.find(item => item.ischecked === true);
      const updateBaselineRs = await updateUser({
        strokesGainedBaseline: baseline?.value,
      });
      if (updateBaselineRs) {
        setTimeout(() => {
          // Update user in redux
          dispatch(addCurrentUser(updateBaselineRs));
          navigation.goBack();
          setLoading(false);
        }, 1000);
        showToast({
          type: 'success',
          message: 'Baseline updated',
        });
      } else {
        setLoading(false);
        showToast({
          type: 'error',
          message: 'Baseline updated FAIL!',
        });
      }
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: 'Baseline updated FAIL!',
      });
    }
  };

  return (
    <SafeAreaView style={[{height: '100%', backgroundColor: 'white'}]}>
      {loading ? <LoadingOverlay transparent={loading} /> : null}
      <ScrollView
        style={[
          {backgroundColor: '#F5F6F7'},
          appStyles.pHSm,
          {marginBottom: 20},
        ]}
      >
        <View style={[appStyles.hCenter, appStyles.pTMd]}>
          <Text>scores.stats.headline.strokes_gained_baseline</Text>
          <View style={[appStyles.mTMd, {width: '100%'}]}>
            {listBaseline.map(option => {
              return (
                <CheckButton
                  key={option.value}
                  style={[
                    {
                      height: moderateScale(50),
                    },
                    appStyles.mRXs,
                    appStyles.mBSm,
                  ]}
                  text={option.label}
                  textColor={'black'}
                  backgroundColor={'white'}
                  borderColor={'black'}
                  onPress={setOptionChecked}
                  isChecked={option.ischecked}
                  value={option.value}
                />
              );
            })}
          </View>
        </View>
      </ScrollView>
      <View style={[appStyles.mHSm, appStyles.mVSm]}>
        <Button
          text={t('common.save')}
          textColor="white"
          backgroundColor={'black'}
          borderColor={'black'}
          loadingMode="dark"
          onPress={() => {
            saveBaseline();
          }}
          centered
          DINbold
        />
      </View>
    </SafeAreaView>
  );
};

export default StrokesGainedBaseline;
