import ProgressCircleCustom from 'components/ProgressCircleCustom';
import Text from 'components/Text';
import React, {useRef, useEffect, useState} from 'react';
import {Animated, StyleSheet, View, Platform} from 'react-native';
import {moderateScale} from 'react-native-size-matters';

import appStyles from 'styles/global';
import {ERROR_RED, GREEN_RADIO} from 'config';

const StatsProgressCircle = ({
  value,
  standardValue,
  color,
  isSmall = false,
  maxValue,
  label,
}) => {
  return (
    <ProgressCircleCustom
      value={value}
      maxValue={maxValue}
      size={isSmall ? moderateScale(96.5) : moderateScale(106)}
      valueDot={standardValue}
      colorProgress={
        color
          ? color
          : standardValue
          ? value > standardValue
            ? ERROR_RED
            : GREEN_RADIO
          : '#34C759'
      }
      label={label}
      endText={standardValue ? '' : '%'}
      duration={400}
      border={8}
    />
  );
};

export default StatsProgressCircle;
