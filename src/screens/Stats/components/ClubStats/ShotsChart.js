import LoadingOverlay from 'components/LoadingOverlay';
import React, {useEffect, useState} from 'react';
import {View} from 'react-native';
import {useSelector} from 'react-redux';
import {
  getClubStatsByShots,
  getClubStatsByShotsAndDate,
  getClubStatsByShotsAndRoundMode,
  getClubStatsByShotsAndRounds,
  getClubStatsByShotsAndCourses,
} from 'requests/play-stats';
import {CLUB_CHART_TYPE} from 'utils/constant';
import DistanceAverageChart from './DistanceAverageChart';
const ShotsChart = ({stats, filterData}) => {
  const [overlayLoading, setOverlayLoading] = useState(false);
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );
  const [dataShot, setDataShot] = useState([]);
  const loadData = async () => {
    try {
      setOverlayLoading(true);
      let dataClubByShots = [];
      if (
        filterData != null &&
        filterData?.type !== 'N' &&
        filterData?.type != null
      ) {
        switch (filterData?.type) {
          case 'D':
            dataClubByShots = await getClubStatsByShotsAndDate(
              playService,
              filterData.startDate,
              filterData.endDate,
            );
            break;
          case 'R':
            dataClubByShots = await getClubStatsByShotsAndRounds(
              playService,
              filterData?.filterRounds?.map(item => item.roundId),
            );
            break;
          case 'C':
            dataClubByShots = await getClubStatsByShotsAndCourses(
              playService,
              filterData?.filterCourses.map(item => item.igolfCourseId),
            );
            break;
          case 'M':
            dataClubByShots = await getClubStatsByShotsAndRoundMode(
              playService,
              filterData.roundMode,
            );
            break;
          default:
            break;
        }
      } else {
        dataClubByShots = await getClubStatsByShots(playService);
      }
      const valueArray = dataClubByShots?.map(item => item.value);
      let maxShot = Math.max(...valueArray);
      if (maxShot === 0) {
        maxShot = 1;
      }
      setDataShot({dataArray: dataClubByShots, maxValue: maxShot});
      setOverlayLoading(false);
    } catch (error) {
      console.log(error);
      setOverlayLoading(false);
    }
  };
  useEffect(() => {
    loadData();
  }, []);
  return (
    <View>
      {overlayLoading ? <LoadingOverlay transparent={overlayLoading} /> : null}
      <DistanceAverageChart
        type={CLUB_CHART_TYPE.SHOTS}
        data={dataShot}
        stats={stats}
      />
    </View>
  );
};

export default ShotsChart;
