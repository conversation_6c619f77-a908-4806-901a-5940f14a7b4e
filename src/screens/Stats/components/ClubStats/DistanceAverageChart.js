import React, {useState, useEffect, useRef, useCallback, useMemo} from 'react';
import {View, Animated, Platform} from 'react-native';
import Text from 'components/Text';
import appStyles from 'styles/global';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {CLUB_NAME, CLUB_CHART_TYPE} from 'utils/constant';
import {ClubNameBox} from './DistanceRangeChart';
import {CLUB_CHART_CONTENT_WIDTH} from 'config';
const LINE_RADIUS = 0.75;
const STROKE_HEIGHT = wp(8);
const DistanceAverageChart = ({
  stats,
  data = {dataArray: [], maxValue: 1},
  type = CLUB_CHART_TYPE.DISTANCE_AVERAGE,
}) => {
  const strokeLineAnimatedVal = useRef(new Animated.Value(0)).current;
  const strokeTextAnimatedVal = useRef(new Animated.Value(0)).current;
  const strokeTextCoverAnimateVal = useRef(new Animated.Value(0)).current;
  const strokeLineDuration = 900;
  const strokeTextCoverDuration = 1009;

  const strokeLineTimingConfig = {
    useNativeDriver: false,
    duration: strokeLineDuration,
  };

  const textCoverTimingConfig = {
    useNativeDriver: false,
    duration: strokeTextCoverDuration,
  };

  const animateStrokeLine = useRef(toValue => {
    Animated.timing(strokeLineAnimatedVal, {
      toValue,
      ...strokeLineTimingConfig,
    }).start();
  }).current;

  const animateTextCover = useRef(toValue => {
    Animated.timing(strokeTextCoverAnimateVal, {
      toValue,
      ...textCoverTimingConfig,
    }).start();
  }).current;

  const animateText = useRef(toValue => {
    Animated.timing(strokeTextAnimatedVal, {
      toValue,
      ...strokeLineTimingConfig,
    }).start();
  }).current;

  useEffect(() => {
    if (
      (data?.dataArray?.length > 0 &&
        type !== CLUB_CHART_TYPE.DISTANCE_AVERAGE) ||
      type === CLUB_CHART_TYPE.DISTANCE_AVERAGE
    ) {
      animateStrokeLine(1);
      animateText(1);
      animateTextCover(1);
      return () => {
        animateStrokeLine(0);
        animateText(0);
        animateTextCover(0);
      };
    }
  }, [data, type]);
  return (
    <View style={[appStyles.pRXs]}>
      {stats?.clubStats?.length > 0 &&
        stats?.clubStats?.map(club => {
          //only get the club stats so remove the ball
          if (
            (club?.club + '').toUpperCase() === CLUB_NAME.BALL.toUpperCase()
          ) {
            return;
          }

          let strokeWidth = 0;
          let strokeValueText = '';
          let textPos = {};
          let TEXT_OUTSIDE_STROKE_LIMIT = 0;

          switch (type) {
            case CLUB_CHART_TYPE.DISTANCE_AVERAGE:
              strokeWidth = ((club.close || 0) / stats?.maxClubDistance) * 95;
              strokeValueText =
                club.close > 0 && Math.round10(club.close, -1).toFixed(1);
              TEXT_OUTSIDE_STROKE_LIMIT = 8;
              break;
            case CLUB_CHART_TYPE.SHOTS:
              if (data?.dataArray?.length > 0) {
                const itemShot = data?.dataArray?.find(
                  item =>
                    (item?.club + '').toUpperCase() ===
                    (club?.club + '').toUpperCase(),
                );
                strokeWidth = ((itemShot.value || 0) / data?.maxValue) * 95;
                strokeValueText =
                  itemShot.value > 0 &&
                  Math.round10(itemShot.value, -1).toFixed(1);
                TEXT_OUTSIDE_STROKE_LIMIT = 8;
              }
              break;
            case CLUB_CHART_TYPE.GREENS:
              if (data?.dataArray?.length > 0) {
                const itemGreen = data?.dataArray?.find(
                  item =>
                    (item?.club + '').toUpperCase() ===
                    (club?.club + '').toUpperCase(),
                );
                strokeWidth =
                  ((itemGreen.value || 0) /
                    (data?.maxValue < 100 ? 100 : data?.maxValue)) *
                  95;
                strokeValueText =
                  itemGreen.value > 0 &&
                  Math.round10(itemGreen.value, -1).toFixed(0);
                TEXT_OUTSIDE_STROKE_LIMIT = 5;
              }
              break;
            default:
              break;
          }
          const isTextInsideStrokeLine =
            strokeWidth >= TEXT_OUTSIDE_STROKE_LIMIT;
          textPos = isTextInsideStrokeLine
            ? {
                right: `${100 - strokeWidth + 1}%`,
              }
            : {
                left: `${strokeWidth + 1}%`,
              };
          return (
            <View key={club.club} style={[appStyles.row, appStyles.hCenter]}>
              <ClubNameBox label={club.club} />
              <View
                style={[
                  appStyles.row,
                  appStyles.hCenter,
                  {width: wp(CLUB_CHART_CONTENT_WIDTH)},
                ]}
              >
                <Animated.View
                  style={[
                    {
                      height: STROKE_HEIGHT,
                      width: strokeLineAnimatedVal.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0%', `${strokeWidth}%`],
                        extrapolate: 'clamp',
                      }),
                      borderTopRightRadius: wp(LINE_RADIUS),
                      borderBottomRightRadius: wp(LINE_RADIUS),
                      backgroundColor: '#00BE4B',
                    },
                  ]}
                />
                <Text
                  style={[
                    isTextInsideStrokeLine ? appStyles.white : appStyles.black,
                    textPos,
                    appStyles.smm,
                    {
                      position: 'absolute',
                      transform: [
                        {
                          translateY: strokeTextAnimatedVal.interpolate({
                            inputRange: [0, 1],
                            outputRange: [-STROKE_HEIGHT / 8, Platform.OS === 'ios' ? 2 : 0],
                            extrapolate: 'clamp',
                          }),
                        },
                      ],
                    },
                  ]}
                  DINbold
                >
                  {strokeValueText}
                </Text>
                {!isTextInsideStrokeLine && (
                  <Animated.View
                    style={[
                      {
                        position: 'absolute',
                        height: STROKE_HEIGHT + STROKE_HEIGHT / 4,
                        width: strokeTextCoverAnimateVal.interpolate({
                          inputRange: [0, 1],
                          outputRange: [`${TEXT_OUTSIDE_STROKE_LIMIT}%`, '0%'],
                          extrapolate: 'clamp',
                        }),
                        borderTopRightRadius: wp(LINE_RADIUS),
                        borderBottomRightRadius: wp(LINE_RADIUS),
                        backgroundColor: 'white',
                        right: `${100 - (strokeWidth + TEXT_OUTSIDE_STROKE_LIMIT)}%`,
                      },
                    ]}
                  />
                )}
              </View>
            </View>
          );
        })}
    </View>
  );
};

export default DistanceAverageChart;
