import React, {useState, useCallback, useRef, useEffect} from 'react';
import {View, Image, Modal, TouchableOpacity} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import appStyles from 'styles/global';
import {t} from 'i18next';
import Text from 'components/Text';
import Header from 'components/Header';
const StrokesGainedModal = ({setShowModal}) => {
  return (
    <Modal transparent animationType="fade">
      <View
        style={[
          appStyles.flex,
          appStyles.hCenter,
          appStyles.vCenter,
          appStyles.pHSm,
          {backgroundColor: 'rgba(0,0,0,0.8)'},
        ]}
      >
        <View
          style={[
            appStyles.pHXSm,
            appStyles.pTSm,
            appStyles.pBLg,
            appStyles.whiteBg,
            {
              borderRadius: 8,
              alignItems: 'flex-start',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 4,
              elevation: 5,
              zIndex: 1000,
            },
          ]}
        >
          <Header
            isClose
            closeDark
            close={() => setShowModal?.(false)}
            headerStyle={{
              width: '100%',
            }}
            titleStyle={{
              fontWeight: 'bold',
              ...appStyles.md,
              ...appStyles.pTSm,
              ...appStyles.black,
            }}
            title={t('scores.stats.headline.strokes_gained_baseline')}
            smallHeader
          />
          <Text style={[appStyles.xsm, appStyles.black]}>
            stats.overall.hover.strokes_gained_stats.p1
          </Text>
          <Text style={[appStyles.pTSm, appStyles.xsm, appStyles.black]}>
            stats.overall.hover.strokes_gained_stats.p2
          </Text>
        </View>
      </View>
    </Modal>
  );
};

export default StrokesGainedModal;