import React from 'react';
import {View, Modal, StyleSheet, TouchableOpacity} from 'react-native';
import appStyles from 'styles/global';
import {t} from 'i18next';
import Text from 'components/Text';
import Button from 'components/Button';
import {isCanadaMarket} from 'utils/commonVariable';
import Close_Icon from 'assets/imgs/ic_close.svg';

const ConfirmDisconnectHandicapModal = ({setShowModal, onConfirm}) => {
  return (
    <Modal transparent animationType="slide">
      <View style={[appStyles.flex, {justifyContent: 'flex-end'}]}>
        <View
          style={[appStyles.whiteBg, styles.viewContainer, appStyles.vCenter]}
        >
          <TouchableOpacity
            onPress={() => {
              setShowModal?.(false);
            }}
            style={{position: 'absolute', right: 15, top: 15, zIndex: 100}}
          >
            <Close_Icon />
          </TouchableOpacity>
          <Text black style={styles.confirmTitle}>
            {isCanadaMarket()
              ? 'GolfCanada.confirm.title.disconnect'
              : 'ghin.confirm.title.disconnect_usga'}
          </Text>
          <Text gray style={styles.confirmDescription}>
            {isCanadaMarket()
              ? 'GolfCanada.confirm.description.disconnect'
              : 'ghin.confirm.description.disconnect_usga'}
          </Text>
          <Button
            style={[styles.btnCommon]}
            textColor="white"
            centered
            textStyle={styles.btnText}
            Din79Font
            text={t('ghin.unlink_my_usga').toLocaleUpperCase()}
            onPress={onConfirm}
            numberOfLines={1}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  confirmTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginTop: 61,
    alignSelf: 'center',
    color: 'rgba(0, 0, 0, 0.6)',
  },
  confirmDescription: {
    fontSize: 16,
    fontWeight: '400',
    marginTop: 16,
    textAlign: 'center',
    marginHorizontal: 16,
    color: 'rgba(0, 0, 0, 0.6)',
  },
  btnText: {
    fontWeight: '700',
    fontSize: 12,
    letterSpacing: 1.2,
  },
  btnCommon: {
    backgroundColor: '#000',
    height: 40,
    marginTop: 24,
    ...appStyles.viewShadow,
    elevation: 2,
    marginHorizontal: 16,
    marginBottom: 34,
  },
  viewContainer: {
    borderTopEndRadius: 46,
    borderTopStartRadius: 46,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    bottom: 0,
  },
});

export default ConfirmDisconnectHandicapModal;
