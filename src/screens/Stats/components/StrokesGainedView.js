import React, {useState, useCallback, useRef, useEffect, useMemo} from 'react';
import {View, TouchableOpacity, StyleSheet, Platform} from 'react-native';
import appStyles from 'styles/global';
import Text from 'components/Text';
import Icon from 'react-native-vector-icons/FontAwesome';

const StrokesGainedView = ({byRoundVal, byShotVal}) => {
  let strokeGainedperRoundValue =
    byRoundVal != null ? `${Math.round10(byRoundVal, -1).toFixed(2)}` : '--';
  let strokeGainedperShotValue =
    byShotVal != null ? `${Math.round10(byShotVal, -1).toFixed(2)}` : '--';
  let styleStrokeGainedPerRound = styles.strokeGainedZeroValue;
  let styleStrokeGainedPerShot = styles.strokeGainedZeroValue;
  switch (true) {
    case strokeGainedperRoundValue > 0:
      styleStrokeGainedPerRound = styles.strokeGainedPositiveValue;
      break;
    case strokeGainedperRoundValue < 0:
      styleStrokeGainedPerRound = styles.strokeGainedNegativeValue;
      break;
    default:
      break;
  }
  switch (true) {
    case strokeGainedperShotValue > 0:
      styleStrokeGainedPerShot = styles.strokeGainedPositiveValue;
      break;
    case strokeGainedperShotValue < 0:
      styleStrokeGainedPerShot = styles.strokeGainedNegativeValue;
      break;
    default:
      break;
  }
  return (
    <View style={[appStyles.whiteBg, appStyles.fullWidth, {marginTop: 21}]}>
      <View
        style={[
          appStyles.row,
          {marginHorizontal: '12%'},
          appStyles.spaceBetween,
        ]}
      >
        <View>
          <Text style={[appStyles.grey, styles.strokeGainedTitle]}>
            my_bag.detail.stats.strokes_gained
          </Text>
          <Text
            style={[appStyles.grey, appStyles.alignCenter, styles.perRound]}
          >
            stats.approach_stat.per_Round
          </Text>
          <View style={{flexDirection: 'row', justifyContent: 'center'}}>
            {strokeGainedperRoundValue > 0 ? (
              <View
                style={[
                  appStyles.alignCenter,
                  Platform.OS === 'android' ? appStyles.mTXs : appStyles.mBSm,
                  {marginRight: 1},
                ]}
              >
                <Icon name="plus" color={'#3ABA56'} size={12} />
              </View>
            ) : null}
            <Text
              DINbold
              style={[appStyles.alignCenter, styleStrokeGainedPerRound]}
            >
              {strokeGainedperRoundValue}
            </Text>
          </View>
        </View>
        <View>
          <Text style={[appStyles.grey, styles.strokeGainedTitle]}>
            my_bag.detail.stats.strokes_gained
          </Text>
          <Text
            style={[appStyles.grey, appStyles.alignCenter, styles.perRound]}
          >
            stats.approach_stat.per_Shot
          </Text>
          <View style={{flexDirection: 'row', justifyContent: 'center'}}>
            {strokeGainedperShotValue > 0 ? (
              <View
                style={[
                  appStyles.alignCenter,
                  Platform.OS === 'android' ? appStyles.mTXs : appStyles.mBSm,
                  {marginRight: 1},
                ]}
              >
                <Icon name="plus" color={'#3ABA56'} size={12} />
              </View>
            ) : null}
            <Text
              DINbold
              style={[appStyles.alignCenter, styleStrokeGainedPerShot]}
            >
              {strokeGainedperShotValue}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  strokeGainedPositiveValue: {
    fontSize: 26,
    color: '#3ABA56',
    fontWeight: '500',
    marginTop: 5,
  },
  strokeGainedNegativeValue: {
    fontSize: 26,
    color: '#FF0000',
    fontWeight: '500',
    marginTop: 5,
  },
  strokeGainedZeroValue: {
    fontSize: 26,
    color: '#3E4D54',
    fontWeight: '500',
    marginTop: 5,
  },
  strokeGainedTitle: {
    fontSize: 13,
    fontWeight: '400',
  },
  perRound: {
    fontSize: 13,
    fontWeight: '700',
  },
});

export default StrokesGainedView;
