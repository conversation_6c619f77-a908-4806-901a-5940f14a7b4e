import React, {useState, useCallback, useEffect, useRef, Fragment} from 'react';
import {View, Image, StyleSheet, Animated} from 'react-native';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import appStyles from 'styles/global';
import Text from 'components/Text';
import {GREEN_COLUMN} from 'config';
import {TabBar, TabView} from 'react-native-tab-view';
import {GREY, PRIMARY_COLOR} from 'config';
import FlagIcon from 'assets/imgs/flag-putting-stats.svg';
const DASH_WIDTH = 0.75;
const ZERO_POS = 6; //percent
const REAL_CHART_HEIGHT_PERCENT = 90; //after minus from the zero pos and the top of the chart
const STROKE_SIZE_PERCENT = 3.5;
const CHART_HEIGHT = 80;
const CHART_WIDTH = 100;
const STROKE_SIZE_PERCENT_BY_CHART_HEIGHT = (STROKE_SIZE_PERCENT * 100) / CHART_HEIGHT; //Stroke's size percent in CHART_HEIGHT
const CHART_RANGE_VALUE = 11.5;
const TEXT_SIZE = hp(1.9);
const TEXT_RATIO_BY_CHART_HEIGHT = (TEXT_SIZE+2) / wp(CHART_HEIGHT) * 100;//TEXT_SIZE+2 cause the actual text height is more 2 pixel than the hp() result
const Y_AXIS_INTERVAL = 18;
const X_AXIS_INTERVAL = 10.9;
const firstXPos = 6.5;
const xAxis_holedPercent = [
  {value: FlagIcon, leftPos: 5, isIcon: true},
  {value: '3', leftPos: firstXPos + X_AXIS_INTERVAL * 1},
  {value: '6', leftPos: firstXPos + X_AXIS_INTERVAL * 2},
  {value: '9', leftPos: firstXPos + X_AXIS_INTERVAL * 3},
  {value: '15', leftPos: firstXPos + X_AXIS_INTERVAL * 4},
  {value: '21', leftPos: firstXPos + X_AXIS_INTERVAL * 5},
  {value: '30', leftPos: firstXPos + X_AXIS_INTERVAL * 6},
  {value: '100', leftPos: firstXPos + X_AXIS_INTERVAL * 7},
  {value: '150', leftPos: firstXPos + X_AXIS_INTERVAL * 8},
];
export const renderXAxis = () => {
  return (
    <View style={[appStyles.mTXs, appStyles.mBMd, {width: '100%'}]}>
      {xAxis_holedPercent.map(item => {
        if (item.isIcon) {
          return (
            <View
              style={[
                appStyles.hCenter,
                appStyles.vCenter,
                appStyles.mTXs,
                {
                  position: 'absolute',
                  left: `${item.leftPos}%`,
                  width: '7%',
                },
              ]}
              key={item.value}
            >
              <FlagIcon />
            </View>
          );
        } else {
          return (
            <Text
              key={item.value}
              style={[
                appStyles.black,
                appStyles.mTSm,
                {
                  position: 'absolute',
                  left: `${item.leftPos - 3}%`,
                  textAlign: 'center',
                  width: '6%',
                  fontSize: TEXT_SIZE,
                  color: '#8C8B8F',
                },
              ]}
              DINbold
            >
              {item.value}
            </Text>
          );
        }
      })}
    </View>
  );
};
function PuttingHoledTabChartScreen({navigation, dataOverall}) {
  const [routes] = useState([
    {key: 'holedPercent', title: '% HOLED'},
    {key: 'puttsPerRound', title: 'PUTTS PER ROUND'},
  ]);
  const [focusTabIndex, setFocusTabIndex] = useState(0);
  const yourHoledAnimateVal = useRef(new Animated.Value(0)).current;
  const proHoledAnimateVal = useRef(new Animated.Value(0)).current;

  const yourPutPerRoundAnimateVal = useRef(new Animated.Value(0)).current;
  const proPutPerRoundAnimateVal = useRef(new Animated.Value(0)).current;

  const holedBlackLineAnimateVal = useRef(new Animated.Value(0)).current;
  const putPerRoundBlackLineAnimateVal = useRef(new Animated.Value(0)).current;

  const strokesDuration = 500;
  const blackLineDuration = 450;

  const strokesTimingConfig = {
    useNativeDriver: true,
    duration: strokesDuration,
  };

  const blackLineTimingConfig = {
    useNativeDriver: false,
    duration: blackLineDuration,
  };

  const animateHoledBlackLine = useRef(toValue => {
    Animated.timing(holedBlackLineAnimateVal, {
      toValue,
      ...blackLineTimingConfig,
    }).start();
  }).current;
  const animatePutPerRoundBlackLine = useRef(toValue => {
    Animated.timing(putPerRoundBlackLineAnimateVal, {
      toValue,
      ...blackLineTimingConfig,
    }).start();
  }).current;

  const animateYourHoled = useRef(toValue => {
    Animated.timing(yourHoledAnimateVal, {
      toValue,
      ...strokesTimingConfig,
    }).start();
  }).current;
  const animateProHoled = useRef(toValue => {
    Animated.timing(proHoledAnimateVal, {
      toValue,
      ...strokesTimingConfig,
    }).start();
  }).current;
  const animateYourPutPerRound = useRef(toValue => {
    Animated.timing(yourPutPerRoundAnimateVal, {
      toValue,
      ...strokesTimingConfig,
    }).start();
  }).current;
  const animateProPutPerRound = useRef(toValue => {
    Animated.timing(proPutPerRoundAnimateVal, {
      toValue,
      ...strokesTimingConfig,
    }).start();
  }).current;
  useEffect(() => {
    if (focusTabIndex === 0 && dataOverall) {
      animateYourHoled(1);
      animateProHoled(1);
      animateHoledBlackLine(1);
      animateYourPutPerRound(0);
      animateProPutPerRound(0);
      animatePutPerRoundBlackLine(0);
    } else {
      animateYourPutPerRound(1);
      animateProPutPerRound(1);
      animatePutPerRoundBlackLine(1);
      animateYourHoled(0);
      animateProHoled(0);
      animateHoledBlackLine(0);
    }
  }, [focusTabIndex, dataOverall]);

  const firstYPos = ZERO_POS - TEXT_RATIO_BY_CHART_HEIGHT/2 - (Platform.OS === 'ios' ? TEXT_RATIO_BY_CHART_HEIGHT * 15/100 : 0);//because the DINBold in IOS, the text has small bottom padding, about 30%
  const yAxis_holedPercent = [
    {value: '0', yPos: `${firstYPos}%`},
    {value: '20', yPos: `${firstYPos + Y_AXIS_INTERVAL}%`},
    {value: '40', yPos: `${firstYPos + Y_AXIS_INTERVAL * 2}%`},
    {value: '60', yPos: `${firstYPos + Y_AXIS_INTERVAL * 3}%`},
    {value: '80', yPos: `${firstYPos + Y_AXIS_INTERVAL * 4}%`},
    {value: '100', yPos: `${firstYPos + Y_AXIS_INTERVAL * 5}%`},
    {value: '%', yPos: `${firstYPos + Y_AXIS_INTERVAL * 5 + TEXT_RATIO_BY_CHART_HEIGHT}%`},
  ];

  const yAxis_putPerRound = [
    {value: '0', yPos: `${firstYPos}%`},
    {value: '4', yPos: `${firstYPos + Y_AXIS_INTERVAL}%`},
    {value: '8', yPos: `${firstYPos + Y_AXIS_INTERVAL * 2}%`},
    {value: '12', yPos: `${firstYPos + Y_AXIS_INTERVAL * 3}%`},
    {value: '16', yPos: `${firstYPos + Y_AXIS_INTERVAL * 4}%`},
    {value: '20', yPos: `${firstYPos + Y_AXIS_INTERVAL * 5}%`},
  ];

  const renderYAxis = (isHoledPercent = true) => {
    const yAxis = isHoledPercent ? yAxis_holedPercent : yAxis_putPerRound;
    return yAxis.map(item => {
      return (
        <View
          key={item.value}
          style={{
            position: 'absolute',
            left: wp(4),
            bottom: item.yPos,
            zIndex: 100,
            width: wp(100),
          }}
        >
          <Text DINbold style={[appStyles.greyQuizHanded, {textAlign: 'left', fontSize: TEXT_SIZE}]}>
            {item.value}
          </Text>
        </View>
      );
    });
  };

  const renderLegendLabel = () => {
    return (
      <View
        style={[
          appStyles.row,
          appStyles.spaceEnd,
          appStyles.pXs,
          appStyles.hCenter,
          appStyles.mBXs,
          {width: '90%'},
        ]}
      >
        <View style={[appStyles.row, appStyles.hCenter, {paddingRight: wp(2)}]}>
          <View
            style={[
              styles.puttingBox,
              {backgroundColor: '#FF453A', marginRight: wp(1)},
            ]}
          />
          <Text DINbold style={[appStyles.xxs]}>
            stats.putting.chart.your_avg
          </Text>
        </View>
        <View style={[appStyles.row, appStyles.hCenter]}>
          <View
            style={[
              styles.puttingBox,
              {backgroundColor: GREEN_COLUMN, marginRight: wp(1)},
            ]}
          />
          <Text DINbold style={[appStyles.xxs]}>
            stats.putting.chart.pro_avg
          </Text>
        </View>
      </View>
    );
  };
  const colorFields = {
    color: '#FF453A',
    proColor: GREEN_COLUMN,
  };

  const dtValueFields = {
    valueField: 'percentage_holed',
    proValueField: 'pro_percentage_holed',
  };

  const dtValueFieldsPerRound = {
    valueField: 'number_of_putts_per_round',
    proValueField: 'pro_number_of_putts_per_round',
  };

  const getDataRangeByType = isHoledPercent => {
    const dtValFields = isHoledPercent ? dtValueFields : dtValueFieldsPerRound;
    const commonFields = {...colorFields, ...dtValFields};
    const firstLeftPos = firstXPos + (CHART_RANGE_VALUE - STROKE_SIZE_PERCENT) / 2;
    const dtByRange = [
      {
        ...commonFields,
        rangeField: '<3',
        leftPos: firstLeftPos,
      },
      {
        ...commonFields,
        rangeField: '3-6',
        leftPos: firstLeftPos + CHART_RANGE_VALUE,
      },
      {
        ...commonFields,
        rangeField: '6-9',
        leftPos: firstLeftPos + CHART_RANGE_VALUE * 2,
      },
      {
        ...commonFields,
        rangeField: '9-15',
        leftPos: firstLeftPos + CHART_RANGE_VALUE * 3,
      },
      {
        ...commonFields,
        rangeField: '15-21',
        leftPos: firstLeftPos + CHART_RANGE_VALUE * 4,
      },
      {
        ...commonFields,
        rangeField: '21-30',
        leftPos: firstLeftPos + CHART_RANGE_VALUE * 5,
      },
      {
        ...commonFields,
        rangeField: '>30',
        leftPos: firstLeftPos + CHART_RANGE_VALUE * 6,
      },
    ];
    return dtByRange;
  };

  const styleAbsolute = [
    styles.puttingBoxAbsolute,
    appStyles.hCenter,
    appStyles.vCenter,
  ];
  //The y pos from 0 to 100 will account for 90%. 3rd to 93rd percentile so 0 value will be at the 3 percent position from the bottom
  const renderStrokes = (isHoledPercent = true) => {
    if (dataOverall) {
      return getDataRangeByType(isHoledPercent).map(item => {
        let yourVal = dataOverall?.[item.rangeField]?.[item.valueField];
        let proVal = dataOverall?.[item.rangeField]?.[item.proValueField];
        let valDif = proVal - yourVal;
        let maxYValue = isHoledPercent ? 100 : 20;
        // check if Pro value is greater than Your value
        const isProValGreater = valDif > 0;
        //blackline height
        const blackLine =
          (Math.abs(valDif) * REAL_CHART_HEIGHT_PERCENT) / maxYValue;
        //Get animated value for each type: holed percentage/ putts per round
        const yourAnimatedVal = isHoledPercent
          ? yourHoledAnimateVal
          : yourPutPerRoundAnimateVal;
        const proAnimatedVal = isHoledPercent
          ? proHoledAnimateVal
          : proPutPerRoundAnimateVal;
        const blackLineAnimatedVal = isHoledPercent
          ? holedBlackLineAnimateVal
          : putPerRoundBlackLineAnimateVal;
        //
        const yourValPercentInChart = (yourVal * REAL_CHART_HEIGHT_PERCENT) / maxYValue;
        const proValPercentInChart = (proVal * REAL_CHART_HEIGHT_PERCENT) / maxYValue;
        //Create 2 half of black line so that these lines can animate from the middle of the REAL BLACKLINE
        const halfTopBlackLineBottom =
          (((isProValGreater ? yourValPercentInChart : proValPercentInChart) +
            ZERO_POS +
            blackLine / 2) *
            wp(CHART_HEIGHT)) /
          100;
        const halfBottomBlackLineTop =
          ((100 -
            ((isProValGreater ? yourValPercentInChart : proValPercentInChart) +
              ZERO_POS +
              blackLine / 2)) *
              wp(CHART_HEIGHT)) /
            100 -
          1;
        //The greater stroke value, its starting position will be at the end of the black line. It will then move to the upper level of the black line. If the value is smaller, it will have a position starting from the middle of the black line and moving to the bottom of the black line
        const yourStrokePos =
          ((yourValPercentInChart +
            ZERO_POS +
            blackLine / (!isProValGreater ? -1 : 2) - STROKE_SIZE_PERCENT_BY_CHART_HEIGHT / 2) *
            wp(CHART_HEIGHT)) /
          100;
        const proStrokePos =
          ((proValPercentInChart +
            ZERO_POS +
            blackLine / (isProValGreater ? -1 : 2) - STROKE_SIZE_PERCENT_BY_CHART_HEIGHT / 2) *
            wp(CHART_HEIGHT)) /
          100;
        const yourTextPos =
          ((yourValPercentInChart +
            ZERO_POS +
            (!isProValGreater ? STROKE_SIZE_PERCENT_BY_CHART_HEIGHT : -STROKE_SIZE_PERCENT_BY_CHART_HEIGHT) - STROKE_SIZE_PERCENT_BY_CHART_HEIGHT / 2) *
            wp(CHART_HEIGHT)) /
          100;
        const proTextPos =
          ((proValPercentInChart +
            ZERO_POS +
            (isProValGreater ? STROKE_SIZE_PERCENT_BY_CHART_HEIGHT : -STROKE_SIZE_PERCENT_BY_CHART_HEIGHT) - STROKE_SIZE_PERCENT_BY_CHART_HEIGHT/2) *
            wp(CHART_HEIGHT)) /
          100;
        const BlackLine = () => {
          return (
            <>
            <Animated.View
                style={[
                  {
                    position: 'absolute',
                    width: wp(STROKE_SIZE_PERCENT),
                    borderTopLeftRadius: wp(STROKE_SIZE_PERCENT / 2),
                    borderTopRightRadius: wp(STROKE_SIZE_PERCENT / 2),
                    height: blackLineAnimatedVal.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, ((blackLine / 2 + STROKE_SIZE_PERCENT_BY_CHART_HEIGHT / 4) * wp(CHART_HEIGHT)) / 100],
                      extrapolate: 'clamp',
                    }),
                  },
                  {
                    backgroundColor: 'black',
                    left: `${item.leftPos}%`,
                    bottom: halfTopBlackLineBottom,
                  },
                ]}
              />
              <Animated.View
                style={[
                  {
                    position: 'absolute',
                    width: wp(STROKE_SIZE_PERCENT),
                    borderBottomLeftRadius: wp(STROKE_SIZE_PERCENT / 2),
                    borderBottomRightRadius: wp(STROKE_SIZE_PERCENT / 2),
                    height: blackLineAnimatedVal.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, ((blackLine / 2 + STROKE_SIZE_PERCENT_BY_CHART_HEIGHT / 4) * wp(CHART_HEIGHT)) / 100],
                      extrapolate: 'clamp',
                    }),
                  },
                  {
                    backgroundColor: 'black',
                    left: `${item.leftPos}%`,
                    top: halfBottomBlackLineTop,
                  },
                ]}
              />
            </>
          );
        };
        const StrokeTexts = () => {
          return (
            <>
              <Text
                DINbold
                style={[
                  {
                    position: 'absolute',
                    left: `${
                      item.leftPos - (CHART_RANGE_VALUE - STROKE_SIZE_PERCENT) / 2
                    }%`,
                    bottom: yourTextPos,
                    width: wp(CHART_RANGE_VALUE),
                    textAlign: 'center',
                  },
                  appStyles.xs,
                  appStyles.black,
                ]}
              >
                {Math.round10(yourVal, -1).toFixed(1)}
              </Text>
              <Text
                DINbold
                style={[
                  {
                    position: 'absolute',
                    left: `${
                      item.leftPos - (CHART_RANGE_VALUE - STROKE_SIZE_PERCENT) / 2
                    }%`,
                    bottom: proTextPos,
                    width: wp(CHART_RANGE_VALUE),
                    textAlign: 'center',
                  },
                  appStyles.xs,
                  appStyles.black,
                ]}
              >
                {Math.round10(proVal, -1).toFixed(1)}
              </Text>
            </>
          );
        };
        const StrokePoints = () => {
          return (
            <>
              <Animated.View
                style={[
                  ...styleAbsolute,
                  {
                    backgroundColor: item.color,
                    left: `${item.leftPos}%`,
                    bottom: yourStrokePos,
                    zIndex: 2,//Your Pos always in front of Pro Pos
                    transform: [
                      {
                        translateY: yourAnimatedVal.interpolate({
                          inputRange: [0, 1],
                          outputRange: [
                            0,
                            (((blackLine / (!isProValGreater ? 1 : 2)) *
                              wp(CHART_HEIGHT)) /
                              100) *
                              (!isProValGreater ? -1 : 1),
                          ],
                          extrapolate: 'clamp',
                        }),
                      },
                    ],
                  },
                ]}
              />
              <Animated.View
                style={[
                  ...styleAbsolute,
                  {
                    backgroundColor: item.proColor,
                    left: `${item.leftPos}%`,
                    bottom: proStrokePos,
                    zIndex: 1,
                    transform: [
                      {
                        translateY: proAnimatedVal.interpolate({
                          inputRange: [0, 1],
                          outputRange: [
                            0,
                            (((blackLine / (isProValGreater ? 1 : 2)) *
                              wp(CHART_HEIGHT)) /
                              100) *
                              (isProValGreater ? -1 : 1),
                          ],
                          extrapolate: 'clamp',
                        }),
                      },
                    ],
                  },
                ]}
              />
            </>
          );
        };
        return (
          <Fragment key={item.leftPos + item.rangeField}>
            <BlackLine key={item.leftPos + item.rangeField + 'BlackLine'} />
            <StrokeTexts key={item.leftPos + item.rangeField + 'StrokeTexts'} />
            <StrokePoints
              key={item.leftPos + item.rangeField + 'StrokePoints'}
            />
          </Fragment>
        );
      });
    }
  };

  const renderChart = (isHoledPercent = true) => {
    return (
      <View
        style={[
          appStyles.whiteBg,
          appStyles.hCenter,
          appStyles.vCenter,
          appStyles.pTXs,
          {width: '100%', overflow: 'hidden'},
        ]}
      >
        {renderLegendLabel()}
        <View
          style={[appStyles.hCenter, {width: wp(CHART_WIDTH), height: wp(CHART_HEIGHT)}]}
        >
          {renderYAxis(isHoledPercent)}
          {/* this view for drawing black xAxis line */}
          <View
            style={[
              {
                position: 'absolute',
                width: wp(90),
                height: wp(CHART_HEIGHT),
                borderBottomWidth: 1,
              },
            ]}
          />
          <View
            style={[
              appStyles.hCenter,
              appStyles.vCenter,
              {
                width: wp(80.5),
                height: wp(CHART_HEIGHT),
                overflow: 'hidden',
              },
            ]}
          >
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp(80.5),
                },
              ]}
            />
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp(57.5),
                },
              ]}
            />
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp(34.5),
                },
              ]}
            />
            <View
              style={[
                styles.dashedBorderBox,
                {
                  width: wp(11.5),
                },
              ]}
            />
          </View>
          {renderStrokes(isHoledPercent)}
        </View>
        {renderXAxis()}
        <Text DINbold style={[appStyles.greyQuizHanded]}>
          FT
        </Text>
      </View>
    );
  };
  const renderTabBar = props => {
    return (
      <TabBar
        {...props}
        indicatorStyle={{backgroundColor: PRIMARY_COLOR}}
        style={[appStyles.whiteBg]}
        inactiveColor={GREY}
        activeColor={PRIMARY_COLOR}
        renderLabel={({route, focused, color}) => (
          <Text DINbold style={[appStyles.smm, {color, margin: 8}]}>
            {route.title}
          </Text>
        )}
      />
    );
  };
  return (
    <>
      <TabView
        navigationState={{index: focusTabIndex, routes}}
        renderScene={() => null}
        onIndexChange={setFocusTabIndex}
        initialLayout={{width: wp('100%')}}
        renderTabBar={renderTabBar}
        swipeEnabled={false}
      />
      {focusTabIndex === 0 ? renderChart() : renderChart(false)}
    </>
  );
}
const styles = StyleSheet.create({
  dashedBorderBox: {
    borderWidth: DASH_WIDTH,
    borderStyle: 'dashed',
    borderColor: '#8C8C91',
    zIndex: -1,
    height: wp(CHART_HEIGHT) + wp(1), //add wp(1) to hide the top dash border
    position: 'absolute',
  },
  puttingBox: {
    width: wp(STROKE_SIZE_PERCENT),
    height: wp(STROKE_SIZE_PERCENT),
    borderRadius: wp(STROKE_SIZE_PERCENT/2),
  },
  puttingBoxAbsolute: {
    width: wp(STROKE_SIZE_PERCENT),
    height: wp(STROKE_SIZE_PERCENT),
    borderRadius: wp(STROKE_SIZE_PERCENT/2),
    position: 'absolute',
  },
});

export default PuttingHoledTabChartScreen;
