import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Text,
  Platform,
  Image,
  BackHandler,
} from 'react-native';
import Video from 'react-native-video';
import Icon from 'react-native-vector-icons/FontAwesome5';
import {createThumbnail} from 'react-native-create-thumbnail';
import {widthPercentageToDP} from 'react-native-responsive-screen';
import appStyles from '../../styles/global';
import {t} from 'i18next';
import {
  PUTTING,
  COACH_VIDEO_TYPE,
  COACH_SWING_SHOT_TYPE,
} from '../../json/describeShot';
import BackButton from '../../components/BackButton';
import Button from '../../components/Button';
import {useFocusEffect} from '@react-navigation/native';
import Images from '../../../assets/imgs/Images';

const VideoPreview = ({navigation, route}) => {
  const {
    swingSubElementId,
    swingShotTypeId,
    videoTypeId,
    rejectSwingName,
    swingName,
    lessonType,
  } = route?.params;

  const [showCaptureIcon, setShowCaptureIcon] = useState(false);
  const [isVisibleIcon, setIsVisibleIcon] = useState(false);
  const [pause, setPause] = useState(true);
  const dataVideo = useRef(null);

  const [thumb, setThumb] = useState('');

  useEffect(() => {
    setShowCaptureIcon(true);
    setTimeout(() => {
      setShowCaptureIcon(false);
      setIsVisibleIcon(true);
    }, 3000);
  }, []);

  useFocusEffect(
    //prevent back behavior from this screen
    React.useCallback(() => {
      const onBackPress = () => {
        return true;
      };
      BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );

  useEffect(() => {
    let title = '';
    switch (swingShotTypeId) {
      case COACH_SWING_SHOT_TYPE.BASELINE:
        if (videoTypeId === COACH_VIDEO_TYPE.downTheLine) {
          title = t('baseline.title.down_the_line');
        } else {
          title = t('video_preview.baseline_face_on_title');
        }
        break;
      case COACH_SWING_SHOT_TYPE.SINGLE_SHOT:
        title = t('video_preview.text_header_scramble');
        break;
      case COACH_SWING_SHOT_TYPE.SINGLE_SHOT_RETURNED:
      case COACH_SWING_SHOT_TYPE.RETURNED:
        if (videoTypeId === COACH_VIDEO_TYPE.downTheLine) {
          title = t('select_video_menu.record_down_the_line');
        } else {
          title = t('select_video_menu.record_face_on');
        }
        break;
      case COACH_SWING_SHOT_TYPE.PUTT:
        title = t('video_preview.text_header_putting');
        break;

      default:
        break;
    }

    navigation.setOptions({
      headerTitle: title,
      headerLeft: () => (
        <BackButton
          color="white"
          onPress={() => navigation.pop(2)}
          navigationText={''}
        />
      ),
    });
  }, []);

  const video = route?.params?.dataRecord;
  const dataRecord = route?.params?.dataRecord?.uri;

  const onLoad = () => {
    dataVideo?.current?.seek(0.01);
  };

  const handleApprove = () => {
    if (swingShotTypeId === COACH_SWING_SHOT_TYPE.PUTT) {
      navigation.navigate('NativeCoach', {
        screen: 'LongPuttInformation',
        params: {
          dataRecord: video,
          swingShotTypeId,
          videoTypeId,
          lessonType,
        },
      });
    } else if (swingShotTypeId === COACH_SWING_SHOT_TYPE.PUTT_RETURNED) {
      navigation.navigate('NativeCoach', {
        screen: 'ShortPutInformation',
        params: {
          dataRecord: video,
          swingShotTypeId,
          videoTypeId,
          lessonType,
        },
      });
    } else {
      navigation.navigate('NativeCoach', {
        screen: 'DescribeShot',
        params: {
          dataRecord: video,
          typeVideo: route?.params?.typeVideo,
          type: route?.params?.type,
          swingSubElementId,
          swingShotTypeId,
          videoTypeId,
          rejectSwingName,
          swingName,
          lessonType,
          originScreen: route?.params?.originScreen || '',
        },
      });
    }
  };
  useEffect(() => {
    createThumbnail({
      url: dataRecord,
      timeStamp: 10000,
    })
      .then(response => setThumb(response.path))
      .catch(err => console.log('err', err));
  }, []);

  const onEnd = () => {
    try {
      setIsVisibleIcon(true);
      setPause(true);
    } catch (error) {
      console.log('error', error);
    }
  };

  const playVideo = () => {
    if (Platform.OS !== 'ios') {
      setIsVisibleIcon(false);
    } else {
      setIsVisibleIcon(true);
    }
    setPause(false);
    dataVideo?.current?.presentFullscreenPlayer();
  };

  return (
    <View style={styles.container}>
      <View style={[appStyles.fullWidth, {height: '80%'}]}>
        <Video
          source={{uri: dataRecord}} // Can be a URL or a local file.
          ref={dataVideo} // Store reference
          style={[styles.backgroundVideo, {position: 'relative'}]}
          paused={pause}
          onLoad={onLoad}
          fullscreen={true}
          onEnd={onEnd}
          resizeMode={'cover'}
        />
        {Platform.OS === 'ios' && (
          <Image
            source={{uri: thumb}}
            style={[styles.backgroundVideo, {position: 'absolute'}]}
          />
        )}

        {isVisibleIcon && (
          <TouchableOpacity
            style={[
              appStyles.absoluteFill,
              appStyles.vCenter,
              appStyles.hCenter,
            ]}
            onPress={playVideo}
          >
            <View
              style={[appStyles.vCenter, appStyles.hCenter, styles.viewIcon]}
            >
              <Icon
                name="play"
                size={widthPercentageToDP('15%')}
                color="white"
              />
            </View>
          </TouchableOpacity>
        )}
      </View>
      <View style={[appStyles.fullWidth, {height: '20%'}]}>
        <View
          style={[
            appStyles.full,
            appStyles.column,
            {
              backgroundColor: '#3E4D54',
            },
          ]}
        >
          <Text style={{marginHorizontal: 20}}>
            <Text style={[appStyles.bold, styles.textNote]}>
              {t('video_preview.review')}
            </Text>{' '}
            <Text style={styles.textNote}>{t('video_preview.note')}</Text>
          </Text>

          <View
            style={[
              appStyles.row,
              appStyles.spaceBetween,
              {marginTop: 13, height: 40},
            ]}
          >
            <TouchableOpacity
              style={[
                styles.boxIcon,
                appStyles.hCenter,
                appStyles.vCenter,
                {marginLeft: 20},
              ]}
              onPress={() =>
                navigation.navigate('SelectVideoMenu', {
                  swingSubElementId,
                  swingShotTypeId,
                  videoTypeId,
                  rejectSwingName,
                  swingName,
                })
              }
            >
              {/* <Icon name="trash" size={20} color="white" /> */}
              <Image
                style={styles.trashIcon}
                source={Images.trashIcon}
                resizeMode="contain"
              />
            </TouchableOpacity>
            <Button
              style={{marginRight: 20}}
              textStyle={{fontWeight: '600'}}
              text={t('video_view.approve')}
              backgroundColor="white"
              borderColor="white"
              onPress={handleApprove}
              centered
            />
          </View>
        </View>
      </View>
      {showCaptureIcon && (
        <View
          style={[
            appStyles.absoluteFill,
            appStyles.vCenter,
            {
              height: '80%',
            },
          ]}
        >
          <View style={styles.boxCaptured}>
            <Text style={styles.textCaptured}>
              {t('video_preview.captured')}
            </Text>
            <Icon
              style={styles.iconCaptured}
              name="check"
              size={widthPercentageToDP('16%')}
              color="green"
            />
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    overflow: 'hidden',
    backgroundColor: 'black',
    width: '100%',
    height: '100%',
  },
  preview: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  capture: {
    flex: 0,
    height: 50,
    width: 50,
  },
  backgroundVideo: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textNote: {
    color: 'white',
    fontSize: 12,
    opacity: 0.8,
    marginHorizontal: 20,
    top: 10,
    marginBottom: 10,
  },
  boxIcon: {
    padding: 10,
    borderColor: 'white',
    borderWidth: 1,
    borderRadius: 50,
    width: 50,
    height: 50,
  },
  boxCaptured: {
    backgroundColor: 'white',
    flexDirection: 'column',
    with: 150,
    height: 150,
    marginHorizontal: 100,
  },
  textCaptured: {
    textAlign: 'center',
    fontSize: 25,
    top: 10,
    fontWeight: 'bold',
    color: 'black',
  },
  iconCaptured: {
    justifyContent: 'center',
    alignSelf: 'center',
    top: 30,
  },
  viewIcon: {
    width: widthPercentageToDP('23%'),
    height: widthPercentageToDP('23%'),
    borderWidth: 5,
    borderColor: 'white',
    borderRadius: widthPercentageToDP('23%'),
    paddingLeft: 10,
  },
  trashIcon: {
    width: 19,
    height: 20,
  },
});

export default VideoPreview;
