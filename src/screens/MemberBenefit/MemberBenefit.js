import React, {useEffect, useImperativeHandle, useRef} from 'react';
import BenefitList from './components/BenefitList';
import {View} from 'react-native';
import Text from 'components/Text';
import {getPerks} from 'utils/home';
import {useDispatch, useSelector} from 'react-redux';
import {GA_logEvent} from 'utils/googleAnalytics';
import {CACHE_KEY, GA_EVENT_NAME} from 'utils/constant';
import {updateDataPerks} from 'reducers/rewards';
import {useIsFocused} from '@react-navigation/native';

const MemberBenefit = ({navigation, route}, ref) => {
  const {navigate} = navigation;
  const perksCache = useSelector(state => state.rewards.perks);
  const benefits = perksCache?.data;
  const dispatch = useDispatch();
  const user = useSelector(state => state?.user);
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const currentPerksCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.REWARD_PERK,
  )?.version;
  const isScreenFocused = useIsFocused();
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (appCacheVersions) {
      refreshPerksCacheDataIfNeeded();
    }
  }, [appCacheVersions]);

  const refreshPerksCacheDataIfNeeded = async () => {
    try {
      if (
        currentPerksCacheVersion === perksCache?.version &&
        appCacheVersions?.country === perksCache?.country &&
        currentPerksCacheVersion != null
      ) {
        if (isScreenFocused && !isFirstRender.current) {
          //if the data is not changed from the Admin Portal, so re-assign old value from cache to trigger the Google Analytics events
          dispatch(
            updateDataPerks({
              country: user?.userCountry,
              version: currentPerksCacheVersion,
              data: perksCache?.data,
            }),
          );
        }
      } else {
        getPerks(dispatch, user?.userCountry, currentPerksCacheVersion);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (benefits && benefits.length) {
      logEventPromotion({
        products: benefits,
        eventName: GA_EVENT_NAME.VIEW_PROMOTION,
      });
      isFirstRender.current = false;
    }
  }, [benefits]);

  const logEventPromotion = ({products, eventName, tileTitle}) => {
    if (products && products.length) {
      const items = [];
      products.map(val => {
        const item = {
          promotion_id: val?.id,
          promotion_name: val?.title,
          creative_name: `${val?.buttonList} CTA`,
          creative_slot: 'Reward > Perk Partner',
          location_id: 'Reward > Perk Partner',
        };
        items.push(item);
      });
      GA_logEvent(eventName, {
        currency: 'USD',
        tile_title: tileTitle,
        items,
      });
    }
  };

  if (benefits?.length > 0) {
    return (
      <View style={{marginBottom: 24, marginTop: -4}}>
        <Text white size={16} style={{marginLeft: 16, marginBottom: 16}}>
          rewards.perk_partners
        </Text>
        <BenefitList navigate={navigate} benefits={benefits} />
      </View>
    );
  }
  return null;
};

export default React.forwardRef(MemberBenefit);
