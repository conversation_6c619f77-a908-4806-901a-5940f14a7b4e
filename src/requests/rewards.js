import {sendRequestMTM} from './api';
import {get} from 'lodash';

export const getFAQs = async data => {
  try {
    const params = {
      ...data,
    };
    const request = await sendRequestMTM({
      url: `/content/faqs`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getLoyaltyActions = async data => {
  try {
    const params = {
      ...data,
    };
    const request = await sendRequestMTM({
      url: `/loyalty/actions`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getRewardProducts = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/shop/products/rewards`,
      method: 'GET',
    });
    return get(request, 'data', []);
  } catch (error) {
    throw error;
  }
};
