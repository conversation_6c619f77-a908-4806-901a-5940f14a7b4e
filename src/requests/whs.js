import {get} from 'lodash';
import {sendRequestMTM} from './api';
import { isCanadaMarket } from 'utils/commonVariable';

export const getWHSGolferProfile = async () => {
  if (!isCanadaMarket()) {
    return {};
  }
  try {
    const request = await sendRequestMTM({
      url: `/mrp/user/golf-net-golfer-profile`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const requestAccessWHS = async (cardId, email) => {
  try {
    const request = await sendRequestMTM({
      url: `/mrp/user/request-access-whs`,
      method: 'POST',
      data: {
        canadaCardId: cardId,
        email: email,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const logoutWHSNHandicap = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/mrp/user/revoke-access-whs`,
      method: 'DELETE',
    });
    return get(request, 'status', 400);
  } catch (error) {
    throw error;
  }
};

export const getScoreWHS = async () => {
  if (!isCanadaMarket()) {
    return {};
  }
  try {
    const request = await sendRequestMTM({
      url: `/mrp/golf-net/scores?skip=0&take=5`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
