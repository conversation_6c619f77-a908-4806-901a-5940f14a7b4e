import queryString from 'query-string';
import axios from 'axios';

export const getPostInformation = async postId => {
  const url = 'https://www.instagram.com/api/graphql';
  const headers = {
    authority: 'www.instagram.com',
    accept: '*/*',
    'content-type': 'application/x-www-form-urlencoded',
    origin: 'https://www.instagram.com',
    referer: `https://www.instagram.com/p/${postId}/`,
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'x-fb-friendly-name': 'PolarisPostActionLoadPostQueryQuery',
    'x-fb-lsd': 'AVoSzCnPe6k',
  };

  const dataPost = {
    lsd: 'AVoSzCnPe6k',
    variables: `{"shortcode":"${postId}","fetch_comment_count":0,"fetch_related_profile_media_count":0,"parent_comment_count":0,"child_comment_count":0,"fetch_like_count":0,"fetch_tagged_user_count":null,"fetch_preview_comment_count":0,"has_threaded_comments":true,"hoisted_comment_id":null,"hoisted_reply_id":null}`,
    server_timestamps: 'true',
    doc_id: '10015901848480474',
  };
  const {data} = await axios.post(url, queryString.stringify(dataPost), {
    headers,
  });

  const shortcode_media = data?.data?.xdt_shortcode_media;
  return {
    width: shortcode_media.dimensions.width,
    height: shortcode_media.dimensions.height,
    likeCount: shortcode_media.edge_media_preview_like.count,
    ownerName: shortcode_media.owner.username,
    ownerFollower: shortcode_media.owner.edge_followed_by?.count,
    ownerIsVerified: shortcode_media.owner.is_verified,
    ownerPicture: shortcode_media.owner.profile_pic_url,
    text: shortcode_media.edge_media_to_caption.edges?.[0]?.node?.text || null,
    timestamp: shortcode_media.taken_at_timestamp,
    // @ts-ignore
    content:
      shortcode_media.__typename === 'XDTGraphSidecar'
        ? shortcode_media.edge_sidecar_to_children.edges.map(element => ({
            type: element.node.__typename === 'GraphImage' ? 'image' : 'video',
            pictureUrl: element.node.display_url,
            videoUrl: element.node.video_url || null,
          }))
        : [
            {
              type:
                shortcode_media.__typename === 'GraphImage' ? 'image' : 'video',
              pictureUrl: shortcode_media.display_url,
              videoUrl: shortcode_media.video_url || null,
            },
          ],
  };
};
