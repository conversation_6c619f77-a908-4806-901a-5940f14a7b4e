import {get} from 'lodash';
import {sendRequestMTM} from './api';

export const getTourTrash = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/tour-trash`,
      method: 'GET',
    });
    return get(request, 'data.data', []);
  } catch (error) {
    throw error;
  }
};

export const joinTourTrash = async id => {
  try {
    await sendRequestMTM({
      url: `/tour-trash/join`,
      method: 'POST',
      data: {
        id,
      },
    });
  } catch (error) {
    throw error;
  }
};
