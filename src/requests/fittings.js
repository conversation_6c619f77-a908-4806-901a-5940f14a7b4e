import {get} from 'lodash';
import {sendRequestMTM} from './api';

export const getUpcomingFittings = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/fitting/coming`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getPastFittings = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/fitting/past`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getLatestRecommendations = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/fitting/latest-recommendations`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getShotData = async Id => {
  try {
    const request = await sendRequestMTM({
      url: `/fitting/shot-data/${Id}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getRecommendationFromFitting = async Id => {
  try {
    const request = await sendRequestMTM({
      url: `/fitting/detail/${Id}?includeRecommendLines=true`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
