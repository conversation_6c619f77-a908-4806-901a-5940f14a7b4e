import {get} from 'lodash';
import {sendRequestMTM} from './api';

export const getClubLaunchMonitor = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/recommendation/club/launch-monitor/drivers`,
      method: 'GET',
    });
    return get(request, 'data', []);
  } catch (error) {
    throw error;
  }
};

export const getClubResults = async clubRecommender => {
  try {
    const request = await sendRequestMTM({
      url: `/recommendation/club/results`,
      method: 'POST',
      data: clubRecommender,
    });
    return get(request, 'data.lhs[0]', {});
  } catch (error) {
    throw error;
  }
};
