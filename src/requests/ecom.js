import axios from 'axios';
import {get} from 'lodash';
import Config from 'react-native-config';
import queryString from 'query-string';
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';

import {getJWT} from '../utils/user';
import {updatePromotions} from '../reducers/home';
import {getConfig, decryptConfig} from '../config/env';
import {getAuth0Client} from 'utils/auth0';
import {sendRequestMTM} from './api';
import {getEcomSite} from 'utils/countries';

export const getOAUTHAccessToken = async () => {
  const username = decryptConfig(Config.ECOM_CLIENT_ID);
  const password = decryptConfig(Config.ECOM_CLIENT_SECRET);
  try {
    const request = await axios({
      url: `https://account.demandware.com/dwsso/oauth2/access_token?client_id=${username}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      auth: {
        username,
        password,
      },
      data: queryString.stringify({
        grant_type: 'client_credentials',
      }),
    });
    return get(request, 'data.access_token', '');
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getJWTCustomerExternal = async email => {
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const ecomSite = getEcomSite();
  if (email) {
    const oauthToken = await getOAUTHAccessToken();
    try {
      const request = await axios({
        url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
          Config.ECOM_API_VERSION,
        )}/customers/auth/trustedsystem`,
        method: 'POST',
        headers: {
          Authorization: `Bearer ${oauthToken}`,
        },
        data: {
          login: email,
          client_id: decryptConfig(Config.ECOM_CLIENT_ID),
        },
      });
      const token = request.headers?.authorization?.replace('Bearer ', '');

      // Save jwt from the request header to use with other endpoints
      await AsyncStorage.setItem('jwtCustomerExternal', token);
      // Set jwt expiration to 30 mins
      await AsyncStorage.setItem(
        'jwtExpiresIn',
        moment().add(25, 'minutes').toISOString(),
      );
      // Save sfcc customer id
      await AsyncStorage.setItem('sfccCustomerId', request.data?.customer_id);
      return token;
    } catch (error) {
      throw error;
    }
  }
};

export const getProductsInCategory = async () => {
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const ecomSite = getEcomSite();

  try {
    const request = await axios({
      url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
        Config.ECOM_API_VERSION,
      )}/product_search?client_id=${decryptConfig(
        Config.ECOM_CLIENT_ID,
      )}&refine_1=cgid=mytmplus&expand=images,availability,prices`,
      method: 'GET',
    });
    return get(request, 'data.hits', []);
  } catch (error) {
    throw error;
  }
};

export const getProductOverwrite = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/shop/products/overwriteEcom`,
      method: 'GET',
    });
    return get(request, 'data.overwrite_products', []);
  } catch (error) {
    return [];
  }
};

export const getProductDetails = async productId => {
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const ecomSite = getEcomSite();

  try {
    const request = await axios({
      url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
        Config.ECOM_API_VERSION,
      )}/products/${productId}?client_id=${decryptConfig(
        Config.ECOM_CLIENT_ID,
      )}&expand=images,prices,availability,variations`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getBasket = async () => {
  const userEmail = await AsyncStorage.getItem('userEmail');
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const token = await getJWT();
  const customerId = await AsyncStorage.getItem('sfccCustomerId');
  const ecomSite = getEcomSite();

  if (userEmail) {
    try {
      const request = await axios({
        url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
          Config.ECOM_API_VERSION,
        )}/customers/${customerId}/baskets`,
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return get(request, 'data', {});
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
};

export const createBasket = async () => {
  const userEmail = await AsyncStorage.getItem('userEmail');
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const token = await getJWT();
  const ecomSite = getEcomSite();

  if (userEmail) {
    try {
      const request = await axios({
        url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
          Config.ECOM_API_VERSION,
        )}/baskets`,
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return get(request, 'data', {});
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
};

export const addProductsToBasket = async ({
  basketId,
  resourceState,
  products,
}) => {
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const token = await getJWT();
  const ecomSite = getEcomSite();

  try {
    const request = await axios({
      url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
        Config.ECOM_API_VERSION,
      )}/baskets/${basketId}/items`,
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'x-dw-resource-state': `${resourceState}`,
      },
      data: products,
    });
    return get(request, 'data', {});
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const removeProductFromBasket = async ({
  basketId,
  resourceState,
  itemId,
}) => {
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const token = await getJWT();
  const ecomSite = getEcomSite();

  try {
    const request = await axios({
      url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
        Config.ECOM_API_VERSION,
      )}/baskets/${basketId}/items/${itemId}`,
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${token}`,
        'x-dw-resource-state': `${resourceState}`,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const updateBasketProduct = async ({
  basketId,
  resourceState,
  itemId,
  updateObject,
}) => {
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const token = await getJWT();
  const ecomSite = getEcomSite();

  try {
    const request = await axios({
      url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
        Config.ECOM_API_VERSION,
      )}/baskets/${basketId}/items/${itemId}`,
      method: 'PATCH',
      headers: {
        Authorization: `Bearer ${token}`,
        'x-dw-resource-state': `${resourceState}`,
      },
      data: updateObject,
    });
    return get(request, 'data', {});
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const deleteBasket = async ({basketId, resourceState}) => {
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const token = await getJWT();
  const ecomSite = getEcomSite();

  try {
    const request = await axios({
      url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
        Config.ECOM_API_VERSION,
      )}/baskets/${basketId}`,
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${token}`,
        'x-dw-resource-state': `${resourceState}`,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const createSFCCSession = async () => {
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const token = await getJWT();
  const ecomSite = getEcomSite();

  try {
    const request = await axios({
      url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
        Config.ECOM_API_VERSION,
      )}/sessions`,
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    // Find the dwsid session identifier from headers
    const data = request.headers['set-cookie'][0]
      .split('; ')
      .filter(cookie => {
        return cookie.includes('dwsid');
      });
    if (data && data.length > 0) {
      let dwsId = '';
      data.map(value => {
        const val = value.split(', ')[1];
        if (val) {
          dwsId = val;
        }
      });
      return dwsId;
    }
    return '';
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getCustomObject = async customObjectKey => {
  const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
  const token = await getJWT();
  const ecomSite = getEcomSite();

  try {
    const request = await axios({
      url: `https://${ECOM_HOST_URL}/s/${ecomSite}/dw/shop/v${decryptConfig(
        Config.ECOM_API_VERSION,
      )}/custom_objects/ProductSpecification/${customObjectKey}?client_id=${decryptConfig(
        Config.ECOM_CLIENT_ID,
      )}`,
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    const parsedTable = JSON.parse(request?.data?.c_table);
    const shaftOptions = [];
    const shaftRecommendations = [];

    // Loop through the parsed table and extract options
    for (let i = 0; i < parsedTable.length; i++) {
      for (let j = 0; j < parsedTable[i].length; j++) {
        if (i === 0) {
          shaftOptions.push(parsedTable[i][j]);
        }
      }
    }

    // Loop through the parsed table and match the shaft option to the value
    for (let i = 0; i < parsedTable.length; i++) {
      if (i !== 0 && parsedTable[i][0]) {
        // Initialize shaft recommendation object
        const shaftRecommendation = {};
        // Loop through the shaft options and create key and match value
        for (let j = 0; j < shaftOptions.length; j++) {
          shaftRecommendation[shaftOptions[j]] = parsedTable[i][j];
        }
        // Push shaft recommendation
        shaftRecommendations.push(shaftRecommendation);
      }
    }

    return shaftRecommendations;
  } catch (error) {
    throw error;
  }
};

export const getPromotion = () => async dispatch => {
  try {
    const request = await sendRequestMTM({
      url: `/promotion`,
      method: 'GET',
    });
    const response = await get(request, 'data', {});
    dispatch(updatePromotions(response));
  } catch (error) {
    throw error;
  }
};
export const getPromotionDetail = async id => {
  try {
    const request = await sendRequestMTM({
      url: `/promotion/${id}`,
      method: 'GET',
    });
    const response = await get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};

export const getAuth0UserProfile = async accessToken => {
  try {
    const auth0 = await getAuth0Client();
    const request = await axios({
      url: `https://${auth0?.auth?.domain}/userinfo`,
      method: 'GET',
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    const response = await get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};
