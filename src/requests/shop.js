import {sendRequestMTM} from './api';
import {get} from 'lodash';

const SHOPTYPE = {
  PRODUCT_TILES: 'PRODUCT_TILES',
  MORE_FOR_YOU: 'MORE_FOR_YOU',
  SUPPORT_CARD: 'SUPPORT_CARD',
  FINAL_TILE: 'FINAL_TILE',
  PROMOTION: 'PROMOTION',
};

const SHOPSTATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
};

export const getMoreForYou = async data => {
  try {
    const params = {
      ...data,
      type: SHOPTYPE.MORE_FOR_YOU,
      sort: true,
      status: SHOPSTATUS.ACTIVE,
    };
    const request = await sendRequestMTM({
      url: `/tiles-widget`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getSupportCards = async data => {
  try {
    const params = {
      ...data,
      type: SHOPTYPE.SUPPORT_CARD,
      sort: true,
      status: SHOPSTATUS.ACTIVE,
    };
    const request = await sendRequestMTM({
      url: `/tiles-widget`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getProductTiles = async data => {
  try {
    const params = {
      ...data,
      type: SHOPTYPE.PRODUCT_TILES,
      sort: true,
      status: SHOPSTATUS.ACTIVE,
    };
    const request = await sendRequestMTM({
      url: `/tiles-widget`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getDataTexts = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/content/texts`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getDiscountItems = async country => {
  try {
    const request = await sendRequestMTM({
      url: `/shop-discount/products`,
      method: 'GET',
      headers: {
        country,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getFinalTile = async data => {
  try {
    const params = {
      ...data,
      type: SHOPTYPE.FINAL_TILE,
      sort: true,
      status: SHOPSTATUS.ACTIVE,
    };
    const request = await sendRequestMTM({
      url: `/tiles-widget`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getPromotionTile = async data => {
  try {
    const params = {
      ...data,
      type: SHOPTYPE.PROMOTION,
      sort: true,
      status: SHOPSTATUS.ACTIVE,
    };
    const request = await sendRequestMTM({
      url: `/tiles-widget`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getTrackingOrders = async email => {
  try {
    const request = await sendRequestMTM({
      url: `/home-product/orders?email=${email}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getShopCategories = async country => {
  try {
    const request = await sendRequestMTM({
      url: `/shop-category?country=${country}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
