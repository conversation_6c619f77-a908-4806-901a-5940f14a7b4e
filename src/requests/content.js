import {get} from 'lodash';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ARCCOS} from '../utils/constant';
import {sendRequestMTM} from './api';

export const getHomeTiles = async playService => {
  const userEmail = await AsyncStorage.getItem('userEmail');
  const playPreference = playService === ARCCOS ? 'ARCCOS' : playService;
  if (userEmail) {
    try {
      const request = await sendRequestMTM({
        url: `/content/home/<USER>/HomeTiles?servicePreference=${playPreference}`,
        method: 'GET',
      });
      return get(request, 'data', []);
    } catch (error) {
      throw error;
    }
  } else {
    return [];
  }
};

export const getHomeExplorer = async playService => {
  const userEmail = await AsyncStorage.getItem('userEmail');
  const playPreference = playService === ARCCOS ? 'ARCCOS' : playService;
  if (userEmail) {
    try {
      const request = await sendRequestMTM({
        url: `/content/home/<USER>/HomeExplorer?servicePreference=${playPreference}`,
        method: 'GET',
      });
      return get(request, 'data', []);
    } catch (error) {
      throw error;
    }
  } else {
    return [];
  }
};

export const getInsights = async playService => {
  const userEmail = await AsyncStorage.getItem('userEmail');
  const playPreference = playService === ARCCOS ? 'ARCCOS' : playService;
  if (userEmail) {
    try {
      const request = await sendRequestMTM({
        url: `/content/Insights?limit=3&servicePreference=${playPreference}`,
        method: 'GET',
      });
      return get(request, 'data.content.insights', []);
    } catch (error) {
      throw error;
    }
  } else {
    return [];
  }
};

export const dismissContent = async widgetId => {
  try {
    await sendRequestMTM({
      url: `/content/dismiss`,
      method: 'POST',
      data: {
        widgetId,
      },
    });
  } catch (error) {
    throw error;
  }
};

export const countContentView = async (contentId, title) => {
  try {
    await sendRequestMTM({
      url: `/content/view`,
      method: 'POST',
      data: {
        contentId: `${contentId}`,
        contentTitle: title,
      },
    });
  } catch (error) {
    throw error;
  }
};

export const updateServicePreference = async playService => {
  try {
    const request = await sendRequestMTM({
      url: `/play/service-preference/update`,
      method: 'POST',
      data: {
        service: `${playService}`,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getHomeStats = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/content/home/<USER>/HomeStats`,
      method: 'GET',
      headers: {
        country: '',
      },
    });
    const response = get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};

export const getEntertainmentList = async (page = 1) => {
  try {
    const request = await sendRequestMTM({
      url: `/cms/clubhouse?&page=${page}`,
      method: 'GET',
      headers: {
        country: '',
      },
    });
    const response = get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};

export const getListCms = async (page = 1) => {
  try {
    const request = await sendRequestMTM({
      url: `/cms/list?&page=${page}`,
      method: 'GET',
      headers: {
        country: '',
      },
    });
    const response = get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};

export const getTourPlayers = async country => {
  try {
    const request = await sendRequestMTM({
      url: `/tiles-widget/player-profiles?country=${country}`,
      method: 'GET',
      headers: {
        country: '',
      },
    });
    const response = get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};

export const getArticlesByPlayer = async (player, page = 1, country) => {
  try {
    const request = await sendRequestMTM({
      url: `/cms/clubhouse?page=${page}&auth=TMDEV_TEAM_BYPASS&tourPlayers=${player}`,
      method: 'GET',
      headers: {
        country: country,
      },
    });
    const response = get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};

export const getSingleCmsContent = async (entryType, contentId) => {
  try {
    const request = await sendRequestMTM({
      url: `/cms/single/${entryType}/${contentId}`,
      method: 'GET',
      headers: {
        country: '',
      },
    });
    const response = get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};

export const getPlayerCmsTags = async country => {
  try {
    const request = await sendRequestMTM({
      url: `/cms/tags?group=mytmProAthlete`,
      method: 'GET',
      headers: {
        country,
      },
    });
    const response = get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};

export const getGearList = async (page = 1) => {
  try {
    const request = await sendRequestMTM({
      url: `/cms/articles/clubhouseArticles?articleCategory=gear&page=${page}`,
      method: 'GET',
      headers: {
        country: '',
      },
    });
    const response = get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};

export const getCollectionList = async country => {
  try {
    const request = await sendRequestMTM({
      url: `/cms/collections`,
      method: 'GET',
      headers: {
        country,
      },
    });
    const response = get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};

export const getCollectionDetail = async (collectionId, country) => {
  try {
    const request = await sendRequestMTM({
      url: `/cms/collections/${collectionId}`,
      method: 'GET',
      headers: {
        country,
      },
    });
    const response = get(request, 'data', {});
    return response;
  } catch (error) {
    throw error;
  }
};
