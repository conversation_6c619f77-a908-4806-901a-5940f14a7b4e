import axios from 'axios';
import {decryptClientIDMyTM, getConfig} from 'config/env';
import {getLanguage, getCountry} from 'utils/commonVariable';
import {COUNTRY_CODE} from 'utils/constant';
import {getIdToken} from 'utils/user';

export const sendRequestMTM = async ({
  url,
  method,
  params,
  data,
  headers,
  options,
}) => {
  try {
    const lang = getLanguage() ? getLanguage() : 'en';
    const country = getCountry() ? getCountry() : COUNTRY_CODE.USA;
    const idToken = await getIdToken();
    const API_URL = await getConfig('MYTM_API_URL');
    const request = await axios({
      baseURL: API_URL,
      url,
      method,
      params: params,
      data,
      headers: {
        language: lang,
        country: country,
        Authorization: idToken ? `Bearer ${idToken}` : '',
        clientId: decryptClientIDMyTM(),
        ...headers,
      },
      ...options,
    });
    return request;
  } catch (error) {
    if (error.response) {
      // The server responded with a status code outside the 2xx range
      const {
        data: responseData,
        status,
        headers: responseHeaders,
      } = error.response;

      throw {
        data: responseData, // Error data returned from the server
        status, // Status code (e.g., 400)
        headers: responseHeaders, // Response headers
        message: responseData?.message || error?.message,
      };
    } else if (error.request) {
      // The request was sent but no response was received
      throw {
        request: error.request,
        message: 'No response received',
      };
    } else {
      // An error occurred while setting up the request
      throw {
        message: error.message,
      };
    }
  }
};
