import axios from 'axios';
import {get} from 'lodash';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ARCCOS, COUNTRY_CODE} from '../utils/constant';
import {getIdToken} from '../utils/user';
import {GUID_EMPTY} from '../utils/constant';
import {getConfig, decryptClientIDMyTM, ENVIRONMENTS} from '../config/env';
import {sendRequestMTM} from './api';
import { getCountry } from 'utils/commonVariable';

export const login = async accessToken => {
  const API_URL = await getConfig('MYTM_API_URL');
  try {
    const request = await axios({
      url: `${API_URL}/auth/sso/login`,
      method: 'POST',
      headers: {
        clientId: decryptClientIDMyTM(),
      },
      data: {
        accessToken,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const register = async (email, password, phoneNumber) => {
  const API_URL = await getConfig('MYTM_API_URL');
  try {
    let data = {
      email,
      password,
    };
    if (phoneNumber && phoneNumber.length >= 10) {
      data = {
        ...data,
        phoneNumber,
      };
    }
    const request = await axios({
      url: `${API_URL}/auth/register`,
      method: 'POST',
      headers: {
        clientId: decryptClientIDMyTM(),
      },
      data,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const registerNoOTP = async (
  email,
  password,
  firstName,
  lastName,
  userCountry,
) => {
  const API_URL = await getConfig('MYTM_API_URL');
  try {
    let data = {
      email,
      password,
      firstName,
      lastName,
      userCountry,
    };

    const request = await axios({
      url: `${API_URL}/auth/register/by-pass-otp`,
      method: 'POST',
      headers: {
        clientId: decryptClientIDMyTM(),
      },
      data,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const changePassword = async (oldPassword, newPassword) => {
  try {
    await sendRequestMTM({
      url: `/auth/change-password`,
      method: 'POST',
      data: {
        password: oldPassword,
        newPassword,
      },
    });
  } catch (error) {
    throw error;
  }
};

export const getAuth0Tokens = async (email, password) => {
  const API_URL = await getConfig('MYTM_API_URL');
  try {
    const request = await axios({
      url: `${API_URL}/auth/auth0/access-token`,
      method: 'POST',
      headers: {
        clientId: decryptClientIDMyTM(),
      },
      data: {
        email,
        password,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
export const getNewTokens = async refreshToken => {
  const API_URL = await getConfig('MYTM_API_URL');
  try {
    const request = await axios({
      url: `${API_URL}/auth/token/refresh`,
      method: 'POST',
      headers: {
        clientId: decryptClientIDMyTM(),
      },
      data: {
        refreshToken,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getUser = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/me`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    console.log(error);
  }
};

export const getDataLocalization = async lng => {
  try {
    const request = await sendRequestMTM({
      url: `/translations/locales/${lng}.json`,
      method: 'GET',
      options: {
        timeout: 10000,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getListDataLocalization = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/translations/locales`,
      method: 'GET',
      options: {
        timeout: 10000,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const updateUser = async attributes => {
  try {
    const request = await sendRequestMTM({
      url: `/profile/update`,
      method: 'POST',
      data: attributes,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const setUserOptIn = async payload => {
  try {
    const request = await sendRequestMTM({
      url: `/consumer-opt-in/save`,
      method: 'POST',
      data: payload,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getProPlayers = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/cms/tour-players`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getMyBag = async playService => {
  const userEmail = await AsyncStorage.getItem('userEmail');
  const playPreference = playService === ARCCOS ? 'ARCCOS' : playService;
  if (userEmail) {
    try {
      const request = await sendRequestMTM({
        url: `/witb?servicePreference=${playPreference}`,
        method: 'GET',
      });
      return get(request, 'data', {});
    } catch (error) {
      throw error;
    }
  } else {
    return [];
  }
};

export const getClubCategories = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/club/categories?take=9999&skip=0`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getAllClubsInCategory = async Id => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/club/category-types-by-category/${Id}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getAllClubsWithCategoryType = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/club/category-types/query`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getAllClubBrands = async Id => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/brands-by-club-category/${Id}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getAllClubModels = async Id => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/models-by-brand/${Id}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getAllClubLofts = async iD => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/club/lofts-by-category/${iD}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getAllClubShaftFlex = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/club/shaft-flex?take=9999&skip=0`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getAllClubShaftLength = async categoryId => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/club/shaft-length-by-club-category/${categoryId}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getAllClubLieAdjustments = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/club/lies?take=9999&skip=0`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getAllClubLoftAdjustments = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/club/loft-adjustment?take=9999&skip=0`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const addClubToBag = async ({
  categoryId,
  categoryTypeId,
  brandId,
  modelId,
  loft,
  shaftFlex,
  shaftLength,
  faceLieAdjustment,
  faceLoftAdjustment,
  inBag,
  completeAction = false,
}) => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/create`,
      method: 'POST',
      data: {
        categoryId,
        categoryTypeId,
        brandId: brandId || GUID_EMPTY,
        modelId: modelId || GUID_EMPTY,
        clubLoftId: loft,
        clubShaftFlexId: shaftFlex,
        clubShaftLengthId: shaftLength,
        faceLieAdjustmentId: faceLieAdjustment,
        faceLoftAdjustmentId: faceLoftAdjustment,
        inBag: inBag,
        completeAction,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const updateClubinBag = async (
  isUpdateAll,
  id,
  brandId,
  modelId,
  inBag,
  loft,
  shaftFlex,
  shaftLength,
  faceLieAdjustment,
  faceLoftAdjustment,
) => {
  let data = {
    id,
    brandId: brandId || GUID_EMPTY,
    modelId: modelId || GUID_EMPTY,
  };
  if (isUpdateAll) {
    data = {
      ...data,
      inBag,
      clubLoftId: get(loft, 'loftId[0].id', null),
      clubShaftFlexId: get(shaftFlex, 'shaftFlexId[0].id', null),
      clubShaftLengthId: get(shaftLength, 'shaftLengthId[0].id', null),
      faceLieAdjustmentId: get(
        faceLieAdjustment,
        'faceLieAdjustmentId[0].id',
        null,
      ),
      faceLoftAdjustmentId: get(
        faceLoftAdjustment,
        'faceLoftAdjustmentId[0].id',
        null,
      ),
    };
  }
  try {
    const request = await sendRequestMTM({
      url: `/witb/update`,
      method: 'POST',
      data,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const deleteClubinBag = async id => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/update`,
      method: 'POST',
      data: {
        id,
        deleted: true,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const updateStatusClubinBag = async (id, inBag) => {
  try {
    const request = await sendRequestMTM({
      url: `/witb/update`,
      method: 'POST',
      data: {
        id,
        inBag,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const sendOTP = async email => {
  try {
    const request = await sendRequestMTM({
      url: `/auth/otp/send`,
      method: 'POST',
      data: {
        email,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
export const sendSMSOTP = async (email, phoneNumber) => {
  try {
    let data = {
      email,
    };
    if (phoneNumber) {
      data = {...data, phoneNumber};
    }
    const request = await sendRequestMTM({
      url: `/auth/sms-otp/v2/send`,
      method: 'POST',
      data,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const verifyOTP = async (email, otpCode) => {
  try {
    const request = await sendRequestMTM({
      url: `/auth/otp/verify`,
      method: 'POST',
      data: {
        email,
        otpCode,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const verifyOTPSMS = async (email, otpCode, phoneNumber) => {
  try {
    const request = await sendRequestMTM({
      url: `/auth/sms-otp/v2/verify`,
      method: 'POST',
      data: {
        email,
        otpCode,
        phoneNumber,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const subscribeToFCM = async token => {
  try {
    const request = await sendRequestMTM({
      url: `/fcm/subscribe`,
      method: 'POST',
      data: {
        token,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const verifyAccessCode = async code => {
  try {
    const request = await sendRequestMTM({
      url: `/access-code/verify`,
      method: 'POST',
      data: {
        code,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getPermissions = async () => {
  const userEmail = await AsyncStorage.getItem('userEmail');
  if (userEmail) {
    try {
      const request = await sendRequestMTM({
        url: `/my-permission`,
        method: 'GET',
      });
      return get(request, 'data', {});
    } catch (error) {
      throw error;
    }
  }
};

export const getStatsClub = async (id, params) => {
  try {
    const request = await sendRequestMTM({
      url: `/stats/club/${id}?${params}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getStatsClubPutter = async (id, params) => {
  try {
    const request = await sendRequestMTM({
      url: `/stats/club/${id}/putter?${params}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const deleteMyAccount = async attributes => {
  try {
    const request = await sendRequestMTM({
      url: `/profile`,
      method: 'DELETE',
      data: attributes,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getCountries = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/countries`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getImageConfigs = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/content/images`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
