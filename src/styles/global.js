import {StyleSheet} from 'react-native';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {moderateScale} from 'react-native-size-matters';

import {
  DARK_GREY,
  ERROR_RED,
  GREEN,
  GREY,
  LIGHT_GREY,
  LINK_BLUE,
  PRIMARY_COLOR,
} from '../config';

const appStyles = StyleSheet.create({
  flex: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
  },
  column: {
    flexDirection: 'column',
  },
  full: {
    height: '100%',
    width: '100%',
  },
  fullWidth: {
    width: '100%',
  },
  fullHeight: {
    height: '100%',
  },
  halfWidth: {
    minWidth: '60%',
  },
  center: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  rotated: {
    transform: [{rotate: '270deg'}],
  },
  wrap: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  responsiveImageBallStrike: {
    height: undefined,
    aspectRatio: 16 / 9,
    width: '100%',
  },
  responsiveImageBallShape: {
    height: undefined,
    aspectRatio: 301 / 251,
    width: '100%',
  },
  responsiveBackSpin: {
    height: undefined,
    aspectRatio: 280 / 200,
    width: '100%',
  },
  responsiveHeadSpeed: {
    height: undefined,
    aspectRatio: 1,
    width: '100%',
  },
  responsiveClubLie: {
    height: undefined,
    aspectRatio: 1,
    width: '100%',
  },
  responsiveClubLoft: {
    height: undefined,
    aspectRatio: 1,
    width: '100%',
  },
  responsiveFaceAngle: {
    height: undefined,
    aspectRatio: 1,
    width: '100%',
  },
  responsiveImageSquare: {
    height: undefined,
    aspectRatio: 1 / 1,
    width: '100%',
    borderWidth: 1,
    borderColor: LIGHT_GREY,
  },
  responsivePeak: {
    height: undefined,
    aspectRatio: 200 / 120,
    width: '100%',
  },

  // Positioning

  hCenter: {
    alignItems: 'center',
  },
  vCenter: {
    justifyContent: 'center',
  },
  vBottom: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  textCenter: {
    textAlign: 'center',
  },
  textRight: {
    textAlign: 'right',
  },
  textLeft: {
    textAlign: 'left',
  },
  spaceEnd: {
    justifyContent: 'flex-end',
  },
  spaceEvenly: {
    justifyContent: 'space-evenly',
  },
  spaceAround: {
    justifyContent: 'space-around',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignBetween: {
    alignContent: 'space-between',
  },
  alignEnd: {
    alignSelf: 'flex-end',
  },
  alignCenter: {
    alignSelf: 'center',
  },
  absoluteFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bottom: {
    position: 'absolute',
    bottom: 0,
  },
  bottomLeft: {
    position: 'absolute',
    left: 20,
    bottom: 20,
  },
  topLeft: {
    position: 'absolute',
    left: 0,
    top: 0,
  },
  topRight: {
    position: 'absolute',
    right: 20,
    top: 20,
  },

  // Navigation

  headerStyle: {
    backgroundColor: PRIMARY_COLOR,
  },
  bottomTabs: {
    backgroundColor: '#fff',
  },

  // Background Color

  primaryBg: {
    backgroundColor: PRIMARY_COLOR,
  },
  whiteBg: {
    backgroundColor: '#fff',
  },
  blackBg: {
    backgroundColor: '#000',
  },
  darkGreyBg: {
    backgroundColor: DARK_GREY,
  },
  lightGreyBg: {
    backgroundColor: '#F5F6F7',
  },
  greyBg: {
    backgroundColor: GREY,
  },
  lightSlateGray: {
    backgroundColor: '#242424',
  },
  transparentGreyBg: {
    backgroundColor: 'rgba(0, 0, 0, .05)',
  },
  greyBorder: {
    borderColor: '#F5F6F7',
  },
  greenBg: {
    backgroundColor: GREEN,
  },
  imageGrayBg: {
    backgroundColor: '#E5E5E5',
  },
  // Border

  borderRadius: {
    borderRadius: 10,
  },
  borderTopRadius: {
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  borderBottomRadius: {
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  pill: {
    borderRadius: 100,
  },

  // Button

  fullButton: {
    width: '100%',
  },
  button: {
    minWidth: 300,
  },
  buttonWMd: {
    minWidth: '25%',
  },
  buttonContentStyle: {
    padding: 5,
  },
  buttonLabel: {
    color: '#fff',
  },
  secondaryButton: {
    backgroundColor: '#4a4a4a',
  },
  buttonGrayBg: {
    backgroundColor: '#FFFFFF33',
  },

  // Modal

  modal: {
    width: '100%',
    height: '95%',
    backgroundColor: 'white',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.6)',
  },
  indicator: {
    width: wp('8%'),
    height: hp('0.3%'),
    backgroundColor: 'white',
    borderRadius: 20,
  },
  indicatorGrey: {
    flex: 1,
    height: 1,
    backgroundColor: GREY + '50',
  },

  // Font Type

  hr: {
    borderBottomColor: GREY + '50',
    borderBottomWidth: moderateScale(1),
  },
  hrTop: {
    borderTopColor: GREY + '50',
    borderTopWidth: moderateScale(1),
  },
  white: {
    color: '#fff',
  },
  black: {
    color: '#000',
  },
  gray: {
    color: 'rgba(140, 139, 143, 1)',
  },
  lightGrey: {
    color: LIGHT_GREY,
  },
  grey: {
    color: GREY,
  },
  primary: {
    color: PRIMARY_COLOR,
  },
  green: {
    color: GREEN,
  },
  secondary: {
    color: '#4a4a4a',
  },
  red: {
    color: ERROR_RED,
  },
  blue: {
    color: LINK_BLUE,
  },
  bold: {
    fontWeight: 'bold',
  },
  semiBold: {
    fontWeight: '600',
  },
  boldL: {
    fontWeight: '700',
  },
  italic: {
    fontStyle: 'italic',
  },
  underlined: {
    textDecorationLine: 'underline',
  },
  uppercase: {
    textTransform: 'uppercase',
  },
  greyQuizHanded: {
    color: '#8C8C91',
  },
  greyText: {
    color: '#8C8C90',
  },

  // Font Size

  xxs: {
    fontSize: hp('1.2%'),
  },
  xs: {
    fontSize: hp('1.5%'),
  },
  xsm: {
    fontSize: hp('1.7%'),
  },
  sm: {
    fontSize: hp('1.9%'),
  },
  smm: {
    fontSize: hp('2.1%'),
  },
  mds: {
    fontSize: hp('2.2%'),
  },
  mdms: {
    fontSize: hp('2.5%'),
  },
  md: {
    fontSize: hp('2.9%'),
  },
  xmd: {
    fontSize: hp('3.3%'),
  },
  lg: {
    fontSize: hp('3.9%'),
  },
  mlg: {
    fontSize: hp('4.4%'),
  },
  xl: {
    fontSize: hp('4.9%'),
  },
  xxl: {
    fontSize: hp('5.9%'),
  },
  xxxl: {
    fontSize: hp('8.6%'),
  },
  xxxxl: {
    fontSize: hp('10.7%'),
  },
  xxxxxl: {
    fontSize: hp('15.4%'),
  },
  xxxs: {
    fontSize: hp('6.9%'),
  },
  xsm: {
    fontSize: hp('1.7%'),
  },

  // Margin

  mHXs: {
    marginHorizontal: '2%',
  },
  mHSm: {
    marginHorizontal: '4%',
  },
  mHMd: {
    marginHorizontal: '8%',
  },
  mTXxs: {
    marginTop: '1%',
  },
  mTXs: {
    marginTop: '2%',
  },
  mTSm: {
    marginTop: '4%',
  },
  mTMd: {
    marginTop: '8%',
  },
  mTMLg: {
    marginTop: '12%',
  },
  mTLg: {
    marginTop: '16%',
  },
  mTXl: {
    marginTop: '32%',
  },
  mTAuto: {
    marginTop: 'auto',
  },
  mHAuto: {
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  noMt: {
    marginTop: 0,
  },
  noMb: {
    marginBottom: 0,
  },
  mBXxs: {
    marginBottom: '1%',
  },
  mBXs: {
    marginBottom: '2%',
  },
  mBSm: {
    marginBottom: '4%',
  },
  mBMd: {
    marginBottom: '8%',
  },
  mBLg: {
    marginBottom: '16%',
  },
  mBXl: {
    marginBottom: '32%',
  },
  mRXs: {
    marginRight: '2%',
  },
  mRXxs: {
    marginRight: '1%',
  },
  mRSm: {
    marginRight: '4%',
  },
  mRMd: {
    marginRight: '8%',
  },
  mRLg: {
    marginRight: '16%',
  },
  mRAuto: {
    marginRight: 'auto',
  },
  mLAuto: {
    marginLeft: 'auto',
  },
  mLXxs: {
    marginLeft: '1%',
  },
  mLXs: {
    marginLeft: '2%',
  },
  mLSm: {
    marginLeft: '4%',
  },
  mLMd: {
    marginLeft: '8%',
  },
  mLLg: {
    marginLeft: '16%',
  },
  mVXs: {
    marginVertical: '2%',
  },
  mVSm: {
    marginVertical: '4%',
  },
  mVMd: {
    marginVertical: '8%',
  },
  mVLg: {
    marginVertical: '16%',
  },
  mVXl: {
    marginVertical: '32%',
  },
  mXs: {
    margin: '2%',
  },
  mSm: {
    margin: '4%',
  },
  mMd: {
    margin: '8%',
  },

  // Padding

  pLXs: {
    paddingLeft: '2%',
  },
  pLSm: {
    paddingLeft: '4%',
  },
  pLMd: {
    paddingLeft: '8%',
  },
  pLLg: {
    paddingLeft: '16%',
  },
  pRXs: {
    paddingRight: '2%',
  },
  pRSm: {
    paddingRight: '4%',
  },
  pRMd: {
    paddingRight: '8%',
  },
  pRLg: {
    paddingRight: '16%',
  },
  pRXl: {
    paddingRight: '32%',
  },
  pTXs: {
    paddingTop: '2%',
  },
  pTSm: {
    paddingTop: '4%',
  },
  pTMd: {
    paddingTop: '8%',
  },
  pTLg: {
    paddingTop: '16%',
  },
  pTXl: {
    paddingTop: '32%',
  },
  pBXs: {
    paddingBottom: '2%',
  },
  pBSm: {
    paddingBottom: '4%',
  },
  pBMd: {
    paddingBottom: '8%',
  },
  pBLg: {
    paddingBottom: '16%',
  },
  pHXs: {
    paddingHorizontal: '2%',
  },
  pHSm: {
    paddingHorizontal: '4%',
  },
  pHXSm: {
    paddingHorizontal: '6%',
  },
  pHMd: {
    paddingHorizontal: '8%',
  },
  pHLg: {
    paddingHorizontal: '16%',
  },
  pVXs: {
    paddingVertical: '2%',
  },
  pVSm: {
    paddingVertical: '4%',
  },
  pVMd: {
    paddingVertical: '8%',
  },
  pVLg: {
    paddingVertical: '16%',
  },
  pXxs: {
    padding: '1%',
  },
  pXs: {
    padding: '2%',
  },
  pSm: {
    padding: '4%',
  },
  pXSm: {
    padding: '6%',
  },
  pMd: {
    padding: '8%',
  },
  pXMd: {
    padding: '12%',
  },
  pLg: {
    padding: '16%',
  },

  // Border(dev use)
  borderRed: {
    borderColor: '#ff0000',
    borderWidth: 1,
    borderStyle: 'solid',
  },
  // Ball Strike Circle
  circle: {
    position: 'absolute',
    width: wp('14%'),
    height: wp('14%'),
  },
  circleRightPure: {
    bottom: '5%',
    right: '45%',
  },
  circleRightToe: {
    bottom: '7%',
    right: '63%',
  },
  circleRightShank: {
    bottom: '5%',
    right: '24%',
  },
  circleRightHeel: {
    bottom: '3%',
    right: '30%',
  },
  circleRightFat: {
    bottom: '19%',
    left: '36%',
  },
  circleRightThin: {
    bottom: '-8%',
    left: '36%',
  },
  circleLeftPure: {
    bottom: '5%',
    left: '46%',
  },
  circleLeftShank: {
    bottom: '5%',
    left: '24%',
  },
  circleLeftToe: {
    bottom: '7%',
    left: '63%',
  },
  circleLeftHeel: {
    bottom: '3%',
    left: '30%',
  },
  circleLeftFat: {
    bottom: '17%',
    left: '48%',
  },
  circleLeftThin: {
    bottom: '-8%',
    left: '50%',
  },
  //padding horizontal with 5%
  pdHzt: {
    paddingHorizontal: '5%',
  },
  viewShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  viewShadowTop: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -1,
    },
    shadowOpacity: 0.17,
    shadowRadius: 2.62,
    elevation: 4,
  },
  viewDispersionPointShadow: {
    shadowColor: 'rgba(62, 77, 84, 0.2)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 1,
    elevation: 5,
  },
  viewShadowLight: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.11,
    shadowRadius: 1.31,
    elevation: 2,
  },
  viewShadowLightBig: {
    shadowColor: '#00000080',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
});

export default appStyles;
